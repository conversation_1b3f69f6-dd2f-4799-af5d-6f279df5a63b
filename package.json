{"name": "seo-analyser-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "aria": "npx eslint --fix \"./src/app/(dashboard)/project/\" && npx prettier --write \"./src/app/(dashboard)/project/\"", "server": "sudo node server.js"}, "dependencies": {"@babel/runtime": "^7.27.6", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@mantine/hooks": "^8.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.8", "@swc/helpers": "^0.5.17", "@tanstack/react-query": "^5.68.0", "apexcharts": "^4.5.0", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "d3": "^7.9.0", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "framer-motion": "^12.19.1", "fs": "^0.0.1-security", "html2pdf": "^0.0.11", "html2pdf.js": "^0.10.3", "https": "^1.0.0", "lodash.isequal": "^4.5.0", "lucide-react": "^0.525.0", "next": "^15.3.2", "next-auth": "^5.0.0-beta.27", "picocolors": "^1.1.1", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-chrome-dino": "^0.1.3", "react-circular-progressbar": "^2.2.0", "react-date-range": "^2.0.1", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-loading-skeleton": "^3.5.0", "react-markdown": "^10.1.0", "react-masonry-css": "^1.0.16", "react-to-print": "^3.1.0", "react-tooltip": "^5.28.1", "react-world-flags": "^1.6.0", "recharts": "^2.15.4", "styled-jsx": "^5.1.7", "swiper": "^11.2.5", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tw-animate-css": "^1.3.5", "yup": "^1.6.1", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/d3": "^7.4.3", "@types/exceljs": "^0.5.3", "@types/lodash.isequal": "^4.5.8", "@types/node": "20.19.6", "@types/react": "19.1.8", "@types/react-dom": "^19", "@types/react-world-flags": "^1.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-next": "15.2.0", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.0", "typescript": "^5"}, "optionalDependencies": {"@tailwindcss/oxide-linux-x64-gnu": "4.0.9", "lightningcss-linux-x64-gnu": "1.29.1"}}