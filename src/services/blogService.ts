import { blogAPI } from "./httpService";

// Blog post types
export interface BlogPost {
  id: number;
  title: string;
  slug: string;
  author: {
    id: number;
    email: string;
    display_name: string;
  };
  body: string;
  publish_timestamp: number;
  status: string;
  snippet: string;
  cover_image: string | null;
  url: string;
  tags: string[];
  similar_posts: {
    id: number;
    title: string;
    slug: string;
    author: {
      id: number;
      email: string;
      display_name: string;
    };
    publish_timestamp: number;
    tags: string[];
    url?: string;
  }[];
}

export interface BlogPostResult {
  id: number;
  title: string;
  slug: string;
  author: {
    id: number;
    email: string;
    display_name: string;
  };
  body: string;
  publish_timestamp: number;
  status: string;
  snippet: string;
  cover_image: string | null;
  url: string;
  tags: string[];
  similar_posts: Array<{
    id: number;
    title: string;
    slug: string;
    author: {
      id: number;
      email: string;
      display_name: string;
    };
    publish_timestamp: number;
    tags: string[];
  }>;
}

export interface BlogApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BlogPostResult[];
  categories: Category[];
}

export interface Category {
  name: string;
  slug: string;
}

export interface SearchResponse {
  query: string;
  results: SearchResult[];
  count: number;
  categories: Category[];
}

export interface SearchResult {
  id: number;
  title: string;
  slug: string;
  category: {
    name: string;
    slug: string;
  };
  author: string;
  publish_timestamp: number;
  snippet: string;
  body_json?: {
    text: string;
    blocks: any[];
  };
  body?: string;
  cover_image?: string;
  tags: string[];
  url: string;
}

/**
 * Get blog post by slug
 * @param slug The blog post slug
 * @returns Promise with the blog post data
 */
export async function getBlogPostBySlug(slug: string): Promise<BlogPost> {
  try {
    const response = await blogAPI.getBlogPostBySlug(slug);
    return response.data;
  } catch (error) {
    console.error("Error fetching blog post:", error);
    throw error;
  }
}

/**
 * Get blog posts with pagination and filters
 * @param params Query parameters for filtering and pagination
 * @returns Promise with the blog posts data
 */
export async function getBlogPosts(params?: {
  page?: number;
  category?: string;
  q?: string;
  tag?: string;
}): Promise<BlogApiResponse> {
  try {
    const response = await blogAPI.getBlogPosts(params);
    return response.data;
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    throw error;
  }
}

/**
 * Search blog posts
 * @param query Search query
 * @param page Page number for pagination
 * @returns Promise with the search results
 */
export async function searchBlogPosts(
  query: string,
  page?: number
): Promise<SearchResponse> {
  try {
    const response = await blogAPI.searchBlogPosts(query, page);
    return response.data;
  } catch (error) {
    console.error("Error searching blog posts:", error);
    throw error;
  }
}

const blogService = {
  getBlogPostBySlug,
  getBlogPosts,
  searchBlogPosts,
};

export default blogService;
