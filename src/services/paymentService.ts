import http from "./httpService";

/**
 * Interface for the create session request payload
 */
export interface CreateSessionRequest {
  plan_id: string;
  email?: string;
  task_id?: string;
  product_id?: string; // Added product_id field
}

/**
 * Interface for the create session response
 */
export interface CreateSessionResponse {
  session_id?: string;
  url?: string;
  checkout_url?: string;
  redirect_url?: string;
  success?: boolean;
}

/**
 * Interface for subscription data from profile API
 */
export interface SubscriptionData {
  plan_name: string;
  current_period_start: string;
  current_period_end: string;
  trial_start: string | null;
  trial_end: string | null;
  price_amount: number;
  price_currency: string;
  price_interval: string;
  price_interval_count: number;
}

/**
 * Interface for user profile data in payment response
 */
export interface UserProfileData {
  email: string;
  first_name: string;
  last_name: string;
  is_verified: boolean;
  subscriptions: SubscriptionData[];
}

/**
 * Interface for the payment status response
 */
export interface PaymentStatusResponse {
  payment_status: string;
  status?: string; // For backward compatibility
  amount: string;
  plan: string;
  plan_period: string;
  profile: UserProfileData | any | null;
  whitelabel_setting: any | null;
  expire_date: string;
  data: Record<string, any>;
  message?: string; // Error message
  error?: string; // Error message from API
  plan_details?: {
    name: string;
    period: string;
    price: string;
  };
}

/**
 * Create a payment session with Stripe
 * @param data The session data including plan_id and task_id
 * @returns Promise with the session data including the redirect URL
 */
export async function createPaymentSession(
  data: CreateSessionRequest
): Promise<CreateSessionResponse> {
  return http
    .post("/api/accounts/payments/create-session/", data, { useAuth: true })
    .then(({ data }) => data)
    .catch((error) => {
      console.error("Error creating payment session:", error);
      // Check if it's an authentication error
      if (error.response && error.response.status === 401) {
        console.error(
          "Authentication error: User not authenticated or token expired"
        );
      }
      throw error;
    });
}

/**
 * Check payment status
 * @param sessionId The payment session ID to check
 * @returns Promise with the payment status data
 */
export async function checkPaymentStatus(
  sessionId: string
): Promise<PaymentStatusResponse> {
  return http
    .get(`/api/accounts/payments/check-status/${sessionId}/`, { useAuth: true })
    .then(({ data }) => data)
    .catch((error) => {
      console.error("Error checking payment status:", error);
      throw error;
    });
}

const paymentService = {
  createPaymentSession,
  checkPaymentStatus,
};

export default paymentService;
