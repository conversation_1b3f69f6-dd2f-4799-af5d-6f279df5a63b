import axios from "axios";
import storageService from "./storageService";
const token = storageService.getToken();
const headerAxios = {
  headers: {
    Authorization: `Bearer ${token}`,
  },
};

export type InvoiceItem = {
  payment_id: string;
  payment_amount: {
    amount: number;
    formatted: string;
  };
  discount_amount: {
    amount: number;
    formatted: string;
  };
  discount_details: any[];
  payment_status: string;
  invoice_pdf_url: string;
  invoice_number: string;
  product_name: string;
  billing_period: {
    period_start: string;
    period_end: string;
    interval: string | null;
  } | null;
  created_date: string;
  paid_date: string | null;
  currency: string;
};

type InvoicesType = {
  count: number;
  message: string;
  next: null | number;
  previous: null | number;
  results: InvoiceItem[];
};

type PresetType = {
  avatars: {
    id: number;
    name: string;
    image_url: string;
    is_default: boolean;
  }[];
  count: number;
};

const getInvoices = async (): Promise<InvoicesType> => {
  console.log("get /api/accounts/invoices/", headerAxios);

  const { data } = await axios.get("/api/accounts/invoices/", headerAxios);
  return data;
};

const getPreset = async (): Promise<PresetType> => {
  console.log("get /api/accounts/avatars/", headerAxios);

  const { data } = await axios.get("/api/accounts/avatars/", headerAxios);
  return data;
};

const updateProfile = async (body: any) => {
  console.log("get /api/accounts/profile/settings/", headerAxios);

  const { data } = await axios.patch(
    "/api/accounts/profile/settings/",
    body,
    headerAxios
  );
  return data;
};

const deleteAccount = async () => {
  const { data } = await axios.delete("/api/accounts/delete/", headerAxios);
  return data;
};

const dashbordService = {
  getInvoices,
  getPreset,
  updateProfile,
  deleteAccount,
};
export default dashbordService;
