import { string } from "yup";
import http from "./httpService";
import storageService from "./storageService";

/**
 * Interface for subscription data
 */
export interface Subscription {
  local_subscription_id?: string; // Local subscription ID for management operations
  stripe_subscription_id?: string; // Stripe subscription ID
  plan_name: string;
  status?: string; // Subscription status (active, canceled, etc.)
  current_period_start: string;
  current_period_end: string;
  trial_start: string | null;
  trial_end: string | null;
  price_amount: number;
  price_currency: string;
  price_interval: string;
  price_interval_count: number;
  cancel_at_period_end?: boolean; // Whether subscription is set to cancel at period end
  auto_renew?: boolean; // Whether subscription auto-renews (defaults to true if not present)
}

interface ProfileSetting {
  business_type: string;
  company_size: string;
  user_role: string;
  offers_seo_services: string;
  help_areas: string[];
  interested_features: string[];
  first_name: string;
  last_name: string;
  phone_number: string;
  current_avatar_id: string;
}

/**
 * Interface for user profile data
 */
export interface UserProfile {
  email: string;
  first_name: string;
  last_name: string;
  is_verified: boolean;
  date_joined: string;
  auth_provider: string;
  google_id: string | null;
  avatar_url: string | null;
  subscriptions: Subscription[];
}

/**
 * Interface for profile API response
 */
export interface ProfileResponse {
  success: boolean;
  data?: UserProfile;
  error?: string;
}

/**
 * Get user profile data
 * @returns Promise with the user profile data
 */
export async function getUserProfile(): Promise<ProfileResponse> {
  console.log("Fetching user profile data from server");

  try {
    const response = await http.get("/api/accounts/profile/", {
      useAuth: true,
    });

    if (response.data) {
      console.log("User profile data fetched successfully:", {
        email: response.data.email,
        name: `${response.data.first_name} ${response.data.last_name}`,
        isVerified: response.data.is_verified,
        subscriptions: response.data.subscriptions?.length || 0,
      });

      return {
        success: true,
        data: response.data,
      };
    } else {
      console.warn("Profile API returned success but no data");
      return {
        success: false,
        error: "No profile data returned from server",
      };
    }
  } catch (error: any) {
    console.error("Error fetching user profile:", error);

    // Extract error message
    let errorMessage = "Failed to fetch user profile. Please try again.";
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
      console.error("Server error detail:", error.response.data.detail);
    } else if (error.message) {
      errorMessage = error.message;
      console.error("Error message:", error.message);
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Check if user has an active subscription (based on current period)
 * @returns Promise<boolean> - True if user has an active subscription
 */
export async function hasActiveSubscription(): Promise<boolean> {
  console.log("Checking if user has active subscription");

  try {
    const response = await getUserProfile();

    if (response.success && response.data) {
      // Log all subscriptions for debugging
      if (
        response.data.subscriptions &&
        response.data.subscriptions.length > 0
      ) {
        console.log(
          "User subscriptions:",
          response.data.subscriptions.map((sub) => ({
            plan: sub.plan_name,
            current_period_end: sub.current_period_end,
            price_amount: sub.price_amount,
            price_currency: sub.price_currency,
          }))
        );
      } else {
        console.log("User has no subscriptions");
      }

      // Check if user has at least one active subscription (current period hasn't ended)
      const now = new Date();
      const hasActive = response.data.subscriptions.some((subscription) => {
        const periodEnd = new Date(subscription.current_period_end);
        return periodEnd > now;
      });

      console.log("User has active subscription:", hasActive);
      return hasActive;
    }

    console.log("Failed to get user profile data for subscription check");
    return false;
  } catch (error) {
    console.error("Error checking subscription:", error);
    return false;
  }
}

/**
 * Check if user has an active white label subscription (deprecated - use hasActiveProPlanSubscription)
 * @returns Promise<boolean> - True if user has an active Pro Plan subscription
 */
export async function hasActiveWhiteLabelSubscription(): Promise<boolean> {
  console.log(
    "Checking if user has active Pro Plan subscription (via deprecated white label function)"
  );
  return hasActiveProPlanSubscription();
}

/**
 * Check if user has an active Pro Plan subscription
 * @returns Promise<boolean> - True if user has an active Pro Plan subscription
 */
export async function hasActiveProPlanSubscription(): Promise<boolean> {
  console.log("Checking if user has active Pro Plan subscription");

  try {
    const response = await getUserProfile();

    if (response.success && response.data) {
      // Check if user has at least one active Pro Plan subscription (includes legacy White label plans)
      const now = new Date();
      const hasProPlan = response.data.subscriptions.some((subscription) => {
        const periodEnd = new Date(subscription.current_period_end);
        return (
          (subscription.plan_name === "Pro Plan" ||
            subscription.plan_name === "Pro Plan & Embedding" ||
            subscription.plan_name === "White label") &&
          periodEnd > now
        );
      });

      console.log("User has active Pro Plan subscription:", hasProPlan);
      return hasProPlan;
    }

    console.log("Failed to get user profile data for subscription check");
    return false;
  } catch (error) {
    console.error("Error checking Pro Plan subscription:", error);
    return false;
  }
}

const getProfileSetting = async (): Promise<ProfileSetting | null> => {
  const token = storageService.getToken();
  const url = `${process.env.NEXT_PUBLIC_API_URL}/api/accounts/profile/settings/`;
  if (!token) return null;
  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    credentials: "omit",
  }).then(async (res) => ({
    status: res.status,
    data: await res.json(),
  }));
  return response.data;
};

const updateProfileSetting = async (data: ProfileSetting) => {
  const token = storageService.getToken();
  const url = `${process.env.NEXT_PUBLIC_API_URL}/api/accounts/profile/settings/`;
  if (!token) return;
  const response = await fetch(url, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
    credentials: "omit",
  }).then(async (res) => ({
    status: res.status,
    data: await res.json(),
  }));
  console.log("Profile setting update response:", response);
  if (response.status === 200) {
    return true;
  } else {
    // Log the error details for debugging
    console.error("Profile setting update failed:", response.data);
    throw new Error(JSON.stringify(response.data));
  }
};

const profileService = {
  getUserProfile,
  hasActiveSubscription,
  hasActiveWhiteLabelSubscription,
  getProfileSetting,
  updateProfileSetting,
  hasActiveProPlanSubscription,
};

export default profileService;
