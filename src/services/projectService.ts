import http from "./httpService";

// Types for project creation and management
export interface ProjectSearchEngine {
  search_engine: string;
  countries: string[];
  languages: string[];
}

export interface ProjectKeyword {
  keyword: string;
  search_engines: string[];
  countries: string[];
  languages: string[];
}

export interface ProjectCompetitor {
  url: string;
  search_engines: string[];
  countries: string[];
}

// Initial project creation request (first step)
export interface CreateProjectRequest {
  url: string;
  domain_type: string;
  project_name: string;
  project_color: string;
}

// Full project creation request (final step)
export interface FullProjectRequest {
  url: string;
  domain_type: string;
  project_name: string;
  project_color: string;
  primary_search_engines: ProjectSearchEngine[];
  keywords: ProjectKeyword[];
  competitors: ProjectCompetitor[];
}

// Project update request (PUT)
export interface UpdateProjectRequest extends FullProjectRequest {
  status: string;
}

// Partial project update request (PATCH)
export interface PartialProjectUpdate {
  url?: string;
  domain_type?: string;
  project_name?: string;
  project_color?: string;
  status?: string;
  primary_search_engines?: ProjectSearchEngine[];
  keywords?: ProjectKeyword[];
  competitors?: ProjectCompetitor[];
}

// Keyword extraction request
export interface ExtractKeywordsRequest {
  url: string;
  limit: number;
}

// Keyword suggestions request
export interface KeywordSuggestionsRequest {
  project_id: string;
  location: string;
  language_code: string;
}

// Competitor suggestions request and response types
export interface CompetitorSuggestionsRequest {
  project_id: string;
  location: string;
  language_code: string;
}

export interface CompetitorSuggestion {
  domain: string;
  intersections?: number;
  avg_position?: number;
  sum_position?: number;
  organic_keywords?: number;
  organic_traffic?: number;
  organic_cost?: number;
  competitor_score?: number;
  strength_level?: string;
  title?: string;
  description?: string;
  relevance_score?: number;
}

export interface CompetitorSuggestionsResponse {
  status?: string;
  data?: {
    target_domain?: string;
    total_competitors?: number;
    competitors: CompetitorSuggestion[];
  };
  suggestions?: CompetitorSuggestion[];
  location?: string;
  language_code?: string;
  total_found?: number;
}

// Supported locations response type
export interface SupportedLocation {
  code: string;
  name: string;
  primary_language: string;
}

export interface SupportedLocationsResponse {
  status: string;
  locations: SupportedLocation[];
  total_locations: number;
}

// Google Analytics connection request
export interface GoogleAnalyticsConnectRequest {
  project_id: string;
  redirect?: "create" | "traffic";
}

// Google Analytics connection response
export interface GoogleAnalyticsConnectResponse {
  status: string;
  message: string;
  authorization_url: string;
  project_id: string;
  error_message?: string;
}

// Google Analytics connection details
export interface GoogleAnalyticsConnection {
  project: string;
  project_name: string;
  project_url: string;
  property_name: string;
  status: string;
  connected_at: string;
  last_sync: string | null;
  minimum_date: string | null;
}

// Google Analytics status response
export interface GoogleAnalyticsStatusResponse {
  status: string;
  message?: string;
  error_message?: string;
  connection?: GoogleAnalyticsConnection;
}

// Google Analytics property
export interface GoogleAnalyticsProperty {
  property_id: string;
  display_name: string;
  website_url?: string;
}

// Google Analytics properties response
export interface GoogleAnalyticsPropertiesResponse {
  status: string;
  properties: GoogleAnalyticsProperty[];
  message?: string;
  error_message?: string;
}

// Google Analytics property selection request
export interface GoogleAnalyticsPropertySelectRequest {
  project_id: string;
  property_id: string;
  property_name: string;
}

// Google Analytics property selection response
export interface GoogleAnalyticsPropertySelectResponse {
  status: string;
  message?: string;
  error_message?: string;
}

// Google Search Console connection request
export interface GoogleSearchConsoleConnectRequest {
  project_id: string;
  redirect?: "create" | "traffic";
}

// Google Search Console connection response
export interface GoogleSearchConsoleConnectResponse {
  status: string;
  message: string;
  authorization_url: string;
  project_id: string;
  error_message?: string;
}

// Google Search Console connection details
export interface GoogleSearchConsoleConnection {
  project: string;
  project_name: string;
  project_url: string;
  property_url: string;
  property_type: string;
  status: string;
  connected_at: string;
  last_sync: string | null;
  minimum_date: string | null;
}

// Google Search Console status response
export interface GoogleSearchConsoleStatusResponse {
  status: string;
  message?: string;
  error_message?: string;
  connection?: GoogleSearchConsoleConnection;
}

// Google Search Console property
export interface GoogleSearchConsoleProperty {
  property_url: string;
  property_type: string;
  permission_level?: string;
}

// Google Search Console properties response
export interface GoogleSearchConsolePropertiesResponse {
  status: string;
  properties: GoogleSearchConsoleProperty[];
  message?: string;
  error_message?: string;
}

// Google Search Console property selection request
export interface GoogleSearchConsolePropertySelectRequest {
  project_id: string;
  property_url: string;
  property_type: string;
}

// Google Search Console property selection response
export interface GoogleSearchConsolePropertySelectResponse {
  status: string;
  message?: string;
  error_message?: string;
}

// API Response types
export interface ProjectResponse {
  id: string;
  url: string;
  domain_type: string;
  project_name: string;
  project_color: string;
  status: string;
  created_at: string;
  updated_at: string;
  favicon_url?: string | null;
  primary_search_engines?: ProjectSearchEngine[];
  keywords?: ProjectKeyword[];
  competitors?: ProjectCompetitor[];
}

// Project list response with pagination
export interface ProjectListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ProjectListItem[];
}

// Individual project item in the list
export interface ProjectListItem {
  id: string;
  project_name: string;
  url: string;
  created_at: string;
  status: "enabled" | "disabled";
  project_color: string;
  favicon_url?: string | null;
}

export interface KeywordData {
  keyword: string;
  frequency: number;
  in_title: boolean;
  in_meta_description: boolean;
  in_headings: boolean;
  in_footer: boolean;
}

export interface PhraseData {
  phrase: string;
  frequency: number;
  in_title: boolean;
  in_meta_description: boolean;
  in_headings: boolean;
  in_footer: boolean;
}

export interface ExtractKeywordsResponse {
  url: string;
  keywords: KeywordData[];
  phrases: PhraseData[];
  total_words: number;
}

// Project API service methods
export const projectAPI = {
  // Create initial project (first step)
  createProject: (
    data: CreateProjectRequest
  ): Promise<{ data: ProjectResponse }> => {
    return http.post("/api/project/create/", data, { useAuth: true });
  },

  // Create full project (final step)
  createFullProject: (
    data: FullProjectRequest
  ): Promise<{ data: ProjectResponse }> => {
    return http.post("/api/project/create/", data, { useAuth: true });
  },

  // Update project (PUT)
  updateProject: (
    projectId: string,
    data: UpdateProjectRequest
  ): Promise<{ data: ProjectResponse }> => {
    return http.put(`/api/project/${projectId}/`, data, { useAuth: true });
  },

  // Partial update project (PATCH)
  partialUpdateProject: (
    projectId: string,
    data: PartialProjectUpdate
  ): Promise<{ data: ProjectResponse }> => {
    return http.patch(`/api/project/${projectId}/`, data, { useAuth: true });
  },

  // Extract keywords for project
  extractKeywords: (
    data: ExtractKeywordsRequest
  ): Promise<{ data: ExtractKeywordsResponse }> => {
    return http.post("/tools/keyword-extraction/", data, { useAuth: true });
  },

  // Get keyword suggestions for project
  getKeywordSuggestions: (
    data: KeywordSuggestionsRequest
  ): Promise<{ data: ExtractKeywordsResponse }> => {
    return http.post("/api/project/keyword-suggestions/", data, {
      useAuth: true,
    });
  },

  // Get project by ID
  getProject: (projectId: string): Promise<{ data: ProjectResponse }> => {
    return http.get(`/api/project/${projectId}/`, { useAuth: true });
  },

  // Delete project
  deleteProject: (projectId: string): Promise<{ data: any }> => {
    return http.delete(`/api/project/${projectId}/`, { useAuth: true });
  },

  // Get all projects with optional filtering and search
  getAllProjects: (params?: {
    ordering?: string;
    search?: string;
    page?: number;
    page_size?: number;
  }): Promise<{ data: ProjectListResponse }> => {
    const searchParams = new URLSearchParams();

    // Add ordering parameter (default to -created_at for newest first)
    if (params?.ordering) {
      searchParams.append("ordering", params.ordering);
    } else {
      searchParams.append("ordering", "-created_at");
    }

    // Add search parameter
    if (params?.search) {
      searchParams.append("search", params.search);
    }

    // Add pagination parameters
    if (params?.page) {
      searchParams.append("page", params.page.toString());
    }

    if (params?.page_size) {
      searchParams.append("page_size", params.page_size.toString());
    }

    const queryString = searchParams.toString();
    return http.get(`/api/project/${queryString ? `?${queryString}` : ""}`, {
      useAuth: true,
    });
  },

  // Update project status (enable/disable)
  updateProjectStatus: (
    projectId: string,
    status: "enabled" | "disabled"
  ): Promise<{ data: any }> => {
    return http.patch(
      `/api/project/${projectId}/`,
      { status },
      { useAuth: true }
    );
  },

  // Connect Google Analytics to project
  connectGoogleAnalytics: (
    data: GoogleAnalyticsConnectRequest
  ): Promise<{ data: GoogleAnalyticsConnectResponse }> => {
    return http.post("/api/project/GA4/c/", data, {
      useAuth: true,
    });
  },

  // Get Google Analytics status
  getGoogleAnalyticsStatus: (
    projectId: string
  ): Promise<{ data: GoogleAnalyticsStatusResponse }> => {
    return http.get(`/api/project/GA4/status/${projectId}/`, {
      useAuth: true,
    });
  },

  // Get Google Analytics properties
  getGoogleAnalyticsProperties: (
    projectId: string
  ): Promise<{ data: GoogleAnalyticsPropertiesResponse }> => {
    return http.get(`/api/project/GA4/properties/${projectId}/`, {
      useAuth: true,
    });
  },

  // Select Google Analytics property
  selectGoogleAnalyticsProperty: (
    data: GoogleAnalyticsPropertySelectRequest
  ): Promise<{ data: GoogleAnalyticsPropertySelectResponse }> => {
    return http.post(`/api/project/GA4/s/`, data, {
      useAuth: true,
    });
  },

  // Disconnect Google Analytics
  disconnectGoogleAnalytics: (
    data: GoogleAnalyticsConnectRequest
  ): Promise<{ data: any }> => {
    return http.post("/api/project/GA4/d/", data, {
      useAuth: true,
    });
  },

  // Connect Google Search Console to project
  connectGoogleSearchConsole: (
    data: GoogleSearchConsoleConnectRequest
  ): Promise<{ data: GoogleSearchConsoleConnectResponse }> => {
    return http.post("/api/project/GSC/c/", data, {
      useAuth: true,
    });
  },

  // Get Google Search Console status
  getGoogleSearchConsoleStatus: (
    projectId: string
  ): Promise<{ data: GoogleSearchConsoleStatusResponse }> => {
    return http.get(`/api/project/GSC/status/${projectId}/`, {
      useAuth: true,
    });
  },

  // Get Google Search Console properties
  getGoogleSearchConsoleProperties: (
    projectId: string
  ): Promise<{ data: GoogleSearchConsolePropertiesResponse }> => {
    return http.get(`/api/project/GSC/properties/${projectId}/`, {
      useAuth: true,
    });
  },

  // Select Google Search Console property
  selectGoogleSearchConsoleProperty: (
    data: GoogleSearchConsolePropertySelectRequest
  ): Promise<{ data: GoogleSearchConsolePropertySelectResponse }> => {
    return http.post(`/api/project/GSC/s/`, data, {
      useAuth: true,
    });
  },

  // Disconnect Google Search Console
  disconnectGoogleSearchConsole: (
    data: GoogleSearchConsoleConnectRequest
  ): Promise<{ data: any }> => {
    return http.post("/api/project/GSC/d/", data, {
      useAuth: true,
    });
  },

  // Get competitor suggestions
  getCompetitorSuggestions: (
    data: CompetitorSuggestionsRequest
  ): Promise<{ data: CompetitorSuggestionsResponse }> => {
    return http.post("/api/project/competitor-suggestions/", data, {
      useAuth: true,
    });
  },

  // Get supported locations for competitors
  getSupportedLocations: (): Promise<{ data: SupportedLocationsResponse }> => {
    return http.get("/api/project/supported-locations/", {
      useAuth: true,
    });
  },
};

export default projectAPI;
