import {
  projectAPI,
  CreateProjectRequest,
  FullProjectRequest,
} from "../projectService";
import http from "../httpService";

// Mock the http service
jest.mock("../httpService");
const mockedHttp = http as jest.Mocked<typeof http>;

describe("ProjectService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("createProject", () => {
    it("should create a project successfully", async () => {
      const mockRequest: CreateProjectRequest = {
        url: "https://example.com",
        domain_type: "*.example.com",
        project_name: "Test Project",
        project_color: "#FF6B35",
      };

      const mockResponse = {
        data: {
          id: "project-123",
          url: "https://example.com",
          domain_type: "*.example.com",
          project_name: "Test Project",
          project_color: "#FF6B35",
          status: "active",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
      };

      mockedHttp.post.mockResolvedValue(mockResponse);

      const result = await projectAPI.createProject(mockRequest);

      expect(mockedHttp.post).toHaveBeenCalledWith(
        "/api/project/create/",
        mockRequest,
        { useAuth: true }
      );
      expect(result).toEqual(mockResponse);
    });

    it("should handle API errors", async () => {
      const mockRequest: CreateProjectRequest = {
        url: "invalid-url",
        domain_type: "*.invalid",
        project_name: "",
        project_color: "#FF6B35",
      };

      const mockError = {
        response: {
          data: {
            message: "Invalid project data",
          },
        },
      };

      mockedHttp.post.mockRejectedValue(mockError);

      await expect(projectAPI.createProject(mockRequest)).rejects.toEqual(
        mockError
      );
    });
  });

  describe("createFullProject", () => {
    it("should create a full project successfully", async () => {
      const mockRequest: FullProjectRequest = {
        url: "https://example.com",
        domain_type: "*.example.com",
        project_name: "Test Project",
        project_color: "#FF6B35",
        primary_search_engines: [
          {
            search_engine: "google",
            countries: ["US"],
            languages: ["en"],
          },
        ],
        keywords: [
          {
            keyword: "test keyword",
            search_engines: ["google"],
            countries: ["US"],
            languages: ["en"],
          },
        ],
        competitors: [],
      };

      const mockResponse = {
        data: {
          id: "project-123",
          ...mockRequest,
          status: "active",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
      };

      mockedHttp.post.mockResolvedValue(mockResponse);

      const result = await projectAPI.createFullProject(mockRequest);

      expect(mockedHttp.post).toHaveBeenCalledWith(
        "/api/project/create/",
        mockRequest,
        { useAuth: true }
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe("extractKeywords", () => {
    it("should extract keywords successfully", async () => {
      const mockRequest = { url: "https://example.com", limit: 10 };
      const mockResponse = {
        data: {
          url: "https://example.com",
          keywords: [
            {
              keyword: "seo",
              frequency: 5,
              in_title: true,
              in_meta_description: true,
              in_headings: false,
              in_footer: false,
            },
            {
              keyword: "marketing",
              frequency: 3,
              in_title: false,
              in_meta_description: true,
              in_headings: true,
              in_footer: false,
            },
          ],
          phrases: [
            {
              phrase: "seo marketing",
              frequency: 2,
              in_title: false,
              in_meta_description: false,
              in_headings: true,
              in_footer: false,
            },
          ],
          total_words: 150,
        },
      };

      mockedHttp.post.mockResolvedValue(mockResponse);

      const result = await projectAPI.extractKeywords(mockRequest);

      expect(mockedHttp.post).toHaveBeenCalledWith(
        "/tools/keyword-extraction/",
        mockRequest,
        { useAuth: true }
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe("getKeywordSuggestions", () => {
    it("should get keyword suggestions successfully", async () => {
      const mockRequest = {
        project_id: "project-123",
        location: "US",
        language_code: "en",
      };
      const mockResponse = {
        data: {
          keywords: [
            {
              keyword: "seo",
              frequency: 5,
              in_title: true,
              in_meta_description: true,
              in_headings: false,
              in_footer: false,
            },
            {
              keyword: "marketing",
              frequency: 3,
              in_title: false,
              in_meta_description: true,
              in_headings: true,
              in_footer: false,
            },
          ],
          project_id: "project-123",
        },
      };

      mockedHttp.post.mockResolvedValue(mockResponse);

      const result = await projectAPI.getKeywordSuggestions(mockRequest);

      expect(mockedHttp.post).toHaveBeenCalledWith(
        "/api/project/keyword-suggestions/",
        mockRequest,
        { useAuth: true }
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe("updateProject", () => {
    it("should update project successfully", async () => {
      const projectId = "project-123";
      const mockRequest: any = {
        url: "https://updated.com",
        domain_type: "*.updated.com",
        project_name: "Updated Project",
        project_color: "#3498DB",
        status: "enabled",
        primary_search_engines: [],
        keywords: [],
        competitors: [],
      };

      const mockResponse = {
        data: {
          id: projectId,
          ...mockRequest,
          updated_at: "2024-01-01T00:00:00Z",
        },
      };

      mockedHttp.put.mockResolvedValue(mockResponse);

      const result = await projectAPI.updateProject(projectId, mockRequest);

      expect(mockedHttp.put).toHaveBeenCalledWith(
        `/api/project/${projectId}/`,
        mockRequest,
        { useAuth: true }
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe("partialUpdateProject", () => {
    it("should partially update project successfully", async () => {
      const projectId = "project-123";
      const mockRequest = {
        project_name: "Partially Updated Project",
        status: "disabled",
      };

      const mockResponse = {
        data: {
          id: projectId,
          project_name: "Partially Updated Project",
          status: "disabled",
          updated_at: "2024-01-01T00:00:00Z",
        },
      };

      mockedHttp.patch.mockResolvedValue(mockResponse);

      const result = await projectAPI.partialUpdateProject(
        projectId,
        mockRequest
      );

      expect(mockedHttp.patch).toHaveBeenCalledWith(
        `/api/project/${projectId}/`,
        mockRequest,
        { useAuth: true }
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe("getProject", () => {
    it("should get project successfully", async () => {
      const projectId = "project-123";
      const mockResponse = {
        data: {
          id: projectId,
          url: "https://example.com",
          project_name: "Test Project",
          status: "active",
        },
      };

      mockedHttp.get.mockResolvedValue(mockResponse);

      const result = await projectAPI.getProject(projectId);

      expect(mockedHttp.get).toHaveBeenCalledWith(
        `/api/project/${projectId}/`,
        { useAuth: true }
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe("deleteProject", () => {
    it("should delete project successfully", async () => {
      const projectId = "project-123";
      const mockResponse = { data: { success: true } };

      mockedHttp.delete.mockResolvedValue(mockResponse);

      const result = await projectAPI.deleteProject(projectId);

      expect(mockedHttp.delete).toHaveBeenCalledWith(
        `/api/project/${projectId}/`,
        { useAuth: true }
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
