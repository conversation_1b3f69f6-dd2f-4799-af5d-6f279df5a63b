// Storage keys
const STORAGE_KEYS = {
  TOKEN: "token",
  REFRESH_TOKEN: "refresh_token",
  USER: "user",
};

// Type definitions
export interface User {
  email: string;
  first_name: string;
  last_name: string;
  token?: string;
}

// Check if we're in a browser environment
const isBrowser = typeof window !== "undefined";

/**
 * Set an item in localStorage
 */
const setItem = (key: string, value: any): void => {
  if (isBrowser) {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(key, serializedValue);
    } catch (error) {
      console.error("Error setting localStorage item:", error);
    }
  }
};

/**
 * Get an item from localStorage
 */
const getItem = <T>(key: string): T | null => {
  if (isBrowser) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error("Error getting localStorage item:", error);
      return null;
    }
  }
  return null;
};

/**
 * Remove an item from localStorage
 */
const removeItem = (key: string): void => {
  if (isBrowser) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error("Error removing localStorage item:", error);
    }
  }
};

/**
 * Set the access token (JWT)
 */
const setToken = (token: string): void => {
  setItem(STORAGE_KEYS.TOKEN, token);
};

/**
 * Get the access token
 */
const getToken = (): string | null => {
  return getItem<string>(STORAGE_KEYS.TOKEN);
};

/**
 * Remove the access token
 */
const removeToken = (): void => {
  removeItem(STORAGE_KEYS.TOKEN);
};

/**
 * Set the refresh token
 */
const setRefreshToken = (token: string): void => {
  setItem(STORAGE_KEYS.REFRESH_TOKEN, token);
};

/**
 * Get the refresh token
 */
const getRefreshToken = (): string | null => {
  return getItem<string>(STORAGE_KEYS.REFRESH_TOKEN);
};

/**
 * Remove the refresh token
 */
const removeRefreshToken = (): void => {
  removeItem(STORAGE_KEYS.REFRESH_TOKEN);
};

/**
 * Set the user data
 */
const setUser = (user: User): void => {
  setItem(STORAGE_KEYS.USER, user);
};

/**
 * Get the user data
 */
const getUser = (): User | null => {
  return getItem<User>(STORAGE_KEYS.USER);
};

/**
 * Remove the user data
 */
const removeUser = (): void => {
  removeItem(STORAGE_KEYS.USER);
};

/**
 * Clear all authentication data
 */
const clearAuth = (): void => {
  removeToken();
  removeRefreshToken();
  removeUser();
};

const storageService = {
  setToken,
  getToken,
  removeToken,
  setRefreshToken,
  getRefreshToken,
  removeRefreshToken,
  setUser,
  getUser,
  removeUser,
  clearAuth,
};

export default storageService;
