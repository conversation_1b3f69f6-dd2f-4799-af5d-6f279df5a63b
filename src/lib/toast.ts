import { toast, ToastOptions } from "react-hot-toast";

// Enhanced toast utility that works with our custom ToastProvider
export const showToast = {
  success: (message: string, options?: ToastOptions) => {
    return toast.success(message, {
      duration: 2500,
      ...options,
    });
  },

  error: (message: string, options?: ToastOptions) => {
    return toast.error(message, {
      duration: 4000, // Longer duration for errors
      ...options,
    });
  },

  loading: (message: string, options?: ToastOptions) => {
    return toast.loading(message, {
      duration: Infinity, // Loading toasts should persist until dismissed
      ...options,
    });
  },

  info: (message: string, options?: ToastOptions) => {
    return toast(message, {
      duration: 3000,
      ...options,
    });
  },

  // Promise-based toast for API calls
  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: ToastOptions
  ) => {
    return toast.promise(promise, messages, {
      loading: { duration: Infinity },
      success: { duration: 3000 },
      error: { duration: 4000 },
      ...options,
    });
  },

  // Custom toast for complex content
  custom: (content: React.ReactElement, options?: ToastOptions) => {
    return toast.custom(content, {
      duration: 3000,
      ...options,
    });
  },

  // Dismiss toasts
  dismiss: (toastId?: string) => {
    if (toastId) {
      toast.dismiss(toastId);
    } else {
      toast.dismiss();
    }
  },

  // API-specific helpers
  api: {
    success: (message: string = "Operation completed successfully") => {
      return showToast.success(message);
    },

    error: (error: any, fallbackMessage: string = "An error occurred") => {
      // Handle different error response formats
      let errorMessage = fallbackMessage;

      // Don't show 401 errors to users (authentication issues)
      if (error?.response?.status === 401) {
        console.log("Authentication error - not displaying to user");
        return;
      }

      // Extract error message from different API response formats
      if (error?.response?.data?.error_message) {
        errorMessage = error.response.data.error_message;
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      return showToast.error(errorMessage);
    },

    loading: (message: string = "Processing...") => {
      return showToast.loading(message);
    },

    // Promise wrapper for API calls
    call: <T>(
      apiCall: Promise<T>,
      messages?: {
        loading?: string;
        success?: string | ((data: T) => string);
        error?: string | ((error: any) => string);
      }
    ) => {
      const defaultMessages = {
        loading: messages?.loading || "Processing...",
        success: messages?.success || "Operation completed successfully",
        error:
          messages?.error ||
          ((error: any) => {
            // Don't show 401 errors to users
            if (error?.response?.status === 401) {
              return ""; // Return empty string to prevent showing toast
            }

            return (
              error?.response?.data?.error_message ||
              error?.response?.data?.message ||
              error?.message ||
              "An error occurred"
            );
          }),
      };

      return toast.promise(apiCall, defaultMessages, {
        loading: { duration: Infinity },
        success: { duration: 3000 },
        error: { duration: 4000 },
      });
    },
  },
};
