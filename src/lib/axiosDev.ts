/* ====================== CONNECTED TO API ===================== */
import axios from "axios";

// Get base URL from environment or use default
const getBaseURL = () => {
  // Check if we're in development and have a specific API URL
  // if (
  //   process.env.NODE_ENV === "development" &&
  //   process.env.NEXT_PUBLIC_API_URL
  // ) {
  //   return process.env.NEXT_PUBLIC_API_URL;
  // }

  // Default to localhost for development
  return "http://localhost:3000";
};

const AXIOS = axios.create({
  baseURL: getBaseURL(),
  headers: {
    "Content-Type": "application/json",
  },
});

export default AXIOS;
