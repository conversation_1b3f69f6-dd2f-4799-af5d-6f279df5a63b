import LocalFont from "next/font/local";

const nunitoSansFont = LocalFont({
  src: [
    {
      path: "../../public/fonts/NunitoSans-ExtraLight.woff2",
      weight: "200",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-Light.woff2",
      weight: "300",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-SemiBold.woff2",
      weight: "600",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-Bold.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-ExtraBold.woff2",
      weight: "800",
      style: "normal",
    },
    {
      path: "../../public/fonts/NunitoSans-Black.woff2",
      weight: "900",
      style: "normal",
    },
  ],
  variable: "--font-nunito-sans",
  style: "normal",
  display: "block",
});

export default nunitoSansFont;
