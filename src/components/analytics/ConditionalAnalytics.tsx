"use client";

import { useEffect } from "react";
import Script from "next/script";
import { hasUserConsent } from "@/components/cookie-consent/CookieConsentModal";
import analyticsService from "@/services/analyticsService";

const GA_ID = process.env.NEXT_PUBLIC_GA_ID;

export default function ConditionalAnalytics() {
  useEffect(() => {
    // Initialize analytics if user has already given consent
    if (hasUserConsent()) {
      analyticsService.initializeAnalytics();
    }
  }, []);

  // Only render Google Analytics scripts if user has given consent
  if (!hasUserConsent() || !GA_ID) {
    return null;
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_ID}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${GA_ID}', {
            page_path: window.location.pathname,
            anonymize_ip: true
          });
        `}
      </Script>
    </>
  );
}
