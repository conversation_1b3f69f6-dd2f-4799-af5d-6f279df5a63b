"use client";

import React, { useState } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import { useAnalyticsConnection } from "@/contexts/AnalyticsConnectionContext";

/* ================================== ICONS ================================= */
import googleAnalyticsLogo from "@/../public/images/create-project/google-analytics.svg";
import googleLogo from "@/../public/images/create-project/google.svg";

interface AnalyticsConnectionButtonProps {
  type: "GA4" | "GSC";
  className?: string;
}

export function AnalyticsConnectionButton({
  type,
  className = "",
}: AnalyticsConnectionButtonProps) {
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const {
    isGoogleAnalyticsConnected,
    isGoogleSearchConsoleConnected,
    connectGA,
    connectGSC,
    disconnectGA,
    disconnectGSC,
    gaConnecting,
    gscConnecting,
    gaDisconnecting,
    gscDisconnecting,
  } = useAnalyticsConnection();

  const isGA4 = type === "GA4";
  const isConnected = isGA4
    ? isGoogleAnalyticsConnected
    : isGoogleSearchConsoleConnected;
  const isConnecting = isGA4 ? gaConnecting : gscConnecting;
  const isDisconnecting = isGA4 ? gaDisconnecting : gscDisconnecting;
  const isLoading = isConnecting || isDisconnecting;

  const handleConnect = () => {
    if (isGA4) {
      connectGA();
    } else {
      connectGSC();
    }
  };

  const handleDisconnectClick = () => {
    setShowDisconnectDialog(true);
  };

  const handleDisconnectConfirm = () => {
    if (isGA4) {
      disconnectGA();
    } else {
      disconnectGSC();
    }
    setShowDisconnectDialog(false);
  };

  const getButtonText = () => {
    if (isDisconnecting) return "Disconnecting...";
    if (isConnecting) return "Connecting...";
    if (isConnected && isHovered) return "Disconnect";
    if (isConnected) return "Connected";
    return isGA4 ? "Connect Google Analytics" : "Connect Google Search Console";
  };

  const getButtonStyles = () => {
    // Set min-width based on the longest possible text to prevent shifting while allowing expansion
    const minWidth = isGA4 ? "min-w-[180px]" : "min-w-[190px]";

    if (isConnected) {
      return {
        className: `text-secondary border border-[#344054CC] hover:bg-secondary/80 ${minWidth} justify-center ${className}`,
        style: {
          backgroundColor: "#F4F4F4",
        },
      };
    }
    return {
      className: `text-white ${minWidth} justify-center ${className}`,
      style: undefined,
    };
  };

  const buttonStyles = getButtonStyles();
  const logo = isGA4 ? googleAnalyticsLogo : googleLogo;
  const serviceName = isGA4 ? "Google Analytics" : "Google Search Console";

  return (
    <>
      <div className="flex gap-2">
        <motion.div
          whileTap={{ scale: 0.98 }}
          transition={{ duration: 0.1, ease: "easeInOut" }}
        >
          <Button
            variant="secondary"
            className={buttonStyles.className}
            style={buttonStyles.style}
            onClick={isConnected ? handleDisconnectClick : handleConnect}
            disabled={isLoading}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <div className="flex items-center gap-2">
              <Image
                src={logo}
                alt={`${serviceName} Logo`}
                className="shrink-0"
              />
              <motion.span
                key={getButtonText()}
                initial={{ opacity: 0, y: 5 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -5 }}
                transition={{ duration: 0.2, ease: "easeInOut" }}
                className="whitespace-nowrap"
              >
                {getButtonText()}
              </motion.span>
            </div>
          </Button>
        </motion.div>
      </div>

      <ConfirmationDialog
        open={showDisconnectDialog}
        onOpenChange={setShowDisconnectDialog}
        title={`Disconnect ${serviceName}`}
        description={`Are you sure you want to disconnect ${serviceName}? This will remove access to your analytics data and you'll need to reconnect to view reports.`}
        confirmText="Disconnect"
        cancelText="Cancel"
        onConfirm={handleDisconnectConfirm}
        isLoading={isDisconnecting}
        variant="destructive"
      />
    </>
  );
}
