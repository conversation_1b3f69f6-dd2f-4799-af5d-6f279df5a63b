"use client";

import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import clsx from "clsx";
import { ArrowRigthIcon } from "@/ui/icons/navigation";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  siblingCount?: number;
  boundaryCount?: number;
  searchQuery?: string;
  tagFilter?: string;
  nextPageUrl?: string | null;
  previousPageUrl?: string | null;
}

const PaginationBox: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  siblingCount = 1,
  boundaryCount = 2,
  searchQuery,
  tagFilter,
  nextPageUrl,
  previousPageUrl,
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createPageURL = (page: number) => {
    const params = new URLSearchParams(searchParams?.toString());
    params.set("page", page.toString());

    // Preserve search query if it exists
    if (searchQuery) {
      params.set("q", searchQuery);
    }

    // Preserve tag filter if it exists
    if (tagFilter) {
      params.set("tag", tagFilter);
    }

    return `${pathname}?${params.toString()}`;
  };

  const getPaginationItems = () => {
    const pages: (number | "...")[] = [];

    // Handle simple cases with few pages
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
      return pages;
    }

    // For more complex pagination with many pages
    const startPages = Array.from(
      { length: Math.min(boundaryCount, totalPages) },
      (_, i) => i + 1
    );

    const endPages = Array.from(
      { length: boundaryCount },
      (_, i) => totalPages - boundaryCount + 1 + i
    ).filter((p) => p > boundaryCount && p <= totalPages);

    const siblingsStart = Math.max(
      Math.min(
        currentPage - siblingCount,
        totalPages - boundaryCount - siblingCount * 2 - 1
      ),
      boundaryCount + 2
    );

    const siblingsEnd = Math.min(
      Math.max(
        currentPage + siblingCount,
        boundaryCount + siblingCount * 2 + 2
      ),
      endPages.length > 0 ? endPages[0] - 2 : totalPages - 1
    );

    // Add start pages
    pages.push(...startPages);

    if (siblingsStart > boundaryCount + 2) {
      pages.push("...");
    } else if (boundaryCount + 1 < totalPages - boundaryCount) {
      pages.push(boundaryCount + 1);
    }

    // Add sibling pages
    for (let i = siblingsStart; i <= siblingsEnd; i++) {
      if (i > boundaryCount && i < (endPages[0] || totalPages + 1)) {
        pages.push(i);
      }
    }

    if (siblingsEnd < totalPages - boundaryCount - 1) {
      pages.push("...");
    } else if (endPages.length && siblingsEnd + 1 < endPages[0]) {
      pages.push(endPages[0] - 1);
    }

    // Add end pages
    pages.push(...endPages);

    // Remove duplicates and sort
    const uniquePages = Array.from(
      new Set(pages.filter((p) => p !== "..."))
    ).sort((a, b) => {
      if (typeof a === "number" && typeof b === "number") {
        return a - b;
      }
      return 0;
    });

    // Rebuild with ellipsis where needed
    const finalPages: (number | "...")[] = [];
    for (let i = 0; i < uniquePages.length; i++) {
      const current = uniquePages[i];
      const next = uniquePages[i + 1];

      finalPages.push(current);

      if (
        typeof current === "number" &&
        typeof next === "number" &&
        next - current > 1
      ) {
        finalPages.push("...");
      }
    }

    return finalPages;
  };

  const paginationItems = getPaginationItems();

  return (
    <div className="flex items-center justify-between w-full mt-10 rounded-2xl shadow bg-white p-6">
      {/* Previous Button */}
      <Link
        href={createPageURL(currentPage - 1)}
        className={clsx(
          "flex items-center md:gap-2 text-sm md:text-base",
          currentPage === 1 || !previousPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary  hover:text-primary duration-300"
        )}
      >
        <ArrowRigthIcon className="rotate-180" />
        Previous
      </Link>

      {/* Full Pagination (Desktop) */}
      <div className="hidden sm:flex items-center gap-2">
        {paginationItems.map((item, index) =>
          item === "..." ? (
            <span key={index} className="px-3 py-1 text-gray-500">
              ...
            </span>
          ) : (
            <Link
              key={index}
              href={createPageURL(item)}
              className={clsx(
                "px-3 py-1 rounded text-secondary transition-all",
                item === currentPage
                  ? "bg-primary text-white shadow"
                  : "hover:bg-primary/10 hover:text-primary duration-300"
              )}
            >
              {item}
            </Link>
          )
        )}
      </div>

      {/* Next Button */}
      <Link
        href={createPageURL(currentPage + 1)}
        className={clsx(
          "flex items-center md:gap-2 text-sm md:text-base",
          currentPage === totalPages || !nextPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary hover:text-primary duration-300"
        )}
      >
        Next
        <ArrowRigthIcon />
      </Link>
    </div>
  );
};

const NoNumberPagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  searchQuery,
  tagFilter,
  nextPageUrl,
  previousPageUrl,
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const createPageURL = (page: number) => {
    const params = new URLSearchParams(searchParams?.toString());
    params.set("page", page.toString());

    // Preserve search query if it exists
    if (searchQuery) {
      params.set("q", searchQuery);
    }

    // Preserve tag filter if it exists
    if (tagFilter) {
      params.set("tag", tagFilter);
    }

    return `${pathname}?${params.toString()}`;
  };

  return (
    <div className="flex items-center justify-between w-full  rounded-lg bg-white  p-6">
      {/* Previous Button */}
      <Link
        href={createPageURL(currentPage - 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === 1 || !previousPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        <ArrowRigthIcon className="rotate-180" />
        Previous
      </Link>

      {/* Next Button */}
      <Link
        href={createPageURL(currentPage + 1)}
        className={clsx(
          "flex items-center gap-2",
          currentPage === totalPages || !nextPageUrl
            ? "pointer-events-none text-gray-400"
            : "text-secondary"
        )}
      >
        Next
        <ArrowRigthIcon />
      </Link>
    </div>
  );
};

export { PaginationBox, NoNumberPagination };
