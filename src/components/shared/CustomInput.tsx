import React, { ComponentProps, memo, useCallback } from "react";
import { BsExclamationCircle } from "react-icons/bs";
import TooltipPortal from "../ui/TooltipPortal";

type EmailInputProps = {
  label?: string;
  labelClass?: string;
  classPlusInput?: string;
  error?: string;
  guideText?: string;
  required?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  onChanges?: (value: React.ChangeEvent<HTMLInputElement>) => void;
} & Omit<ComponentProps<"input">, "onChange">;

function CustomInput({
  label,
  startIcon,
  endIcon,
  required,
  classPlusInput,
  labelClass,
  error,
  onChanges,
  guideText,
  ...other
}: EmailInputProps) {
  // Optimize onChange handler to prevent unnecessary re-renders
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onChanges?.(e);
    },
    [onChanges]
  );

  return (
    <label htmlFor={other.name} className="w-full relative">
      {label ? (
        <div className="mb-2 flex items-center gap-2 text-gray-600">
          <div className="text-sm lg:text-base">
            {required ? <span className="mr-1 text-primary-red">*</span> : null}
            {label}
          </div>
          {guideText ? (
            <TooltipPortal width="xl" content={<span>{guideText}</span>}>
              <BsExclamationCircle className="text-gray-500" />
            </TooltipPortal>
          ) : null}
        </div>
      ) : null}
      <div className="relative">
        {startIcon ? (
          <i className="absolute left-3 z-10 top-1/2 transform -translate-y-1/2">
            {startIcon}
          </i>
        ) : null}
        <input
          id={other.name}
          onChange={handleChange}
          autoComplete="off"
          data-lpignore="true"
          type="text"
          className={` textField__input bg-white border-gray-200 placeholder:text-gray-400 text-sm focus:border-transparent focus:ring-2 focus:ring-primary active:border-transparent p-2 md:p-3 w-full transition-all duration-150 ${
            error ? "bg-red-100/60 border-primary-red" : ""
          } ${
            other.disabled ? "opacity-50 cursor-not-allowed bg-gray-50" : ""
          } ${classPlusInput} ${
            (startIcon && "!pl-10") || (endIcon && "!pr-10")
          }`}
          {...other}
        />
        {endIcon ? (
          <i className="absolute right-3 z-10 top-1/2 transform -translate-y-1/2">
            {endIcon}
          </i>
        ) : null}
      </div>
      {error ? (
        <span className={`text-primary-red text-xs`}>{error}</span>
      ) : null}
    </label>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default memo(CustomInput);
