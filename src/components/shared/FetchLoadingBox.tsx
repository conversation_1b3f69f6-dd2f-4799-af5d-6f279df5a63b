import React from 'react'

export default function FetchLoadingBox({ classPlus, children, isFetching }: { classPlus?: string, isFetching: boolean, children: React.ReactNode }) {
    return (
        isFetching ?
            <div className={`p-6 flex items-center gap-3 justify-center rounded-md bg-white flex-col ${classPlus}`}>
                <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                <p className="text-secondary">Loading Data ...</p>
            </div>
            :
            children
    )
}
