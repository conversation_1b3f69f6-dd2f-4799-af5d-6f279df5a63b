"use client";
import Modal from "@/ui/Modal";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";

type ExistingSubscriptionModalProps = {
  isOpen: boolean;
  onClose: () => void;
  planName: string;
};

export default function ExistingSubscriptionModal({
  isOpen,
  onClose,
  planName,
}: ExistingSubscriptionModalProps) {
  const router = useRouter();

  const handleClose = () => {
    onClose();
    router.push("/pricing");
  };

  const handleGoToPricing = () => {
    onClose();
    router.push("/pricing");
  };

  const handleReturnHome = () => {
    onClose();
    router.push("/");
  };

  return (
    <Modal
      open={isOpen}
      onClose={handleClose}
      title="Existing Subscription"
      size="md"
    >
      <div className="flex flex-col items-center p-4">
        <div className="w-18 h-18 bg-primary/10 rounded-full flex items-center justify-center mb-3">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-10 w-10 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>

        <h3 className="text-lg font-bold text-secondary mb-2">
          You Already Have {planName}
        </h3>

        <p className="text-center text-secondary/90 text-sm mb-4">
          You already have an active {planName} subscription.
        </p>

        <div className="flex flex-col w-full gap-2 mt-4">
          <motion.button
            onClick={handleReturnHome}
            className="btn btn--primary w-full text-center"
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.15, ease: "easeOut" },
            }}
            whileTap={{
              scale: 0.98,
              transition: { duration: 0.1, ease: "easeInOut" },
            }}
            initial={{ opacity: 0, y: 10 }}
            animate={{
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.3,
                ease: "easeOut",
                delay: 0.2,
              },
            }}
          >
            Return to Home
          </motion.button>
          {/* <button
          onClick={handleGoToPricing}
        
            className="btn btn--secondary w-full text-center"
          >
            Go To Home
          </button> */}
        </div>
      </div>
    </Modal>
  );
}
