"use client";
import Modal from "@/ui/Modal";
import Link from "next/link";
import { useRouter } from "next/navigation";

type TooManyRequestsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  retryAfterSeconds?: number;
};

export default function TooManyRequestsModal({
  isOpen,
  onClose,
  retryAfterSeconds,
}: TooManyRequestsModalProps) {
  const router = useRouter();

  // Handle "Maybe Later" button click
  const handleMaybeLater = () => {
    onClose(); // Close the modal
    router.push("/"); // Navigate to main page
  };

  // Format the retry time in a human-readable format
  const formatRetryTime = () => {
    if (!retryAfterSeconds) return "tomorrow";

    const hours = Math.floor(retryAfterSeconds / 3600);
    if (hours >= 24) return "tomorrow";
    if (hours > 0) return `in ${hours} ${hours === 1 ? "hour" : "hours"}`;

    const minutes = Math.floor(retryAfterSeconds / 60);
    if (minutes > 0)
      return `in ${minutes} ${minutes === 1 ? "minute" : "minutes"}`;

    return `in ${retryAfterSeconds} seconds`;
  };

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      title="Free Audit Limit Reached"
      size="md"
    >
      <div className="flex flex-col items-center p-6">
        {/* Icon */}
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>

        {/* Title and description */}
        <h3 className="text-xl font-bold text-secondary mb-3 text-center">
          You've Used All Your Free Audits
        </h3>

        <p className="text-center text-secondary/80 mb-6">
          You've reached the limit of 4 free SEO audits per day. Your free
          audits will reset {formatRetryTime()}.
        </p>

        {/* Upgrade box */}
        <div className="w-full bg-primary/5 rounded-lg p-4 mb-6 border border-primary/20">
          <h4 className="font-semibold text-primary mb-2">
            Need unlimited SEO audits?
          </h4>
          <p className="text-sm text-secondary/80 mb-3">
            Upgrade to a paid plan to get unlimited SEO audits, Pro Plan
            reports, and more advanced features to boost your SEO strategy.
          </p>
          <ul className="text-sm text-secondary/80 list-disc list-inside mb-4 space-y-1">
            <li>Unlimited SEO audits</li>
            <li>Pro Plan reports</li>
            <li>Advanced SEO metrics</li>
            <li>Priority support</li>
          </ul>
        </div>

        {/* Buttons */}
        <div className="flex flex-col w-full gap-3">
          <Link
            href="/pricing"
            className="btn btn--primary w-full text-center py-3"
          >
            View Pricing Plans
          </Link>
          <button
            onClick={handleMaybeLater}
            className="btn btn--secondary w-full text-center py-3"
          >
            Maybe Later
          </button>
        </div>
      </div>
    </Modal>
  );
}
