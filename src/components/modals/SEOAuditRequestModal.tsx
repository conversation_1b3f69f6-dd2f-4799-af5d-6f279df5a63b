"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Modal from "@/ui/Modal";
import http from "@/services/httpService";

declare global {
  interface Window {
    grecaptcha?: {
      ready: (callback: () => void) => void;
      execute: (
        siteKey: string,
        options: { action: string }
      ) => Promise<string>;
    };
  }
}

interface SEOAuditRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  websiteUrl: string;
}

export default function SEOAuditRequestModal({
  isOpen,
  onClose,
  websiteUrl,
}: SEOAuditRequestModalProps) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: "",
    name: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [recaptchaError, setRecaptchaError] = useState<string | null>(null);
  const [recaptchaLoading, setRecaptchaLoading] = useState<boolean>(false);

  const siteKey = process.env.NEXT_PUBLIC_CAPTCHA_SITE_KEY;
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;

  // Initialize reCAPTCHA
  useEffect(() => {
    if (!siteKey) {
      setRecaptchaError(
        "reCAPTCHA site key is missing. Please check your environment variables."
      );
      return;
    }

    setRecaptchaLoading(true);

    // Check if script already exists
    const existingScript = document.querySelector(
      'script[src*="recaptcha/api.js"]'
    );
    if (existingScript) {
      setRecaptchaLoading(false);
      return;
    }

    const script = document.createElement("script");
    script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
    script.async = true;
    script.onload = () => {
      setRecaptchaLoading(false);
    };
    script.onerror = () => {
      setRecaptchaError("Failed to load reCAPTCHA. Please try again later.");
      setRecaptchaLoading(false);
    };
    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [siteKey]);

  // Custom close handler that redirects to main page
  const handleClose = () => {
    onClose(); // Close the modal
    router.push("/"); // Redirect to main page
  };

  // Get reCAPTCHA token
  const getRecaptchaToken = async (): Promise<string> => {
    if (!siteKey) {
      throw new Error("reCAPTCHA site key is not configured.");
    }

    if (!window.grecaptcha) {
      throw new Error("reCAPTCHA is not available. Please try again later.");
    }

    try {
      return await new Promise((resolve, reject) => {
        const grecaptcha = window.grecaptcha;
        if (!grecaptcha || typeof grecaptcha.ready !== "function") {
          reject(new Error("reCAPTCHA ready method is not available"));
          return;
        }

        grecaptcha.ready(async () => {
          try {
            if (typeof grecaptcha.execute !== "function") {
              reject(new Error("reCAPTCHA execute method is not available"));
              return;
            }

            const token = await grecaptcha.execute(siteKey, {
              action: "seo_audit_request",
            });

            if (!token) {
              reject(new Error("Received empty token from reCAPTCHA."));
            } else {
              resolve(token);
            }
          } catch (error) {
            reject(
              error instanceof Error
                ? error
                : new Error(`reCAPTCHA execution failed: ${error}`)
            );
          }
        });
      });
    } catch (error) {
      throw error;
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Basic validation
    if (!formData.name.trim() || !formData.email.trim()) {
      setIsSubmitting(false);
      return;
    }

    let recaptchaToken = "";

    try {
      // Get reCAPTCHA token
      recaptchaToken = await getRecaptchaToken();
    } catch (error) {
      console.error("reCAPTCHA verification failed:", error);
      setIsSubmitting(false);
      return;
    }

    try {
      // Submit to contact API
      const response = await http.post("/api/contact/", {
        subject: `SEO Audit Request Failed - ${websiteUrl}`,
        email: formData.email.trim(),
        text: `Name: ${formData.name.trim()}\n\nWebsite: ${websiteUrl}\n\nAdditional Information: ${
          formData.message.trim() || "None provided"
        }\n\nNote: This is a request for manual SEO analysis after automated analysis failed.`,
        recaptcha_token: recaptchaToken,
      });

      if (response.status !== 200 && response.status !== 201) {
        throw new Error("Failed to submit form");
      }

      setIsSubmitted(true);

      // Show success message for 2.5 seconds, then redirect to main page
      setTimeout(() => {
        onClose();
        setIsSubmitted(false);
        setFormData({ email: "", name: "", message: "" });
        router.push("/"); // Redirect to main page
      }, 2500);
    } catch (error) {
      console.error("Failed to submit request:", error);
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      open={isOpen}
      onClose={handleClose}
      title="SEO Audit Request Failed"
      size="lg"
    >
      <div className="flex flex-col items-center p-4 md:p-5">
        {!isSubmitted ? (
          <>
            {/* Icon */}
            <div className="w-10 h-10 md:w-12 md:h-12 bg-red-100 rounded-full flex items-center justify-center mb-2">
              <svg
                className="w-5 h-5 md:w-6 md:h-6 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>

            {/* Header */}
            <div className="mb-3 md:mb-4 text-center">
              <h3 className="text-lg md:text-xl font-bold text-secondary mb-2">
                Analysis Failed
              </h3>
              <div className="space-y-2">
                <p className="text-xs md:text-sm text-secondary/80">
                  SEO audit for{" "}
                  <span className="font-semibold text-primary">
                    {websiteUrl}
                  </span>{" "}
                  failed. Submit a request for manual analysis.
                </p>

                <p className="text-xs md:text-sm text-secondary/80">
                  May be due to firewall, security measures, SSL issues, or
                  server maintenance.
                </p>
              </div>
            </div>

            {/* Form */}
            <div className="w-full bg-gray-50/50 rounded-lg p-3 md:p-4 mb-3 md:mb-4 border border-gray-200">
              <form onSubmit={handleSubmit} className="space-y-3 md:space-y-4">
                <div>
                  <label
                    htmlFor="name"
                    className="block text-xs md:text-sm font-medium text-secondary mb-1.5"
                  >
                    <span className="flex items-center gap-1.5">
                      <svg
                        className="w-3.5 h-3.5 md:w-4 md:h-4 text-primary"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                      Name *
                    </span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-1.5 md:px-3 md:py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none transition-all duration-200 bg-white text-sm"
                    placeholder="Your full name"
                  />
                </div>

                <div>
                  <label
                    htmlFor="email"
                    className="block text-xs md:text-sm font-medium text-secondary mb-1.5"
                  >
                    <span className="flex items-center gap-1.5">
                      <svg
                        className="w-3.5 h-3.5 md:w-4 md:h-4 text-primary"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                      Email *
                    </span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-1.5 md:px-3 md:py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none transition-all duration-200 bg-white text-sm"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label
                    htmlFor="message"
                    className="block text-xs md:text-sm font-medium text-secondary mb-1.5"
                  >
                    <span className="flex items-center gap-1.5">
                      <svg
                        className="w-3.5 h-3.5 md:w-4 md:h-4 text-primary"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                        />
                      </svg>
                      Additional Information (Optional)
                    </span>
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={2}
                    className="w-full px-3 py-1.5 md:px-3 md:py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none transition-all duration-200 resize-none bg-white text-sm"
                    placeholder="Any specific requirements or information about the website..."
                  />
                </div>

                {/* reCAPTCHA Loading State */}
                {recaptchaLoading && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-xs md:text-sm text-blue-700 flex items-center justify-center gap-2">
                      <svg
                        className="animate-spin w-4 h-4"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Loading reCAPTCHA... Please wait.
                    </p>
                  </div>
                )}

                {/* reCAPTCHA Error State */}
                {recaptchaError && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-xs md:text-sm text-red-700 flex items-center gap-2">
                      <svg
                        className="w-4 h-4 flex-shrink-0"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                      </svg>
                      {recaptchaError}
                    </p>
                  </div>
                )}

                {/* Buttons */}
                <div className="flex flex-col gap-2">
                  <button
                    type="submit"
                    disabled={
                      isSubmitting ||
                      !!recaptchaError ||
                      !siteKey ||
                      !apiUrl ||
                      recaptchaLoading
                    }
                    className="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium flex items-center justify-center gap-1.5 shadow-lg hover:shadow-xl text-sm"
                  >
                    {isSubmitting ? (
                      <>
                        <svg
                          className="animate-spin w-4 h-4 md:w-5 md:h-5"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Submitting Request...
                      </>
                    ) : (
                      <>
                        <svg
                          className="w-4 h-4 md:w-5 md:h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                          />
                        </svg>
                        Submit Request
                      </>
                    )}
                  </button>

                  <button
                    type="button"
                    onClick={handleClose}
                    disabled={isSubmitting}
                    className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium flex items-center justify-center gap-1.5 text-sm"
                  >
                    <svg
                      className="w-4 h-4 md:w-5 md:h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 19l-7-7m0 0l7-7m-7 7h18"
                      />
                    </svg>
                    Go Back to Main Page
                  </button>
                </div>
              </form>
            </div>

            {/* reCAPTCHA Badge and Privacy Notice */}
            <div className="w-full flex flex-col items-center gap-1">
              <div
                className="g-recaptcha-badge"
                style={{ visibility: "hidden" }}
              ></div>
              <p className="text-xs text-secondary/60 text-center">
                By submitting this form you agree to the terms and conditions in
                our privacy notice. This site is protected by reCAPTCHA.
              </p>
            </div>

            {/* Footer */}
            <div className="w-full bg-primary/5 rounded-lg mt-2 p-2.5 md:p-3 border border-primary/20">
              <div className="flex items-start gap-2">
                <div className="w-4 h-4 md:w-5 md:h-5 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <svg
                    className="w-2 h-2 md:w-2.5 md:h-2.5 text-primary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-primary text-xs mb-0.5">
                    What happens next?
                  </h4>
                  <p className="text-xs text-secondary/80">
                    Our SEO experts will manually analyze your website and send
                    you a detailed report within 24 hours.
                  </p>
                </div>
              </div>
            </div>
          </>
        ) : (
          /* Success state */
          <div className="text-center py-3 md:py-4">
            <div className="w-14 h-14 md:w-16 md:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg animate-pulse">
              <svg
                className="w-7 h-7 md:w-8 md:h-8 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h3 className="text-base md:text-lg font-bold text-secondary mb-2">
              ✅ Request Submitted Successfully!
            </h3>
            <p className="text-xs md:text-sm text-secondary/80 mb-3">
              Thank you,{" "}
              <span className="font-semibold text-primary">
                {formData.name}
              </span>
              ! Our SEO experts will manually analyze{" "}
              <span className="font-semibold text-primary">{websiteUrl}</span>{" "}
              and send detailed results to{" "}
              <span className="font-semibold text-primary">
                {formData.email}
              </span>{" "}
              within 24 hours.
            </p>

            <div className="space-y-2">
              <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                <p className="text-xs text-green-700 flex items-center justify-center gap-1.5">
                  <svg
                    className="w-3.5 h-3.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  Check your email for updates
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-2">
                <p className="text-xs text-blue-700 flex items-center justify-center gap-1.5">
                  <svg
                    className="w-3.5 h-3.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Redirecting to main page...
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
