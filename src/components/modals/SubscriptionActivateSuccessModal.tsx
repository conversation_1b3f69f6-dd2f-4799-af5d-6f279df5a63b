"use client";
import { motion, AnimatePresence } from "framer-motion";

interface ActivationDetails {
  current_period_end: string;
  cancel_at_period_end: boolean;
  will_auto_renew: boolean;
}

interface SubscriptionActivateSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  message: string;
  details?: ActivationDetails;
  planName?: string;
}

export default function SubscriptionActivateSuccessModal({
  isOpen,
  onClose,
  message,
  details,
  planName,
}: SubscriptionActivateSuccessModalProps) {
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6"
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">
                Auto-Renewal Activated
              </h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Success Icon */}
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>

            {/* Content */}
            <div className="text-center mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Auto-Renewal Successfully Activated
              </h3>
              
              <div className="text-sm text-gray-600 space-y-3">
                <p className="font-medium text-gray-900">{message}</p>
                
                {details && (
                  <div className="bg-gray-50 rounded-lg p-4 text-left space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Plan:</span>
                      <span className="font-medium text-gray-900">
                        {planName || "Subscription"}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Next billing date:</span>
                      <span className="font-medium text-gray-900">
                        {formatDate(details.current_period_end)}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Auto-renewal:</span>
                      <span className="font-medium text-green-600">
                        {details.will_auto_renew ? "Active" : "Inactive"}
                      </span>
                    </div>
                    
                    {!details.cancel_at_period_end && (
                      <div className="mt-3 p-3 bg-green-50 rounded-lg">
                        <p className="text-xs text-green-800">
                          <strong>Great!</strong> Your subscription will automatically 
                          renew on the next billing date. You can cancel auto-renewal 
                          anytime before then.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-lg font-medium transition-colors"
              >
                Got it
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
