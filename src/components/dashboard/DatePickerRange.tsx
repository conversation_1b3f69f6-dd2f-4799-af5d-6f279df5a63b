"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { DateRange } from "react-date-range";
import { CalendarIcon, X } from "lucide-react";
import {
  format,
  addDays,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  subDays,
  subWeeks,
  subMonths,
} from "date-fns";

// Import required styles
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DatePickerRangeProps {
  onChange?: (range: { startDate: Date; endDate: Date } | undefined) => void;
  value?: { startDate: Date; endDate: Date };
  disabled?: boolean;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
  showPreview?: boolean;
  showDateDisplay?: boolean;
  months?: number;
  showPresets?: boolean;
}

interface DatePreset {
  label: string;
  getValue: () => { startDate: Date; endDate: Date };
}

export function DatePickerRange({
  onChange,
  value,
  disabled = false,
  minDate,
  maxDate,
  className,
  showPreview = true,
  showDateDisplay = false, // Disable date display by default for cleaner look
  months = 2,
  showPresets = true,
}: DatePickerRangeProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [state, setState] = useState(() => {
    if (value) {
      return [
        {
          startDate: value.startDate,
          endDate: value.endDate,
          key: "selection",
        },
      ];
    }
    return [
      {
        startDate: undefined,
        endDate: undefined,
        key: "selection",
      },
    ];
  });

  // Update internal state when value prop changes
  useEffect(() => {
    if (value) {
      setState([
        {
          startDate: value.startDate,
          endDate: value.endDate,
          key: "selection",
        },
      ]);
    } else {
      // Reset to default state when value is undefined
      setState([
        {
          startDate: undefined,
          endDate: undefined,
          key: "selection",
        },
      ]);
    }
  }, [value]);

  // Define preset date ranges
  const datePresets: DatePreset[] = [
    {
      label: "Today",
      getValue: () => ({
        startDate: new Date(),
        endDate: new Date(),
      }),
    },
    {
      label: "Yesterday",
      getValue: () => ({
        startDate: subDays(new Date(), 1),
        endDate: subDays(new Date(), 1),
      }),
    },
    {
      label: "This Week",
      getValue: () => ({
        startDate: startOfWeek(new Date()),
        endDate: endOfWeek(new Date()),
      }),
    },
    {
      label: "Last Week",
      getValue: () => ({
        startDate: startOfWeek(subWeeks(new Date(), 1)),
        endDate: endOfWeek(subWeeks(new Date(), 1)),
      }),
    },
    {
      label: "This Month",
      getValue: () => ({
        startDate: startOfMonth(new Date()),
        endDate: endOfMonth(new Date()),
      }),
    },
    {
      label: "Last Month",
      getValue: () => ({
        startDate: startOfMonth(subMonths(new Date(), 1)),
        endDate: endOfMonth(subMonths(new Date(), 1)),
      }),
    },
  ];

  const handleRangeChange = useCallback(
    (item: any) => {
      const newSelection = item.selection;

      // Only update state if there's an actual change
      setState((prevState) => {
        const currentSelection = prevState[0];
        if (
          currentSelection.startDate?.getTime() ===
            newSelection.startDate?.getTime() &&
          currentSelection.endDate?.getTime() ===
            newSelection.endDate?.getTime()
        ) {
          return prevState; // No change, prevent rerender
        }
        return [newSelection];
      });

      // Only call onChange when we have a complete range
      if (newSelection.startDate && newSelection.endDate) {
        onChange?.({
          startDate: newSelection.startDate,
          endDate: newSelection.endDate,
        });
      } else if (!newSelection.startDate && !newSelection.endDate) {
        // Handle clearing the selection
        onChange?.(undefined);
      }
    },
    [onChange]
  );

  const handlePresetClick = useCallback(
    (preset: DatePreset) => {
      const range = preset.getValue();
      const newRange = [
        {
          startDate: range.startDate,
          endDate: range.endDate,
          key: "selection",
        },
      ];
      setState(newRange);
      onChange?.(range);
    },
    [onChange]
  );

  const handleClear = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      const newRange = [
        {
          startDate: undefined,
          endDate: undefined,
          key: "selection",
        },
      ];
      setState(newRange);
      onChange?.(undefined);
    },
    [onChange]
  );

  const formatDateRange = useMemo(() => {
    const range = state[0];
    if (range.startDate && range.endDate) {
      const start = format(range.startDate, "MMM dd, yyyy");
      const end = format(range.endDate, "MMM dd, yyyy");

      if (range.startDate.getTime() === range.endDate.getTime()) {
        return start;
      }
      return `${start} - ${end}`;
    }
    return "Select date range";
  }, [state]);

  const hasValidRange = useMemo(() => {
    const range = state[0];
    return range.startDate && range.endDate;
  }, [state]);

  if (showPresets) {
    return (
      <div className={cn("w-full", className)}>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal transition-colors duration-200",
                "border-gray-200 bg-white hover:bg-purple-50 hover:border-purple-300",
                "focus:ring-2 focus:ring-purple-200 focus:border-purple-400 focus:outline-none",
                !hasValidRange && "text-muted-foreground",
                disabled &&
                  "opacity-50 cursor-not-allowed hover:bg-white hover:border-gray-200",
                "h-11 px-4 rounded-lg"
              )}
              disabled={disabled}
            >
              <CalendarIcon className="mr-2 h-4 w-4 text-purple-600" />
              <span className="flex-1 truncate">{formatDateRange}</span>
              {hasValidRange && !disabled && (
                <div
                  onClick={handleClear}
                  className="ml-2 h-6 w-6 rounded-full hover:bg-purple-100 flex items-center justify-center transition-colors duration-200 cursor-pointer"
                  aria-label="Clear date range"
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e: React.KeyboardEvent) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      handleClear(e as any);
                    }
                  }}
                >
                  <X className="h-3.5 w-3.5 text-gray-400 hover:text-purple-600 transition-colors duration-200" />
                </div>
              )}
            </Button>
          </PopoverTrigger>

          <PopoverContent
            className="w-auto p-0 max-w-none mx-8"
            align="start"
            sideOffset={4}
          >
            <div className="flex bg-white rounded-lg shadow-lg border">
              {/* Preset buttons on the left */}
              <div className="flex flex-col p-3 border-r border-gray-200 bg-gray-50 rounded-l-lg w-full max-w-[200px]">
                <div className="space-y-1">
                  {datePresets.map((preset) => (
                    <Button
                      key={preset.label}
                      variant="ghost"
                      size="sm"
                      onClick={() => handlePresetClick(preset)}
                      className={cn(
                        "w-full justify-start text-left text-sm font-normal h-8 px-3",
                        "hover:bg-purple-100 hover:text-purple-700 transition-colors duration-200",
                        "text-gray-600"
                      )}
                    >
                      {preset.label}
                    </Button>
                  ))}
                </div>

                {/* Custom range options */}
                <div className="mt-4 pt-3 border-t border-gray-200">
                  <div className="text-xs text-gray-500 mb-2">Custom</div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "w-full justify-start text-left text-sm font-normal h-8 px-3",
                      "hover:bg-purple-100 hover:text-purple-700 transition-colors duration-200",
                      "text-gray-600"
                    )}
                  >
                    - days up to today
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "w-full justify-start text-left text-sm font-normal h-8 px-3",
                      "hover:bg-purple-100 hover:text-purple-700 transition-colors duration-200",
                      "text-gray-600"
                    )}
                  >
                    - days starting today
                  </Button>
                </div>
              </div>

              {/* Date range picker on the right */}
              <div className="p-3">
                <DateRange
                  editableDateInputs={true}
                  onChange={handleRangeChange}
                  moveRangeOnFirstSelection={false}
                  retainEndDateOnFirstSelection={false}
                  ranges={state}
                  rangeColors={["#7c2d92"]}
                  minDate={minDate}
                  maxDate={maxDate}
                  showSelectionPreview={true}
                  showDateDisplay={false}
                  direction="horizontal"
                  months={2}
                />
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    );
  }

  // Fallback to original layout without presets
  return (
    <>
      <div className={cn("w-full", className)}>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal transition-colors duration-200",
                "border-gray-200 bg-white hover:bg-purple-50 hover:border-purple-300",
                "focus:ring-2 focus:ring-purple-200 focus:border-purple-400 focus:outline-none",
                !hasValidRange && "text-muted-foreground",
                disabled &&
                  "opacity-50 cursor-not-allowed hover:bg-white hover:border-gray-200",
                "h-11 px-4 rounded-lg"
              )}
              disabled={disabled}
            >
              <CalendarIcon className="mr-2 h-4 w-4 text-purple-600" />
              <span className="flex-1 truncate">{formatDateRange}</span>
              {hasValidRange && !disabled && (
                <div
                  onClick={handleClear}
                  className="ml-2 h-6 w-6 rounded-full hover:bg-purple-100 flex items-center justify-center transition-colors duration-200 cursor-pointer"
                  aria-label="Clear date range"
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e: React.KeyboardEvent) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.preventDefault();
                      handleClear(e as any);
                    }
                  }}
                >
                  <X className="h-3.5 w-3.5 text-gray-400 hover:text-purple-600 transition-colors duration-200" />
                </div>
              )}
            </Button>
          </PopoverTrigger>

          <PopoverContent className="w-auto p-0" align="start" sideOffset={4}>
            <div className="p-3">
              <DateRange
                editableDateInputs={true}
                onChange={handleRangeChange}
                moveRangeOnFirstSelection={false}
                retainEndDateOnFirstSelection={false}
                ranges={state}
                rangeColors={["#7c2d92", "#6b21a8", "#581c87"]} // Darker purple color palette for selection preview
                minDate={minDate}
                maxDate={maxDate}
                showSelectionPreview={true}
                showDateDisplay={false}
                direction="horizontal"
                months={2}
              />
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Custom styles for react-date-range */}
    </>
  );
}
