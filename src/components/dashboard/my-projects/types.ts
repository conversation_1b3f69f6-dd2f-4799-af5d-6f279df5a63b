export interface Project {
  name: string;
  url: string;
  createdAt: string;
  id: string; // Changed to string to match API
  color: string; // Project color from ColorPickerBox
  status: "enabled" | "disabled"; // Project status
  favicon_url?: string | null; // Favicon URL from API
}

export interface FilterOption {
  name: string;
  query: string;
}

export interface ProjectsListProps {
  projects?: Project[];
  onSwitchProject: (id: string, currentStatus: "enabled" | "disabled") => void;
  onEdit: (id: string) => void;
  onExport: (id: string) => void;
  onDelete: (id: string) => void;
  switchPending: boolean;
  deletePending: boolean;
  exportPending: boolean;
  closeModal: boolean;
  filterKey?: string; // Add filter key for animations
}

export interface ProjectsFilterProps {
  filter: FilterOption;
  visibleFilters: FilterOption[];
  onFilterChange: (query: string) => void;
  closeModal: boolean;
}
