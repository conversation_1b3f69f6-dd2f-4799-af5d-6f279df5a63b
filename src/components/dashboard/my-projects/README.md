# My Projects Components

This directory contains the modular components for the My Projects page in the dashboard.

## Components

### MyProjectsContent
The main container component that orchestrates all the functionality for the my-projects page. It handles:
- State management for projects, filters, and modals
- API calls for fetching projects and profile data
- Business logic for project operations (switch, edit, delete, export)
- Layout structure with two main sections (projects list and welcome area)

### ProjectsList
Displays the list of projects in a card format. Features:
- Project cards with name, URL, creation date
- Switch toggle for project activation
- Dropdown menu with project actions (edit, advance settings, export, delete)
- View button for each project
- Empty state when no projects are found

### ProjectsFilter
Provides filtering functionality for the projects list. Features:
- Dropdown with filter options (Recently opened, By status, Newest first, etc.)
- Updates URL parameters when filter changes
- Visual indication of current filter

### ProjectSearch
Real-time search functionality with debouncing. Features:
- Search input with loading indicator
- Debounced search (1.5 second delay)
- URL parameter updates
- Tooltip with help information
- Suspense boundary for search params

## Types

### Project
```typescript
interface Project {
  name: string;
  url: string;
  createdAt: string;
  id: number;
}
```

### FilterOption
```typescript
interface FilterOption {
  name: string;
  query: string;
}
```

## Usage

```typescript
import MyProjectsContent from "@/components/dashboard/my-projects/MyProjectsContent";

// In your page component
export default function MyProjectsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <MyProjectsContent />
    </Suspense>
  );
}
```

## File Structure

```
my-projects/
├── MyProjectsContent.tsx    # Main container component
├── ProjectsList.tsx         # Projects list display
├── ProjectsFilter.tsx       # Filter dropdown
├── ProjectSearch.tsx        # Search functionality
├── types.ts                 # TypeScript interfaces
├── index.ts                 # Component exports
└── README.md               # This documentation
```

## Features

- **Modular Architecture**: Each component has a single responsibility
- **TypeScript Support**: Full type safety with shared interfaces
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Search**: Debounced search with URL parameter updates
- **State Management**: Uses React Query for server state and local state for UI
- **Error Handling**: Comprehensive error boundaries and loading states
- **Accessibility**: Proper ARIA labels and keyboard navigation
