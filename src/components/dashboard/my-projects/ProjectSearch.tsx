"use client";
import React, {
  ChangeEvent,
  useEffect,
  useRef,
  useState,
  Suspense,
} from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { SearchIcon } from "@/ui/icons/action";
import { BsExclamationCircle } from "react-icons/bs";
import TooltipPortal from "@/components/ui/TooltipPortal";

// Component that uses useSearchParams for search functionality
const ProjectSearchContent = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [errText, setErrorText] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();

  // Set initial value from URL
  useEffect(() => {
    const initialSearch = searchParams?.get("search") || "";
    setSearchTerm(initialSearch);
  }, [searchParams]);

  // Handle debounce and update URL
  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (searchTerm.length === 0) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    timeoutRef.current = setTimeout(() => {
      const currentFilter = searchParams?.get("filter") || "recently-opened";
      const query = new URLSearchParams();

      if (searchTerm) query.set("search", searchTerm);
      if (currentFilter) query.set("filter", currentFilter);

      const newQuery = query.toString();
      const currentQuery = searchParams?.toString();

      if (newQuery !== currentQuery) {
        router.push(`?${newQuery}`);
      }

      setIsLoading(false);
    }, 1500);

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [searchTerm, searchParams, router]);

  return (
    <label htmlFor="search" className="w-full relative">
      <div className="mb-2 flex items-center gap-2 text-gray-600">
        <div className="text-sm lg:text-base">search project</div>
        <TooltipPortal
          width="xl"
          content={<span>search projectsearch project</span>}
        >
          <BsExclamationCircle className="text-gray-500" />
        </TooltipPortal>
      </div>
      <div className="relative">
        <i className="absolute left-3 z-10 top-1/2 transform -translate-y-1/2">
          <SearchIcon />
        </i>
        <input
          id="search"
          placeholder="Type Search Project..."
          onChange={(e: ChangeEvent<HTMLInputElement>) => {
            setSearchTerm(e.target.value);
          }}
          autoComplete="off"
          data-lpignore="true"
          type="text"
          value={searchTerm}
          className={`textField__input bg-white border-[var(--color-border-input)] placeholder:text-[var(--color-border-input)] text-sm focus:border-primary p-2 md:p-3 w-full !pl-10 pr-10 ${
            errText ? "bg-red-100/60 border-primary-red" : ""
          }`}
        />
        {isLoading && (
          <i className="absolute right-3 z-10 top-1/2 transform -translate-y-1/2">
            <svg
              className="w-4 h-4 text-primary mr-2 animate-spin"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </i>
        )}
      </div>
      {errText && <span className="text-primary-red text-xs">{errText}</span>}
    </label>
  );
};

// Wrapper component with Suspense boundary for search functionality
export default function ProjectSearch() {
  return (
    <Suspense
      fallback={<div className="animate-pulse bg-gray-200 rounded h-10" />}
    >
      <ProjectSearchContent />
    </Suspense>
  );
}
