"use client";
import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Switch } from "@/components/ui/switch";
import Dropdown from "@/components/shared/DropDown";
import SettingFillIcon from "@/ui/icons/dashboard/SettingFillIcon";
import { CalendarIcon } from "@/ui/icons/dateTime";
import { ExternalLinkIcon } from "@/ui/icons/general";
import { ProjectsListProps, Project } from "./types";
import { motion, AnimatePresence } from "framer-motion";

// Enhanced Project Card Component
interface ProjectCardProps {
  project: Project;
  index: number;
  onSwitchProject: (id: string, currentStatus: "enabled" | "disabled") => void;
  onEdit: (id: string) => void;
  onExport: (id: string) => void;
  onDelete: (id: string) => void;
  switchPending: boolean;
  deletePending: boolean;
  exportPending: boolean;
  closeModal: boolean;
}

function ProjectCard({
  project,
  index,
  onSwitchProject,
  onEdit,
  onExport,
  onDelete,
  switchPending,
  deletePending,
  exportPending,
  closeModal,
}: ProjectCardProps) {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Close dropdown when closeModal changes
  useEffect(() => {
    if (closeModal) {
      setDropdownOpen(false);
    }
  }, [closeModal]);
  // Animation variants for the card with enhanced filter change animations
  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.4,
        delay: index * 0.08, // Stagger animation
        ease: [0.25, 0.46, 0.45, 0.94] as const,
      },
    },
    exit: {
      opacity: 0,
      y: -20,
      scale: 0.95,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94] as const,
      },
    },
  };

  // Color mapping from ColorPickerBox hex values to Tailwind classes
  const getProjectColorClasses = (color?: string) => {
    const colorMap: Record<
      string,
      { background: string; border: string; accent: string; bgColor: string }
    > = {
      "#C189E9": {
        background: "bg-purple-100/60",
        border: "border-purple-300/50",
        accent: "bg-purple-500",
        bgColor: "#C189E9",
      }, // pink
      "#2756A9": {
        background: "bg-blue-100/60",
        border: "border-blue-300/50",
        accent: "bg-blue-600",
        bgColor: "#2756A9",
      }, // blue
      "#587DBD": {
        background: "bg-slate-100/60",
        border: "border-slate-300/50",
        accent: "bg-slate-500",
        bgColor: "#587DBD",
      }, // silver
      "#00BBEC": {
        background: "bg-cyan-100/60",
        border: "border-cyan-300/50",
        accent: "bg-cyan-500",
        bgColor: "#00BBEC",
      }, // blue-low
      "#FF0000": {
        background: "bg-red-100/60",
        border: "border-red-300/50",
        accent: "bg-red-500",
        bgColor: "#FF0000",
      }, // red
      "#FF5E00": {
        background: "bg-orange-100/60",
        border: "border-orange-300/50",
        accent: "bg-orange-600",
        bgColor: "#FF5E00",
      }, // orange
      "#FF9500": {
        background: "bg-amber-100/60",
        border: "border-amber-300/50",
        accent: "bg-amber-500",
        bgColor: "#FF9500",
      }, // orange-low
      "#F8BD00": {
        background: "bg-yellow-100/60",
        border: "border-yellow-300/50",
        accent: "bg-yellow-500",
        bgColor: "#F8BD00",
      }, // yellow
      "#319F43": {
        background: "bg-green-100/60",
        border: "border-green-300/50",
        accent: "bg-green-600",
        bgColor: "#319F43",
      }, // green
      "#31D37A": {
        background: "bg-emerald-100/60",
        border: "border-emerald-300/50",
        accent: "bg-emerald-500",
        bgColor: "#31D37A",
      }, // green (light)
      "#8C00FF": {
        background: "bg-violet-100/60",
        border: "border-violet-300/50",
        accent: "bg-violet-600",
        bgColor: "#8C00FF",
      }, // purple
      "#FF00C3": {
        background: "bg-fuchsia-100/60",
        border: "border-fuchsia-300/50",
        accent: "bg-fuchsia-500",
        bgColor: "#FF00C3",
      }, // red-pink
    };

    // Default to primary color if no color is provided or color not found
    const defaultColors = {
      background: "bg-gray-100/60",
      border: "border-gray-300/50",
      accent: "bg-primary",
      bgColor: "#f3f4f6",
    };
    return color && colorMap[color] ? colorMap[color] : defaultColors;
  };

  // Extract domain from URL for better display
  const getDomain = (url: string) => {
    try {
      return new URL(url).hostname.replace("www.", "");
    } catch {
      return url;
    }
  };

  // Format date for better readability
  const formatDate = (dateString: string) => {
    // Simple formatting - you can enhance this based on your date format
    return dateString.replace("craeted at ", "").replace("-", " ");
  };

  // Get project colors
  const projectColors = getProjectColorClasses(project.color);

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      layout
      className="group relative"
    >
      <div
        className={`rounded-lg shadow-sm border-2 p-3 transition-all duration-200 ${projectColors.border}`}
        style={{
          backgroundColor: project.color ? `${project.color}15` : "#f3f4f615",
          borderColor: project.color ? `${project.color}40` : "#d1d5db40",
        }}
      >
        {/* Header with Project Name and Switch */}
        <div className="flex justify-between items-start mb-2.5">
          <div className="flex items-center gap-3">
            {project.favicon_url ? (
              <img
                src={project.favicon_url}
                alt={`${project.name} favicon`}
                className="w-8 h-8 object-contain rounded"
                onError={(e) => {
                  // Fallback to first letter in colored box if favicon fails to load
                  const target = e.target as HTMLImageElement;
                  const fallbackDiv = document.createElement("div");
                  fallbackDiv.className =
                    "w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm shadow-sm";
                  fallbackDiv.style.backgroundColor = project.color;
                  fallbackDiv.textContent = project.name
                    .charAt(0)
                    .toUpperCase();
                  target.parentElement?.replaceChild(fallbackDiv, target);
                }}
              />
            ) : (
              <div
                className="w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm shadow-sm"
                style={{ backgroundColor: project.color }}
              >
                {project.name.charAt(0).toUpperCase()}
              </div>
            )}
            <div className="flex flex-col">
              <h3 className="text-base font-bold text-secondary line-clamp-1 mb-0.5">
                {project.name}
              </h3>
              <div className="flex items-center gap-1">
                <CalendarIcon className="w-4 h-4 text-gray-600" />
                <span className="text-xs text-light-gray-3 font-medium">
                  {formatDate(project.createdAt)}
                </span>
              </div>
            </div>
          </div>
          <div>
            <Switch
              checked={project.status === "enabled"}
              disabled={switchPending}
              className={`${
                switchPending ? "!cursor-wait" : ""
              } transition-all duration-200`}
              onClick={() => onSwitchProject(project.id, project.status)}
            />
          </div>
        </div>

        {/* Project URL */}
        <div className="mb-3">
          <div className="flex items-center gap-1">
            <Link
              href={project.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-light-gray-3 font-medium truncate hover:text-gray-800 hover:underline hover:underline-offset-2 transition-all duration-200 cursor-pointer group-hover:text-primary-500 flex flex-row"
            >
              <ExternalLinkIcon className="w-5 h-5" />
              {getDomain(project.url)}
            </Link>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center pt-2 border-t border-gray-100">
          <div>
            <Dropdown
              open={dropdownOpen}
              setOpen={setDropdownOpen}
              width={180}
              align="start"
              className="hover:text-primary"
              button={
                <button
                  className={`p-1.5 rounded-md transition-all duration-200 ${
                    dropdownOpen
                      ? "bg-primary/10 text-primary"
                      : "hover:bg-gray-50 text-gray-600"
                  }`}
                >
                  <motion.div
                    className="w-5 h-5 flex items-center justify-center"
                    animate={{
                      rotate: dropdownOpen ? 180 : 0,
                      scale: dropdownOpen ? 1.1 : 1,
                    }}
                    transition={{ duration: 0.2, ease: "easeInOut" }}
                  >
                    <SettingFillIcon />
                  </motion.div>
                </button>
              }
            >
              <div className="p-1">
                <button
                  disabled={deletePending || exportPending}
                  className="p-2.5 text-light-gray-3 w-full hover:text-primary hover:bg-gray-50 duration-200 text-left border-b rounded-t-lg transition-all text-sm"
                  onClick={() => onEdit(project.id)}
                >
                  Edit project
                </button>

                <button
                  disabled={deletePending || exportPending}
                  className="p-2.5 text-light-gray-3 w-full hover:text-primary hover:bg-gray-50 duration-200 text-left border-b transition-all text-sm"
                  onClick={() => onExport(project.id)}
                >
                  Export report
                </button>
                <button
                  disabled={deletePending || exportPending}
                  className="p-2.5 text-light-gray-3 w-full hover:text-red-500 hover:bg-red-50 duration-200 text-left rounded-b-lg transition-all text-sm"
                  onClick={() => onDelete(project.id)}
                >
                  Delete project
                </button>
              </div>
            </Dropdown>
          </div>

          <div>
            <Link
              href={`/project/${project.id}/analytics-traffics/overview`}
              className="bg-primary text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-primary/90 transition-colors duration-200"
            >
              View Project
            </Link>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export default function ProjectsList({
  projects,
  onSwitchProject,
  onEdit,
  onExport,
  onDelete,
  switchPending,
  deletePending,
  exportPending,
  closeModal,
  filterKey,
}: ProjectsListProps) {
  // Container animation variants with enhanced filter change animations
  const containerVariants = {
    hidden: {
      opacity: 0,
      y: 20,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        staggerChildren: 0.08,
        ease: [0.25, 0.46, 0.45, 0.94] as const,
      },
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.3,
        staggerChildren: 0.05,
        staggerDirection: -1,
        ease: [0.25, 0.46, 0.45, 0.94] as const,
      },
    },
  };

  // Empty state with animation
  if (!projects?.length) {
    return (
      <div className="h-full flex flex-col">
        <motion.div
          className="flex items-center justify-center flex-1 py-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="text-center space-y-4 max-w-sm">
            <div className="w-20 h-20 mx-auto rounded-full bg-gray-100 flex items-center justify-center">
              <svg
                className="w-10 h-10 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">
                No projects found
              </h3>
              <p className="text-sm text-gray-500 leading-relaxed">
                You haven't created any projects yet. Start by creating your
                first SEO project to begin tracking your website's performance.
              </p>
            </div>
            <Link
              href="/create-project"
              className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary/90 transition-colors duration-200"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Create Project
            </Link>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <AnimatePresence mode="wait">
        <motion.div
          key={filterKey || "default"} // Key changes trigger re-animation
          className="h-full flex flex-col"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          layout
        >
          {/* Single Column Layout with visible scrollbar */}
          <div className="flex-1 overflow-y-auto projects-scrollbar px-1 pb-4">
            <div className="grid grid-cols-1 gap-3.5">
              <AnimatePresence>
                {projects.map((project, index) => (
                  <ProjectCard
                    key={project.id}
                    project={project}
                    index={index}
                    onSwitchProject={onSwitchProject}
                    onEdit={onEdit}
                    onExport={onExport}
                    onDelete={onDelete}
                    switchPending={switchPending}
                    deletePending={deletePending}
                    exportPending={exportPending}
                    closeModal={closeModal}
                  />
                ))}
              </AnimatePresence>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
