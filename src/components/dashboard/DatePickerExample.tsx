"use client";

import * as React from "react";
import { DateRange } from "react-day-picker";
import { DatePickerRange } from "./DatePickerRange";

export function DatePickerExample() {
  const [separateRange, setSeparateRange] = React.useState<DateRange | undefined>();
  const [singleRange, setSingleRange] = React.useState<DateRange | undefined>();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold text-foreground">
          Date Picker Range Examples
        </h2>
        <p className="text-muted-foreground">
          Demonstrating both separate date pickers and single range picker modes
        </p>
      </div>

      <div className="grid gap-8">
        {/* Separate Start/End Date Pickers */}
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-foreground">
              Separate Start & End Date Pickers
            </h3>
            <p className="text-sm text-muted-foreground">
              Two separate buttons for start and end dates with primary color highlighting for today
            </p>
          </div>
          
          <DatePickerRange
            value={separateRange}
            onChange={setSeparateRange}
            className="max-w-md"
          />
          
          {separateRange && (
            <div className="text-sm text-muted-foreground">
              Selected: {separateRange.from?.toDateString()} 
              {separateRange.to && ` - ${separateRange.to.toDateString()}`}
            </div>
          )}
        </div>

        {/* Single Range Picker */}
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-foreground">
              Single Range Picker
            </h3>
            <p className="text-sm text-muted-foreground">
              Single button with proper range highlighting and primary color for today
            </p>
          </div>
          
          <DatePickerRange
            value={singleRange}
            onChange={setSingleRange}
            useRangeMode={true}
            className="max-w-md"
          />
          
          {singleRange && (
            <div className="text-sm text-muted-foreground">
              Selected: {singleRange.from?.toDateString()} 
              {singleRange.to && ` - ${singleRange.to.toDateString()}`}
            </div>
          )}
        </div>
      </div>

      <div className="mt-8 p-4 bg-muted/50 rounded-lg">
        <h4 className="font-medium text-foreground mb-2">Key Improvements:</h4>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• Current day now uses primary color with border highlight</li>
          <li>• Selected dates use primary color background</li>
          <li>• Range highlighting with start, middle, and end styling</li>
          <li>• Smooth animations and hover effects</li>
          <li>• Consistent primary color theme throughout</li>
          <li>• Better accessibility with focus rings</li>
        </ul>
      </div>
    </div>
  );
}
