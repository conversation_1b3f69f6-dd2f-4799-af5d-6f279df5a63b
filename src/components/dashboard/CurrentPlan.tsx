"use client";

import Link from "next/link";
import React from "react";
import { FaPlus } from "react-icons/fa";
import { motion } from "framer-motion";
import { useAuth } from "@/providers/AuthProvider";
import { CrownIcon } from "@/ui/icons/general";
import {
  CalendarDaysIcon,
  ClockIcon,
  CreditCardIcon,
} from "@heroicons/react/24/outline";
import TitlePageDashboard from "./TitlePageDashboard";

export default function CurrentPlan() {
  const { user } = useAuth();

  // Check if user has active subscription (based on current period)
  const hasActiveSubscription = user?.subscriptions?.some((subscription) => {
    const periodEnd = new Date(subscription.current_period_end);
    return periodEnd > new Date();
  });

  // Get active subscription details
  const activeSubscription = user?.subscriptions?.find((subscription) => {
    const periodEnd = new Date(subscription.current_period_end);
    return periodEnd > new Date();
  });

  // Format price with currency
  const formatPrice = (amount: number, currency: string) => {
    return (
      <>
        {amount.toFixed(2)}{" "}
        <span className="text-xs opacity-75">{currency.toUpperCase()}</span>
      </>
    );
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <>
      <div className="flex flex-col gap-2 w-full">
        <TitlePageDashboard value="Your current plan" />
        {hasActiveSubscription && activeSubscription ? (
          // Active Subscription Box
          <motion.div
            className="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden w-full"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Header */}
            <div className="p-3 font-bold bg-primary text-white text-center">
              <span className="flex items-center justify-center gap-2">
                <CrownIcon className="w-5 h-5" />
                {activeSubscription.plan_name}
              </span>
            </div>

            {/* Content */}
            <div className="p-4 md:p-6 space-y-4">
              {/* Plan Info */}
              <div className="bg-primary/10 p-4 rounded-xl">
                <div className="flex items-center gap-3 mb-2">
                  <CrownIcon className="text-primary text-lg" />
                  <span className="text-primary font-bold">
                    ACTIVE SUBSCRIPTION
                  </span>
                </div>
                <p className="text-sm text-gray-700">
                  You have full access to all premium features. Enjoy unlimited
                  SEO analysis, white-label reports, and priority support.
                </p>
              </div>

              <div className="w-full h-[1px] bg-gray-200" />

              {/* Subscription Details */}
              <div className="space-y-3">
                {/* Price */}
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CreditCardIcon className="w-5 h-5 text-gray-500" />
                    <span className="text-sm text-gray-700">Price</span>
                  </div>
                  <span className="text-sm font-semibold text-primary">
                    {formatPrice(
                      activeSubscription.price_amount,
                      activeSubscription.price_currency
                    )}{" "}
                    / {activeSubscription.price_interval}
                  </span>
                </div>

                {/* Current Period */}
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CalendarDaysIcon className="w-5 h-5 text-gray-500" />
                    <span className="text-sm text-gray-700">
                      Current Period
                    </span>
                  </div>
                  <span className="text-sm font-medium">
                    {formatDate(activeSubscription.current_period_start)} -{" "}
                    {formatDate(activeSubscription.current_period_end)}
                  </span>
                </div>

                {/* Next Billing */}
                {activeSubscription.auto_renew !== false &&
                  !activeSubscription.cancel_at_period_end && (
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <ClockIcon className="w-5 h-5 text-gray-500" />
                        <span className="text-sm text-gray-700">
                          Next Billing
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {formatDate(activeSubscription.current_period_end)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {getDaysRemaining(
                            activeSubscription.current_period_end
                          )}{" "}
                          days remaining
                        </div>
                      </div>
                    </div>
                  )}

                {/* Cancellation Notice */}
                {(activeSubscription.cancel_at_period_end ||
                  activeSubscription.auto_renew === false) && (
                  <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <ClockIcon className="w-5 h-5 text-orange-600" />
                        <span className="text-sm text-orange-800 font-medium">
                          Subscription Ending
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-orange-800">
                          {formatDate(activeSubscription.current_period_end)}
                        </div>
                        <div className="text-xs text-orange-600">
                          Access until{" "}
                          {getDaysRemaining(
                            activeSubscription.current_period_end
                          )}{" "}
                          days
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Action Button */}
              <Link
                href="/profile"
                className="w-full text-sm text-white bg-primary rounded-xl p-3 flex items-center gap-2 justify-center hover:bg-primary/90 transition-colors"
              >
                <CrownIcon className="text-lg" />
                Manage Subscription
              </Link>
            </div>
          </motion.div>
        ) : (
          // Promotional Box for No Subscription
          <motion.div
            className="bg-white rounded-2xl border border-gray-200 shadow-sm overflow-hidden w-full"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {/* Header */}
            <div className="p-4 bg-primary text-white text-center">
              <h3 className="font-bold text-lg">Unlock Premium Features</h3>
              <p className="text-sm opacity-90 mt-1">
                Upgrade to access all professional tools
              </p>
            </div>

            {/* Content */}
            <div className="p-6 space-y-5">
              {/* Features List */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg
                      className="w-3 h-3 text-primary"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-700 font-medium">
                    White-label reports with your branding
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg
                      className="w-3 h-3 text-primary"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-700 font-medium">
                    Unlimited website analysis
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg
                      className="w-3 h-3 text-primary"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-700 font-medium">
                    Priority customer support
                  </span>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg
                      className="w-3 h-3 text-primary"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-700 font-medium">
                    Advanced SEO tools and insights
                  </span>
                </div>
              </div>

              <div className="w-full h-[1px] bg-gray-200" />

              {/* Pricing */}
              <div className="text-center">
                <div className="text-xl font-bold text-secondary">
                  Starting at $29/month
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Cancel anytime • 14-day free trial
                </div>
              </div>

              {/* CTA Button */}
              <Link
                href="/pricing"
                className="w-full bg-primary text-white rounded-xl p-3 flex items-center gap-2 justify-center hover:bg-primary/90 transition-colors font-medium"
              >
                <FaPlus className="text-sm" />
                Upgrade to Pro Plan
              </Link>
            </div>
          </motion.div>
        )}
      </div>
    </>
  );
}
