"use client";

import { motion } from "framer-motion";
import { CrossIcon } from "@/ui/icons/general";
import Link from "next/link";

interface PaymentErrorProps {
  message?: string;
}

/**
 * Component to display payment error messages with guidance
 */
export default function PaymentError({ message }: PaymentErrorProps) {
  // Check if this is a "Payment record not found" error
  const isPaymentNotFoundError =
    message && message.includes("Payment record not found");

  return (
    <div className="w-full max-w-2xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
      {/* Error Header */}
      <div className="bg-white p-6 text-center border-b border-gray-200">
        <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CrossIcon className="w-10 h-10 text-red-600" />
        </div>
        <h2 className="text-2xl md:text-3xl font-bold text-red-600 mb-4">
          {isPaymentNotFoundError ? "Payment Not Found" : "Payment Failed"}
        </h2>
        <p className="text-secondary mb-2">
          {message || "Your payment could not be processed. Please try again."}
        </p>
      </div>

      {/* Guidance Section */}
      <div className="p-6 bg-gray-50">
        <h3 className="font-bold text-lg mb-3 text-secondary">
          What to do next:
        </h3>
        {isPaymentNotFoundError ? (
          <ul className="space-y-2 text-secondary mb-6">
            <li className="flex items-start">
              <span className="inline-block w-4 h-4 rounded-full bg-gray-300 mr-2 mt-1"></span>
              <span>Check that you're using the correct payment ID</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block w-4 h-4 rounded-full bg-gray-300 mr-2 mt-1"></span>
              <span>The payment record may have expired or been deleted</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block w-4 h-4 rounded-full bg-gray-300 mr-2 mt-1"></span>
              <span>Try making a new purchase from the pricing page</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block w-4 h-4 rounded-full bg-gray-300 mr-2 mt-1"></span>
              <span>
                Please contact our support team with your payment ID at{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary hover:underline"
                >
                  <EMAIL>
                </a>
              </span>
            </li>
          </ul>
        ) : (
          <ul className="space-y-2 text-secondary mb-6">
            <li className="flex items-start">
              <span className="inline-block w-4 h-4 rounded-full bg-gray-300 mr-2 mt-1"></span>
              <span>Check your payment details and try again</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block w-4 h-4 rounded-full bg-gray-300 mr-2 mt-1"></span>
              <span>Ensure your card has sufficient funds</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block w-4 h-4 rounded-full bg-gray-300 mr-2 mt-1"></span>
              <span>Contact your bank if the issue persists</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block w-4 h-4 rounded-full bg-gray-300 mr-2 mt-1"></span>
              <span>
                If you need assistance, please contact our support team at{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary hover:underline"
                >
                  <EMAIL>
                </a>
              </span>
            </li>
          </ul>
        )}
      </div>

      {/* Action Buttons */}
      <div className="p-6 border-t border-gray-200 flex flex-col sm:flex-row gap-4">
        {isPaymentNotFoundError ? (
          <>
            <motion.div
              className="flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href="/pricing"
                className="w-full btn btn--primary block text-center py-3"
              >
                View Pricing Plans
              </Link>
            </motion.div>
            <motion.div
              className="flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href="/"
                className="w-full btn btn--outline block text-center py-3"
              >
                Return to Home
              </Link>
            </motion.div>
          </>
        ) : (
          <>
            <motion.div
              className="flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href="/pricing"
                className="w-full btn btn--primary block text-center py-3"
              >
                Try Again
              </Link>
            </motion.div>
            <motion.div
              className="flex-1"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href="/"
                className="w-full btn btn--outline block text-center py-3"
              >
                Return to Home
              </Link>
            </motion.div>
          </>
        )}
      </div>
    </div>
  );
}
