"use client";
import { motion } from "framer-motion";

interface LoadingSlateProps {
  title?: string;
  showHeader?: boolean;
  showCards?: boolean;
  cardCount?: number;
  showChart?: boolean;
  showProgress?: boolean;
  height?: "sm" | "md" | "lg" | "xl";
  className?: string;
}

export default function LoadingSlate({
  title = "Loading...",
  showHeader = true,
  showCards = true,
  cardCount = 4,
  showChart = false,
  showProgress = false,
  height = "md",
  className = "",
}: LoadingSlateProps) {
  // Height variants
  const heightClasses = {
    sm: "min-h-[200px]",
    md: "min-h-[300px]",
    lg: "min-h-[400px]",
    xl: "min-h-[500px]",
  };

  // Animation variants for staggered loading effect
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      className={`w-full ${heightClasses[height]} ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header Section */}
      {showHeader && (
        <motion.div variants={itemVariants} className="mb-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
            <div className="h-6 bg-gray-200 rounded-lg animate-pulse flex-1 max-w-xs"></div>
          </div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
          </div>
        </motion.div>
      )}

      {/* Cards Section */}
      {showCards && (
        <motion.div variants={itemVariants} className="mb-6">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: cardCount }).map((_, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-gray-50 p-4 rounded-lg border border-gray-200"
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse flex-1"></div>
                </div>
                <div className="h-8 bg-gray-200 rounded animate-pulse w-16 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded animate-pulse w-20"></div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Chart Section */}
      {showChart && (
        <motion.div variants={itemVariants} className="mb-6">
          <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
            <div className="h-6 bg-gray-200 rounded animate-pulse w-48 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded-lg animate-pulse"></div>
          </div>
        </motion.div>
      )}

      {/* Progress Section */}
      {showProgress && (
        <motion.div variants={itemVariants} className="mb-6">
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-32 mb-2"></div>
                  <div className="h-2 bg-gray-200 rounded-full animate-pulse w-full"></div>
                </div>
                <div className="w-16 h-8 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Content Sections */}
      <motion.div variants={itemVariants} className="space-y-4">
        {Array.from({ length: 2 }).map((_, index) => (
          <div
            key={index}
            className="bg-gray-50 p-4 rounded-lg border border-gray-200"
          >
            <div className="flex items-start gap-3">
              <div className="w-16 h-16 bg-gray-200 rounded-lg animate-pulse flex-shrink-0"></div>
              <div className="flex-1 space-y-2">
                <div className="h-5 bg-gray-200 rounded animate-pulse w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
              </div>
            </div>
          </div>
        ))}
      </motion.div>

      {/* Loading indicator */}
      <motion.div
        variants={itemVariants}
        className="flex items-center justify-center mt-8"
      >
        <div className="flex items-center gap-2 text-gray-500">
          <div className="w-4 h-4 border-2  border-gray-300 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-sm font-medium">{title}</span>
        </div>
      </motion.div>
    </motion.div>
  );
}
