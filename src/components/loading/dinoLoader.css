/* Dinosaur bounce animation */
.bounce-6 {
  animation-name: bounce-6;
  animation-timing-function: ease;
  animation-duration: 1s;
  animation-iteration-count: infinite;
  transform-origin: bottom;
}

@keyframes bounce-6 {
  0% {
    transform: scale(1, 1) translateY(0);
  }
  10% {
    transform: scale(1.1, 0.9) translateY(0);
  }
  30% {
    transform: scale(0.9, 1.1) translateY(-100px);
  }
  50% {
    transform: scale(1.05, 0.95) translateY(0);
  }
  57% {
    transform: scale(1, 1) translateY(-7px);
  }
  64% {
    transform: scale(1, 1) translateY(0);
  }
  100% {
    transform: scale(1, 1) translateY(0);
  }
}

/* Road animation */
.road-container {
  position: relative;
  width:70%;
  border-radius: 2rem;
  height: 12px; /* Thin road */
  background: #d3d3d3; /* Light gray road surface */
  /* overflow: hidden; */
}

.road-lines {
  position: absolute;
  top: 50%;
  width: 400%; /* Double width to ensure seamless looping */
  height: 1px; /* Thickness of dashed lines */
  background: linear-gradient(
    to right,
    transparent 0%,
    transparent 40%,
    #ffffff 40%,
    #ffffff 60%,
    transparent 60%,
    transparent 100%
  );
  background-size: 50px 10px; /* Dash length and spacing */
  animation: move-road .3s linear infinite; /* Matches bounce duration */
}

@keyframes move-road {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50px); /* Moves by one dash cycle */
  }
}

/* Cloud animation */
.cloud,.cloud2 {
  position: absolute;
  animation: move-cloud linear infinite;
  opacity: 0.7;
}

.cloud {
  bottom: 3250px;
  width: 80px;
  height: 40px;
  animation-duration: 5s;
  opacity: 1;
}

.cloud2 {
  bottom: 200px;
  width: 60px;
  height: 30px;
  animation-duration: 8s;
  animation-delay: -10s;
  opacity: 0.6;
}

@keyframes move-cloud {
  0% {
    left: 100%;
  }
  100% {
    left: -100px;
  }
}

/* Cactus animation */
.cactus {
  position: absolute;
  bottom: 12px;
  animation: move-cactus 10s linear infinite;
}

.cactus:nth-child(1) {
  width: 40px;
  height: 60px;
  animation-duration: 4s;
  bottom: 40px;

}

.cactus:nth-child(2) {
  width: 35px;
  height: 55px;
  animation-duration: 5s;
  animation-delay: -2s;
}

@keyframes move-cactus {
  0% {
    left: 100%;
  }
  100% {
    left: -50px;
  }
}

/* Box for dinosaur */
.box {
  position: relative;
  z-index: 10;
}
