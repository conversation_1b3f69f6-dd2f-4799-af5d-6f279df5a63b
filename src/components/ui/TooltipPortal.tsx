'use client'

import { useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import { motion, AnimatePresence } from 'framer-motion'

type TooltipProps = {
  content: React.ReactNode
  children: React.ReactNode
  position?: 'top' | 'right' | 'bottom' | 'left'
  width?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

const animationVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: { opacity: 0.95, scale: 1 },
  exit: { opacity: 0, scale: 0.95 },
}

const widthMap = {
  sm: 'max-w-[120px]',
  md: 'max-w-[150px]',
  lg: 'max-w-[200px]',
  xl: 'max-w-[250px]',
}

export default function TooltipPortal({
  content,
  children,
  position = 'top',
  width = 'md',
  className = '',
}: TooltipProps) {
  const [visible, setVisible] = useState(false)
  const [coords, setCoords] = useState({ top: 0, left: 0 })
  const [dir, setDir] = useState(position)
  const triggerRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!visible || !triggerRef.current || !tooltipRef.current) return

    const calculatePosition = () => {
      const trigger = triggerRef.current!.getBoundingClientRect()
      const tooltip = tooltipRef.current!.getBoundingClientRect()
      const margin = 10

      let top = 0
      let left = 0
      let newDir = position

      switch (position) {
        case 'top':
          top = trigger.top - tooltip.height - margin
          left = trigger.left + trigger.width / 2 - tooltip.width / 2
          if (top < 0) {
            newDir = 'bottom'
            top = trigger.bottom + margin
          }
          break
        case 'bottom':
          top = trigger.bottom + margin
          left = trigger.left + trigger.width / 2 - tooltip.width / 2
          if (top + tooltip.height > window.innerHeight) {
            newDir = 'top'
            top = trigger.top - tooltip.height - margin
          }
          break
        case 'left':
          top = trigger.top + trigger.height / 2 - tooltip.height / 2
          left = trigger.left - tooltip.width - margin
          if (left < 0) {
            newDir = 'right'
            left = trigger.right + margin
          }
          break
        case 'right':
          top = trigger.top + trigger.height / 2 - tooltip.height / 2
          left = trigger.right + margin
          if (left + tooltip.width > window.innerWidth) {
            newDir = 'left'
            left = trigger.left - tooltip.width - margin
          }
          break
      }

      setCoords({ top: Math.max(top, 0), left: Math.max(left, 0) })
      setDir(newDir)
    }

    calculatePosition()

    window.addEventListener('resize', calculatePosition)
    window.addEventListener('scroll', calculatePosition, true)

    return () => {
      window.removeEventListener('resize', calculatePosition)
      window.removeEventListener('scroll', calculatePosition, true)
    }
  }, [visible, position])

  const arrowClass = (() => {
    const base = "absolute w-0 h-0 border-[6px]"
    switch (dir) {
      case 'top':
        return `${base} border-x-transparent border-b-transparent border-t-gray-800 bottom-0 left-[41%] translate-x-1/2 translate-y-full`
      case 'bottom':
        return `${base} border-x-transparent border-t-transparent border-b-gray-800 top-0 left-[43%] translate-x-1/2 -translate-y-full`
      case 'left':
        return `${base} border-y-transparent border-r-transparent border-l-gray-800 right-0 top-1/2 -translate-y-1/2 translate-x-full`
      case 'right':
        return `${base} border-y-transparent border-l-transparent border-r-gray-800 left-0 top-1/2 -translate-y-1/2 -translate-x-full`
    }
  })()

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
        onClick={() => setVisible((v) => !v)}
        className="inline-flex items-center cursor-pointer"
      >
        {children}
      </div>

      {typeof window !== 'undefined' &&
        createPortal(
          <AnimatePresence>
            {visible && (
              <motion.div
                ref={tooltipRef}
                initial="hidden"
                animate="visible"
                exit="exit"
                variants={animationVariants}
                transition={{ duration: 0.15 }}
                style={{
                  position: 'fixed',
                  top: coords.top,
                  left: coords.left,
                  zIndex: 1000,
                }}
                className={`
                    relative
                    p-2 rounded-md shadow-lg bg-gray-800 text-white text-sm break-words
                    ${widthMap[width]} ${className}
                    `}
              >
                <div className="absolute inset-0 pointer-events-none">
                  <div className={arrowClass}></div>
                </div>
                {content}
              </motion.div>
            )}
          </AnimatePresence>,
          document.body
        )}
    </>
  )
}
