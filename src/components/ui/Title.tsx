import React from "react";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";
import capitalize from "@/utils/capitalize";

const Title = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <h3 className={cn("text-secondary font-extrabold text-xl", className)}>
      {typeof children === "string" ? capitalize(children) : children}
    </h3>
  );
};

export default Title;
