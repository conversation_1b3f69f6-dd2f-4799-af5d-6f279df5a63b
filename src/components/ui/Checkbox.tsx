import React from "react";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";

/* ================================ RADIX UI ================================ */
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { Check } from "lucide-react";

/* ============================== FRAMER MOTION ============================= */
import { motion, AnimatePresence } from "framer-motion";

/* ================================== TYPES ================================= */
type TCheckboxProps = {
  checked: boolean;
  onChange?: (checked: boolean) => void;
  className?: string;
  id?: string;
  isDisabled?: boolean;
};

/* ========================================================================== */
/**
 * `Checkbox` – A custom-styled, accessible checkbox component built on top of Radix UI.
 *
 * Features:
 * - Controlled component via `checked` prop
 * - Supports optional `onChange` callback to handle toggle logic
 * - Styled with custom gradient checkmark using Lucide + SVG
 * - Safely handles `indeterminate` state from Radix API
 * - Accepts custom `className` and `id` for flexibility
 *
 * Props:
 * @param {boolean} checked – Controls whether the checkbox is checked.
 * @param {(checked: boolean) => void} [onChange] – Callback fired when checkbox changes. Receives the new boolean state.
 * @param {string} [className] – Optional Tailwind or custom class names for styling override.
 * @param {string} [id] – Optional `id` to set on the root checkbox element.
 *
 * Notes:
 * - If `onChange` is not provided, a console warning will be logged on state change.
 * - Radix may emit `"indeterminate"` as a value, so it’s filtered before calling `onChange`.
 *
 * Example usage:
 * ```tsx
 * const [isChecked, setIsChecked] = useState(false);
 * <Checkbox checked={isChecked} onChange={setIsChecked} />
 * ```
 */

const Checkbox = ({
  checked,
  onChange,
  className,
  id,
  isDisabled = false,
}: TCheckboxProps) => {
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <CheckboxPrimitive.Root
      disabled={isDisabled}
      checked={checked}
      onCheckedChange={(checked) =>
        typeof checked === "boolean"
          ? onChange?.(checked)
          : console.warn(
              "%c[Checkbox]",
              "color: orange; font-weight: bold",
              "Missing onChange handler"
            )
      }
      className={cn(
        "h-4 w-4 bg-transparent border-2 border-[#34405466] text-primary flex items-center justify-center transition-colors duration-300 rounded-xs",
        "data-[state=checked]:bg-[#914AC41A] data-[state=checked]:border-0",
        className
      )}
      id={id ?? "checkbox-fancy"}
    >
      <AnimatePresence>
        {checked && (
          <motion.div
            key="checkmark"
            initial={{ scale: 0, opacity: 0, rotate: -90 }}
            animate={{ scale: 1, opacity: 1, rotate: 0 }}
            exit={{ scale: 0.7, opacity: 0, rotate: 90 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <svg width="13" height="13" viewBox="0 0 24 24" fill="none">
              <defs>
                <linearGradient id="checkGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#B17CD8" />
                  <stop offset="100%" stopColor="#914AC4" />
                </linearGradient>
              </defs>
              <Check
                className="h-4 w-4"
                stroke="url(#checkGradient)"
                strokeWidth={4}
                strokeLinecap="square"
              />
            </svg>
          </motion.div>
        )}
      </AnimatePresence>
    </CheckboxPrimitive.Root>
  );
};

export default Checkbox;
