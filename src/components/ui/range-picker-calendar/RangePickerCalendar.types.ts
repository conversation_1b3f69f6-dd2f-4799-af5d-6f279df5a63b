import type { DateRange } from "react-day-picker";

type DropdownButton = React.ReactNode;
type DropdownOptions = React.ReactNode[];

type Dropdown = {
  button: DropdownButton;
  options: DropdownOptions;
};

export type RangePickerCalendarClassNames = {
  title?: string;
  dropdownButton?: string;
  dropdownOption?: string;
  header?: string;
};
export type RangePickerCalendarProps = {
  title?: string;
  monthDropdown?: Dropdown;
  yearDropdown?: Dropdown;
  className?: string;
  classNames?: RangePickerCalendarClassNames;
  month: Date;
  setMonth: React.Dispatch<React.SetStateAction<Date>>;
  selected: DateRange | undefined;
  setSelected: React.Dispatch<React.SetStateAction<DateRange | undefined>>;
  disabled?: boolean;
  selectedColor?: string;
};
