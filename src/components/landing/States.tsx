export default function States({ className }: { className?: string }) {
  const states = [
    {
      description: "Run as many audits as you need, no limits, no delays.",
      value: "Unlimited SEO Audits",
    },
    {
      description: "Pro-branded reports with your own identity, perfect for agencies and freelancers.",
      value: "White Label Reports",
    },
    {
      description: "Deeper analysis of backlink and domain status to improve authority and rankings.",
      value: "Advanced Backlink & Insights",
    },
    {
      description: "From solo site owners to growing agencies, our tools make SEO simple, scalable, and effective for all.",
      value: "Flexible for Everyone",
    },
  ];
  return (
    <div className={`w-full container mt-[115px] lg:mt-[154px] ${className}`}>
      <div className="bg-white rounded-2xl p-6 hidden lg:flex justify-between">
        {states.map((state, index) => (
          <StateCard
            key={index}
            description={state.description}
            value={state.value}
          />
        ))}
      </div>
      <div className="lg:hidden bg-white rounded-2xl p-6">
        <div className="flex items-start justify-between [&>*:nth-child(1)]:border-r-0 border-b border-light-gray mb-4 pb-4">
          {states.slice(0, 2).map((state, index) => (
            <StateCard
              key={index}
              description={state.description}
              value={state.value}
            />
          ))}
        </div>
        <div className="flex items-center justify-between [&>*:nth-child(1)]:border-r-0">
          {states.slice(2, 4).map((state, index) => (
            <StateCard
              key={index}
              description={state.description}
              value={state.value}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

type StateCardProps = {
  description: string;
  value: string;
};

function StateCard({ description, value }: StateCardProps) {
  return (
    <div className="w-full group flex flex-col justify-center items-center gap-2 px-4 lg:px-6 border-x border-light-gray first:border-l-0 last:border-r-0">
      <h2 className="max-[1024px]:group-first:-ml-3 text-xl   font-semibold text-secondary text-center">
        + {value}
      </h2>
      <p className="text-xs min-[430px]:text-sm lg:text-base text-secondary/50 text-center">
        {description}
      </p>
    </div>
  );
}
