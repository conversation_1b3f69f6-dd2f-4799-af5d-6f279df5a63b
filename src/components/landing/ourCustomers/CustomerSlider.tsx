import React from "react";

type Props = {
  icon: React.ReactNode;
  label: string;
  description: string;
};

export default function CustomerSlider({ icon, label, description }: Props) {
  return (
    <div className="min-w-[322px] flex items-center gap-4 bg-white p-6 rounded-lg shadow-[0_0_20px_0_rgba(29,34,65,0.1)]">
      <div>
        <div className="w-11 h-11 bg-secondary/10 rounded-lg flex items-center justify-center text-secondary">
          {icon}
        </div>
      </div>
      <div className="flex flex-col gap-[3px]">
        <div className="text-secondary font-semibold">{label}</div>
        <div className="text-secondary text-sm  line-clamp-2">
          {description}
        </div>
      </div>
    </div>
  );
}
