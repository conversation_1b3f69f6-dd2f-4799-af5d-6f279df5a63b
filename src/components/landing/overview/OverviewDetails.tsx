import Link from "next/link";
import React from "react";

type Props = {
  title: string;
  description: React.ReactNode;
  icon: React.ReactNode;
  btnTxt?: string;
  className?: string;
};

export default function OverviewDetails({
  title,
  description,
  icon,
  btnTxt,
  className,
}: Props) {
  return (
    <div className={`bg-light-blue p-8 lg:p-16 rounded-2xl ${className}`}>
      <div>{icon}</div>
      <h4 className="mt-6 mb-4 text-secondary font-black text-xl">{title}</h4>
      <div className="mb-6 font-medium text-secondary whitespace-pre-line text-sm lg:text-base">{description}</div>
      <button className="btn btn--primary !w-full lg:!w-auto"><Link href="#analyze">{btnTxt}</Link></button>
    </div>
  );
}
