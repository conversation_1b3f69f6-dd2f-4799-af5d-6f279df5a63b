import { CalendarIcon } from "@/ui/icons/dateTime";
import { StarBoldIcon, StarOutlineIcon } from "@/ui/icons/general";
import Image from "next/image";

export type CommentCardProps = {
  rate: number;
  text: string;
  userName: string;
  userAvatar?: string;
  createdAt: Date;
};

export default function CommentCard({
  createdAt = new Date(),
  rate,
  text,
  userAvatar = "/images/user-avatar.webp",
  userName,
}: CommentCardProps) {  
  return (
    <div className="bg-white rounded-lg p-6 w-full">
      <div className="flex items-center gap-2">
        {Array(5)
          .fill({})
          .map((star, index) =>
            rate >= index + 1 ? (
              <StarBoldIcon
                key={index}
                className="w-6 h-6 text-primary-yellow"
              />
            ) : (
              <StarOutlineIcon key={index} className="w-6 h-6 text-secondary" />
            )
          )}
      </div>
      <div className="py-4">
        <p className="text-xs leading-5 text-secondary font-light">{text}</p>
      </div>
      <div className="flex items-center gap-2">
        <div>
          <div className="w-[51px] h-[51px] rounded-full overflow-hidden relative">
            <Image
              src={userAvatar}
              alt={userName}
              width={1000}
              height={1000}
              className="w-full h-full object-cover object-center"
            />
          </div>
        </div>
        <div>
          <div className="text-secondary font-medium mb-2">{userName}</div>
          <div className="text-[10px] text-secondary flex items-center gap-1 opacity-60">
            <CalendarIcon />
            <span>
              {new Date(createdAt).toLocaleDateString("en", {
                month: "short",
                day: "numeric",
              }).split(" ").reverse().join(" ")}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
