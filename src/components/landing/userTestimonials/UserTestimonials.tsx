"use client";
import Masonry from "react-masonry-css";
import Title from "../Title";
import CommentCard, { CommentCardProps } from "./CommentCard";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import { DownIcon } from "@/ui/icons/navigation";

export default function UserTestimonials() {
  // const fakeData: CommentCardProps[] = [
  //   {
  //     createdAt: new Date(),
  //     rate: 4,
  //     text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra accumsan in nisl nisi Arcu cursus vitae congue mauris rhoncus aenean vel elit scelerisque In egestas erat imperdiet sed euismod nisi porta lorem mollis Morbi tristique senectus et netus",
  //     userName: "Arthor Morgan",
  //   },
  //   {
  //     createdAt: new Date(),
  //     rate: 4,
  //     text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua ",
  //     userName: "Arthor Morgan",
  //   },
  //   {
  //     createdAt: new Date(),
  //     rate: 4,
  //     text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra accumsan in nisl nisi Arcu cursus vitae ",
  //     userName: "Arthor Morgan",
  //   },
  //   {
  //     createdAt: new Date(),
  //     rate: 4,
  //     text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra accumsan in nisl nisi",
  //     userName: "Arthor Morgan",
  //   },
  //   {
  //     createdAt: new Date(),
  //     rate: 4,
  //     text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra accumsan in nisl nisi Arcu cursus vitae congue mauris rhoncus aenean vel elit scelerisque In egestas erat imperdiet sed euismod nisi porta lorem mollis Morbi tristique ",
  //     userName: "Arthor Morgan",
  //   },
  //   {
  //     createdAt: new Date(),
  //     rate: 4,
  //     text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra accumsan in nisl nisi Arcu cursus vitae ",
  //     userName: "Arthor Morgan",
  //   },
  //   {
  //     createdAt: new Date(),
  //     rate: 4,
  //     text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua senectus et netus",
  //     userName: "Arthor Morgan",
  //   },
  //   {
  //     createdAt: new Date(),
  //     rate: 4,
  //     text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra accumsan in nisl nisi Arcu cursus vitae congue mauris rhoncus aenean vel elit scelerisque In egestas erat imperdiet sed euismod nisi porta lorem mollis Morbi tristique senectus et netus",
  //     userName: "Arthor Morgan",
  //   },
  //   {
  //     createdAt: new Date(),
  //     rate: 4,
  //     text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas purus viverra accumsan in nisl nisi Arcu cursus vitae",
  //     userName: "Arthor Morgan",
  //   },
  // ];

  const fakeData: CommentCardProps[] = [
    {
      createdAt: new Date(),
      rate: 4,
      text: "Our website’s performance skyrocketed after working with this team. The SEO strategies they implemented not only improved our rankings but also increased our organic traffic significantly. Highly recommended!",
      userName: "David Johnson",
    },
    {
      createdAt: new Date(),
      rate: 4,
      text: "I was struggling to get my website noticed on search engines, but after their expert optimizations, we saw a massive boost in rankings and engagement. Their transparency and professionalism are unmatched!",
      userName: "Emma Williams",
    },
    {
      createdAt: new Date(),
      rate: 4,
      text: "This team truly understands the ins and outs of SEO. They developed a custom strategy for our business, and within months, we saw a major improvement in lead generation and brand visibility!",
      userName: "Liam Brown",
    },
    {
      createdAt: new Date(),
      rate: 4,
      text: "Exceptional service! The team provided detailed reports, continuous support, and most importantly, real results. If you're looking for an SEO agency that delivers, this is the one!",
      userName: "Sophia Martinez",
    },
    {
      createdAt: new Date(),
      rate: 4,
      text: "Their SEO tactics helped us climb to the first page of Google for multiple keywords. The increase in organic traffic translated directly into higher sales. Couldn't be happier!",
      userName: "Oliver Scott",
    },
    {
      createdAt: new Date(),
      rate: 4,
      text: "Before working with them, we barely had any traffic from search engines. Now, our site is attracting thousands of visitors monthly, and our business is thriving like never before!",
      userName: "Isabella Clark",
    },
    {
      createdAt: new Date(),
      rate: 4,
      text: "What sets this team apart is their ability to explain complex SEO strategies in a way that's easy to understand. Their approach is data-driven, and they always keep us informed about our progress.",
      userName: "Ethan Walker",
    },
    {
      createdAt: new Date(),
      rate: 4,
      text: "Within just a few months, our website went from barely ranking to being in the top 5 for our main keywords. Their SEO strategies are effective and sustainable!",
      userName: "Ava Lewis",
    },
    {
      createdAt: new Date(),
      rate: 4,
      text: "I've worked with multiple SEO agencies in the past, but none have delivered the results that this team has. They take the time to understand your business and implement tailored solutions that work.",
      userName: "Mason Hall",
    },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "Our online visibility has never been better. Their approach to SEO is methodical, and the improvements we've seen in both traffic and conversions are remarkable!",
    //   userName: "Charlotte Adams",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "SEO can be overwhelming, but this team made it simple and effective for us. Their expertise and commitment to delivering results have truly made a difference in our business.",
    //   userName: "Benjamin Carter",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "I was skeptical about SEO agencies, but this team completely changed my perspective. They focus on long-term results and provide honest, transparent updates every step of the way.",
    //   userName: "Harper White",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "Our website's performance improved significantly thanks to their in-depth keyword research, Technology SEO fixes, and content strategies. I highly recommend their services!",
    //   userName: "Lucas Nelson",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "If you're looking for an SEO agency that actually cares about your success, look no further. Their personalized approach and expertise have helped us achieve impressive growth.",
    //   userName: "Amelia Young",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "Their team has an excellent understanding of Google's ever-changing algorithms. They adapted our strategy accordingly, ensuring that our rankings kept improving month after month!",
    //   userName: "Henry Parker",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "We’ve seen an incredible return on investment since working with them. Their focus on high-quality SEO and ethical practices has helped our business grow exponentially.",
    //   userName: "Ella Harris",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "In just six months, we went from struggling to attract visitors to becoming a top-ranked website in our niche. Their SEO expertise is the real deal!",
    //   userName: "Jack Ramirez",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "One of the best decisions we made for our business was investing in their SEO services. The impact on our rankings and lead generation has been outstanding!",
    //   userName: "Lily Foster",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "They don’t just focus on rankings—they help you build an SEO strategy that aligns with your business goals. The results speak for themselves!",
    //   userName: "Daniel Green",
    // },
    // {
    //   createdAt: new Date(),
    //   rate: 4,
    //   text: "I appreciate their hands-on approach and the level of detail in their reports. They always keep us updated on the progress, and their results-driven mindset has made a huge difference for us!",
    //   userName: "Grace Turner",
    // },
  ];

  const breakpointColumns = {
    default: 3,
  };

  return (
    <div className="w-full container max-w-full__customeLG  my-8 lg:my-[84px] space-y-4">
      <Title title="User Testimonisls" />
      <Masonry
        breakpointCols={breakpointColumns}
        className="hidden lg:flex gap-6 lg:mx-36"
        columnClassName="bg-clip-padding space-y-6"
      >
        {fakeData.map((item, index) => (
          <div key={index} className="break`insideavoid">
            <CommentCard
              createdAt={item.createdAt}
              rate={item.rate}
              text={item.text}
              userName={item.userName}
            />
          </div>
        ))}
      </Masonry>

      <div className="w-full lg:hidden">
        <Swiper
          className=""
          navigation={{
            nextEl: `.comments-arrow-left`,
            prevEl: `.comments-arrow-right`,
          }}
          modules={[Navigation]}
        >
          {fakeData.map((item, index) => (
            <SwiperSlide key={index}>
              <CommentCard
                createdAt={item.createdAt}
                rate={item.rate}
                text={item.text}
                userName={item.userName}
              />
            </SwiperSlide>
          ))}

          <div className="flex items-center justify-center gap-4 mt-2">
            <button
              className={`comments-arrow-left btn btn--outline !py-1 !px-2.5 disabled:!border-transparent disabled:bg-secondary/20`}
            >
              <DownIcon className={"rotate-90 w-6 h-6"} />
            </button>
            <button
              className={`comments-arrow-right btn btn--outline !py-1 !px-2.5 disabled:!border-transparent disabled:bg-secondary/20`}
            >
              <DownIcon className={"-rotate-90 w-6 h-6"} />
            </button>
          </div>
        </Swiper>
      </div>
    </div>
  );
}
