"use client";



type RadioGroupPlansProps = {
  selectedPlan: string;
  onPlanChange: (value: string) => void;
};

export default function RadioGroupPlans({ selectedPlan, onPlanChange }: RadioGroupPlansProps) {
  const handleChange = (value: string) => {
    onPlanChange(value);
  };

  return (
    <div className="w-full flex justify-center mt-6">
      <div className="bg-primary/10 py-2 px-4 rounded-lg flex items-center gap-6">
        <RadioItem
          name="timeFrame"
          value="Month"
          selectedPlan={selectedPlan}
          onChange={handleChange}
          label="Monthly"
        />
        <RadioItem
          name="timeFrame"
          value="Year"
          selectedPlan={selectedPlan}
          onChange={handleChange}
          label="Annual"
        />
      </div>
    </div>
  );
}

type RadioItemProps = {
  name: string;
  value: string;
  onChange: (value: string) => void;
  label: string;
  selectedPlan: string;
};

function RadioItem({
  name,
  value,
  onChange,
  label,
  selectedPlan,
}: RadioItemProps) {
  return (
    <label htmlFor={value} className="cursor-pointer">
      <input
        type="radio"
        name={name}
        id={value}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        checked={selectedPlan === value}
        hidden={true}
      />
      <div className="flex items-center gap-2">
        <div className="w-4.5 h-4.5 rounded-full border border-primary flex items-center justify-center">
          {selectedPlan === value && (
            <div className="w-2.5 h-2.5 rounded-full bg-primary"></div>
          )}
        </div>
        <div className="font-bold text-secondary text-sm lg:text-base">{label}</div>
      </div>
    </label>
  );
}
