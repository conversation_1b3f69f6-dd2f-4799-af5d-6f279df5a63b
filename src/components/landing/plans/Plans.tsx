"use client";

import { useState, useEffect } from "react";
import { KeyIcon } from "@/ui/icons/general";
import RadioGroupPlans from "./RadioGroupPlans";
import PlanCard from "./PlanCard";
import pricingService, {
  PricingData as PricingDataType,
} from "@/services/pricingService";

type PlansProps = {
  pricingData?: PricingDataType;
};

export default function Plans({ pricingData: propsPricingData }: PlansProps) {
  const [selectedPlan, setSelectedPlan] = useState<string>("Month");
  const [pricingData, setPricingData] = useState<PricingDataType | undefined>(
    propsPricingData
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchPricingData = async () => {
      if (!pricingData) {
        setIsLoading(true);
        try {
          const data = await pricingService.getPricingData();
          setPricingData(data);
        } catch (error) {
          console.error("Failed to fetch pricing data:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchPricingData();
  }, [pricingData]);

  const handlePlanChange = (value: string) => {
    setSelectedPlan(value);
  };

  // Default descriptions for each plan
  const defaultDescriptions = {
    "DIY SEO":
      "Designed for website owners, solopreneurs, and marketers who want to boost visibility without hiring an agency. With DIY SEO, you get powerful tools to find what's holding you back—and fix it fast.",
    "Pro Plan":
      "Offer top-tier SEO reports under your own branding. Whether you're a freelancer or growing agency, Pro Plan helps you scale client services without building a team.",
    "Pro Plan & Embedding":
      "Built for agencies, SaaS products, and platforms ready to offer seamless, scalable SEO services under their own brand. This plan gives you full control over integration, customization, and delivery—powered by automation and AI.",
  };

  // Function to get the price for a specific plan and period
  const getPlanPrice = (planName: string, period: string) => {
    if (!pricingData || !pricingData[planName]) {
      // Fallback to default prices if API data is not available
      switch (planName) {
        case "DIY SEO":
          return period === "monthly" ? 29 : 278;
        case "Pro Plan":
          return period === "monthly" ? 28 : 288;
        case "Pro Plan & Embedding":
          return period === "monthly" ? 79 : 758;
        default:
          return 0;
      }
    }

    // Find the plan data for the specified interval (month/year)
    const intervalType = period === "monthly" ? "month" : "year";
    const planData = pricingData[planName].find(
      (plan) => plan.interval === intervalType
    );

    if (!planData) {
      console.warn(
        `No pricing data found for ${planName} with interval ${intervalType}`
      );
      return 0;
    }

    // Use the price directly as it's now a number in the API response
    return planData.price;
  };

  // Function to get the plan ID for a specific plan and period
  const getPlanId = (planName: string, period: string) => {
    if (!pricingData || !pricingData[planName]) {
      return ""; // Default empty string ID if no data available
    }

    // Find the plan data for the specified interval (month/year)
    const intervalType = period === "monthly" ? "month" : "year";
    const planData = pricingData[planName].find(
      (plan) => plan.interval === intervalType
    );

    if (!planData) {
      console.warn(
        `No pricing data found for ${planName} with interval ${intervalType}`
      );
      return "";
    }

    return planData.id;
  };

  // Function to get the description for a specific plan
  const getPlanDescription = (planName: string) => {
    if (!pricingData || !pricingData[planName]) {
      return (
        defaultDescriptions[planName as keyof typeof defaultDescriptions] || ""
      );
    }

    // Use the description from the first item in the array (both monthly and yearly should have the same description)
    return (
      pricingData[planName][0]?.description ||
      defaultDescriptions[planName as keyof typeof defaultDescriptions] ||
      ""
    );
  };

  // Function to get the currency for a specific plan and period
  const getPlanCurrency = (planName: string, period: string) => {
    if (!pricingData || !pricingData[planName]) {
      return "aud"; // Default fallback
    }

    // Find the plan data for the specified interval (month/year)
    const intervalType = period === "monthly" ? "month" : "year";
    const planData = pricingData[planName].find(
      (plan) => plan.interval === intervalType
    );

    return planData ? planData.currency : "aud";
  };

  return (
    <div className="w-full container mt-8 lg:mt-[84px]">
      <h4 className="w-full mx-auto text-2xl lg:text-[32px] font-black text-secondary text-center">
        Professional SEO Tools, Without the Complexity{" "}
        <span className="text-primary"> Today!</span>
      </h4>
      <p className="text-secondary text-sm lg:text-base text-center leading-[22px] mt-4">
        Whether you're a solo founder or a growing agency, choose the plan that
        helps you rank faster, deliver client-ready insights, and turn traffic
        into results.
      </p>

      <RadioGroupPlans
        selectedPlan={selectedPlan}
        onPlanChange={handlePlanChange}
      />

      {isLoading ? (
        <div className="w-full flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : pricingData && pricingData["Pro Plan"] ? (
        <div className="w-full flex justify-center mt-6">
          <div className="max-w-md">
            <PlanCard
              variant="blue"
              price={getPlanPrice(
                "Pro Plan",
                selectedPlan === "Month" ? "monthly" : "yearly"
              )}
              label="Pro Plan"
              iconForBox={<KeyIcon />}
              timeFrame={selectedPlan === "Month" ? "Month" : "Year"}
              selectedBillingPlan={selectedPlan}
              labelForBox="Perfect for: consultants, agencies, white-label resellers"
              descForBox="Deliver the SEO Reports with Your Brand"
              description={getPlanDescription("Pro Plan")}
              planId={getPlanId(
                "Pro Plan",
                selectedPlan === "Month" ? "monthly" : "yearly"
              )}
              currency={getPlanCurrency(
                "Pro Plan",
                selectedPlan === "Month" ? "monthly" : "yearly"
              )}
              listItems={
                <>
                  <li>Includes all features from the Free version</li>
                  <li>Unlimited analyses</li>
                  <li>Generate beautiful, branded PDF audit reports</li>
                  <li>
                    Add  your logo, and contact info to impress
                    clients
                  </li>
                  <li>Local SEO audits tailored for service-area businesses</li>
                  <li>Advanced Backlink and Domain status</li>
                </>
              }
            />
          </div>
        </div>
      ) : (
        <div className="w-full flex justify-center items-center py-12">
          <div className="text-secondary">Loading pricing data...</div>
        </div>
      )}
    </div>
  );
}
