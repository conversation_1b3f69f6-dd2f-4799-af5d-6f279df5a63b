import Link from "next/link";
import ShapesFreeToolMobile from "./ShapesFreeToolMobile";

type Props = {
  title?: string;
  description?: string;
  button?: string;
  className?: string;
  shapeNum?: number;
  link_address?: string;
};

const defaultDescription =
  "At SEOAnalyser, we’re not just simplifying SEO, we’re transforming how Australian businesses and agencies deliver results.<br>From unlimited website audits and visual SEO reports to white-label exports for agencies and freelancers, our platform gives you the tools to scale, impress clients, and make smarter decisions.";

export default function FreeSEOToolbox({
  button = "Let’s Start Now",
  link_address = "/pricing",
  description = defaultDescription,
  title = "Free SEO Tools",
  className,
  shapeNum = 0,
}: Props) {
  return (
    <div className="w-full">
      <div
        className={`w-full min-h-[320px] sm:min-h-[302px] flex items-center justify-center bg-primary rounded-2xl relative overflow-hidden ${className}`}
      >
        {shapeNum === 0 ? (
          <>
            <div className="hidden lg:block">
              <Shapes />
            </div>
            <div className="lg:hidden">
              <ShapesFreeToolMobile />
            </div>
          </>
        ) : (
          <Shapes2 />
        )}
        <div className="relative z-10 flex flex-col items-center px-4 sm:px-6 py-6 sm:py-10 max-w-[657px] w-full mx-auto">
          <h3 className="text-white !text-xl !sm:text-2xl  font-black text-center">
            {title}
          </h3>
          <div className="w-full mt-3 sm:mt-4 mb-4 sm:mb-5 lg:mb-6">
            <div
              className="text-sm lg:text-base text-white text-center break-words hyphens-auto overflow-y-auto
              max-h-[180px] sm:max-h-none scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent
              px-2 py-1 sm:px-3"
              dangerouslySetInnerHTML={{ __html: description }}
            />
          </div>
          <Link
            href={link_address}
            className="!w-full sm:!w-auto btn btn--primary !bg-white !text-primary !border-white"
          >
            {button}
          </Link>
        </div>
      </div>
    </div>
  );
}

function Shapes() {
  return (
    <svg
      width="1240"
      height="303"
      viewBox="0 0 1240 303"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-full h-full absolute top-0 right-0"
    >
      <g filter="url(#filter0_d_0_1)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M787.778 -9.40342L716.875 32.1761L791.594 159.588L791.592 159.589C793.317 162.531 798.707 163.161 806.575 161.342C814.443 159.523 824.145 155.404 833.548 149.89C842.95 144.376 851.282 137.92 856.711 131.941C862.139 125.963 864.22 120.952 862.495 118.01L862.497 118.009L787.778 -9.40342Z"
          fill="#914AC4"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M787.778 -9.40342L716.875 32.1761L791.594 159.588L791.592 159.589C793.317 162.531 798.707 163.161 806.575 161.342C814.443 159.523 824.145 155.404 833.548 149.89C842.95 144.376 851.282 137.92 856.711 131.941C862.139 125.963 864.22 120.952 862.495 118.01L862.497 118.009L787.778 -9.40342Z"
          fill="url(#paint0_linear_0_1)"
          fillOpacity="0.4"
        />
      </g>
      <ellipse
        cx="752.326"
        cy="11.3864"
        rx="41.0976"
        ry="12.8571"
        transform="rotate(-30.3887 752.326 11.3864)"
        fill="#914AC4"
      />
      <ellipse
        cx="752.326"
        cy="11.3864"
        rx="41.0976"
        ry="12.8571"
        transform="rotate(-30.3887 752.326 11.3864)"
        fill="url(#paint1_radial_0_1)"
        fillOpacity="0.5"
      />
      <g filter="url(#filter1_d_0_1)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M435.492 170.71L389.219 145.418L343.769 228.57L343.769 228.569C342.719 230.489 344.151 233.663 347.748 237.391C351.345 241.12 356.813 245.1 362.949 248.454C369.085 251.807 375.387 254.261 380.467 255.276C385.548 256.29 388.992 255.781 390.041 253.862L390.042 253.862L435.492 170.71Z"
          fill="#914AC4"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M435.492 170.71L389.219 145.418L343.769 228.57L343.769 228.569C342.719 230.489 344.151 233.663 347.748 237.391C351.345 241.12 356.813 245.1 362.949 248.454C369.085 251.807 375.387 254.261 380.467 255.276C385.548 256.29 388.992 255.781 390.041 253.862L390.042 253.862L435.492 170.71Z"
          fill="url(#paint2_linear_0_1)"
          fillOpacity="0.4"
        />
      </g>
      <ellipse
        cx="412.356"
        cy="158.064"
        rx="26.3668"
        ry="8.24869"
        transform="rotate(28.6604 412.356 158.064)"
        fill="#914AC4"
      />
      <ellipse
        cx="412.356"
        cy="158.064"
        rx="26.3668"
        ry="8.24869"
        transform="rotate(28.6604 412.356 158.064)"
        fill="url(#paint3_radial_0_1)"
        fillOpacity="0.5"
      />
      <g filter="url(#filter2_d_0_1)">
        <circle
          cx="90.0368"
          cy="1.40373"
          r="117.905"
          transform="rotate(-50.2767 90.0368 1.40373)"
          fill="#914AC4"
        />
        <circle
          cx="90.0368"
          cy="1.40373"
          r="117.905"
          transform="rotate(-50.2767 90.0368 1.40373)"
          fill="url(#paint4_linear_0_1)"
          fillOpacity="0.5"
        />
        <circle
          cx="90.0368"
          cy="1.40373"
          r="117.905"
          transform="rotate(-50.2767 90.0368 1.40373)"
          fill="url(#paint5_radial_0_1)"
          fillOpacity="0.5"
        />
      </g>
      <g filter="url(#filter3_d_0_1)">
        <circle
          cx="1213.19"
          cy="225.744"
          r="60.257"
          transform="rotate(-50.2767 1213.19 225.744)"
          fill="#914AC4"
        />
        <circle
          cx="1213.19"
          cy="225.744"
          r="60.257"
          transform="rotate(-50.2767 1213.19 225.744)"
          fill="url(#paint6_linear_0_1)"
          fillOpacity="0.5"
        />
        <circle
          cx="1213.19"
          cy="225.744"
          r="60.257"
          transform="rotate(-50.2767 1213.19 225.744)"
          fill="url(#paint7_radial_0_1)"
          fillOpacity="0.5"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_0_1"
          x="670.734"
          y="-9.40332"
          width="330.777"
          height="356.331"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="46.1413" dy="92.2827" />
          <feGaussianBlur stdDeviation="46.1413" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_0_1"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_0_1"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_0_1"
          x="313.83"
          y="145.418"
          width="210.47"
          height="228.779"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="29.6027" dy="59.2054" />
          <feGaussianBlur stdDeviation="29.6027" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_0_1"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_0_1"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_d_0_1"
          x="-55.1015"
          y="-116.505"
          width="344.736"
          height="344.736"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="27.2299" dy="54.4598" />
          <feGaussianBlur stdDeviation="27.2299" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_0_1"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_0_1"
            result="shape"
          />
        </filter>
        <filter
          id="filter3_d_0_1"
          x="1139.02"
          y="165.485"
          width="176.182"
          height="176.181"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="13.9162" dy="27.8323" />
          <feGaussianBlur stdDeviation="13.9162" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_0_1"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_0_1"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_0_1"
          x1="757.485"
          y1="101.428"
          x2="839.275"
          y2="78.411"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0.75" />
          <stop offset="1" stopColor="#373737" stopOpacity="0" />
        </linearGradient>
        <radialGradient
          id="paint1_radial_0_1"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(723.113 8.76572) rotate(97.125) scale(53.0444 169.556)"
        >
          <stop stopColor="white" stopOpacity="0.49" />
          <stop offset="1" stopOpacity="0" />
        </radialGradient>
        <linearGradient
          id="paint2_linear_0_1"
          x1="364.515"
          y1="190.613"
          x2="404.167"
          y2="228.019"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0.75" />
          <stop offset="1" stopColor="#373737" stopOpacity="0" />
        </linearGradient>
        <radialGradient
          id="paint3_radial_0_1"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(393.613 156.383) rotate(97.125) scale(34.0315 108.781)"
        >
          <stop stopColor="white" stopOpacity="0.49" />
          <stop offset="1" stopOpacity="0" />
        </radialGradient>
        <linearGradient
          id="paint4_linear_0_1"
          x1="6.25458"
          y1="-81.5462"
          x2="180.2"
          y2="79.6374"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0.47" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </linearGradient>
        <radialGradient
          id="paint5_radial_0_1"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(60.9072 -57.1328) rotate(53.9993) scale(131.681)"
        >
          <stop stopColor="white" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </radialGradient>
        <linearGradient
          id="paint6_linear_0_1"
          x1="1170.38"
          y1="183.351"
          x2="1259.27"
          y2="265.726"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0.47" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </linearGradient>
        <radialGradient
          id="paint7_radial_0_1"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(1198.31 195.828) rotate(53.9993) scale(67.297)"
        >
          <stop stopColor="white" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </radialGradient>
      </defs>
    </svg>
  );
}
function Shapes2() {
  return (
    <svg
      width="874"
      height="278"
      viewBox="0 0 874 278"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-full h-full absolute top-0 right-0"
    >
      <g filter="url(#filter0_d_0_1)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M604.778 -9.9552L533.875 31.6243L608.594 159.037L608.593 159.037C610.318 161.979 615.707 162.609 623.575 160.79C631.443 158.972 641.146 154.852 650.548 149.338C659.95 143.825 668.282 137.368 673.711 131.389C679.139 125.411 681.22 120.4 679.495 117.458L679.497 117.458L604.778 -9.9552Z"
          fill="#914AC4"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M604.778 -9.9552L533.875 31.6243L608.594 159.037L608.593 159.037C610.318 161.979 615.707 162.609 623.575 160.79C631.443 158.972 641.146 154.852 650.548 149.338C659.95 143.825 668.282 137.368 673.711 131.389C679.139 125.411 681.22 120.4 679.495 117.458L679.497 117.458L604.778 -9.9552Z"
          fill="url(#paint0_linear_0_1)"
          fillOpacity="0.4"
        />
      </g>
      <ellipse
        cx="569.326"
        cy="10.8342"
        rx="41.0976"
        ry="12.8571"
        transform="rotate(-30.3887 569.326 10.8342)"
        fill="#914AC4"
      />
      <ellipse
        cx="569.326"
        cy="10.8342"
        rx="41.0976"
        ry="12.8571"
        transform="rotate(-30.3887 569.326 10.8342)"
        fill="url(#paint1_radial_0_1)"
        fillOpacity="0.5"
      />
      <g filter="url(#filter1_d_0_1)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M252.492 145.16L206.219 119.868L160.77 203.02L160.769 203.019C159.72 204.939 161.151 208.112 164.748 211.841C168.345 215.57 173.813 219.549 179.949 222.903C186.085 226.257 192.387 228.711 197.467 229.725C202.548 230.739 205.992 230.231 207.041 228.311L207.042 228.312L252.492 145.16Z"
          fill="#914AC4"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M252.492 145.16L206.219 119.868L160.77 203.02L160.769 203.019C159.72 204.939 161.151 208.112 164.748 211.841C168.345 215.57 173.813 219.549 179.949 222.903C186.085 226.257 192.387 228.711 197.467 229.725C202.548 230.739 205.992 230.231 207.041 228.311L207.042 228.312L252.492 145.16Z"
          fill="url(#paint2_linear_0_1)"
          fillOpacity="0.4"
        />
      </g>
      <ellipse
        cx="229.356"
        cy="132.514"
        rx="26.3668"
        ry="8.24869"
        transform="rotate(28.6604 229.356 132.514)"
        fill="#914AC4"
      />
      <ellipse
        cx="229.356"
        cy="132.514"
        rx="26.3668"
        ry="8.24869"
        transform="rotate(28.6604 229.356 132.514)"
        fill="url(#paint3_radial_0_1)"
        fillOpacity="0.5"
      />
      <g filter="url(#filter2_d_0_1)">
        <circle
          cx="90.0368"
          cy="-24.147"
          r="117.905"
          transform="rotate(-50.2767 90.0368 -24.147)"
          fill="#914AC4"
        />
        <circle
          cx="90.0368"
          cy="-24.147"
          r="117.905"
          transform="rotate(-50.2767 90.0368 -24.147)"
          fill="url(#paint4_linear_0_1)"
          fillOpacity="0.5"
        />
        <circle
          cx="90.0368"
          cy="-24.147"
          r="117.905"
          transform="rotate(-50.2767 90.0368 -24.147)"
          fill="url(#paint5_radial_0_1)"
          fillOpacity="0.5"
        />
      </g>
      <g filter="url(#filter3_d_0_1)">
        <circle
          cx="847.195"
          cy="200.192"
          r="60.257"
          transform="rotate(-50.2767 847.195 200.192)"
          fill="#914AC4"
        />
        <circle
          cx="847.195"
          cy="200.192"
          r="60.257"
          transform="rotate(-50.2767 847.195 200.192)"
          fill="url(#paint6_linear_0_1)"
          fillOpacity="0.5"
        />
        <circle
          cx="847.195"
          cy="200.192"
          r="60.257"
          transform="rotate(-50.2767 847.195 200.192)"
          fill="url(#paint7_radial_0_1)"
          fillOpacity="0.5"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_0_1"
          x="487.734"
          y="-9.95508"
          width="330.777"
          height="356.331"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="46.1413" dy="92.2827" />
          <feGaussianBlur stdDeviation="46.1413" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_0_1"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_0_1"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_0_1"
          x="130.83"
          y="119.867"
          width="210.47"
          height="228.78"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="29.6027" dy="59.2054" />
          <feGaussianBlur stdDeviation="29.6027" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_0_1"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_0_1"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_d_0_1"
          x="-55.1016"
          y="-142.056"
          width="344.736"
          height="344.737"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="27.2299" dy="54.4598" />
          <feGaussianBlur stdDeviation="27.2299" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_0_1"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_0_1"
            result="shape"
          />
        </filter>
        <filter
          id="filter3_d_0_1"
          x="773.02"
          y="139.934"
          width="176.181"
          height="176.182"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="13.9162" dy="27.8323" />
          <feGaussianBlur stdDeviation="13.9162" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_0_1"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_0_1"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_0_1"
          x1="574.485"
          y1="100.877"
          x2="656.275"
          y2="77.8592"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0.75" />
          <stop offset="1" stopColor="#373737" stopOpacity="0" />
        </linearGradient>
        <radialGradient
          id="paint1_radial_0_1"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(540.113 8.21348) rotate(97.125) scale(53.0444 169.556)"
        >
          <stop stopColor="white" stopOpacity="0.49" />
          <stop offset="1" stopOpacity="0" />
        </radialGradient>
        <linearGradient
          id="paint2_linear_0_1"
          x1="181.516"
          y1="165.062"
          x2="221.167"
          y2="202.469"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0.75" />
          <stop offset="1" stopColor="#373737" stopOpacity="0" />
        </linearGradient>
        <radialGradient
          id="paint3_radial_0_1"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(210.613 130.833) rotate(97.125) scale(34.0315 108.781)"
        >
          <stop stopColor="white" stopOpacity="0.49" />
          <stop offset="1" stopOpacity="0" />
        </radialGradient>
        <linearGradient
          id="paint4_linear_0_1"
          x1="6.25458"
          y1="-107.097"
          x2="180.2"
          y2="54.0866"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0.47" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </linearGradient>
        <radialGradient
          id="paint5_radial_0_1"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(60.9072 -82.6836) rotate(53.9993) scale(131.681)"
        >
          <stop stopColor="white" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </radialGradient>
        <linearGradient
          id="paint6_linear_0_1"
          x1="804.377"
          y1="157.8"
          x2="893.274"
          y2="240.175"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0.47" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </linearGradient>
        <radialGradient
          id="paint7_radial_0_1"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(832.308 170.277) rotate(53.9993) scale(67.297)"
        >
          <stop stopColor="white" />
          <stop offset="1" stopColor="white" stopOpacity="0" />
        </radialGradient>
      </defs>
    </svg>
  );
}
