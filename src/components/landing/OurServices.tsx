import { FolderCheckIcon } from "@/ui/icons/general";
import Title from "./Title";
import { ArrowRigthIcon } from "@/ui/icons/navigation";
import Link from "next/link";

type Service = {
  title: string;
  description: string;
};

export default function OurServices() {
  const services: Service[] = [
    {
      title: "Keyword Research & Analysis",
      description: "Discover the most profitable keywords for your business.",
    },
    {
      title: "On-Page SEO Optimisation",
      description: "Improve your website’s structure, speed, and content.",
    },
    {
      title: "Technology Review",
      description: "Fix crawling, indexing, and performance issues that block search engine visibility.",
    },
    {
      title: "Backlink Building",
      description:
        "Increase your site’s authority with high-quality backlinks.",
    },
    {
      title: "Analytics & Reporting",
      description: "Data-driven insights and practical recommendations.",
    },
    {
      title: "Local SEO",
      description: "Optimise for location-based searches and get found by nearby customers.",
    },
    {
      title: "Performance SEO",
      description: "Optimise speed, usability, and mobile experience to meet core SEO standards.",
    },
    {
      title: "SEO Audit & Consultation",
      description: "Get a full-site audit with expert insights and recommendations.",
    },
  ];

  return (
    <div className="w-full container max-w-full__customeLG mt-8 lg:mt-[84px]">
      <div className="flex items-center justify-between lg:justify-center">
        <Title title="Our Services" />
        <button className="flex lg:hidden items-center gap-1 text-primary text-sm">
          <div>
            <ArrowRigthIcon className="w-8 h-8" />
          </div>
        </button>
      </div>
      <div className="w-full grid grid-cols-1 lg:grid-cols-4 gap-4 lg:gap-6 my-4">
        {services.map((item, index) => (
          <ServiceCard
            key={index}
            title={item.title}
            description={item.description}
            icon={<FolderCheckIcon />}
          />
        ))}
      </div>
      <div className="w-full hidden lg:flex justify-end">
        <button className="flex items-center justify-end gap-1 text-primary text-sm">
          <Link href="/pricing">
            Discover all our SEO services and find the right solution for your
            business
          </Link>
          <div>
            <ArrowRigthIcon />
          </div>
        </button>
      </div>
    </div>
  );
}

type ServiceCardProps = {
  title: string;
  description: string;
  icon: React.ReactNode;
};

function ServiceCard({ title, description, icon }: ServiceCardProps) {
  return (
    <div className="w-full flex flex-col justify-between bg-white rounded-lg p-6 hover:shadow-[0_0_25px_-5px_rgba(0,0,0,0.25)] duration-300 ease-in-out">
      <div>
        <div className="flex items-center gap-2 text-secondary">
          {icon}
          <h5 className="font-semibold">{title}</h5>
        </div>
        <p className="text-sm text-secondary/60 mt-2">{description}</p>
      </div>
      <Link href="/blog">
        {" "}
        <button className="w-full flex items-center justify-end gap-2 text-primary text-sm mt-4">
          See More
          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
            <ArrowRigthIcon />
          </div>
        </button>
      </Link>
    </div>
  );
}
