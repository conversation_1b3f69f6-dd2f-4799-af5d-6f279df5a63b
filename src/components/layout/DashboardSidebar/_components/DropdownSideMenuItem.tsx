import React, { forwardRef } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================== TYPES ================================= */
import type { TDropdownSideMenuItemProps } from "../Types";

/* ========================================================================== */
const DropdownSideMenuItem = forwardRef<
  HTMLDivElement,
  TDropdownSideMenuItemProps
>(({ href, title, themeColor }, ref) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const pathname = usePathname();
  const isActive = pathname === href;

  // Helper function to convert hex to rgba
  const hexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  };

  // Helper function to darken a color
  const darkenColor = (hex: string, amount: number = 0.2) => {
    const r = Math.max(0, parseInt(hex.slice(1, 3), 16) * (1 - amount));
    const g = Math.max(0, parseInt(hex.slice(3, 5), 16) * (1 - amount));
    const b = Math.max(0, parseInt(hex.slice(5, 7), 16) * (1 - amount));
    return `rgb(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)})`;
  };

  const activeBackgroundColor = isActive && themeColor ? hexToRgba(themeColor, 0.1) : "transparent";
  const activeTextColor = isActive && themeColor ? darkenColor(themeColor) : (isActive ? themeColor : "#344054");

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Link className="w-[236px]" href={href}>
      <motion.div 
        className="px-6 py-2 rounded-lg relative overflow-hidden my-1" 
        ref={ref}
        initial={{ backgroundColor: activeBackgroundColor }}
        
        whileHover={!isActive ? { 
          backgroundColor: "rgba(156, 163, 175, 0.113)", // bg-secondary/20 equivalent
          transition: { duration: 0.1, ease: "easeInOut" }
        } : {}}
        whileTap={!isActive ? { 
          scale:0.98,
          backgroundColor: "rgba(156, 163, 175, 0.154)",
          transition: { duration: 0.1, ease: "easeInOut" }
        } : {}}
        animate={{ 
          backgroundColor: activeBackgroundColor,
          transition: { duration: 0.1, ease: "easeInOut" }
        }}
      >
        <motion.span
          style={{ color: activeTextColor }}
          className={`text-sm ${
            isActive ? "text-primary font-medium" : "text-secondary"
          } transition-colors duration-300 ease-in-out relative z-10`}
          whileHover={!isActive ? {
            x: 2,
            transition: { duration: 0.2, ease: "easeInOut" }
          } : {}}
          whileTap={!isActive ? {
            x: 4,
            transition: { duration: 0.1, ease: "easeInOut" }
          } : {}}
        >
          {title}
        </motion.span>
      </motion.div>
    </Link>
  );
});

DropdownSideMenuItem.displayName = "DropdownSideMenuItem";

export default DropdownSideMenuItem;