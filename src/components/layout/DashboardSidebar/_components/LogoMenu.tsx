"use client";
import { motion } from "framer-motion";
import React, { useEffect, useRef, useState } from "react";

/* ================================== DATA ================================== */
import { LOGO_MENU } from "@/constants/logoMenuItems";

/* ========================================================================== */
const LogoMenu = ({ themeColor }: { themeColor: string }) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const LogoMenuRefs = useRef<(HTMLElement | null)[]>([]);
  const [selectedLogoMenu, setSelectedLogoMenu] = useState(1);
  const [indicatorTop, setIndicatorTop] = useState(0);
  const [hasInteracted, setHasInteracted] = useState(false);

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */
  const handleLogoMenuClick = (id: number, index: number) => {
    setHasInteracted(true);
    setSelectedLogoMenu(id);
    const selectedEl = LogoMenuRefs.current[index];
    if (selectedEl) {
      const offsetTop = selectedEl.offsetTop;
      setIndicatorTop(offsetTop);
    }
  };

  // Position the indicator on mount without animating
  useEffect(() => {
    const initialIndex = LOGO_MENU.findIndex(
      (menuItem) => menuItem.id === selectedLogoMenu
    );
    const initialEl = LogoMenuRefs.current[initialIndex];
    if (initialEl) {
      setIndicatorTop(initialEl.offsetTop);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="flex flex-col items-center w-fit gap-40 h-[85vh]">
      <div className="relative flex flex-col items-center w-fit">
        {LOGO_MENU.map((item, index) => (
          <div
            key={item.id}
            className="z-10 cursor-pointer"
            onClick={() => handleLogoMenuClick(item.id, index)}
            ref={(el) => {
              LogoMenuRefs.current[index] = el;
            }}
          >
            <div
              style={{
                color: selectedLogoMenu === item.id ? themeColor : "#344054",
              }}
              className={`px-4 py-4  w-fit flex flex-col justify-center items-center gap-2 transition-colors duration-300`}
            >
              {item.icon}
              <span className="text-sm text-nowrap">{item.title}</span>
            </div>
          </div>
        ))}
        <motion.div
          layout={hasInteracted}
          initial={false}
          transition={
            hasInteracted
              ? { type: "spring", stiffness: 500, damping: 30 }
              : { duration: 0 }
          }
          className="bg-primary/15 w-28 h-20 rounded-lg absolute z-0"
          style={{
            top: indicatorTop,
            background: themeColor || "#ff00ff",
            opacity: 0.1,
          }}
        />
      </div>
    </div>
  );
};

export default LogoMenu;