"use client";
import React, { useMemo } from "react";
import { usePathname } from "next/navigation";

/* =============================== COMPONENTS =============================== */
import DropdownSideMenuItem from "./DropdownSideMenuItem";

/* ================================== ICONS ================================= */
import { IoChevronDown } from "react-icons/io5";

/* ============================== FRAMER MOTION ============================= */
import { motion, AnimatePresence } from "framer-motion";

/* ================================== TYPES ================================= */
import type { TDashboardTextSideMenuProps } from "../Types";

/* ========================================================================== */
const DashboardTextSideMenu = ({
  title,
  subMenus,
  openMenu,
  onClick,
  themeColor,
  href,
}: TDashboardTextSideMenuProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const pathname = usePathname();

  // Check if current pathname matches any submenu or if it's a direct match with main href
  const isMainMenuActive =
    pathname === href || subMenus?.some((submenu) => pathname === submenu.href);

  const renderedSubMenus = useMemo(
    () =>
      subMenus?.map((submenu) => (
        <DropdownSideMenuItem
          themeColor={themeColor}
          key={submenu.id}
          title={submenu.title}
          href={submenu.href}
        />
      )),
    [subMenus, themeColor]
  );

  const isActive = isMainMenuActive;

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-[190px]">
      <div
        onClick={onClick}
        className={`flex justify-between items-center px-3 py-3.5 cursor-pointer transition-all duration-300`}
      >
        <span
          className={`text-sm transition-all duration-300 ${
            isActive ? "font-bold" : "font-normal"
          }`}
          style={{
            color: isActive ? themeColor : "#344054",
          }}
        >
          {title}
        </span>
        {subMenus && (
          <IoChevronDown
            className="transition-all duration-300"
            style={{
              rotate: openMenu ? "180deg" : "0deg",
              color: isActive ? themeColor : "#344054",
            }}
          />
        )}
      </div>

      <AnimatePresence initial={false}>
        {openMenu && (
          <motion.div
            key="submenu"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.25, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            {renderedSubMenus}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default DashboardTextSideMenu;