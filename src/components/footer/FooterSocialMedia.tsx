import { CallIcon, LocationIcon } from "@/ui/icons/communication";
import { InstagramIcon, TelegramIcon } from "@/ui/icons/socialMedia";
import Link from "next/link";

type Socials = {
  href: string;
  icon: React.ReactNode;
  title: string;
};
export default function FooterSocialMedia() {
  const socials: Socials[] = [
    {
      href: "#",
      icon: <TelegramIcon className="w-5 h-5" />,
      title: "telegram",
    },
    {
      href: "#",
      icon: <InstagramIcon className="w-5 h-5" />,
      title: "instagram",
    },
    {
      href: "#",
      icon: <CallIcon className="w-5 h-5" />,
      title: "call",
    },
    {
      href: "#",
      icon: <LocationIcon className="w-5 h-5" />,
      title: "location",
    },
  ];

  return (
    <ul className="flex items-center gap-4.5">
      {socials.map((item, index) => (
        <Link
          href={item.href}
          key={index}
          className="w-9.5 h-9.5 bg-white rounded-[9px] flex items-center justify-center text-primary hover:opacity-80 duration-200"
        >
          {item.icon}
        </Link>
      ))}
    </ul>
  );
}
