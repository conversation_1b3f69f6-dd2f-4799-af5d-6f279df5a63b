"use client";

import { useState, useEffect } from "react";
import analyticsService from "@/services/analyticsService";

const COOKIE_CONSENT_KEY = "seo_analyser_cookie_consent";

interface CookieConsentModalProps {
  onAccept?: () => void;
  onDecline?: () => void;
}

export default function CookieConsentModal({
  onAccept,
  onDecline,
}: CookieConsentModalProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [cookiePreferences, setCookiePreferences] = useState({
    essential: true, // Always true, cannot be disabled
    analytics: true,
    marketing: true,
  });

  useEffect(() => {
    // Check if user has already made a choice
    const hasConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
    if (!hasConsent) {
      // Small delay to ensure page has loaded
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleDecline = async () => {
    setIsLoading(true);
    try {
      localStorage.setItem(COOKIE_CONSENT_KEY, "declined");
      // Disable analytics
      analyticsService.disableAnalytics();
      setIsVisible(false);
      onDecline?.();
    } catch (error) {
      console.error("Error saving cookie consent:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSavePreferences = async () => {
    setIsLoading(true);
    try {
      // Save based on user's checkbox selections
      if (cookiePreferences.analytics || cookiePreferences.marketing) {
        localStorage.setItem(COOKIE_CONSENT_KEY, "accepted");
        analyticsService.enableAnalytics();
        onAccept?.();
      } else {
        localStorage.setItem(COOKIE_CONSENT_KEY, "declined");
        analyticsService.disableAnalytics();
        onDecline?.();
      }
      setIsVisible(false);
    } catch (error) {
      console.error("Error saving cookie preferences:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    // Treat close as decline
    handleDecline();
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "overview":
        return (
          <div className="space-y-6">
            <section>
              <p className="text-secondary text-sm lg:text-base leading-relaxed mb-6">
                SEO ANALYSER ("we," "our," or "us") uses cookies on our website
                (https://seoanalyser.com.au) and related services (collectively,
                the "Services"). This policy explains what cookies are, which
                cookies we use, and how you can manage your cookie preferences.
              </p>
            </section>

            <section>
              <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
                What Are Cookies?
              </h2>
              <p className="text-secondary text-sm lg:text-base leading-relaxed">
                Cookies are small text files sent from a website and stored on
                your computer or mobile device. They help websites remember your
                settings and preferences, enabling a smooth browsing experience.
                Cookies are widely used across the internet and do not harm your
                device.
              </p>
            </section>

            <section>
              <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
                How We Use Cookies
              </h2>
              <p className="text-secondary text-sm lg:text-base mb-4">
                We use cookies to:
              </p>
              <ul className="list-disc list-inside space-y-2 text-secondary text-sm lg:text-base ml-4">
                <li>
                  Provide essential functionality (e.g., user authentication)
                </li>
                <li>Limit the number of free audits per visitor</li>
                <li>Analyze site usage and performance</li>
                <li>Provide relevant marketing and advertising</li>
                <li>Improve user experience and website functionality</li>
              </ul>
            </section>
          </div>
        );

      case "types":
        return (
          <div className="space-y-6">
            <section>
              <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-6">
                Types of Cookies We Use
              </h2>

              {/* Essential Cookies */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-primary mb-3">
                  Essential Cookies (Required)
                </h3>
                <p className="text-secondary text-sm lg:text-base mb-4">
                  These cookies are essential for basic site functionality and
                  security:
                </p>
                <div className="bg-light-gray-6 rounded-lg p-4 mb-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4 text-sm font-semibold text-secondary border-b pb-2">
                      <div>Cookie Name</div>
                      <div>Purpose</div>
                      <div>Duration</div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm text-secondary">
                      <div>Access_token</div>
                      <div>Maintains your logged-in session</div>
                      <div>24 hours</div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm text-secondary">
                      <div>Refresh_token</div>
                      <div>Recognizes authenticated users</div>
                      <div>24 hours</div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm text-secondary">
                      <div>Device_id</div>
                      <div>Tracking user audit</div>
                      <div>24 hours</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Analytics Cookies */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-primary mb-3">
                  Analytics Cookies
                </h3>
                <p className="text-secondary text-sm lg:text-base mb-4">
                  These cookies help us understand how you use our site,
                  enabling improvements:
                </p>
                <div className="bg-light-gray-6 rounded-lg p-4 mb-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4 text-sm font-semibold text-secondary border-b pb-2">
                      <div>Cookie Name</div>
                      <div>Purpose</div>
                      <div>Duration</div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm text-secondary">
                      <div>_ga, _gid (Google Analytics)</div>
                      <div>Visitor analytics and site performance</div>
                      <div>up to 2 years</div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm text-secondary">
                      <div>ga_session</div>
                      <div>Tracks user interactions across sessions</div>
                      <div>30 days</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Marketing Cookies */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-primary mb-3">
                  Marketing & Retargeting Cookies (Optional)
                </h3>
                <p className="text-secondary text-sm lg:text-base mb-4">
                  These cookies enable us to show relevant ads and measure
                  campaign effectiveness:
                </p>
                <div className="bg-light-gray-6 rounded-lg p-4 mb-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4 text-sm font-semibold text-secondary border-b pb-2">
                      <div>Cookie Name</div>
                      <div>Purpose</div>
                      <div>Duration</div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm text-secondary">
                      <div>retention.com_id</div>
                      <div>Facilitates retargeting of ads across websites</div>
                      <div>up to 9 months</div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm text-secondary">
                      <div>fbp (Facebook Pixel)</div>
                      <div>Advertising via Facebook platforms</div>
                      <div>3 months</div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        );

      case "third-party":
        return (
          <div className="space-y-6">
            <section>
              <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
                Third-Party Cookies
              </h2>
              <p className="text-secondary text-sm lg:text-base mb-4">
                We partner with third-party services, which may place their
                cookies on your device when you visit our site:
              </p>
              <ul className="list-disc list-inside space-y-2 text-secondary text-sm lg:text-base ml-4 mb-6">
                <li>Stripe: Payments processing</li>
                <li>Google Analytics: Usage analytics</li>
                <li>Retention.com: Retargeting and marketing</li>
                <li>AWS SES: Email delivery tracking</li>
              </ul>
              <div className="bg-primary/10 border border-primary rounded-lg p-4 mb-6">
                <p className="text-secondary text-sm lg:text-base">
                  These third parties handle the data they collect according to
                  their privacy policies. You can find their policies via their
                  respective websites.
                </p>
              </div>
            </section>

            <section>
              <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
                Managing Your Cookie Preferences
              </h2>
              <p className="text-secondary text-sm lg:text-base mb-4">
                You can manage or disable cookies through your browser settings:
              </p>
              <ul className="list-disc list-inside space-y-2 text-secondary text-sm lg:text-base ml-4 mb-4">
                <li>Google Chrome</li>
                <li>Mozilla Firefox</li>
                <li>Apple Safari</li>
                <li>Microsoft Edge</li>
              </ul>
              <div className="bg-primary-yellow/10 border border-primary-yellow rounded-lg p-4">
                <p className="text-secondary text-sm lg:text-base">
                  <strong>Please note:</strong> disabling cookies may reduce
                  site functionality and your experience may be affected.
                </p>
              </div>
            </section>
          </div>
        );

      case "manage":
        return (
          <div className="space-y-6">
            <section>
              <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
                Manage Your Cookie Preferences
              </h2>
              <p className="text-secondary text-sm lg:text-base mb-6">
                Choose which types of cookies you'd like to accept. Essential
                cookies are required for basic site functionality and cannot be
                disabled.
              </p>

              {/* Cookie Preferences */}
              <div className="space-y-4">
                {/* Essential Cookies */}
                <div className="bg-light-gray-6 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-secondary mb-2">
                        Essential Cookies
                      </h3>
                      <p className="text-secondary text-sm">
                        Required for basic site functionality and security.
                        These cannot be disabled.
                      </p>
                    </div>
                    <div className="ml-4">
                      <div className="w-6 h-6 bg-green-500 rounded border border-green-600 flex items-center justify-center">
                        <svg
                          className="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Analytics Cookies */}
                <div className="bg-light-gray-6 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-secondary mb-2">
                        Analytics Cookies
                      </h3>
                      <p className="text-secondary text-sm">
                        Help us understand how you use our site to improve
                        performance and user experience.
                      </p>
                    </div>
                    <div className="ml-4">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={cookiePreferences.analytics}
                          onChange={(e) =>
                            setCookiePreferences((prev) => ({
                              ...prev,
                              analytics: e.target.checked,
                            }))
                          }
                          className="sr-only peer"
                        />
                        <div className="w-6 h-6 bg-white border-2 border-gray-300 rounded peer-checked:bg-primary peer-checked:border-primary flex items-center justify-center transition-colors">
                          {cookiePreferences.analytics && (
                            <svg
                              className="w-4 h-4 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Marketing Cookies */}
                <div className="bg-light-gray-6 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-secondary mb-2">
                        Marketing Cookies
                      </h3>
                      <p className="text-secondary text-sm">
                        Enable personalized advertising and help us measure the
                        effectiveness of our marketing campaigns.
                      </p>
                    </div>
                    <div className="ml-4">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={cookiePreferences.marketing}
                          onChange={(e) =>
                            setCookiePreferences((prev) => ({
                              ...prev,
                              marketing: e.target.checked,
                            }))
                          }
                          className="sr-only peer"
                        />
                        <div className="w-6 h-6 bg-white border-2 border-gray-300 rounded peer-checked:bg-primary peer-checked:border-primary flex items-center justify-center transition-colors">
                          {cookiePreferences.marketing && (
                            <svg
                              className="w-4 h-4 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <section className="mt-8 pt-6 border-t border-light-gray">
              <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4">
                Changes to this Cookie Policy
              </h2>
              <p className="text-secondary text-sm lg:text-base mb-4">
                We may update this Cookie Policy periodically. Significant
                changes will be communicated via a notice on our website. Please
                review this policy occasionally.
              </p>

              <h2 className="text-xl lg:text-2xl font-bold text-secondary mb-4 mt-6">
                Contact Us
              </h2>
              <div className="bg-primary/10 border border-primary rounded-lg p-4">
                <p className="text-secondary text-sm lg:text-base">
                  For questions about our Cookie Policy, email us at:{" "}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-primary hover:text-secondary transition-colors duration-300 font-semibold"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </section>
          </div>
        );

      default:
        return null;
    }
  };

  if (!isVisible) return null;

  if (!isExpanded) {
    // Compact default view
    return (
      <>
        {/* Minimal backdrop */}
        <div
          className="fixed inset-0 bg-black/30 backdrop-blur-xs transition-opacity z-40"
          onClick={handleClose}
        />

        {/* Compact Bottom Banner */}
        <div className="fixed bottom-0 left-0 right-0 z-50 bg-white shadow-2xl animate-in slide-in-from-bottom duration-300">
          <div className="px-4 py-4 sm:px-6 sm:py-5">
            <div className="max-w-7xl mx-auto">
              <div className="flex flex-col gap-4">
                {/* Top row - Cookie info with inline Learn more */}
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-7 h-7 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-cookie-icon lucide-cookie"
                    >
                      <path d="M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5" />
                      <path d="M8.5 8.5v.01" />
                      <path d="M16 15.5v.01" />
                      <path d="M12 12v.01" />
                      <path d="M11 17v.01" />
                      <path d="M7 14v.01" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h2 className="text-xl font-bold text-secondary mb-1">
                      Cookie Consent
                    </h2>
                    <p className="text-sm text-secondary leading-relaxed">
                      We use cookies to enhance your experience and analyze site
                      usage.{" "}
                      <button
                        onClick={() => setIsExpanded(true)}
                        className="text-primary hover:text-secondary underline font-medium"
                      >
                        Learn more
                      </button>
                    </p>
                  </div>
                </div>

                {/* Bottom row - Checkboxes and Action buttons */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  {/* Cookie Preferences Checkboxes - Left side */}
                  <div className="flex flex-wrap items-center gap-4 sm:gap-6 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-green-500 rounded flex items-center justify-center">
                        <svg
                          className="w-3 h-3 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <span className="text-secondary font-medium">
                        Essential
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={cookiePreferences.analytics}
                          onChange={(e) =>
                            setCookiePreferences((prev) => ({
                              ...prev,
                              analytics: e.target.checked,
                            }))
                          }
                          className="sr-only peer"
                        />
                        <div className="w-4 h-4 bg-white border-2 border-gray-300 rounded peer-checked:bg-primary peer-checked:border-primary flex items-center justify-center transition-colors">
                          {cookiePreferences.analytics && (
                            <svg
                              className="w-3 h-3 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                      </label>
                      <span className="text-secondary font-medium">
                        Analytics
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={cookiePreferences.marketing}
                          onChange={(e) =>
                            setCookiePreferences((prev) => ({
                              ...prev,
                              marketing: e.target.checked,
                            }))
                          }
                          className="sr-only peer"
                        />
                        <div className="w-4 h-4 bg-white border-2 border-gray-300 rounded peer-checked:bg-primary peer-checked:border-primary flex items-center justify-center transition-colors">
                          {cookiePreferences.marketing && (
                            <svg
                              className="w-3 h-3 text-white"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </div>
                      </label>
                      <span className="text-secondary font-medium">
                        Marketing
                      </span>
                    </div>
                  </div>

                  {/* Action buttons - Right side */}
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 sm:flex-shrink-0">
                    <button
                      onClick={handleDecline}
                      disabled={isLoading}
                      className="px-4 py-2 sm:px-6 sm:py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Essential Only
                    </button>

                    <button
                      onClick={() => {
                        // Accept cookies based on current preferences
                        handleSavePreferences();
                      }}
                      disabled={isLoading}
                      className="px-4 py-2 sm:px-6 sm:py-3 bg-primary hover:bg-primary/90 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isLoading ? "Saving..." : "Accept Selected"}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Expanded detailed view
  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-md transition-opacity z-40"
        onClick={() => setIsExpanded(false)}
      />

      {/* Expanded Modal */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-white shadow-2xl animate-in slide-in-from-bottom duration-300 h-[80vh] flex flex-col">
        {/* Header */}
        <div className="px-4 py-4 sm:px-6 sm:py-6 border-b border-light-gray flex-shrink-0">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-cookie-icon lucide-cookie"
                >
                  <path d="M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5" />
                  <path d="M8.5 8.5v.01" />
                  <path d="M16 15.5v.01" />
                  <path d="M12 12v.01" />
                  <path d="M11 17v.01" />
                  <path d="M7 14v.01" />
                </svg>
              </div>
              <h1 className="text-xl sm:text-2xl lg:text-[32px] font-black mt-1 sm:mt-2 text-secondary">
                Cookie Policy
              </h1>
            </div>
            <button
              onClick={() => setIsExpanded(false)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-full transition-colors"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="px-4 py-3 sm:px-6 sm:py-4 border-b border-light-gray flex-shrink-0">
          <div className="max-w-7xl mx-auto">
            <nav className="flex space-x-1 overflow-x-auto">
              {[
                { id: "overview", label: "Overview" },
                { id: "types", label: "Cookie Types" },
                { id: "third-party", label: "Third-Party" },
                { id: "manage", label: "Manage Preferences" },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-3 py-2 sm:px-4 text-xs sm:text-sm font-medium rounded-md transition-colors whitespace-nowrap ${
                    activeTab === tab.id
                      ? "bg-primary text-white"
                      : "text-secondary hover:text-primary hover:bg-primary/10"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-y-auto px-4 py-4 sm:px-6 sm:py-6">
          <div className="max-w-7xl mx-auto">{renderTabContent()}</div>
        </div>

        {/* Fixed Bottom Action Bar */}
        <div className="border-t border-light-gray bg-white px-4 py-3 sm:px-6 sm:py-4 flex-shrink-0">
          <div className="max-w-7xl mx-auto">
            {/* Desktop Layout: Checkboxes on left, Buttons on right */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 sm:gap-4">
              {/* Cookie Preferences Summary - Left side on desktop */}
              <div className="flex flex-wrap items-center gap-3 sm:gap-6 text-xs sm:text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 sm:w-4 sm:h-4 bg-green-500 rounded flex items-center justify-center">
                    <svg
                      className="w-2 h-2 sm:w-3 sm:h-3 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <span className="text-secondary font-medium">Essential</span>
                </div>

                <div className="flex items-center gap-2">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={cookiePreferences.analytics}
                      onChange={(e) =>
                        setCookiePreferences((prev) => ({
                          ...prev,
                          analytics: e.target.checked,
                        }))
                      }
                      className="sr-only peer"
                    />
                    <div className="w-3 h-3 sm:w-4 sm:h-4 bg-white border-2 border-gray-300 rounded peer-checked:bg-primary peer-checked:border-primary flex items-center justify-center transition-colors">
                      {cookiePreferences.analytics && (
                        <svg
                          className="w-2 h-2 sm:w-3 sm:h-3 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </div>
                  </label>
                  <span className="text-secondary font-medium">Analytics</span>
                </div>

                <div className="flex items-center gap-2">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={cookiePreferences.marketing}
                      onChange={(e) =>
                        setCookiePreferences((prev) => ({
                          ...prev,
                          marketing: e.target.checked,
                        }))
                      }
                      className="sr-only peer"
                    />
                    <div className="w-3 h-3 sm:w-4 sm:h-4 bg-white border-2 border-gray-300 rounded peer-checked:bg-primary peer-checked:border-primary flex items-center justify-center transition-colors">
                      {cookiePreferences.marketing && (
                        <svg
                          className="w-2 h-2 sm:w-3 sm:h-3 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </div>
                  </label>
                  <span className="text-secondary font-medium">Marketing</span>
                </div>
              </div>

              {/* Action Buttons - Right side on desktop */}
              <div className="flex gap-2 sm:gap-3 lg:flex-shrink-0">
                <button
                  onClick={handleDecline}
                  disabled={isLoading}
                  className="flex-1 sm:flex-none px-3 py-2 sm:px-6 sm:py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs sm:text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Essential Only
                </button>

                <button
                  onClick={() => {
                    // Accept all cookies
                    setCookiePreferences({
                      essential: true,
                      analytics: true,
                      marketing: true,
                    });
                    handleSavePreferences();
                  }}
                  disabled={isLoading}
                  className="flex-1 sm:flex-none px-3 py-2 sm:px-6 sm:py-3 bg-primary hover:bg-primary/90 text-white text-xs sm:text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? "Saving..." : "Accept All"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Utility function to check if user has given consent
export const hasUserConsent = (): boolean => {
  if (typeof window === "undefined") return false;
  try {
    const consent = localStorage.getItem(COOKIE_CONSENT_KEY);
    return consent === "accepted";
  } catch {
    return false;
  }
};

// Utility function to check if user has made any choice
export const hasUserMadeChoice = (): boolean => {
  if (typeof window === "undefined") return false;
  try {
    const consent = localStorage.getItem(COOKIE_CONSENT_KEY);
    return consent !== null;
  } catch {
    return false;
  }
};

// Utility function to reset consent (for testing)
export const resetConsent = (): void => {
  if (typeof window === "undefined") return;
  try {
    localStorage.removeItem(COOKIE_CONSENT_KEY);
  } catch (error) {
    console.error("Error resetting consent:", error);
  }
};
