"use client";

import { useState } from "react";
import { CrossSmallIcon } from "@/ui/icons/general";
import {
  hasUserConsent,
  hasUserMadeChoice,
  resetConsent,
} from "./CookieConsentModal";
import analyticsService from "@/services/analyticsService";

// Simple settings icon component
const SettingsIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm7.4-2a5.6 5.6 0 0 0 0-2l2-1.5a.5.5 0 0 0 .1-.7l-2-3.5a.5.5 0 0 0-.6-.2l-2.3 1a5.7 5.7 0 0 0-1.7-1l-.3-2.5a.5.5 0 0 0-.5-.4h-4a.5.5 0 0 0-.5.4l-.3 2.5a5.7 5.7 0 0 0-1.7 1l-2.3-1a.5.5 0 0 0-.6.2l-2 3.5a.5.5 0 0 0 .1.7l2 1.5a5.6 5.6 0 0 0 0 2l-2 1.5a.5.5 0 0 0-.1.7l2 3.5a.5.5 0 0 0 .6.2l2.3-1a5.7 5.7 0 0 0 1.7 1l.3 2.5a.5.5 0 0 0 .5.4h4a.5.5 0 0 0 .5-.4l.3-2.5a5.7 5.7 0 0 0 1.7-1l2.3 1a.5.5 0 0 0 .6-.2l2-3.5a.5.5 0 0 0-.1-.7l-2-1.5z"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

interface CookiePreferencesProps {
  className?: string;
}

export default function CookiePreferences({
  className = "",
}: CookiePreferencesProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const currentConsent = hasUserConsent();
  const hasMadeChoice = hasUserMadeChoice();

  const handleToggleAnalytics = async (enabled: boolean) => {
    setIsLoading(true);
    try {
      const consentValue = enabled ? "accepted" : "declined";
      localStorage.setItem("seo_analyser_cookie_consent", consentValue);

      if (enabled) {
        analyticsService.enableAnalytics();
      } else {
        analyticsService.disableAnalytics();
      }

      // Reload page to apply changes
      window.location.reload();
    } catch (error) {
      console.error("Error updating cookie preferences:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPreferences = () => {
    resetConsent();
    window.location.reload();
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className={`inline-flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors ${className}`}
        title="Cookie Preferences"
      >
        <SettingsIcon className="w-4 h-4" />
        Cookie Preferences
      </button>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={() => setIsOpen(false)}
      />

      {/* Modal */}
      <div className="relative w-full max-w-md bg-white rounded-lg shadow-xl border border-gray-200">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Cookie Preferences
          </h2>
          <button
            onClick={() => setIsOpen(false)}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <CrossSmallIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900">
                  Essential Cookies
                </h3>
                <p className="text-xs text-gray-600">
                  Required for basic functionality
                </p>
              </div>
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                Always On
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-900">
                  Analytics Cookies
                </h3>
                <p className="text-xs text-gray-600">
                  Help us improve our service
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={currentConsent}
                  onChange={(e) => handleToggleAnalytics(e.target.checked)}
                  disabled={isLoading}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>

          <div className="pt-2 border-t border-gray-200">
            <p className="text-xs text-gray-500 mb-3">
              Current status:{" "}
              {hasMadeChoice
                ? currentConsent
                  ? "Analytics enabled"
                  : "Essential only"
                : "No preference set"}
            </p>

            <button
              onClick={handleResetPreferences}
              className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
            >
              Reset all preferences
            </button>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end p-4 border-t border-gray-200">
          <button
            onClick={() => setIsOpen(false)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  );
}
