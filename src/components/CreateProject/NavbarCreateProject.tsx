"use client";
import React, { useState } from "react";
import { MdClose } from "react-icons/md";
import BoxCreateProject from "./BoxCreateProject";
import { useRouter, usePathname } from "next/navigation";
import { useCreateProjectStore } from "@/store/createProjectStore";
import SaveExitModal from "./SaveExitModal";

export default function NavbarCreateProject({
  children,
}: {
  children: React.ReactNode;
}) {
  const route = useRouter();
  const pathname = usePathname();
  const { resetAll } = useCreateProjectStore();
  const [showSaveExitModal, setShowSaveExitModal] = useState(false);

  // Check if current page should show save and exit modal
  const shouldShowModal =
    pathname !== "/create-project/project-information" &&
    pathname !== "/create-project";

  const handleClose = () => {
    if (shouldShowModal) {
      setShowSaveExitModal(true);
    } else {
      // For project-information and shortcut pages, directly exit without modal
      resetAll();
      route.replace("/my-projects");
    }
  };

  const handleConfirmExit = () => {
    resetAll();
    route.replace("/my-projects");
  };

  return (
    <>
      <BoxCreateProject classPlus="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
          <h1 className="text-lg lg:text-xl font-semibold text-[#344054] tracking-tight">
            {children}
          </h1>
        </div>
        <button
          type="button"
          onClick={handleClose}
          title="Close and return to profile"
          className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-900 transition-all duration-200 group"
        >
          <MdClose className="text-xl group-hover:scale-110 transition-transform duration-200" />
        </button>
      </BoxCreateProject>

      <SaveExitModal
        open={showSaveExitModal}
        onOpenChange={setShowSaveExitModal}
        onConfirmExit={handleConfirmExit}
      />
    </>
  );
}
