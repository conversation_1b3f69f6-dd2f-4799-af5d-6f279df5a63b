"use client";

import React from "react";
import Image from "next/image";
import { AnimatedElement } from "@/components/CreateProject/PageTransition";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Checkbox } from "@/components/shared/CheckBox";
import { IoIosArrowDown } from "react-icons/io";
import { Button } from "@/components/ui/button";

interface SearchEngineConfig {
  id: string;
  searchEngine: {
    name: string;
    image: string;
  };
  country: {
    name: string;
    code: string;
    image: string;
  };
  language: {
    name: string;
    code: string;
  };
  location: {
    name: string;
  } | null;
}

interface SearchEngineConfigurationStepProps {
  searchEngineConfigs: SearchEngineConfig[];
  selectedConfigIds: string[];
  configDropdownOpen: boolean;
  setConfigDropdownOpen: (open: boolean) => void;
  onConfigToggle: (configId: string) => void;
  onSelectAll: () => void;
  onDeselectAll: () => void;
}

export default function SearchEngineConfigurationStep({
  searchEngineConfigs,
  selectedConfigIds,
  configDropdownOpen,
  setConfigDropdownOpen,
  onConfigToggle,
  onSelectAll,
  onDeselectAll,
}: SearchEngineConfigurationStepProps) {
  return (
    <AnimatedElement variant="child" delay={0.5}>
      <div className="space-y-8">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
            <span className="text-primary font-semibold text-sm">1</span>
          </div>
          <h3 className="text-base font-semibold text-[#344054]">
            Select Search Engine Configurations
          </h3>
        </div>

        {/* Configuration Selection Section */}
        <AnimatedElement variant="form" delay={0.6}>
          <div className="bg-gray-50 rounded-xl p-6">
            <div className="mb-4">
              <h4 className="text-base font-medium text-[#344054] mb-2">
                Choose Search Engine Configurations
              </h4>
              <p className="text-sm text-[#344054] mb-4">
                Select the search engines and locations where you want to track
                your keywords
              </p>

              <div className="flex w-full bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors duration-200 overflow-hidden">
                <Popover
                  open={configDropdownOpen}
                  onOpenChange={setConfigDropdownOpen}
                >
                  <PopoverTrigger asChild>
                    <div className="flex min-h-[48px] w-full">
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={configDropdownOpen}
                        className="w-full focus:!border rounded-none shadow-none min-h-full justify-between border-none bg-white placeholder:text-gray-400 px-4 py-3 text-sm"
                      >
                        {selectedConfigIds.length > 0 ? (
                          <div className="flex items-center gap-2 min-w-4 flex-1">
                            {selectedConfigIds.length <= 5 ? (
                              <div className="flex items-center gap-2 flex-wrap">
                                {searchEngineConfigs
                                  .filter((config) =>
                                    selectedConfigIds.includes(config.id)
                                  )
                                  .map((config) => (
                                    <div
                                      key={config.id}
                                      className="flex items-center gap-1.5 bg-gray-100 hover:bg-gray-200 transition-colors px-2 py-1.5 rounded-md border"
                                    >
                                      <div className="w-4 h-4 rounded-full bg-white flex items-center justify-center">
                                        <Image
                                          src={config.searchEngine.image}
                                          alt={config.searchEngine.name}
                                          width={12}
                                          height={12}
                                          className="rounded"
                                        />
                                      </div>
                                      <div className="w-4 h-4 rounded overflow-hidden border border-gray-300">
                                        <Image
                                          src={config.country.image}
                                          alt={config.country.name}
                                          width={16}
                                          height={16}
                                          className="w-full h-full object-cover"
                                        />
                                      </div>
                                      <span className="text-xs font-medium text-gray-700">
                                        {config.country.code}
                                      </span>
                                    </div>
                                  ))}
                              </div>
                            ) : (
                              <span className="text-sm font-medium text-gray-700">
                                {selectedConfigIds.length} configurations
                                selected
                              </span>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-500">
                            Select Search Engine Configurations
                          </span>
                        )}
                        <IoIosArrowDown className="ml-2 opacity-50" />
                      </Button>
                    </div>
                  </PopoverTrigger>
                  <PopoverContent
                    align="start"
                    sideOffset={4}
                    className="p-0 min-w-64 max-h-[300px]"
                    style={{
                      width: "var(--radix-popover-trigger-width)",
                    }}
                  >
                    <Command className="p-1" shouldFilter={false}>
                      <CommandList className="max-h-[300px] overflow-auto">
                        <CommandEmpty>
                          No search engine configurations available.
                        </CommandEmpty>
                        <CommandGroup>
                          {/* Select All / Deselect All Buttons */}
                          <div className="flex gap-2 p-2 border-b border-gray-100 mb-2">
                            <button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                onSelectAll();
                              }}
                              className="flex-1 px-3 py-2 text-sm font-medium text-primary bg-primary/10 hover:bg-primary/20 rounded-md transition-colors"
                            >
                              Select All
                            </button>
                            <button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                onDeselectAll();
                              }}
                              className="flex-1 px-3 py-2 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                            >
                              Deselect All
                            </button>
                          </div>
                          {searchEngineConfigs.map((config) => (
                            <CommandItem
                              key={config.id}
                              value={config.id}
                              className="w-full flex items-center justify-start cursor-pointer hover:bg-gray-50 transition-colors duration-200 p-2 rounded-md"
                              onSelect={() => onConfigToggle(config.id)}
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                onConfigToggle(config.id);
                              }}
                            >
                              <div className="mr-3">
                                <Checkbox
                                  checked={selectedConfigIds.includes(
                                    config.id
                                  )}
                                />
                              </div>
                              <div className="flex items-center gap-3 min-w-0 flex-1">
                                {/* Search Engine */}
                                <div className="flex items-center gap-2">
                                  <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center p-1">
                                    <Image
                                      src={config.searchEngine.image}
                                      alt={config.searchEngine.name}
                                      width={14}
                                      height={14}
                                      className="flex-shrink-0"
                                    />
                                  </div>
                                  <span className="text-sm font-medium text-gray-700">
                                    {config.searchEngine.name}
                                  </span>
                                </div>

                                {/* Separator */}
                                <span className="text-gray-300 text-xs">•</span>

                                {/* Country */}
                                <div className="flex items-center gap-2">
                                  <div className="w-5 h-5 rounded overflow-hidden border border-gray-200">
                                    <Image
                                      src={config.country.image}
                                      alt={config.country.name}
                                      width={20}
                                      height={20}
                                      className="w-full h-full object-cover"
                                    />
                                  </div>
                                  <span className="text-sm text-gray-600">
                                    {config.country.code}
                                  </span>
                                </div>

                                {/* Location */}
                                {config.location?.name && (
                                  <>
                                    <span className="text-gray-300 text-xs">
                                      •
                                    </span>
                                    <span className="text-xs text-gray-500 truncate">
                                      {config.location.name}
                                    </span>
                                  </>
                                )}
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {selectedConfigIds.length === 0 && (
                <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <p className="text-xs text-amber-700">
                    <strong>Note:</strong> You can proceed without selecting
                    configurations, but you'll need to select at least one to
                    add keywords for tracking.
                  </p>
                </div>
              )}
            </div>
          </div>
        </AnimatedElement>
      </div>
    </AnimatedElement>
  );
}
