import React, { useState } from "react";
import { defaultSearchEngines } from "@/utils/flagUtils";
import Image from "next/image";
import { BsExclamationCircle } from "react-icons/bs";
import TooltipPortal from "../ui/TooltipPortal";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Checkbox } from "../shared/CheckBox";
import { IoIosArrowDown } from "react-icons/io";
import { Button } from "@/components/ui/button";
import { RobotIcon } from "@/ui/icons/general/RobotIcon";

const dataSearchEngine = [
  defaultSearchEngines.google,
  defaultSearchEngines.bing,
  defaultSearchEngines.chatgpt,
];

type SearchEngineItem = {
  name: string;
  image: string;
  isIcon?: boolean;
};

type SelectSearchEngineMultiType = {
  title?: string;
  setValue: (val: SearchEngineItem[]) => void;
  value: SearchEngineItem[];
  guideText?: string;
  data?: SearchEngineItem[];
  placeHolder?: string;
  soloChoose?: boolean;
  classPlusButton?: string;
  classNameBox?: string;
  noNameSelect?: boolean;
};

export default function SelectSearchEngine({
  title,
  guideText,
  setValue,
  value,
  data = dataSearchEngine,
  placeHolder,
  soloChoose,
  classPlusButton,
  classNameBox,
  noNameSelect,
}: SelectSearchEngineMultiType) {
  const [open, setOpen] = useState(false);
  const toggleItem = (item: SearchEngineItem) => {
    const exists = value.some((v) => v.name === item.name);
    if (exists) {
      setValue(value.filter((v) => v.name !== item.name));
    } else {
      setValue([...value, item]);
    }
  };

  const handleSelect = (item: SearchEngineItem) => {
    if (soloChoose) {
      setValue([item]);
      setOpen(false);
    } else {
      toggleItem(item);
    }
  };

  return (
    <div className={"w-full " + classNameBox}>
      {title ? (
        <div className="flex mb-2  items-center gap-2 text-gray-600">
          <span className="text-sm lg:text-base">{title}</span>
          {guideText ? (
            <TooltipPortal width="xl" content={<span>{guideText}</span>}>
              <BsExclamationCircle />
            </TooltipPortal>
          ) : null}
        </div>
      ) : null}

      <div className="flex w-full transition-colors duration-200">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <div className="flex h-[45px] w-full">
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={open}
                className={`w-full focus:!border shadow-none h-full justify-between ${
                  classPlusButton?.includes("border-none") ? "border-none" : ""
                } ${
                  classPlusButton?.includes("bg-white")
                    ? "bg-white"
                    : "bg-gray-100"
                } placeholder:text-gray-400 ${
                  noNameSelect ? "px-3 py-2 justify-center" : "px-4 py-3"
                } ${classPlusButton}`}
              >
                {value.length ? (
                  <div
                    className={`flex items-center ${
                      noNameSelect ? "justify-center" : "gap-2"
                    } min-w-4`}
                  >
                    {value[0].isIcon ? (
                      <RobotIcon
                        width={noNameSelect ? 20 : 16}
                        height={noNameSelect ? 20 : 16}
                        className="flex-shrink-0"
                      />
                    ) : (
                      <Image
                        src={value[0].image}
                        alt={value[0].name}
                        width={noNameSelect ? 20 : 16}
                        height={noNameSelect ? 20 : 16}
                        className="flex-shrink-0"
                      />
                    )}
                    {noNameSelect ? null : <span>{value[0].name}</span>}
                  </div>
                ) : (
                  <span>{placeHolder || "Select Search Engine"}</span>
                )}
                {!noNameSelect && (
                  <IoIosArrowDown className="ml-2 opacity-50" />
                )}
              </Button>
            </div>
          </PopoverTrigger>
          <PopoverContent
            align="start"
            sideOffset={4}
            className="p-0 min-w-64 max-h-[300px]"
            style={{ width: "var(--radix-popover-trigger-width)" }}
          >
            <Command className="p-1" shouldFilter={false}>
              <CommandList className="max-h-[250px] overflow-auto">
                <CommandEmpty>No search engines found.</CommandEmpty>
                <CommandGroup>
                  {data.map((item) => (
                    <CommandItem
                      key={item.name}
                      value={item.name}
                      className="w-full flex items-center justify-start cursor-pointer hover:bg-gray-100 transition-colors duration-100"
                      onSelect={() => handleSelect(item)}
                    >
                      {!soloChoose && (
                        <div className="mr-2">
                          <Checkbox
                            checked={value.some((v) => v.name === item.name)}
                          />
                        </div>
                      )}
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        {item.isIcon ? (
                          <RobotIcon
                            width={16}
                            height={16}
                            className="flex-shrink-0"
                          />
                        ) : (
                          <Image
                            src={item.image}
                            alt={item.name}
                            width={16}
                            height={16}
                            className="flex-shrink-0"
                          />
                        )}
                        <span className="truncate">{item.name}</span>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
