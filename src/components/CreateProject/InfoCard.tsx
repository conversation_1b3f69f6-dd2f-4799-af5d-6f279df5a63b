"use client";
import React, { ReactNode } from "react";

interface InfoCardProps {
  title: string;
  description: string | ReactNode;
  variant?: "info" | "tips" | "benefits" | "warning";
  className?: string;
}

export default function InfoCard({
  title,
  description,
  variant = "info",
  className = "",
}: InfoCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case "info":
        return {
          background: "bg-blue-50 border-blue-200",
          iconBg: "bg-blue-500",
          textColor: "text-[#344054]",
          icon: (
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          ),
        };
      case "tips":
        return {
          background: "bg-amber-50 border-amber-200",
          iconBg: "bg-amber-500",
          textColor: "text-[#344054]",
          icon: (
            <path
              fillRule="evenodd"
              d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
              clipRule="evenodd"
            />
          ),
        };
      case "benefits":
        return {
          background: "bg-green-50 border-green-200",
          iconBg: "bg-green-500",
          textColor: "text-[#344054]",
          icon: (
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          ),
        };
      case "warning":
        return {
          background: "bg-red-50 border-red-200",
          iconBg: "bg-red-500",
          textColor: "text-[#344054]",
          icon: (
            <path
              fillRule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          ),
        };
      default:
        return {
          background: "bg-blue-50 border-blue-200",
          iconBg: "bg-blue-500",
          textColor: "text-[#344054]",
          icon: (
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          ),
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <div className={`${styles.background} border rounded-xl p-4 ${className}`}>
      <div className="flex items-start gap-3">
        <div
          className={`w-8 h-8 ${styles.iconBg} rounded-full flex items-center justify-center flex-shrink-0 mt-0.5`}
        >
          <svg
            className="w-5 h-5 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            {styles.icon}
          </svg>
        </div>
        <div>
          <h4 className={`text-sm font-medium ${styles.textColor} mb-1`}>
            {title}
          </h4>
          <div className={`text-sm ${styles.textColor}`}>{description}</div>
        </div>
      </div>
    </div>
  );
}
