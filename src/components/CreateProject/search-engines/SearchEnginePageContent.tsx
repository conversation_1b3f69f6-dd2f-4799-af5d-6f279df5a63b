"use client";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useCreateProjectStore } from "@/store/createProjectStore";
import {
  getFlagImageUrl,
  defaultCountries,
  defaultSearchEngines,
} from "@/utils/flagUtils";
import { FieldsCreateProject } from "@/components/CreateProject/KeywordsInput";
import BoxCreateProject from "@/components/CreateProject/BoxCreateProject";
import TitleCreateProject from "@/components/CreateProject/TitleCreateProject";
import { AnimatedElement } from "@/components/CreateProject/PageTransition";
import SearchEngineForm from "./SearchEngineForm";
import SearchEngineTable from "./SearchEngineTable";
import SearchEngineInfoCard from "./SearchEngineInfoCard";
import { projectAPI } from "@/services/projectService";

export default function SearchEnginePageContent() {
  // Store hooks
  const {
    searchEngineConfigs,
    addSearchEngineConfig,
    removeSearchEngineConfig,
    setCurrentStep,
    setSupportedLocations,
    supportedLocations,
  } = useCreateProjectStore();

  // Local state
  const [error, setError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [countriesData, setCountriesData] = useState<any[]>([]);
  const [googleLanguage, setGoogleLanguage] = useState<any[]>([]);
  const [allCity, setAllCity] = useState<any>();
  const [allLocation, setAllLocation] = useState<any[]>([]);
  const route = useRouter();

  // Load supported locations on page mount
  useQuery({
    queryKey: ["supported-locations"],
    queryFn: async () => {
      try {
        const response = await projectAPI.getSupportedLocations();
        setSupportedLocations(response.data.locations);
        return response.data;
      } catch (error) {
        console.error("Failed to load supported locations:", error);
        return { locations: [] };
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });
  const [dataState, setDataState] = useState<FieldsCreateProject>({
    language: {
      name: "English",
      code: "en",
    },
    location: null,
    country: defaultCountries.australia,
    searchEngines: [defaultSearchEngines.google],
  });

  const ITEMS_PER_PAGE = 6;

  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return searchEngineConfigs.slice(startIndex, endIndex);
  };

  const handleAddTable = () => {
    const { country, language, location, searchEngines } = dataState;
    const isBingSelected = searchEngines?.[0]?.name === "Bing";

    // For Bing, location is not required
    if (
      !country ||
      !language ||
      (!isBingSelected && !location) ||
      !searchEngines ||
      searchEngines.length === 0
    ) {
      setError("Please fill in all required fields");
      return;
    }

    // Check for duplicates
    const isDuplicate = searchEngineConfigs.some(
      (config) =>
        config.country?.code === country.code &&
        config.language?.name === language.name &&
        (isBingSelected
          ? config.searchEngine?.name === "Bing" // For Bing, only check engine, country, and language
          : config.location?.name === location?.name &&
            config.searchEngine?.name === searchEngines[0]?.name)
    );

    if (isDuplicate) {
      setError("This combination has already been added.");
      return;
    }

    // Add configuration to store
    addSearchEngineConfig({
      searchEngine: searchEngines[0],
      country,
      language: language as { name: string; code: string },
      location: isBingSelected ? null : (location as { name: string }),
    });

    setError("");

    // Reset form
    setDataState({
      language: {
        name: "English",
        code: "en",
      },
      location: null,
      country: defaultCountries.australia,
      searchEngines: [defaultSearchEngines.google],
    });
  };

  const handleDelete = (id: string) => {
    setError("");
    removeSearchEngineConfig(id);

    // Update pagination if needed
    const totalItemsAfterDelete = searchEngineConfigs.length - 1;
    const totalPage = Math.ceil(totalItemsAfterDelete / ITEMS_PER_PAGE);
    if (totalItemsAfterDelete === 0 && currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
    if (currentPage > totalPage && totalPage > 0) {
      setCurrentPage(totalPage);
    }
  };

  const { mutate, isPending } = useMutation({
    mutationFn: () => {
      // Validate that at least one configuration exists
      if (searchEngineConfigs.length === 0) {
        throw new Error(
          "Please add at least one search engine configuration before proceeding."
        );
      }

      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(true);
        }, 500);
      });
    },
    onSuccess: () => {
      setCurrentStep("keywords");
      route.push("/create-project/keywords");
    },
    onError: (error: any) => {
      setError(error.message || "An error occurred");
    },
  });

  useEffect(() => {
    // Set current step
    setCurrentStep("search-engines");

    // Load data
    import("@/data/countries").then((mod) =>
      setCountriesData(mod.default || mod)
    );
    import("@/data/googleLanguage").then((mod) =>
      setGoogleLanguage(mod.default || mod)
    );
    import("@/data/cities").then((mod) => setAllCity(mod.default || mod));
  }, [setCurrentStep]);

  useEffect(() => {
    const code = dataState?.country?.code;
    if (!code || !allCity || !allCity[code]) return;

    const newBody = (allCity[code] || []).map((item: string) => ({
      name: item,
    }));

    setAllLocation(newBody);
  }, [dataState?.country, allCity]);

  // Merge supported locations with country images
  const supportedCountriesWithImages = React.useMemo(() => {
    if (supportedLocations.length === 0) return [];

    return supportedLocations.map((location) => {
      const countryData = countriesData.find(
        (country) => country.code === location.code
      );
      return {
        ...location,
        image: countryData?.image || getFlagImageUrl(location.code),
      };
    });
  }, [supportedLocations, countriesData]);

  // Filter languages to only show those used as primary languages in supported locations
  const supportedLanguages = React.useMemo(() => {
    if (supportedLocations.length === 0 || googleLanguage.length === 0)
      return googleLanguage;

    const primaryLanguageCodes = [
      ...new Set(supportedLocations.map((loc) => loc.primary_language)),
    ];
    return googleLanguage.filter((lang) =>
      primaryLanguageCodes.includes(lang.code)
    );
  }, [supportedLocations, googleLanguage]);

  // Update default selections when supported locations are loaded
  useEffect(() => {
    if (
      supportedCountriesWithImages.length > 0 &&
      supportedLanguages.length > 0
    ) {
      // Check if current country is in supported list
      const currentCountrySupported = supportedCountriesWithImages.find(
        (country) => country.code === dataState.country?.code
      );

      // Check if current language is in supported list
      const currentLanguageSupported = supportedLanguages.find(
        (lang) => lang.code === dataState.language?.code
      );

      // If current selections are not supported, update to first supported options
      if (!currentCountrySupported || !currentLanguageSupported) {
        const defaultCountry =
          currentCountrySupported || supportedCountriesWithImages[0];
        const defaultLanguage =
          currentLanguageSupported ||
          supportedLanguages.find(
            (lang) => lang.code === defaultCountry.primary_language
          ) ||
          supportedLanguages[0];

        setDataState((prev) => ({
          ...prev,
          country: defaultCountry,
          language: defaultLanguage,
        }));
      }
    }
  }, [supportedCountriesWithImages, supportedLanguages]);

  return (
    <BoxCreateProject classPlus="relative">
      {/* Header Section */}
      <AnimatedElement variant="child" delay={0.3}>
        <div className="mb-4">
          <TitleCreateProject
            head="Configure search engines"
            description="Set up keyword tracking across multiple search engines and regions. Monitor your rankings on Google and Bing with location-specific results and language preferences."
            classHead="text-lg lg:text-xl font-semibold text-[#344054]"
            classP="text-[#344054] mt-3 leading-relaxed"
          />
        </div>
      </AnimatedElement>

      {/* Configuration Section */}
      <AnimatedElement variant="child" delay={0.4}>
        <div className="space-y-8">
          <SearchEngineForm
            dataState={dataState}
            setDataState={setDataState}
            countriesData={countriesData}
            googleLanguage={googleLanguage}
            allLocation={allLocation}
            error={error}
            onAddTable={handleAddTable}
            supportedLocations={supportedLocations}
          />

          <SearchEngineTable
            searchEngineConfigs={searchEngineConfigs}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            itemsPerPage={ITEMS_PER_PAGE}
            getCurrentPageItems={getCurrentPageItems}
            onDelete={handleDelete}
          />

          <SearchEngineInfoCard />
        </div>
      </AnimatedElement>
    </BoxCreateProject>
  );
}
