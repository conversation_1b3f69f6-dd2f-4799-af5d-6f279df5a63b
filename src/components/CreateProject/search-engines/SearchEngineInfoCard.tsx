"use client";
import React from "react";
import { AnimatedElement } from "@/components/CreateProject/PageTransition";
import InfoCard from "@/components/CreateProject/InfoCard";

export default function SearchEngineInfoCard() {
  return (
    <AnimatedElement variant="card" delay={0.8}>
      <InfoCard
        variant="info"
        title="Search Engine Setup"
        description="Each configuration represents a unique combination of search engine, country, language, and location. You can add multiple configurations to track your keywords across different markets and regions."
        className="mt-6"
      />
    </AnimatedElement>
  );
}
