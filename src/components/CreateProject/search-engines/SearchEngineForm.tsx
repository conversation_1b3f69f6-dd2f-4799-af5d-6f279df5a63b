"use client";
import React, { useMemo } from "react";
import { motion } from "framer-motion";
import { FieldsCreateProject } from "@/components/CreateProject/KeywordsInput";
import SelectSearchEngine from "@/components/CreateProject/SelectSearchEngine";
import { AutoCompleteCountry } from "@/components/CreateProject/AutoCompleteCountry";
import { AutoComplete } from "@/components/shared/AutoComplete";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import { AnimatedElement } from "@/components/CreateProject/PageTransition";
import { getFlagImageUrl } from "@/utils/flagUtils";

interface SearchEngineFormProps {
  dataState: FieldsCreateProject;
  setDataState: React.Dispatch<React.SetStateAction<FieldsCreateProject>>;
  countriesData: any[];
  googleLanguage: any[];
  allLocation: any[];
  error: string;
  onAddTable: () => void;
  supportedLocations: Array<{
    code: string;
    name: string;
    primary_language: string;
  }>;
}

export default function SearchEngineForm({
  dataState,
  setDataState,
  countriesData,
  googleLanguage,
  allLocation,
  error,
  onAddTable,
  supportedLocations,
}: SearchEngineFormProps) {
  // Check if Bing is selected
  const isBingSelected = dataState.searchEngines?.[0]?.name === "Bing";

  // Merge supported locations with country images
  const supportedCountriesWithImages = useMemo(() => {
    if (supportedLocations.length === 0) return countriesData;

    return supportedLocations.map((location) => {
      const countryData = countriesData.find(
        (country) => country.code === location.code
      );
      return {
        ...location,
        image: countryData?.image || getFlagImageUrl(location.code),
      };
    });
  }, [supportedLocations, countriesData]);

  // Filter languages to only show those used as primary languages in supported locations
  const supportedLanguages = useMemo(() => {
    if (supportedLocations.length === 0 || googleLanguage.length === 0)
      return googleLanguage;

    const primaryLanguageCodes = [
      ...new Set(supportedLocations.map((loc) => loc.primary_language)),
    ];
    return googleLanguage.filter((lang) =>
      primaryLanguageCodes.includes(lang.code)
    );
  }, [supportedLocations, googleLanguage]);

  return (
    <>
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
          <span className="text-primary font-semibold text-sm">1</span>
        </div>
        <h3 className="text-base font-semibold text-[#344054]">
          Search Engine Configuration
        </h3>
      </div>

      {/* Form Grid */}
      <AnimatedElement variant="form" delay={0.5}>
        <div className="bg-gray-50 rounded-xl p-6 space-y-6">
          <div className="grid gap-4 items-end grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5">
            <div className="space-y-2">
              <label className="text-sm font-medium text-[#344054]">
                Search Engine
              </label>
              <SelectSearchEngine
                placeHolder="Choose engine"
                soloChoose
                classNameBox="w-full"
                classPlusButton="bg-white border-none transition-colors duration-200"
                setValue={(items: any) =>
                  setDataState((prev) => ({
                    ...prev,
                    searchEngines: items,
                  }))
                }
                value={dataState.searchEngines || []}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#344054]">
                Country
              </label>
              <AutoCompleteCountry
                placeHoldre="Select country"
                classNameBox="w-full"
                classPlusButton="bg-white border-none transition-colors duration-200"
                data={
                  supportedCountriesWithImages.length > 0
                    ? supportedCountriesWithImages
                    : countriesData
                }
                valueKey="code"
                labelKey="name"
                imageKey="image"
                value={dataState.country?.code || ""}
                setValue={(item: any) => {
                  setDataState((prev) => {
                    // Auto-set language based on supported location's primary language
                    const supportedLocation = supportedLocations.find(
                      (loc) => loc.code === item.code
                    );
                    const primaryLanguageCode =
                      supportedLocation?.primary_language;

                    let newLanguage = prev.language;
                    if (primaryLanguageCode && supportedLanguages.length > 0) {
                      const matchingLanguage = supportedLanguages.find(
                        (lang) => lang.code === primaryLanguageCode
                      );
                      if (matchingLanguage) {
                        newLanguage = matchingLanguage;
                      }
                    }

                    return {
                      ...prev,
                      country: item,
                      language: newLanguage,
                    };
                  });
                }}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#344054]">
                Language
              </label>
              <AutoComplete
                placeHoldre="Choose language"
                classNameBox="w-full"
                classPlusButton="bg-white border-none transition-colors duration-200"
                dataSelector={
                  supportedLanguages.length > 0
                    ? supportedLanguages
                    : googleLanguage
                }
                valueKey="name"
                value={dataState.language?.name || ""}
                setValue={(item: any) =>
                  setDataState((prev) => ({
                    ...prev,
                    language: item,
                  }))
                }
                option={(item: any) => <div>{item?.name}</div>}
              />
            </div>

            <div className="space-y-2">
              <label
                className={`text-sm font-medium ${
                  isBingSelected ? "text-gray-400" : "text-[#344054]"
                }`}
              >
                Location {isBingSelected && "(Not available for Bing)"}
              </label>
              <AutoComplete
                classNameBox="w-full"
                classPlusButton={`border-none transition-colors duration-200 ${
                  isBingSelected ? "bg-gray-100" : "bg-white"
                }`}
                valueKey="name"
                dataSelector={allLocation}
                setValue={(value: any) =>
                  setDataState((prev) => ({
                    ...prev,
                    location: isBingSelected ? null : value,
                  }))
                }
                value={isBingSelected ? "" : dataState.location?.name || ""}
                placeHoldre={
                  isBingSelected ? "Not available for Bing" : "Choose location"
                }
                option={(item: any) => <div>{item?.name}</div>}
                disabled={isBingSelected}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-[#344054] opacity-0">
                Action
              </label>
              <ButtenSubmit
                text="Add "
                classPluss="w-full bg-primary hover:bg-primary/90 transition-colors duration-200"
                onClick={onAddTable}
              />
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-50 border border-red-200 rounded-lg p-4"
            >
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg
                    className="w-3 h-3 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <p className="text-[#344054] text-sm font-medium">{error}</p>
              </div>
            </motion.div>
          )}
        </div>
      </AnimatedElement>
    </>
  );
}
