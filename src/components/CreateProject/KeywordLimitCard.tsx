"use client";
import React from "react";

interface KeywordLimitCardProps {
  keywordCount: number;
  searchEngineCount: number;
  countryCount: number;
  totalUsed: number;
  totalLimit: number;
  className?: string;
}

export default function KeywordLimitCard({
  keywordCount,
  searchEngineCount,
  countryCount,
  totalUsed,
  totalLimit,
  className = "",
}: KeywordLimitCardProps) {
  return (
    <div className={`mt-6 p-4 bg-purple-100 rounded-xl flex items-center justify-center gap-3 ${className}`}>
      <div className="flex-shrink-0">
        <svg
          className="w-8 h-8 text-purple-700"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
            clipRule="evenodd"
          />
        </svg>
      </div>
      <div className="flex-1">
        <h4 className="text-sm font-semibold text-purple-900 mb-1">
          Keyword Limit Usage
        </h4>
        <p className="text-sm text-purple-800">
          Keywords ({keywordCount}) × Search engines ({searchEngineCount}) × Countries ({countryCount}) ={" "}
          <span className="font-semibold">{totalUsed}</span> of{" "}
          <span className="font-semibold">{totalLimit}</span> available keyword limits will be used
        </p>
      </div>
    </div>
  );
}
