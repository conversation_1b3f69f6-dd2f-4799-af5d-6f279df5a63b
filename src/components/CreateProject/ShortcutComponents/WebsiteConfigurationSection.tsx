"use client";
import React, { memo } from "react";
import CustomInput from "../../shared/CustomInput";
import StepCounter from "../StepCounter";

interface WebsiteConfigurationSectionProps {
  register: any;
  errors: any;
  hasAttemptedSubmit: boolean;
  onNameFocus: () => void;
}

const WebsiteConfigurationSection = memo(({
  register,
  errors,
  hasAttemptedSubmit,
  onNameFocus,
}: WebsiteConfigurationSectionProps) => {
  return (
    <div className="space-y-3">
      <StepCounter stepNumber={1} title="Website Configuration" />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
        <div>
          <CustomInput
            required
            label="Domain/URL"
            placeholder="Mycompany.com"
            guideText="Enter the website address, video URL, or YouTube channel."
            error={
              hasAttemptedSubmit ? errors.domain?.message : undefined
            }
            classPlusInput="!rounded-md !p-2.5"
            {...register("domain")}
          />
        </div>
        <div>
          <CustomInput
            label="Project name (optional)"
            guideText="The name of the project in the control panel"
            error={errors.name?.message}
            placeholder="Input And Type..."
            classPlusInput="!rounded-md !p-2.5"
            onFocus={onNameFocus}
            {...register("name")}
          />
        </div>
      </div>
    </div>
  );
});

WebsiteConfigurationSection.displayName = "WebsiteConfigurationSection";

export default WebsiteConfigurationSection;
