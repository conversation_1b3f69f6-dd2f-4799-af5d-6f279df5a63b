"use client";
import React, { memo } from "react";
import { motion } from "framer-motion";
import ButtenSubmit from "../../shared/ButtenSubmit";
import { SparklesIcon } from "@/ui/icons/general/SparklesIcon";

interface ActionButtonsSectionProps {
  onAdvancedRoute: () => void;
  onFormSubmit: () => void;
  isPending: boolean;
}

const ActionButtonsSection = memo(({
  onAdvancedRoute,
  onFormSubmit,
  isPending,
}: ActionButtonsSectionProps) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between gap-3 pt-4 border-t border-gray-100">
      {/* Enhanced Advanced Project Button - Left Side */}
      <motion.button
        onClick={onAdvancedRoute}
        type="button"
        className="group relative w-full sm:w-auto bg-gradient-to-r from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 border-2 border-primary/30 hover:border-primary/60 text-primary hover:text-primary/90 font-bold text-sm py-2.5 px-4 rounded-lg transition-all duration-300 overflow-hidden"
        whileHover={{
          scale: 1.02,
          boxShadow: "0 8px 25px rgba(145, 74, 196, 0.15)",
        }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Animated background gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Button content */}
        <div className="relative z-10 flex items-center justify-center gap-2">
          <SparklesIcon className="w-3.5 h-3.5 group-hover:scale-110 transition-transform duration-300" />
          <span className="font-bold">Advanced Project Setup</span>
          <svg
            className="w-3.5 h-3.5 group-hover:translate-x-1 transition-transform duration-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 7l5 5m0 0l-5 5m5-5H6"
            />
          </svg>
        </div>

        {/* Subtle shine effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
      </motion.button>

      {/* Let's Create Button - Right Side */}
      <ButtenSubmit
        isLoading={isPending}
        onClick={onFormSubmit}
        textloading="Creating..."
        text="Let's Create"
        classPluss="w-full sm:w-auto sm:flex-shrink-0"
      />
    </div>
  );
});

ActionButtonsSection.displayName = "ActionButtonsSection";

export default ActionButtonsSection;
