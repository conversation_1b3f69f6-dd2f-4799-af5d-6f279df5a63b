"use client";
import React, { memo, useCallback, useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { MdClose } from "react-icons/md";
import { FaPlus, FaArrowLeft, FaArrowRight } from "react-icons/fa6";
import { HiPaperClip } from "react-icons/hi2";
import Image from "next/image";
import { Switch } from "../../ui/switch";
import StepCounter from "../StepCounter";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  projectAPI,
  CreateProjectRequest,
  UpdateProjectRequest,
} from "@/services/projectService";
import createProjectToast from "@/lib/createProjectToast";

interface KeywordTableRow {
  id: string;
  keyword: string;
  searchEngine: {
    name: string;
    image: string;
  };
  country: {
    name: string;
    code: string;
    image: string;
  };
  language: {
    name: string;
    code: string;
  };
  location: {
    name: string;
  } | null;
  volume: string;
  numericVolume?: number; // Numeric value for sorting
  configId: string;
  isSuggested?: boolean; // Optional flag to indicate if keyword was AI-suggested
}

interface KeywordsSectionProps {
  keywords: KeywordTableRow[];
  setKeywords: React.Dispatch<React.SetStateAction<KeywordTableRow[]>>;
  dataState: {
    country: any;
    searchEngines: any[];
    language: any;
  };
  error: string;
  setError: React.Dispatch<React.SetStateAction<string>>;
  onFileImport: (event: React.ChangeEvent<HTMLInputElement>) => void;
  domainUrl?: string; // Add domain URL for keyword extraction
  projectName?: string; // Add project name for project creation
}

const KeywordsSection = memo(
  ({
    keywords,
    setKeywords,
    dataState,
    error,
    setError,
    onFileImport,
    domainUrl,
    projectName,
  }: KeywordsSectionProps) => {
    const [keywordInput, setKeywordInput] = useState("");
    const [autoFill, setAutoFill] = useState<boolean>(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(5);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [projectId, setProjectId] = useState<string | null>(null);
    const [isProjectCreated, setIsProjectCreated] = useState<boolean>(false);
    const [lastProjectData, setLastProjectData] = useState<{
      url: string;
      name: string;
    } | null>(null);
    const initialLocationRef = useRef<{
      country: string | null;
      language: string | null;
    }>({ country: null, language: null });
    const hasInitializedLocationRef = useRef(false);

    // Calculate pagination
    const totalRows = keywords.length;
    const totalPages = Math.ceil(totalRows / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentRows = keywords.slice(startIndex, endIndex);

    // Format URL for API call
    const formatUrl = (url: string): string => {
      if (!url) return "";
      // Add https:// if no protocol is specified
      if (!url.match(/^https?:\/\//)) {
        return `https://${url}`;
      }
      return url;
    };

    // Extract clean domain for project creation
    const extractCleanDomain = (url: string): string => {
      if (!url) return "";
      return url
        .replace(/^https?:\/\//, "")
        .replace(/^www\./, "")
        .split("/")[0];
    };

    // Project creation/update mutation
    const createProjectMutation = useMutation({
      mutationFn: async (): Promise<string> => {
        if (!domainUrl?.trim() || !projectName?.trim()) {
          throw new Error("Domain URL and project name are required");
        }

        const formattedUrl = formatUrl(domainUrl);
        const cleanDomain = extractCleanDomain(domainUrl);

        if (isProjectCreated && projectId) {
          // Update existing project
          const updateData: UpdateProjectRequest = {
            url: formattedUrl,
            domain_type: `*.${cleanDomain}`,
            project_name: projectName,
            project_color: "#914AC4",
            status: "enabled",
            primary_search_engines: [],
            keywords: [],
            competitors: [],
          };

          const response = await projectAPI.updateProject(
            projectId,
            updateData
          );
          return response.data.id;
        } else {
          // Create new project
          const projectData: CreateProjectRequest = {
            url: formattedUrl,
            domain_type: `*.${cleanDomain}`,
            project_name: projectName,
            project_color: "#914AC4", // Default purple color for shortcut projects
          };

          const response = await projectAPI.createProject(projectData);
          return response.data.id;
        }
      },
      onSuccess: (returnedProjectId: string) => {
        setProjectId(returnedProjectId);
        setIsProjectCreated(true);

        // Track the current project data
        setLastProjectData({
          url: domainUrl || "",
          name: projectName || "",
        });

        if (isProjectCreated) {
          createProjectToast.success.configurationSaved();
        } else {
          createProjectToast.success.projectCreated(projectName || "Project");
        }
      },
      onError: (error: any) => {
        const context = isProjectCreated ? "update project" : "create project";
        createProjectToast.handleError(error, context);

        // Only turn off suggest slider for severe project creation errors
        const errorStatus = error.response?.status;
        const shouldDisableSlider =
          errorStatus &&
          (errorStatus === 401 || // Unauthorized
            errorStatus === 403 || // Forbidden
            errorStatus >= 500 || // Server errors
            error.message?.includes("Network Error"));

        if (shouldDisableSlider) {
          setAutoFill(false);
        }
      },
    });

    // Handle adding suggested keywords directly to table
    const handleAddSuggestedKeywords = useCallback(
      (suggestedKeywords: any[]) => {
        if (!dataState.searchEngines?.length || !dataState.country) {
          setError("Please configure search engine and country first");
          return;
        }

        // Collect new keywords to add
        const newKeywords: KeywordTableRow[] = [];

        // Process each suggested keyword
        suggestedKeywords.forEach((keywordData) => {
          // Check if keyword already exists with the same country and language combination
          const existingKeyword = keywords.find(
            (k) =>
              k.keyword.toLowerCase() ===
                (keywordData.keyword || keywordData.phrase).toLowerCase() &&
              k.country?.code === dataState.country?.code &&
              k.language?.code === dataState.language?.code
          );

          // Only add if this specific keyword + country + language combination doesn't exist
          if (!existingKeyword) {
            // Format volume display
            let volumeDisplay = "N/A";
            let numericVolume = 0;
            if (keywordData.search_volume) {
              numericVolume = keywordData.search_volume;
              if (keywordData.search_volume >= 1000) {
                volumeDisplay = `${(keywordData.search_volume / 1000).toFixed(
                  1
                )}k`;
              } else {
                volumeDisplay = keywordData.search_volume.toString();
              }
            }

            const newKeyword: KeywordTableRow = {
              id:
                Date.now().toString() +
                Math.random().toString(36).substring(2, 7) +
                (dataState.country?.code || "") +
                (dataState.language?.code || ""),
              keyword: keywordData.keyword || keywordData.phrase,
              searchEngine: dataState.searchEngines[0],
              country: dataState.country,
              language: dataState.language || { name: "English", code: "en" },
              location: dataState.country
                ? { name: dataState.country.name }
                : null,
              volume: volumeDisplay,
              numericVolume: numericVolume, // Store numeric value for sorting
              configId:
                Date.now().toString() +
                (dataState.country?.code || "") +
                (dataState.language?.code || ""),
              isSuggested: true, // Mark as suggested keyword
            };

            newKeywords.push(newKeyword);
          }
        });

        // Sort suggested keywords by search volume (highest first)
        newKeywords.sort(
          (a, b) => (b.numericVolume || 0) - (a.numericVolume || 0)
        );

        // Add sorted suggested keywords, but maintain manual keywords at the top
        if (newKeywords.length > 0) {
          setKeywords((prev) => {
            // Separate manual and suggested keywords
            const manualKeywords = prev.filter((k) => !k.isSuggested);
            const existingSuggestedKeywords = prev.filter((k) => k.isSuggested);

            // Combine: manual keywords first, then new suggested (sorted), then existing suggested (sorted)
            const allSuggestedKeywords = [
              ...newKeywords,
              ...existingSuggestedKeywords,
            ];
            allSuggestedKeywords.sort(
              (a, b) => (b.numericVolume || 0) - (a.numericVolume || 0)
            );

            return [...manualKeywords, ...allSuggestedKeywords];
          });
          // Reset to first page to show newly added keywords
          setCurrentPage(1);
        }

        setError("");
      },
      [dataState, keywords, setKeywords, setError]
    );

    // Fetch keyword suggestions - only when autoFill is enabled and project is created
    // Also refetch when location or language changes
    const {
      isLoading: isLoadingSuggestions,
      error: queryError,
      refetch: refetchSuggestions,
    } = useQuery({
      queryKey: [
        "keyword-suggestions-shortcut",
        projectId,
        autoFill,
        dataState.country?.code,
        dataState.language?.code,
      ],
      queryFn: async () => {
        if (!projectId || !autoFill) return null;

        if (!dataState.country?.code || !dataState.language?.code) {
          throw new Error("Country and language must be selected");
        }

        try {
          const response = await projectAPI.getKeywordSuggestions({
            project_id: projectId,
            location: dataState.country.code,
            language_code: dataState.language.code,
          });

          // The API returns { data: { status: "success", data: { keywords: [...], phrases: [...] } } }
          // So we need to access response.data.data to get the actual keyword data
          const actualData = (response.data as any).data || response.data;

          if (actualData) {
            // Use keywords if available, otherwise use phrases
            const suggestionsToAdd =
              actualData.keywords && actualData.keywords.length > 0
                ? actualData.keywords
                : actualData.phrases || [];

            // Convert phrases to keyword format if needed
            const formattedSuggestions = suggestionsToAdd.map((item: any) => {
              if (item.phrase) {
                // This is a phrase object, convert to keyword format
                return {
                  keyword: item.phrase,
                  search_volume: item.search_volume,
                  competition: item.competition,
                  cpc: item.cpc,
                  difficulty: item.difficulty,
                  relevance_score: item.relevance_score,
                };
              }
              // This is already a keyword object
              return item;
            });

            // Automatically add suggested keywords/phrases to the table
            if (formattedSuggestions.length > 0) {
              handleAddSuggestedKeywords(formattedSuggestions);
            }

            // Show success toast
            const totalSuggestions = formattedSuggestions.length;
            if (totalSuggestions > 0) {
              console.log(
                "Successfully added",
                totalSuggestions,
                "keyword suggestions"
              );
              createProjectToast.success.keywordsSuggested(totalSuggestions);
            }

            return actualData;
          }

          return null;
        } catch (error) {
          console.error("Keyword suggestions API error:", error);

          // Handle API errors with toast notifications
          createProjectToast.handleError(
            error as any,
            "get keyword suggestions"
          );

          // Only turn off suggest slider for severe errors (network, auth, server errors)
          const errorStatus = (error as any).response?.status;
          const shouldDisableSlider =
            errorStatus &&
            (errorStatus === 401 || // Unauthorized
              errorStatus === 403 || // Forbidden
              errorStatus >= 500 || // Server errors
              (error as any).message?.includes("Network Error"));

          if (shouldDisableSlider) {
            console.log(
              "Disabling suggest slider due to severe error:",
              errorStatus
            );
            setAutoFill(false);
          }

          throw error;
        }
      },
      enabled:
        !!projectId &&
        autoFill &&
        !!dataState.country?.code &&
        !!dataState.language?.code,
      refetchOnWindowFocus: false, // Don't refetch when window regains focus
      refetchOnReconnect: false, // Don't refetch on network reconnect
    });

    // Effect to create/update project when autoFill is enabled
    useEffect(() => {
      if (
        autoFill &&
        !createProjectMutation.isPending &&
        domainUrl?.trim() &&
        projectName?.trim() &&
        dataState.country?.code &&
        dataState.language?.code
      ) {
        // Check if project data has changed
        const hasDataChanged =
          lastProjectData &&
          (lastProjectData.url !== domainUrl ||
            lastProjectData.name !== projectName);

        // Create project if not created, or update if project details changed
        if (!isProjectCreated || !projectId || hasDataChanged) {
          createProjectMutation.mutate();
        }
      }
    }, [
      autoFill,
      projectId,
      isProjectCreated,
      lastProjectData,
      createProjectMutation,
      domainUrl,
      projectName,
      dataState.country?.code,
      dataState.language?.code,
    ]);

    // Reset project state when autoFill is disabled
    useEffect(() => {
      if (!autoFill && (projectId || isProjectCreated)) {
        setProjectId(null);
        setIsProjectCreated(false);
        setLastProjectData(null);
        // Reset location tracking
        hasInitializedLocationRef.current = false;
        initialLocationRef.current = { country: null, language: null };
      }
    }, [autoFill, projectId, isProjectCreated]);

    // Log query errors for debugging (but don't auto-disable slider)
    useEffect(() => {
      if (queryError) {
        console.error("Keyword suggestions query error:", queryError);
      }
    }, [queryError]);

    // Automatically refetch suggestions when location or language changes
    // while suggest is enabled and project exists
    useEffect(() => {
      const currentCountry = dataState.country?.code || null;
      const currentLanguage = dataState.language?.code || null;

      // Initialize the refs on first run
      if (!hasInitializedLocationRef.current) {
        initialLocationRef.current = {
          country: currentCountry,
          language: currentLanguage,
        };
        hasInitializedLocationRef.current = true;
        return;
      }

      // Check if location or language actually changed
      const countryChanged =
        initialLocationRef.current.country !== currentCountry;
      const languageChanged =
        initialLocationRef.current.language !== currentLanguage;
      const hasChanged = countryChanged || languageChanged;

      if (
        hasChanged &&
        autoFill &&
        projectId &&
        isProjectCreated &&
        !createProjectMutation.isPending &&
        !isLoadingSuggestions &&
        currentCountry &&
        currentLanguage
      ) {
        console.log(
          "Location/language changed - refetching keyword suggestions",
          {
            countryChanged: countryChanged
              ? `${initialLocationRef.current.country} → ${currentCountry}`
              : false,
            languageChanged: languageChanged
              ? `${initialLocationRef.current.language} → ${currentLanguage}`
              : false,
          }
        );

        // Update the refs with new values
        initialLocationRef.current = {
          country: currentCountry,
          language: currentLanguage,
        };

        // Small delay to avoid rapid successive calls
        const timeoutId = setTimeout(() => {
          refetchSuggestions();
        }, 300);

        return () => clearTimeout(timeoutId);
      }
    }, [
      dataState.country?.code,
      dataState.language?.code,
      autoFill,
      projectId,
      isProjectCreated,
      createProjectMutation.isPending,
      isLoadingSuggestions,
      refetchSuggestions,
    ]);

    const handleAddKeyword = useCallback(() => {
      if (!keywordInput.trim()) {
        setError("Please enter a keyword");
        return;
      }

      if (!dataState.searchEngines?.length) {
        setError("Please select a search engine");
        return;
      }

      if (!dataState.country) {
        setError("Please select a country");
        return;
      }

      // Check if keyword already exists with the same country and language combination
      const existingKeyword = keywords.find(
        (k) =>
          k.keyword.toLowerCase() === keywordInput.trim().toLowerCase() &&
          k.country?.code === dataState.country?.code &&
          k.language?.code === dataState.language?.code
      );

      if (existingKeyword) {
        setError(
          "This keyword has already been added for this country and language combination"
        );
        return;
      }

      const newKeyword: KeywordTableRow = {
        id:
          Date.now().toString() +
          Math.random().toString(36).substring(2, 7) +
          (dataState.country?.code || "") +
          (dataState.language?.code || ""),
        keyword: keywordInput.trim(),
        searchEngine: dataState.searchEngines[0],
        country: dataState.country,
        language: dataState.language || { name: "English", code: "en" },
        location: dataState.country ? { name: dataState.country.name } : null,
        volume: "N/A",
        numericVolume: 0, // Manual keywords have 0 volume for sorting
        configId:
          Date.now().toString() +
          (dataState.country?.code || "") +
          (dataState.language?.code || ""),
        isSuggested: false, // Explicitly mark as manual keyword
      };

      setKeywords((prev) => [newKeyword, ...prev]);
      setKeywordInput("");
      setError("");
      // Reset to first page to show newly added keyword
      setCurrentPage(1);
    }, [keywordInput, dataState, keywords, setKeywords, setError]);

    const handleRemoveKeyword = useCallback(
      (id: string) => {
        setKeywords((prev) => {
          // Filter out the removed keyword
          const newKeywords = prev.filter((k) => k.id !== id);

          // Maintain the separation between manual and suggested keywords
          const manualKeywords = newKeywords.filter((k) => !k.isSuggested);
          const suggestedKeywords = newKeywords.filter((k) => k.isSuggested);

          // Sort suggested keywords by search volume (highest first)
          suggestedKeywords.sort(
            (a, b) => (b.numericVolume || 0) - (a.numericVolume || 0)
          );

          // Update pagination if needed
          const newTotalRows = newKeywords.length;
          const newTotalPages = Math.ceil(newTotalRows / itemsPerPage);
          if (currentPage > newTotalPages && newTotalPages > 0) {
            setCurrentPage(newTotalPages);
          }

          // Return manual keywords first, then suggested keywords
          return [...manualKeywords, ...suggestedKeywords];
        });
        setError("");
      },
      [setKeywords, currentPage, itemsPerPage]
    );

    const handleFileImportClick = useCallback(() => {
      fileInputRef.current?.click();
    }, []);

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === "Enter") {
          e.preventDefault();
          handleAddKeyword();
        }
      },
      [handleAddKeyword]
    );

    return (
      <div className="space-y-3 pt-3 border-t border-gray-100">
        <StepCounter stepNumber={3} title="Keywords" />

        {/* Error Display */}
        {error && (
          <div className="mb-3 p-2.5 bg-red-50 border border-red-200 rounded-md">
            <p className="text-[#344054] text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-2.5">
          <p className="text-[#344054] text-sm leading-relaxed">
            Add keywords manually or copy and paste them below from a text
            editor.
          </p>

          {/* Keywords Input Section */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center gap-2.5 mb-3">
              <button
                type="button"
                onClick={handleFileImportClick}
                title="Import keywords from file (supports .txt, .csv, .xlsx, .xls)"
                className="flex items-center justify-center w-10 h-10 bg-primary hover:bg-primary/85 rounded-md hover:border-primary/50 transition-colors"
              >
                <HiPaperClip size={19} className="text-white" />
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept=".txt,.csv,.xlsx,.xls"
                onChange={onFileImport}
                className="hidden"
              />
              <div className="relative flex-1 bg-white rounded-md">
                <input
                  type="text"
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Enter a keyword..."
                  className="text-sm focus:ring-primary focus:border-primary p-2 w-full rounded-sm transition-colors border border-gray-200"
                />
                <button
                  type="button"
                  onClick={handleAddKeyword}
                  disabled={!keywordInput.trim()}
                  className="absolute z-20 focus:bg-primary/80 hover:bg-primary/80 right-1.5 w-8 h-8 flex items-center justify-center rounded-full bg-primary transform cursor-pointer text-white -translate-y-1/2 top-1/2 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  <FaPlus size={14} />
                </button>
              </div>
              <div className="flex items-center gap-1.5">
                <Switch checked={autoFill} onCheckedChange={setAutoFill} />
                <span className="text-sm font-medium text-[#344054]">
                  Suggest
                </span>
              </div>
            </div>

            {/* Keywords Table */}
            <div className="flex flex-col gap-1 mt-3 p-1 h-[330px] overflow-y-auto mb-2">
              {autoFill &&
              (createProjectMutation.isPending || isLoadingSuggestions) ? (
                <div className="flex items-center justify-center h-full">
                  <div className="flex flex-col items-center gap-3">
                    <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-primary text-sm">
                      {createProjectMutation.isPending
                        ? isProjectCreated
                          ? "Updating project..."
                          : "Creating project..."
                        : "Analyzing domain for keyword suggestions..."}
                    </span>
                  </div>
                </div>
              ) : totalRows > 0 ? (
                <AnimatePresence mode="popLayout">
                  {currentRows.map((row, index) => (
                    <motion.div
                      key={row.id}
                      initial={{ opacity: 0, scale: 0.95, y: 10 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      transition={{ duration: 0.2 }}
                      className="bg-white rounded-md py-2 px-3 flex justify-between items-center select-none"
                      style={{
                        userSelect: "none",
                        WebkitUserSelect: "none",
                        MozUserSelect: "none",
                        msUserSelect: "none",
                      }}
                      onMouseDown={(e) => e.preventDefault()}
                      onSelectStart={(e) => e.preventDefault()}
                      onDragStart={(e) => e.preventDefault()}
                    >
                      <div className="flex gap-3 items-center">
                        <span
                          className="text-xs w-6 min-w-6 max-w-6 text-right select-none"
                          style={{
                            userSelect: "none",
                            WebkitUserSelect: "none",
                            MozUserSelect: "none",
                            msUserSelect: "none",
                          }}
                          onMouseDown={(e) => e.preventDefault()}
                          onSelectStart={(e) => e.preventDefault()}
                          onDragStart={(e) => e.preventDefault()}
                        >
                          {startIndex + index + 1}.
                        </span>
                        <div className="flex items-center mr-5 w-48 min-w-48 max-w-48">
                          <span className="truncate text-xs w-32 min-w-32 max-w-32 block">
                            {row.keyword}
                          </span>
                          <div className="w-20 min-w-20 max-w-20 flex items-center justify-start ml-2">
                            {row.isSuggested && (
                              <>
                                <div className="flex items-center gap-1 text-primary bg-primary/10 py-1 px-2 rounded-full">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    strokeWidth={1.5}
                                    stroke="currentColor"
                                    className="size-3"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"
                                    />
                                  </svg>
                                  <span className="text-xs font-medium whitespace-nowrap">
                                    Suggest
                                  </span>
                                </div>
                                <span className="text-gray-300 text-xs ml-2">
                                  •
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                        {row.searchEngine && (
                          <figure className="py-1 px-2 bg-gray-200 rounded-full">
                            <Image
                              src={row.searchEngine.image}
                              alt="search engine"
                              width={20}
                              height={20}
                            />
                          </figure>
                        )}
                        {row.country && (
                          <figure className="py-1 px-2 bg-gray-200 rounded-full">
                            <Image
                              src={row.country.image}
                              alt="country"
                              width={24}
                              height={24}
                            />
                          </figure>
                        )}
                      </div>
                      <div className="flex gap-2 ml-2 items-center">
                        {row.language && (
                          <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded mr-2">
                            {row.language.code.toUpperCase()}
                          </span>
                        )}
                        {row.volume && (
                          <span className="text-xs text-gray-500 mr-2 w-8 min-w-8 max-w-8 text-right">
                            {row.volume}
                          </span>
                        )}
                        <button
                          type="button"
                          title="Delete keyword"
                          className="select-none"
                          style={{
                            userSelect: "none",
                            WebkitUserSelect: "none",
                            MozUserSelect: "none",
                            msUserSelect: "none",
                          }}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                          }}
                          onSelectStart={(e) => e.preventDefault()}
                          onDragStart={(e) => e.preventDefault()}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleRemoveKeyword(row.id);
                          }}
                        >
                          <MdClose size={16} />
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500 text-sm">
                  No keywords added yet. Start by entering a keyword above.
                </div>
              )}
            </div>

            {/* Pagination Box */}
            <div className="grid grid-cols-3 items-center">
              <div className="text-left">
                <span className="text-sm text-purple-800 font-medium bg-purple-100 px-2.5 py-1 rounded-md">
                  {totalRows} of 750
                </span>
              </div>
              <div className="flex gap-1 items-center justify-center">
                {totalRows > 0 && totalPages > 1 && (
                  <>
                    {/* Previous Button */}
                    <button
                      type="button"
                      className={`w-6 h-6 min-w-6 min-h-6 rounded-full border-2 border-pagination flex items-center justify-center flex-shrink-0 transition-all duration-200 ease-in-out ${
                        currentPage === 1
                          ? "opacity-40 cursor-not-allowed text-pagination"
                          : "cursor-pointer text-pagination hover:text-gray-800 hover:bg-gray-200 hover:border-gray-600 hover:scale-105 active:scale-95"
                      }`}
                      onClick={() =>
                        currentPage > 1 && setCurrentPage(currentPage - 1)
                      }
                      disabled={currentPage === 1}
                    >
                      <FaArrowLeft size={10} />
                    </button>

                    {/* Page Numbers - Show 4 pages at a time */}
                    <div className="flex items-center gap-1">
                      {(() => {
                        const pages = [];
                        const maxVisiblePages = 4;

                        if (totalPages <= maxVisiblePages) {
                          // Show all pages if total is 4 or less
                          for (let i = 1; i <= totalPages; i++) {
                            pages.push(
                              <button
                                key={i}
                                type="button"
                                className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                                  i === currentPage
                                    ? "text-black font-semibold"
                                    : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                                }`}
                                onClick={() => setCurrentPage(i)}
                              >
                                {i}
                              </button>
                            );
                          }
                        } else {
                          // Determine which pages to show based on current page
                          if (currentPage <= maxVisiblePages - 1) {
                            // Show first 4 pages + ellipsis + last page
                            for (let i = 1; i <= maxVisiblePages; i++) {
                              pages.push(
                                <button
                                  key={i}
                                  type="button"
                                  className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                                    i === currentPage
                                      ? "text-black font-semibold"
                                      : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                                  }`}
                                  onClick={() => setCurrentPage(i)}
                                >
                                  {i}
                                </button>
                              );
                            }
                            pages.push(
                              <span
                                key="ellipsis"
                                className="text-xs text-gray-500 opacity-60 px-1"
                              >
                                ...
                              </span>
                            );
                            pages.push(
                              <button
                                key={totalPages}
                                type="button"
                                className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                                  totalPages === currentPage
                                    ? "text-black font-semibold"
                                    : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                                }`}
                                onClick={() => setCurrentPage(totalPages)}
                              >
                                {totalPages}
                              </button>
                            );
                          } else if (
                            currentPage >=
                            totalPages - maxVisiblePages + 2
                          ) {
                            // Show first page + ellipsis + last 4 pages
                            pages.push(
                              <button
                                key={1}
                                type="button"
                                className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                                  1 === currentPage
                                    ? "text-black font-semibold"
                                    : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                                }`}
                                onClick={() => setCurrentPage(1)}
                              >
                                1
                              </button>
                            );
                            pages.push(
                              <span
                                key="ellipsis"
                                className="text-xs text-gray-500 opacity-60 px-1"
                              >
                                ...
                              </span>
                            );
                            for (
                              let i = totalPages - maxVisiblePages + 1;
                              i <= totalPages;
                              i++
                            ) {
                              pages.push(
                                <button
                                  key={i}
                                  type="button"
                                  className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                                    i === currentPage
                                      ? "text-black font-semibold"
                                      : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                                  }`}
                                  onClick={() => setCurrentPage(i)}
                                >
                                  {i}
                                </button>
                              );
                            }
                          } else {
                            // Show first page + ellipsis + current page range + ellipsis + last page
                            pages.push(
                              <button
                                key={1}
                                type="button"
                                className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                                  1 === currentPage
                                    ? "text-black font-semibold"
                                    : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                                }`}
                                onClick={() => setCurrentPage(1)}
                              >
                                1
                              </button>
                            );
                            pages.push(
                              <span
                                key="ellipsis1"
                                className="text-xs text-gray-500 opacity-60 px-1"
                              >
                                ...
                              </span>
                            );
                            for (
                              let i = currentPage - 1;
                              i <= currentPage + 1;
                              i++
                            ) {
                              pages.push(
                                <button
                                  key={i}
                                  type="button"
                                  className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                                    i === currentPage
                                      ? "text-black font-semibold"
                                      : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                                  }`}
                                  onClick={() => setCurrentPage(i)}
                                >
                                  {i}
                                </button>
                              );
                            }
                            pages.push(
                              <span
                                key="ellipsis2"
                                className="text-xs text-gray-500 opacity-60 px-1"
                              >
                                ...
                              </span>
                            );
                            pages.push(
                              <button
                                key={totalPages}
                                type="button"
                                className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                                  totalPages === currentPage
                                    ? "text-black font-semibold"
                                    : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
                                }`}
                                onClick={() => setCurrentPage(totalPages)}
                              >
                                {totalPages}
                              </button>
                            );
                          }
                        }

                        return pages;
                      })()}
                    </div>

                    {/* Next Button */}
                    <button
                      type="button"
                      className={`w-6 h-6 min-w-6 min-h-6 rounded-full border-2 border-pagination flex items-center justify-center flex-shrink-0 transition-all duration-200 ease-in-out ${
                        currentPage === totalPages
                          ? "opacity-40 cursor-not-allowed text-pagination"
                          : "cursor-pointer text-pagination hover:text-gray-800 hover:bg-gray-200 hover:border-gray-600 hover:scale-105 active:scale-95"
                      }`}
                      onClick={() =>
                        currentPage < totalPages &&
                        setCurrentPage(currentPage + 1)
                      }
                      disabled={currentPage === totalPages}
                    >
                      <FaArrowRight size={10} />
                    </button>
                  </>
                )}
              </div>
              <div className="text-right flex justify-end">
                {/* Import button moved to before input field */}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

KeywordsSection.displayName = "KeywordsSection";

export default KeywordsSection;
