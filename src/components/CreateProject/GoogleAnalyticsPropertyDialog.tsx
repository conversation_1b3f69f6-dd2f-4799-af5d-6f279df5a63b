"use client";

import React, { useState, useEffect } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import * as Select from "@radix-ui/react-select";
import {
  ChevronDownIcon,
  CheckIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { XMarkIcon } from "@heroicons/react/24/solid";
import { LogosGoogleAnalytics } from "@/components/icons/LogosGoogleAnalytics";

import { useMutation, useQuery } from "@tanstack/react-query";
import {
  projectAPI,
  GoogleAnalyticsProperty,
  GoogleAnalyticsPropertiesResponse,
  GoogleAnalyticsPropertySelectRequest,
} from "@/services/projectService";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "react-hot-toast";

interface GoogleAnalyticsPropertyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  onPropertySelected?: (property: GoogleAnalyticsProperty) => void;
  testingMode?: boolean;
}

export default function GoogleAnalyticsPropertyDialog({
  isOpen,
  onClose,
  projectId,
  onPropertySelected,
  testingMode = false,
}: GoogleAnalyticsPropertyDialogProps) {
  const [selectedProperty, setSelectedProperty] = useState<string>("");

  // Mock data for testing
  const mockPropertiesData: GoogleAnalyticsPropertiesResponse = {
    status: "success",
    properties: [
      {
        property_id: "*********",
        display_name: "My Website - GA4",
        website_url: "https://mywebsite.com",
      },
      {
        property_id: "*********",
        display_name: "E-commerce Store Analytics",
        website_url: "https://store.example.com",
      },
      {
        property_id: "*********",
        display_name: "Blog Analytics Property",
        website_url: "https://blog.example.com",
      },
      {
        property_id: "*********",
        display_name: "Company Website GA4",
        website_url: "https://company.example.com",
      },
      {
        property_id: "*********",
        display_name: "Portfolio Site Analytics",
      },
    ],
  };

  // Fetch Google Analytics properties
  const {
    data: propertiesData,
    isLoading: propertiesLoading,
    error: propertiesError,
    refetch: refetchProperties,
  } = useQuery({
    queryKey: ["google-analytics-properties", projectId, testingMode],
    queryFn: async () => {
      if (testingMode) {
        // Simulate API delay for testing
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return mockPropertiesData;
      }
      const response = await projectAPI.getGoogleAnalyticsProperties(projectId);
      return response.data;
    },
    enabled: isOpen && (!!projectId || testingMode),
    retry: testingMode ? 0 : 3,
    retryDelay: 1000,
  });

  // Handle property selection submission
  const { mutate: selectProperty, isPending: isSelecting } = useMutation({
    mutationFn: async (propertyId: string) => {
      if (testingMode) {
        // Simulate API delay for testing
        await new Promise((resolve) => setTimeout(resolve, 800));
        return {
          response: {
            status: "success",
            message: "Property selected successfully",
          },
          selectedProperty: propertiesData?.properties.find(
            (prop) => prop.property_id === propertyId
          ),
        };
      }

      const selectedProperty = propertiesData?.properties.find(
        (prop) => prop.property_id === propertyId
      );

      if (!selectedProperty) {
        throw new Error("Selected property not found");
      }

      const data: GoogleAnalyticsPropertySelectRequest = {
        project_id: projectId,
        property_id: propertyId,
        property_name: selectedProperty.property_name,
      };

      const response = await projectAPI.selectGoogleAnalyticsProperty(data);
      return {
        response: response.data,
        selectedProperty,
      };
    },
    onSuccess: ({ selectedProperty, response }) => {
      // Show success toast
      toast.success(
        response?.message || "Google Analytics property selected successfully!"
      );

      if (selectedProperty && onPropertySelected) {
        onPropertySelected(selectedProperty);
      }
      onClose();
    },
    onError: (error: any) => {
      console.error("Error selecting property:", error);

      // Extract error message from API response
      let errorMessage = "Failed to select Google Analytics property";

      if (error?.response?.data?.error_message) {
        errorMessage = error.response.data.error_message;
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Show error toast
      toast.error(errorMessage);
    },
  });

  const handleSubmit = () => {
    if (selectedProperty) {
      selectProperty(selectedProperty);
    }
  };

  const handleRetry = () => {
    refetchProperties();
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl shadow-2xl border border-gray-200 w-full max-w-md mx-4 z-50 overflow-visible">
          <AnimatePresence>
            {isOpen && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="p-6"
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <LogosGoogleAnalytics className="w-5 h-5" />
                    </div>
                    <Dialog.Title className="text-lg font-semibold text-[#344054]">
                      Select Google Analytics Property
                    </Dialog.Title>
                  </div>
                  <Dialog.Close asChild>
                    <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                      <XMarkIcon className="w-5 h-5 text-gray-500" />
                    </button>
                  </Dialog.Close>
                </div>

                {/* Content */}
                <div className="space-y-6">
                  {/* Description */}
                  <Dialog.Description className="text-sm text-[#344054] leading-relaxed">
                    Choose which Google Analytics property to connect to your
                    project. This will enable advanced reporting and data
                    synchronization.
                  </Dialog.Description>

                  {propertiesLoading && (
                    <div className="flex items-center justify-center py-8">
                      <div className="flex items-center gap-3">
                        <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-sm text-[#344054]">
                          Loading properties...
                        </span>
                      </div>
                    </div>
                  )}

                  {propertiesError && (
                    <div className="text-center py-8">
                      <div className="mb-4">
                        <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <XMarkIcon className="w-6 h-6 text-red-600" />
                        </div>
                        <h3 className="text-sm font-medium text-[#344054] mb-2">
                          Failed to load properties
                        </h3>
                        <p className="text-xs text-gray-600">
                          Unable to fetch your Google Analytics properties
                        </p>
                      </div>
                      <button
                        onClick={handleRetry}
                        className="px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary/90 transition-colors"
                      >
                        Try Again
                      </button>
                    </div>
                  )}

                  {propertiesData && propertiesData.properties && (
                    <>
                      {propertiesData.properties.length > 0 && (
                        <div className="space-y-4">
                          {/* Property Selection Header */}
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                              <LogosGoogleAnalytics className="w-4 h-4" />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-[#344054]">
                                Available Properties
                              </label>
                              <p className="text-xs text-gray-500 mt-0.5">
                                Choose which property to connect to your project
                              </p>
                            </div>
                          </div>

                          {/* Enhanced Property Selector */}
                          <div className="bg-gray-50 rounded-lg p-4">
                            <Select.Root
                              value={selectedProperty}
                              onValueChange={setSelectedProperty}
                            >
                              <Select.Trigger
                                className="w-full flex items-center justify-between px-4 py-3 border border-gray-200 rounded-md bg-white hover:border-primary/50 focus:border-primary focus:ring-2 focus:ring-primary/20 focus:outline-none transition-all duration-200 shadow-sm data-[state=open]:border-primary data-[state=open]:ring-2 data-[state=open]:ring-primary/20"
                                aria-label="Select Google Analytics property"
                              >
                                <Select.Value
                                  placeholder="Select a Google Analytics property..."
                                  className="text-sm text-[#344054] truncate"
                                />
                                <Select.Icon asChild>
                                  <ChevronDownIcon className="w-4 h-4 text-gray-500 transition-transform duration-200 data-[state=open]:rotate-180" />
                                </Select.Icon>
                              </Select.Trigger>

                              <Select.Portal container={document.body}>
                                <Select.Content
                                  className="bg-white border border-gray-200 rounded-md shadow-lg max-h-72 overflow-hidden min-w-[var(--radix-select-trigger-width)] w-[var(--radix-select-trigger-width)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2"
                                  style={{ zIndex: 10000 }}
                                  position="popper"
                                  side="bottom"
                                  align="start"
                                  sideOffset={4}
                                  avoidCollisions={false}
                                >
                                  <Select.ScrollUpButton className="flex items-center justify-center h-6 bg-white text-gray-400 cursor-default">
                                    <ChevronDownIcon className="w-4 h-4 rotate-180" />
                                  </Select.ScrollUpButton>
                                  <Select.Viewport className="p-1">
                                    {propertiesData.properties.map(
                                      (property) => (
                                        <Select.Item
                                          key={property.property_id}
                                          value={property.property_id}
                                          className="relative flex items-center justify-between px-3 py-2.5 text-sm text-[#344054] hover:bg-primary/5 hover:text-primary rounded-md cursor-pointer outline-none data-[highlighted]:bg-primary/5 data-[highlighted]:text-primary data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary focus:bg-primary/5 focus:text-primary transition-all duration-150 select-none"
                                        >
                                          <Select.ItemText asChild>
                                            <div className="flex-1 min-w-0 text-left">
                                              <div className="font-medium text-[#344054] truncate text-left text-sm">
                                                {property.display_name}
                                              </div>
                                              {property.website_url && (
                                                <div className="text-xs text-gray-500 mt-0.5 truncate text-left">
                                                  {property.website_url}
                                                </div>
                                              )}
                                              <div className="text-xs text-gray-400 mt-0.5 text-left">
                                                ID: {property.property_id}
                                              </div>
                                            </div>
                                          </Select.ItemText>

                                          {/* Selection Indicator */}
                                          <Select.ItemIndicator className="flex-shrink-0 ml-2">
                                            <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center animate-in zoom-in-75 duration-200">
                                              <CheckIcon className="w-2.5 h-2.5 text-white" />
                                            </div>
                                          </Select.ItemIndicator>
                                        </Select.Item>
                                      )
                                    )}
                                  </Select.Viewport>
                                  <Select.ScrollDownButton className="flex items-center justify-center h-6 bg-white text-gray-400 cursor-default">
                                    <ChevronDownIcon className="w-4 h-4" />
                                  </Select.ScrollDownButton>
                                </Select.Content>
                              </Select.Portal>
                            </Select.Root>

                            {/* Property Count Info */}
                            <div className="mt-3 text-xs text-gray-500">
                              <span>
                                {propertiesData.properties.length}{" "}
                                {propertiesData.properties.length === 1
                                  ? "property"
                                  : "properties"}{" "}
                                available
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      {propertiesData.properties.length === 0 && (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <ExclamationTriangleIcon className="w-8 h-8 text-gray-400" />
                          </div>
                          <h3 className="text-sm font-medium text-[#344054] mb-2">
                            No properties found
                          </h3>
                          <p className="text-xs text-gray-600 max-w-sm mx-auto">
                            No Google Analytics properties are available for
                            your account. Make sure you have GA4 properties set
                            up in your Google Analytics account.
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* Footer */}
                {propertiesData &&
                  propertiesData.properties &&
                  propertiesData.properties.length > 0 && (
                    <div className="flex gap-3 mt-6 pt-4 border-t border-gray-100">
                      <Dialog.Close asChild>
                        <button className="flex-1 px-4 py-2 border border-gray-200 text-[#344054] text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors">
                          Cancel
                        </button>
                      </Dialog.Close>
                      <button
                        onClick={handleSubmit}
                        disabled={!selectedProperty || isSelecting}
                        className="flex-1 px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                      >
                        {isSelecting && (
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        )}
                        {isSelecting ? "Connecting..." : "Connect Property"}
                      </button>
                    </div>
                  )}
              </motion.div>
            )}
          </AnimatePresence>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
