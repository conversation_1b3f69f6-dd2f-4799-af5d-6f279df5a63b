import React from "react";
import { Fa<PERSON>rrowLeft, FaArrowRight } from "react-icons/fa6";
type PaginationTableType = {
  currentPage: number;
  limitPage: number;
  total: number;
  setCurrentPage: (val: number) => void;
};

export default function PaginationTable({
  setCurrentPage,
  currentPage,
  limitPage,
  total,
}: PaginationTableType) {
  const totalPages = Math.ceil(total / limitPage);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const renderPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 4;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is 4 or less
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <button
            key={i}
            type="button"
            className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
              i === currentPage
                ? "text-black font-semibold"
                : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
            }`}
            onClick={() => handlePageChange(i)}
          >
            {i}
          </button>
        );
      }
    } else {
      // Determine which pages to show based on current page
      if (currentPage <= maxVisiblePages - 1) {
        // Show first 4 pages + ellipsis + last page
        for (let i = 1; i <= maxVisiblePages; i++) {
          pages.push(
            <button
              key={i}
              type="button"
              className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                i === currentPage
                  ? "text-black font-semibold"
                  : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
              }`}
              onClick={() => handlePageChange(i)}
            >
              {i}
            </button>
          );
        }
        pages.push(
          <span
            key="ellipsis"
            className="text-xs text-gray-500 opacity-60 px-1"
          >
            ...
          </span>
        );
        pages.push(
          <button
            key={totalPages}
            type="button"
            className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
              totalPages === currentPage
                ? "text-black font-semibold"
                : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
            }`}
            onClick={() => handlePageChange(totalPages)}
          >
            {totalPages}
          </button>
        );
      } else if (currentPage >= totalPages - maxVisiblePages + 2) {
        // Show first page + ellipsis + last 4 pages
        pages.push(
          <button
            key={1}
            type="button"
            className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
              1 === currentPage
                ? "text-black font-semibold"
                : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
            }`}
            onClick={() => handlePageChange(1)}
          >
            1
          </button>
        );
        pages.push(
          <span
            key="ellipsis"
            className="text-xs text-gray-500 opacity-60 px-1"
          >
            ...
          </span>
        );
        for (let i = totalPages - maxVisiblePages + 1; i <= totalPages; i++) {
          pages.push(
            <button
              key={i}
              type="button"
              className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                i === currentPage
                  ? "text-black font-semibold"
                  : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
              }`}
              onClick={() => handlePageChange(i)}
            >
              {i}
            </button>
          );
        }
      } else {
        // Show first page + ellipsis + current page range + ellipsis + last page
        pages.push(
          <button
            key={1}
            type="button"
            className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
              1 === currentPage
                ? "text-black font-semibold"
                : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
            }`}
            onClick={() => handlePageChange(1)}
          >
            1
          </button>
        );
        pages.push(
          <span
            key="ellipsis1"
            className="text-xs text-gray-500 opacity-60 px-1"
          >
            ...
          </span>
        );
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(
            <button
              key={i}
              type="button"
              className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
                i === currentPage
                  ? "text-black font-semibold"
                  : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
              }`}
              onClick={() => handlePageChange(i)}
            >
              {i}
            </button>
          );
        }
        pages.push(
          <span
            key="ellipsis2"
            className="text-xs text-gray-500 opacity-60 px-1"
          >
            ...
          </span>
        );
        pages.push(
          <button
            key={totalPages}
            type="button"
            className={`w-6 h-6 p-1 rounded-full flex items-center justify-center text-xs select-none ${
              totalPages === currentPage
                ? "text-black font-semibold"
                : "text-gray-500 opacity-60 hover:opacity-80 hover:text-gray-700"
            }`}
            onClick={() => handlePageChange(totalPages)}
          >
            {totalPages}
          </button>
        );
      }
    }

    return pages;
  };
  return (
    <div className="flex gap-1 items-center justify-center">
      {total > 0 && totalPages > 1 ? (
        <>
          {/* Previous Button */}
          <button
            type="button"
            className={`w-6 h-6 min-w-6 min-h-6 rounded-full border-2 border-pagination flex items-center justify-center flex-shrink-0 transition-all duration-200 ease-in-out ${
              currentPage === 1
                ? "opacity-40 cursor-not-allowed text-pagination"
                : "cursor-pointer text-pagination hover:text-gray-800 hover:bg-gray-200 hover:border-gray-600 hover:scale-105 active:scale-95"
            }`}
            onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <FaArrowLeft size={10} />
          </button>

          {/* Page Numbers - Show 4 pages at a time */}
          <div className="flex items-center gap-1">{renderPageNumbers()}</div>

          {/* Next Button */}
          <button
            type="button"
            className={`w-6 h-6 min-w-6 min-h-6 rounded-full border-2 border-pagination flex items-center justify-center flex-shrink-0 transition-all duration-200 ease-in-out ${
              currentPage === totalPages
                ? "opacity-40 cursor-not-allowed text-pagination"
                : "cursor-pointer text-pagination hover:text-gray-800 hover:bg-gray-200 hover:border-gray-600 hover:scale-105 active:scale-95"
            }`}
            onClick={() =>
              currentPage < totalPages && handlePageChange(currentPage + 1)
            }
            disabled={currentPage === totalPages}
          >
            <FaArrowRight size={10} />
          </button>
        </>
      ) : null}
    </div>
  );
}
