"use client";

import React, { useState } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import * as Select from "@radix-ui/react-select";
import {
  XMarkIcon,
  ChevronDownIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { LogosGoogleSearchConsole } from "@/components/icons/LogosGoogleSearchConsole";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  projectAPI,
  GoogleSearchConsoleProperty,
  GoogleSearchConsolePropertiesResponse,
  GoogleSearchConsolePropertySelectRequest,
} from "@/services/projectService";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "react-hot-toast";

interface GoogleSearchConsolePropertyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  onPropertySelected?: (property: GoogleSearchConsoleProperty) => void;
  testingMode?: boolean;
}

export default function GoogleSearchConsolePropertyDialog({
  isOpen,
  onClose,
  projectId,
  onPropertySelected,
  testingMode = false,
}: GoogleSearchConsolePropertyDialogProps) {
  const [selectedProperty, setSelectedProperty] = useState<string>("");

  // Mock data for testing
  const mockPropertiesData: GoogleSearchConsolePropertiesResponse = {
    status: "success",
    properties: [
      {
        property_url: "sc-domain:example.com",
        property_type: "domain",
        permission_level: "owner",
      },
      {
        property_url: "https://www.example.com/",
        property_type: "url_prefix",
        permission_level: "owner",
      },
      {
        property_url: "sc-domain:mystore.com",
        property_type: "domain",
        permission_level: "full",
      },
      {
        property_url: "https://blog.example.com/",
        property_type: "url_prefix",
        permission_level: "restricted",
      },
    ],
  };

  // Fetch Google Search Console properties
  const {
    data: propertiesData,
    isLoading: propertiesLoading,
    error: propertiesError,
    refetch: refetchProperties,
  } = useQuery({
    queryKey: ["google-search-console-properties", projectId, testingMode],
    queryFn: async () => {
      if (testingMode) {
        // Simulate API delay for testing
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return mockPropertiesData;
      }
      const response = await projectAPI.getGoogleSearchConsoleProperties(
        projectId
      );
      return response.data;
    },
    enabled: isOpen && (!!projectId || testingMode),
    retry: testingMode ? 0 : 3,
    retryDelay: 1000,
  });

  // Handle property selection submission
  const { mutate: selectProperty, isPending: isSelecting } = useMutation({
    mutationFn: async (propertyUrl: string) => {
      if (testingMode) {
        // Simulate API delay for testing
        await new Promise((resolve) => setTimeout(resolve, 800));
        return {
          response: {
            status: "success",
            message: "Property selected successfully",
          },
          selectedProperty: propertiesData?.properties.find(
            (prop) => prop.property_url === propertyUrl
          ),
        };
      }

      const selectedProperty = propertiesData?.properties.find(
        (prop) => prop.property_url === propertyUrl
      );

      if (!selectedProperty) {
        throw new Error("Selected property not found");
      }

      const data: GoogleSearchConsolePropertySelectRequest = {
        project_id: projectId,
        property_url: selectedProperty.property_url,
        property_type: selectedProperty.property_type,
      };

      const response = await projectAPI.selectGoogleSearchConsoleProperty(data);
      return {
        response: response.data,
        selectedProperty,
      };
    },
    onSuccess: ({ selectedProperty, response }) => {
      // Show success toast
      toast.success(
        response?.message ||
          "Google Search Console property selected successfully!"
      );

      if (selectedProperty && onPropertySelected) {
        onPropertySelected(selectedProperty);
      }
      onClose();
    },
    onError: (error: any) => {
      if (process.env.NODE_ENV === "development") {
        console.error("Error selecting property:", error);
      }

      // Extract error message from API response
      let errorMessage = "Failed to select Google Search Console property";

      if (error?.response?.data?.error_message) {
        errorMessage = error.response.data.error_message;
      } else if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Show error toast
      toast.error(errorMessage);
    },
  });

  const handleSubmit = () => {
    if (selectedProperty) {
      selectProperty(selectedProperty);
    }
  };

  const handleRetry = () => {
    refetchProperties();
  };

  // Helper function to format property display name
  const formatPropertyName = (property: GoogleSearchConsoleProperty) => {
    if (property.property_type === "domain") {
      return property.property_url.replace("sc-domain:", "");
    }
    return property.property_url;
  };

  // Helper function to get property type badge
  const getPropertyTypeBadge = (type: string) => {
    return type === "domain" ? "Domain" : "URL Prefix";
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl shadow-2xl border border-gray-200 w-full max-w-md mx-4 z-50 overflow-visible">
          <AnimatePresence>
            {isOpen && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="p-6"
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <LogosGoogleSearchConsole className="w-5 h-5" />
                    </div>
                    <Dialog.Title className="text-lg font-semibold text-[#344054]">
                      Select Search Console Property
                    </Dialog.Title>
                  </div>
                  <Dialog.Close asChild>
                    <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                      <XMarkIcon className="w-5 h-5 text-gray-500" />
                    </button>
                  </Dialog.Close>
                </div>

                {/* Content */}
                <div className="space-y-6">
                  {/* Description */}
                  <Dialog.Description className="text-sm text-[#344054] leading-relaxed">
                    Choose which Google Search Console property to connect to
                    your project. This will enable comprehensive SEO tracking
                    and insights.
                  </Dialog.Description>

                  {propertiesLoading && (
                    <div className="flex items-center justify-center py-8">
                      <div className="flex items-center gap-3">
                        <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-sm text-[#344054]">
                          Loading properties...
                        </span>
                      </div>
                    </div>
                  )}

                  {propertiesError && (
                    <div className="text-center py-8">
                      <div className="mb-4">
                        <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <XMarkIcon className="w-6 h-6 text-red-600" />
                        </div>
                        <h3 className="text-sm font-medium text-[#344054] mb-2">
                          Failed to load properties
                        </h3>
                        <p className="text-xs text-gray-600">
                          Unable to fetch your Google Search Console properties
                        </p>
                      </div>
                      <button
                        onClick={handleRetry}
                        className="px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary/90 transition-colors"
                      >
                        Try Again
                      </button>
                    </div>
                  )}

                  {propertiesData && propertiesData.properties && (
                    <>
                      {propertiesData.properties.length > 0 && (
                        <div className="space-y-4">
                          {/* Property Selection Header */}
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                              <LogosGoogleSearchConsole className="w-4 h-4 text-primary" />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-[#344054]">
                                Available Properties
                              </label>
                              <p className="text-xs text-gray-500 mt-0.5">
                                Choose which property to connect to your project
                              </p>
                            </div>
                          </div>

                          {/* Enhanced Property Selector */}
                          <div className="bg-gray-50 rounded-lg p-4">
                            <Select.Root
                              value={selectedProperty}
                              onValueChange={setSelectedProperty}
                            >
                              <Select.Trigger
                                className="w-full flex items-center justify-between px-4 py-3 border border-gray-200 rounded-md bg-white hover:border-primary/50 focus:border-primary focus:ring-2 focus:ring-primary/20 focus:outline-none transition-all duration-200 shadow-sm data-[state=open]:border-primary data-[state=open]:ring-2 data-[state=open]:ring-primary/20"
                                aria-label="Select Google Search Console property"
                              >
                                <Select.Value
                                  placeholder="Select a Google Search Console property..."
                                  className="text-sm text-[#344054] truncate"
                                />
                                <Select.Icon asChild>
                                  <ChevronDownIcon className="w-4 h-4 text-gray-400" />
                                </Select.Icon>
                              </Select.Trigger>

                              <Select.Portal>
                                <Select.Content className="overflow-hidden bg-white rounded-md shadow-lg border border-gray-200 z-50">
                                  <Select.ScrollUpButton className="flex items-center justify-center h-6 bg-white text-gray-400 cursor-default">
                                    <ChevronDownIcon className="w-4 h-4 rotate-180" />
                                  </Select.ScrollUpButton>
                                  <Select.Viewport className="p-1">
                                    {propertiesData.properties.map(
                                      (property) => (
                                        <Select.Item
                                          key={property.property_url}
                                          value={property.property_url}
                                          className="relative flex items-center justify-between px-3 py-2.5 text-sm text-[#344054] hover:bg-primary/5 hover:text-primary rounded-md cursor-pointer outline-none data-[highlighted]:bg-primary/5 data-[highlighted]:text-primary data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary focus:bg-primary/5 focus:text-primary transition-all duration-150 select-none"
                                        >
                                          <Select.ItemText asChild>
                                            <div className="flex flex-col gap-1 min-w-0 flex-1">
                                              <div className="font-medium truncate">
                                                {formatPropertyName(property)}
                                              </div>
                                              <div className="flex items-center gap-2">
                                                <span className="text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full">
                                                  {getPropertyTypeBadge(
                                                    property.property_type
                                                  )}
                                                </span>
                                                {property.permission_level && (
                                                  <span className="text-xs text-gray-500">
                                                    {property.permission_level}
                                                  </span>
                                                )}
                                              </div>
                                            </div>
                                          </Select.ItemText>
                                          <Select.ItemIndicator className="ml-2">
                                            <svg
                                              className="w-4 h-4 text-primary"
                                              fill="currentColor"
                                              viewBox="0 0 20 20"
                                            >
                                              <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                              />
                                            </svg>
                                          </Select.ItemIndicator>
                                        </Select.Item>
                                      )
                                    )}
                                  </Select.Viewport>
                                  <Select.ScrollDownButton className="flex items-center justify-center h-6 bg-white text-gray-400 cursor-default">
                                    <ChevronDownIcon className="w-4 h-4" />
                                  </Select.ScrollDownButton>
                                </Select.Content>
                              </Select.Portal>
                            </Select.Root>
                          </div>
                        </div>
                      )}

                      {propertiesData.properties.length === 0 && (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <ExclamationTriangleIcon className="w-8 h-8 text-gray-400" />
                          </div>
                          <h3 className="text-sm font-medium text-[#344054] mb-2">
                            No properties found
                          </h3>
                          <p className="text-xs text-gray-600 max-w-sm mx-auto">
                            No Google Search Console properties are available
                            for your account. Make sure you have properties set
                            up in your Google Search Console account.
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* Footer Actions */}
                {propertiesData &&
                  propertiesData.properties &&
                  propertiesData.properties.length > 0 && (
                    <div className="flex gap-3 mt-8 pt-6 border-t border-gray-100">
                      <button
                        onClick={onClose}
                        className="flex-1 px-4 py-2.5 text-sm font-medium text-[#344054] bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                        disabled={isSelecting}
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleSubmit}
                        disabled={!selectedProperty || isSelecting}
                        className="flex-1 px-4 py-2.5 text-sm font-medium text-white bg-primary hover:bg-primary/90 disabled:bg-gray-300 disabled:cursor-not-allowed rounded-lg transition-colors flex items-center justify-center gap-2"
                      >
                        {isSelecting && (
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        )}
                        {isSelecting ? "Connecting..." : "Connect Property"}
                      </button>
                    </div>
                  )}
              </motion.div>
            )}
          </AnimatePresence>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
