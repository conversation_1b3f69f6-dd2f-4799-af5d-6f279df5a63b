'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '../ui/select'
import Tooltip from '@/ui/Tooltip'
import { BsExclamationCircle } from 'react-icons/bs'
import { FaPlus } from 'react-icons/fa6'
import Modal from '@/ui/Modal'
import CustomInput from '../shared/CustomInput'
import ButtenSubmit from '../shared/ButtenSubmit'

type SelectGroupType = {
    title?: string
    guideText?: string
}

export default function SelectGroup({ title, guideText }: SelectGroupType) {
    const [groupNames, setGroupNames] = useState<string[]>(['No group selected']) // نمونه گروه اولیه
    const [isSelectOpen, setIsSelectOpen] = useState(false)
    const [searchText, setSearchText] = useState('')
    const [showAddForm, setShowAddForm] = useState(false)
    const [newGroupName, setNewGroupName] = useState('')
    const [selectedValue, setSelectedValue] = useState<string | undefined>(undefined)

    const filteredGroups = groupNames.filter((g) =>
        g.toLowerCase().includes(searchText.toLowerCase())
    )

    function addGroup() {
        if (newGroupName.trim() && !groupNames.includes(newGroupName.trim())) {
            setGroupNames((prev) => [...prev, newGroupName.trim()])
            setSelectedValue(newGroupName.trim())
            setNewGroupName('')
            setShowAddForm(false)
            setSearchText('')
        }
    }
    useEffect(() => {
        setSearchText('')
        if (showAddForm) {
            setIsSelectOpen(false)
        }
    }, [showAddForm])
    return (
        <div>
            <div className="mb-2 flex items-center gap-2 text-gray-600">
                <span className="text-sm lg:text-base">{title}</span>
                {guideText ? (
                    <Tooltip width="xl" content={<span>{guideText}</span>}>
                        <BsExclamationCircle />
                    </Tooltip>
                ) : null}
            </div>
            <Select
                open={isSelectOpen}
                onOpenChange={(open) => setIsSelectOpen(open)}
                onValueChange={(val) => setSelectedValue(val)}
                value={selectedValue}
            >
                <SelectTrigger className="w-full !h-[45px]">
                    <SelectValue placeholder="Select or Search a Group" className="!p-3" />
                </SelectTrigger>

                <SelectContent>
                    <div className="p-2">
                        <CustomInput
                            onBlur={(e) => {
                                const related = e.relatedTarget as HTMLElement
                                if (related && related.getAttribute("data-select-item")) {
                                    e.preventDefault()
                                    e.target.focus()
                                }
                            }}
                            value={searchText}
                            placeholder="Search groups..."
                            onChanges={(e) => setSearchText(e.target.value)}
                        />
                    </div>

                    {filteredGroups.length > 0 ? (
                        filteredGroups.map((item, index) => (
                            <SelectItem
                                data-select-item
                                key={index}
                                value={item}
                                className="flex items-center gap-2 cursor-pointer"
                                onClick={() => setShowAddForm(false)}
                            >
                                <span>{item}</span>
                            </SelectItem>
                        ))
                    ) : (
                        <SelectItem
                            data-select-item
                            value={'No group selected'}
                            className="flex items-center gap-2 cursor-pointer"
                            onClick={() => setShowAddForm(false)}
                        >
                            No group selected
                        </SelectItem>
                    )}

                    {!showAddForm && (
                        <>
                            <span className='border-t block w-full my-2'></span>
                            <div onClick={() => setShowAddForm(true)} className="p-2 hover:text-primary flex items-center gap-2 text-center cursor-pointer hover:bg-gray-100 w-full rounded-md">
                                <FaPlus />
                                <span>
                                    Create new...
                                </span>
                            </div>
                        </>
                    )}
                </SelectContent>

            </Select>
            <Modal title="Create Group" open={showAddForm} onClose={() => setShowAddForm(false)}>
                <div className="border-gray-200">
                    <CustomInput

                        value={newGroupName}
                        placeholder="New group name"
                        onChanges={(e) => setNewGroupName(e.target.value)}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                e.preventDefault()
                                addGroup()
                            }
                        }}
                    />
                    <div className="flex w-full justify-between mt-4 gap-2">
                        <ButtenSubmit text='Cancel' color='secondary' onClick={() => setShowAddForm(false)} />
                        <ButtenSubmit text='Create' disabled={!newGroupName.trim()} onClick={addGroup} />
                    </div>
                </div>
            </Modal>
        </div>
    )
}
