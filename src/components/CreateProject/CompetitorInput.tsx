"use client";
import React, { useState, useRef, useEffect } from "react";
import { MdClose } from "react-icons/md";
import { FaPlus } from "react-icons/fa6";
import Image from "next/image";
import PaginationTable from "./PaginationTable";
import { AutoCompleteCountry } from "./AutoCompleteCountry";
import { AutoComplete } from "@/components/shared/AutoComplete";
import countriesData from "@/data/countries";
import { motion, AnimatePresence } from "framer-motion";
import * as yup from "yup";
import { Switch } from "../ui/switch";
import { HiPaperClip } from "react-icons/hi2";
import { Loader2 } from "lucide-react";
import * as ExcelJS from "exceljs";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { getFlagImageUrl, defaultCountries } from "@/utils/flagUtils";
import { projectAPI } from "@/services/projectService";
import toast from "react-hot-toast";

const schema = yup.object({
  username: yup
    .string()
    .required("")
    .matches(
      /^(?:(?:https?:\/\/)?(?:www\.)?)?((([a-zA-Z0-9\u0600-\u06FF\-]+\.)+[a-zA-Z]{2,}|localhost|\d{1,3}(\.\d{1,3}){3})(:\d+)?)(\/.*)?$/,
      "Enter a valid domain like example.com"
    )
    .max(50, "Keywords must be at most 50 characters"),
});

export type CompetitorFieldsType = {
  country?: {
    name: string;
    code: string;
    image: string;
  } | null;
  language?: {
    name: string;
    code: string;
  } | null;
};

type CompetitorValueType = {
  id: string;
  name: string;
  country: string | null;
  language: string | null;
  countryCode: string; // Store country code for API
  languageCode: string; // Store language code for API
  suggestAi: boolean;
  // Additional metrics from competitor suggestions API
  intersections?: number;
  avg_position?: number;
  sum_position?: number;
  organic_keywords?: number;
  organic_traffic?: number;
  organic_cost?: number;
  competitor_score?: number;
  strength_level?: string;
};

type CompetitorInputType = {
  competitorNames?: string[];
  setData?: (val: any) => void;
  placeHolder?: string;
  classPlusBox?: string;
  classPlusInput?: string;
  maxList?: number;
  importButtonText?: string;
};

export default function CompetitorInput({
  maxList,
  competitorNames,
  classPlusInput,
  setData,
  placeHolder,
  classPlusBox,
  importButtonText,
}: CompetitorInputType) {
  const [value, setValue] = useState<CompetitorValueType[]>([]);
  const [dataState, setDataState] = useState<CompetitorFieldsType>({
    country: defaultCountries.australia,
    language: {
      name: "English",
      code: "en",
    },
  });
  const [addInputValue, setAddInputValue] = useState("");
  const [autoFill, setAutoFill] = useState<boolean>(false);
  const [error, setError] = useState("");
  const [lastErrorInput, setLastErrorInput] = useState(""); // Track what input caused the error
  const [duplicateError, setDuplicateError] = useState(""); // Track duplicate error separately
  const [currentPage, setCurrentPage] = useState(1);
  const [googleLanguage, setGoogleLanguage] = useState<any[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const MAX_COMPETITORS = maxList || 5;
  const ITEMS_PER_PAGE = 6;

  // Get project info and supported locations from store
  const { projectInfo, supportedLocations, competitors } =
    useCreateProjectStore();

  // Track if we're in a delete operation to prevent any errors from showing
  const [isDeleting, setIsDeleting] = useState(false);

  // Force clear all error states - use this for delete operations
  const forceClearErrors = () => {
    setError("");
    setLastErrorInput("");
    setDuplicateError("");
  };

  // Merge supported locations with country images
  const supportedCountriesWithImages = React.useMemo(() => {
    if (supportedLocations.length === 0) return [];

    return supportedLocations.map((location) => {
      const countryData = countriesData.find(
        (country) => country.code === location.code
      );
      return {
        ...location,
        image: countryData?.image || getFlagImageUrl(location.code),
      };
    });
  }, [supportedLocations]);

  // Filter languages to only show those used as primary languages in supported locations
  const supportedLanguages = React.useMemo(() => {
    if (supportedLocations.length === 0 || googleLanguage.length === 0)
      return googleLanguage;

    const primaryLanguageCodes = [
      ...new Set(supportedLocations.map((loc) => loc.primary_language)),
    ];
    return googleLanguage.filter((lang) =>
      primaryLanguageCodes.includes(lang.code)
    );
  }, [supportedLocations, googleLanguage]);

  // Load language data
  useEffect(() => {
    import("@/data/googleLanguage").then((mod) =>
      setGoogleLanguage(mod.default || mod)
    );
  }, []);

  // Initialize competitors from store in edit mode or from competitorNames prop
  useEffect(() => {
    // Priority 1: Initialize from store competitors (edit mode)
    if (competitors && competitors.length > 0 && value.length === 0) {
      const existingCompetitors: CompetitorValueType[] = competitors.map(
        (comp, index) => ({
          id: comp.id || `existing-${index}`,
          name: comp.domain,
          country: comp.countries?.[0]
            ? getFlagImageUrl(comp.countries[0])
            : getFlagImageUrl("AU"),
          language: comp.searchEngines?.[0] || "google",
          countryCode: comp.countries?.[0] || "AU",
          languageCode: "en", // Default to English, could be enhanced to map from comp data
          suggestAi: comp.isSuggested || false,
          // Include additional metrics if available
          intersections: comp.intersections,
          avg_position: comp.avg_position,
          sum_position: comp.sum_position,
          organic_keywords: comp.organic_keywords,
          organic_traffic: comp.organic_traffic,
          organic_cost: comp.organic_cost,
          competitor_score: comp.competitor_score,
          strength_level: comp.strength_level,
        })
      );

      setValue(existingCompetitors);
    }
    // Priority 2: Initialize from competitorNames prop (fallback)
    else if (
      competitorNames &&
      competitorNames.length > 0 &&
      value.length === 0
    ) {
      const propCompetitors: CompetitorValueType[] = competitorNames.map(
        (name, index) => ({
          id: `prop-${index}`,
          name: name,
          country: getFlagImageUrl("AU"),
          language: "google",
          countryCode: "AU",
          languageCode: "en",
          suggestAi: false,
        })
      );
      setValue(propCompetitors);
    }
  }, [competitors, competitorNames, value.length]);

  // Update default selections when supported locations are loaded
  useEffect(() => {
    if (
      supportedCountriesWithImages.length > 0 &&
      supportedLanguages.length > 0
    ) {
      // Check if current country is in supported list
      const currentCountrySupported = supportedCountriesWithImages.find(
        (country) => country.code === dataState.country?.code
      );

      // Check if current language is in supported list
      const currentLanguageSupported = supportedLanguages.find(
        (lang) => lang.code === dataState.language?.code
      );

      // If current selections are not supported, update to first supported options
      if (!currentCountrySupported || !currentLanguageSupported) {
        const defaultCountry =
          currentCountrySupported || supportedCountriesWithImages[0];
        const defaultLanguage =
          currentLanguageSupported ||
          supportedLanguages.find(
            (lang) => lang.code === defaultCountry.primary_language
          ) ||
          supportedLanguages[0];

        setDataState({
          country: defaultCountry,
          language: defaultLanguage,
        });
      }
    }
  }, [supportedCountriesWithImages, supportedLanguages]);

  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return value.slice(startIndex, endIndex);
  };

  // Validate input function - NO local duplicate checking
  const validateInput = async (input: string): Promise<string | null> => {
    if (!input.trim()) return null;

    if (input.length > 50) {
      return "Competitor URLs must be at most 50 characters";
    }

    if (value.length >= MAX_COMPETITORS) {
      return `Maximum limit of ${MAX_COMPETITORS} competitors reached`;
    }

    // Only validate URL format - NO duplicate checking
    try {
      await schema.validate({ username: input });
      return null; // No error
    } catch (err: any) {
      return err.message;
    }
  };

  const handleAdd = async (inputValue?: string, bool?: boolean) => {
    const trimmed = inputValue || addInputValue.trim();

    if (!trimmed) return;

    // Validate the input
    const validationError = await validateInput(trimmed);
    if (validationError) {
      setError(validationError);
      setLastErrorInput(trimmed); // Remember what input caused the error
      return;
    }

    // Check for duplicates in local state and global store
    const isDuplicateInLocal = value.some(
      (item) => item.name.toLowerCase() === trimmed.toLowerCase()
    );

    const isDuplicateInStore = competitors.some(
      (comp) => comp.domain.toLowerCase() === trimmed.toLowerCase()
    );

    if (isDuplicateInLocal || isDuplicateInStore) {
      setDuplicateError("This competitor has already been added");
      setLastErrorInput(trimmed); // Remember what input caused the error
      return;
    }

    // Success - add the competitor and clear any existing errors
    const newItem: CompetitorValueType = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
      name: trimmed,
      country:
        dataState?.country?.image ||
        getFlagImageUrl(dataState?.country?.code || "AU"),
      language: dataState?.language?.code || "en",
      countryCode: dataState?.country?.code || "AU",
      languageCode: dataState?.language?.code || "en",
      suggestAi: bool || false,
    };

    setValue((prev) => [...prev, newItem].slice(0, MAX_COMPETITORS));
    setAddInputValue("");

    // Clear error on successful add
    forceClearErrors();
  };

  const handlePaste = async (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    forceClearErrors();

    if (value.length >= MAX_COMPETITORS) {
      setError(`Maximum limit of ${MAX_COMPETITORS} competitors reached`);
      return;
    }

    const pasted = e.clipboardData.getData("text");
    const words = pasted
      .split(/\s+/)
      .map((w) => w.trim())
      .filter((w) => w.length > 0);

    // Validate URLs
    for (const word of words) {
      try {
        await schema.validate({ username: word });
      } catch (err: any) {
        setError("Enter valid domains like example.com");
        return;
      }
    }

    // No duplicate checking - just add all valid words
    const newItems: CompetitorValueType[] = words.map((word) => ({
      id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
      name: word,
      country:
        dataState?.country?.image ||
        getFlagImageUrl(dataState?.country?.code || "AU"),
      language: dataState?.language?.code || "en",
      countryCode: dataState?.country?.code || "AU",
      languageCode: dataState?.language?.code || "en",
      suggestAi: false,
    }));

    setValue((prev) => [...prev, ...newItems].slice(0, MAX_COMPETITORS));
  };

  // Separate delete handler that is completely isolated from error logic
  const handleDeleteCompetitor = (id: string) => {
    // Set deleting flag to prevent any errors from showing
    setIsDeleting(true);

    // Clear any existing errors immediately
    setError("");
    setLastErrorInput("");
    setDuplicateError("");

    // Remove the item from the list
    setValue((prev) => {
      const newValue = prev.filter((item) => item.id !== id);

      // Handle pagination
      const totalItemsAfterDelete = newValue.length;
      const totalPages = Math.ceil(totalItemsAfterDelete / ITEMS_PER_PAGE);
      if (currentPage > totalPages) {
        setCurrentPage(totalPages || 1);
      }

      return newValue;
    });

    // Reset the deleting flag after a short delay
    setTimeout(() => {
      setIsDeleting(false);
      setError("");
      setLastErrorInput("");
      setDuplicateError("");
    }, 100);
  };

  const handleFileImport = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const fileExtension = file.name.split(".").pop()?.toLowerCase();

    try {
      if (fileExtension === "xlsx" || fileExtension === "xls") {
        // Handle Excel files
        const reader = new FileReader();
        reader.onload = async (e) => {
          try {
            const buffer = e.target?.result as ArrayBuffer;
            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.load(buffer);

            const worksheet = workbook.worksheets[0];
            if (!worksheet) {
              setError("No worksheet found in the Excel file");
              return;
            }

            const importedItems: string[] = [];
            const headerRow = worksheet.getRow(1);
            let nameColumnIndex = -1;

            headerRow.eachCell((cell, colNumber) => {
              if (
                cell.value &&
                cell.value.toString().toLowerCase() === "name"
              ) {
                nameColumnIndex = colNumber;
              }
            });

            if (nameColumnIndex > 0) {
              worksheet.eachRow((row, rowNumber) => {
                if (rowNumber > 1) {
                  const cell = row.getCell(nameColumnIndex);
                  const value = cell.value;
                  if (
                    value &&
                    typeof value === "string" &&
                    value.trim().length > 0
                  ) {
                    importedItems.push(value.trim());
                  }
                }
              });
            } else {
              worksheet.eachRow((row) => {
                const cell = row.getCell(1);
                const value = cell.value;
                if (
                  value &&
                  typeof value === "string" &&
                  value.trim().length > 0
                ) {
                  importedItems.push(value.trim());
                }
              });
            }

            addItemsToTable(importedItems);
          } catch (err) {
            setError(
              "Error processing Excel file. Please check the file format."
            );
            console.error(err);
          }
        };

        reader.onerror = () => {
          setError("Failed to read Excel file. Please try again.");
        };

        reader.readAsArrayBuffer(file);
      } else {
        // Handle text and CSV files
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          if (!content) return;

          let importedItems: string[] = [];

          if (fileExtension === "csv") {
            importedItems = content
              .split("\n")
              .map((line) => {
                const firstColumn = line.split(",")[0];
                return firstColumn ? firstColumn.trim().replace(/"/g, "") : "";
              })
              .filter((line) => line.length > 0);
          } else {
            importedItems = content
              .split("\n")
              .map((line) => line.trim())
              .filter((line) => line.length > 0);
          }

          addItemsToTable(importedItems);
        };

        reader.onerror = () => {
          setError("Failed to read file. Please try again.");
        };

        reader.readAsText(file);
      }
    } catch (err) {
      setError("Error processing file. Please try again.");
      console.error(err);
    }

    event.target.value = "";
  };

  const addItemsToTable = (importedItems: string[]) => {
    importedItems.forEach((item) => {
      if (item.trim()) {
        handleAdd(item.trim(), false);
      }
    });
  };

  // Auto-fetch competitor suggestions when autoFill is enabled and conditions are met
  useEffect(() => {
    const fetchAutoSuggestions = async () => {
      if (
        autoFill &&
        dataState.country?.code &&
        dataState.language?.code &&
        projectInfo?.id &&
        !isLoadingSuggestions
      ) {
        setIsLoadingSuggestions(true);
        try {
          const response = await projectAPI.getCompetitorSuggestions({
            project_id: projectInfo.id,
            location: dataState.country.code,
            language_code: dataState.language.code,
          });

          // Add suggested competitors directly to the global store
          let addedCount = 0;

          // Handle both old and new API response formats
          const suggestions =
            response.data.data?.competitors || response.data.suggestions || [];

          // Add suggested competitors to local state only
          suggestions.forEach((suggestion) => {
            if (addedCount < MAX_COMPETITORS - value.length) {
              const newItem: CompetitorValueType = {
                id:
                  Date.now().toString() +
                  Math.random().toString(36).substr(2, 5),
                name: suggestion.domain,
                country:
                  dataState?.country?.image ||
                  getFlagImageUrl(dataState?.country?.code || "AU"),
                language: dataState?.language?.code || "en",
                countryCode: dataState?.country?.code || "AU",
                languageCode: dataState?.language?.code || "en",
                suggestAi: true,
                intersections: suggestion.intersections,
                avg_position: suggestion.avg_position,
                sum_position: suggestion.sum_position,
                organic_keywords: suggestion.organic_keywords,
                organic_traffic: suggestion.organic_traffic,
                organic_cost: suggestion.organic_cost,
                competitor_score: suggestion.competitor_score,
                strength_level: suggestion.strength_level,
              };

              setValue((prev) => [...prev, newItem]);
              addedCount++;
            }
          });

          // Clear any errors after processing suggestions
          forceClearErrors();

          // Show success message
          if (addedCount > 0) {
            toast.success(
              `Added ${addedCount} competitor suggestion${
                addedCount > 1 ? "s" : ""
              }`
            );
          } else if (response.data.suggestions.length > 0) {
            toast.info("All suggested competitors are already in your list");
          } else {
            toast.info(
              "No competitor suggestions found for this location and language"
            );
          }
        } catch (error: any) {
          console.error("Failed to fetch auto suggestions:", error);

          // Turn off the suggest switch when API fails
          setAutoFill(false);

          // Handle specific error responses
          if (error.response?.data?.status === "error") {
            const errorData = error.response.data;
            if (errorData.error === "no_subscription") {
              toast.error(
                errorData.message ||
                  "An active subscription is required for competitor analysis."
              );
            } else {
              toast.error(
                errorData.message || "Failed to fetch competitor suggestions"
              );
            }
          } else {
            toast.error(
              "Failed to fetch competitor suggestions. Please try again."
            );
          }
        } finally {
          setIsLoadingSuggestions(false);
        }
      }
    };

    // Only fetch when autoFill is turned on and all conditions are met
    if (autoFill) {
      if (!projectInfo?.id) {
        toast.error(
          "Project information is required for competitor suggestions"
        );
        return;
      }

      if (!dataState.country?.code || !dataState.language?.code) {
        toast.info(
          "Please select both country and language to get competitor suggestions"
        );
        return;
      }

      // Small delay to ensure smooth UX
      const timeoutId = setTimeout(fetchAutoSuggestions, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [autoFill]); // Only depend on autoFill to trigger when user toggles it

  useEffect(() => {
    if (setData) {
      // Transform data to the format expected by the store
      const competitorData = value.map((v) => ({
        domain: v.name,
        searchEngines: ["google"], // Default to Google since we removed search engine selector
        countries: [v.countryCode],
        languages: [v.languageCode],
      }));
      setData(competitorData);
    }
  }, [value]);

  return (
    <>
      <div className={"p-4 rounded-lg  " + classPlusBox}>
        <div className="flex items-center gap-4 mb-5">
          <button
            type="button"
            onClick={handleFileImport}
            title="Import from file (supports .txt, .csv, .xlsx, .xls)"
            className="flex items-center justify-center w-10 h-10 bg-primary hover:bg-primary/85 rounded-md  hover:border-primary/50 transition-colors"
          >
            <HiPaperClip size={18} className="text-white" />
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept=".txt,.csv,.xlsx,.xls"
            onChange={handleFileChange}
            className="hidden"
          />
          <div className="relative flex-1 bg-white rounded-lg">
            <input
              ref={inputRef}
              type="text"
              value={addInputValue}
              onChange={(e) => {
                const newValue = e.target.value;
                setAddInputValue(newValue);

                // Clear error when user starts typing (any change clears the error)
                if (error) {
                  forceClearErrors();
                }

                // Clear duplicate error only when input differs from the one that caused the error
                if (
                  duplicateError &&
                  newValue.trim().toLowerCase() !== lastErrorInput.toLowerCase()
                ) {
                  setDuplicateError("");
                  setLastErrorInput("");
                }
              }}
              onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleAdd();
                }
                if (
                  e.key === "Backspace" &&
                  addInputValue === "" &&
                  value.length &&
                  addInputValue.length
                ) {
                  e.preventDefault();
                  const last = value[value.length - 1].name;
                  setAddInputValue(last);
                  setValue((prev) => prev.slice(0, -1));
                }
              }}
              onPaste={handlePaste}
              className="text-sm focus:ring-primary focus:border-primary p-3 w-full rounded-md transition-colors"
              placeholder={placeHolder || "Type or paste competitor URLs..."}
            />
            <button
              type="submit"
              onClick={() => handleAdd()}
              disabled={!addInputValue.trim()}
              className="absolute z-20 focus:bg-primary/80 hover:bg-primary/80 right-3 w-8 h-8 flex items-center justify-center rounded-full bg-primary transform cursor-pointer text-white -translate-y-1/2 top-1/2 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              <FaPlus size={14} />
            </button>
            <span className="absolute -bottom-[21px] text-[12px] left-0 text-primary-red">
              {!isDeleting && (error || duplicateError)}
            </span>
          </div>
          {/* Country and Language Selectors */}
          <div className="flex gap-3 flex-shrink-0">
            <div className="w-14">
              <AutoCompleteCountry
                size="Small"
                noNameSelect
                data={
                  supportedCountriesWithImages.length > 0
                    ? supportedCountriesWithImages
                    : countriesData
                }
                valueKey="code"
                labelKey="name"
                classPlusButton="!min-w-0 w-14 h-12 px-3 py-2 justify-center bg-white border-none shadow-none rounded-lg"
                imageKey="image"
                value={dataState?.country?.code || ""}
                setValue={(item) => {
                  setDataState((prev) => {
                    // Auto-set language based on supported location's primary language
                    const supportedLocation = supportedLocations.find(
                      (loc) => loc.code === item.code
                    );
                    const primaryLanguageCode =
                      supportedLocation?.primary_language;

                    let newLanguage = prev.language;
                    if (primaryLanguageCode && supportedLanguages.length > 0) {
                      const matchingLanguage = supportedLanguages.find(
                        (lang) => lang.code === primaryLanguageCode
                      );
                      if (matchingLanguage) {
                        newLanguage = matchingLanguage;
                      }
                    }

                    return {
                      ...prev,
                      country: item,
                      language: newLanguage,
                    };
                  });
                }}
              />
            </div>
            <div className="w-14">
              <AutoComplete
                classPlusButton="!min-w-0 w-14 h-12 px-3 py-2 justify-center bg-white border-none shadow-none rounded-lg"
                dataSelector={supportedLanguages}
                valueKey="code"
                value={dataState?.language?.code || "en"}
                setValue={(language) =>
                  setDataState((prev) => ({ ...prev, language }))
                }
                option={(item) => <div className="text-xs">{item?.code}</div>}
                hideDropdownIcon={true}
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Switch
              checked={autoFill}
              onCheckedChange={(checked) => {
                setAutoFill(checked);
                // Clear errors when toggling auto-suggest
                forceClearErrors();
              }}
            />
            <span className="text-sm font-medium text-[#344054]">Suggest</span>
          </div>
        </div>
        {/* Competitor Box */}
        <div className="flex flex-col gap-3 mt-4 p-2 h-[450px] overflow-y-auto mb-3 relative">
          {/* Loading overlay for suggestions */}
          {isLoadingSuggestions && (
            <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10 rounded-lg">
              <div className="flex items-center gap-3 text-primary bg-white px-4 py-2 rounded-lg shadow-sm border">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span className="text-sm font-medium">
                  Fetching suggestions...
                </span>
              </div>
            </div>
          )}

          <AnimatePresence mode="popLayout">
            {getCurrentPageItems().map((competitor, index) => (
              <motion.div
                key={competitor.id}
                initial={{ opacity: 0, scale: 0.95, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className={`bg-white rounded-md py-3 px-4 flex justify-between items-center select-none`}
                style={{
                  userSelect: "none",
                  WebkitUserSelect: "none",
                  MozUserSelect: "none",
                  msUserSelect: "none",
                }}
                onMouseDown={(e) => e.preventDefault()}
                onSelectStart={(e) => e.preventDefault()}
                onDragStart={(e) => e.preventDefault()}
              >
                <div className="flex gap-3 items-center flex-1">
                  <span
                    className="text-xs font-medium text-gray-600 w-6 min-w-6 max-w-6 text-right select-none"
                    style={{
                      userSelect: "none",
                      WebkitUserSelect: "none",
                      MozUserSelect: "none",
                      msUserSelect: "none",
                    }}
                    onMouseDown={(e) => e.preventDefault()}
                    onSelectStart={(e) => e.preventDefault()}
                    onDragStart={(e) => e.preventDefault()}
                  >
                    {(currentPage - 1) * ITEMS_PER_PAGE + index + 1}.
                  </span>
                  <div className="flex flex-col gap-1 w-56 min-w-56 max-w-56">
                    <span className="text-sm font-medium text-gray-800 truncate">
                      {competitor.name}
                    </span>
                    {competitor.suggestAi && competitor.intersections && (
                      <div className="flex gap-2 text-xs text-gray-500">
                        <span>
                          {competitor.intersections?.toLocaleString()}{" "}
                          intersections
                        </span>
                        {competitor.avg_position && (
                          <span>
                            • Avg pos: {competitor.avg_position.toFixed(1)}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="flex gap-1.5 items-center">
                    {competitor.country && (
                      <figure className="p-2 bg-gray-100 rounded-full flex items-center justify-center">
                        <Image
                          src={competitor.country}
                          alt="country"
                          width={24}
                          height={24}
                          className="rounded-sm"
                        />
                      </figure>
                    )}
                    {competitor.suggestAi && (
                      <span className="text-primary text-xs bg-primary/10 px-2 py-1 rounded-full font-medium flex items-center gap-1">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-3 h-3"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"
                          />
                        </svg>
                        Suggested
                      </span>
                    )}
                    {competitor.strength_level && (
                      <span
                        className={`text-xs px-2 py-1 rounded-full font-medium ${
                          competitor.strength_level === "High"
                            ? "bg-red-100 text-red-700"
                            : competitor.strength_level === "Moderate"
                            ? "bg-yellow-100 text-yellow-700"
                            : "bg-green-100 text-green-700"
                        }`}
                      >
                        {competitor.strength_level}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex gap-2 ml-4 items-center">
                  {competitor.language && (
                    <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded mr-2">
                      {competitor.language.toUpperCase()}
                    </span>
                  )}
                  <button
                    type="button"
                    title="Delete competitor"
                    className="p-1.5 hover:bg-red-50 rounded-full transition-colors duration-200 text-gray-400 hover:text-red-500 select-none"
                    style={{
                      userSelect: "none",
                      WebkitUserSelect: "none",
                      MozUserSelect: "none",
                      msUserSelect: "none",
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onSelectStart={(e) => e.preventDefault()}
                    onDragStart={(e) => e.preventDefault()}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleDeleteCompetitor(competitor.id);
                    }}
                  >
                    <MdClose size={18} />
                  </button>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
        {/* Pagination Box */}
        <div className="grid grid-cols-3 items-center">
          <div className="text-left">
            <span className="text-sm text-purple-800 font-medium bg-purple-100 px-3 py-1.5 rounded-lg">
              {value.length} of {MAX_COMPETITORS}
            </span>
          </div>
          <PaginationTable
            currentPage={currentPage || 1}
            limitPage={6}
            setCurrentPage={setCurrentPage}
            total={value.length}
          />
          <div className="text-right flex justify-end">
            {/* Import button moved to before input field */}
          </div>
        </div>
      </div>
    </>
  );
}
