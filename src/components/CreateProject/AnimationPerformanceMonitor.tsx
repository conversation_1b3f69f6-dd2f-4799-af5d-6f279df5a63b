"use client";
import React, { useEffect, useState } from "react";

interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  memoryUsage?: number;
}

interface AnimationPerformanceMonitorProps {
  enabled?: boolean;
  showOverlay?: boolean;
}

export default function AnimationPerformanceMonitor({
  enabled = false,
  showOverlay = false,
}: AnimationPerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    frameTime: 0,
  });

  useEffect(() => {
    if (!enabled) return;

    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measurePerformance = () => {
      const currentTime = performance.now();
      frameCount++;

      // Calculate FPS every second
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        const frameTime = (currentTime - lastTime) / frameCount;

        // Get memory usage if available
        const memoryUsage = (performance as any).memory
          ? Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)
          : undefined;

        setMetrics({
          fps,
          frameTime: Math.round(frameTime * 100) / 100,
          memoryUsage,
        });

        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(measurePerformance);
    };

    animationId = requestAnimationFrame(measurePerformance);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enabled]);

  // Performance optimization recommendations
  const getPerformanceStatus = () => {
    if (metrics.fps >= 55) return { status: "excellent", color: "text-green-600" };
    if (metrics.fps >= 45) return { status: "good", color: "text-yellow-600" };
    if (metrics.fps >= 30) return { status: "fair", color: "text-orange-600" };
    return { status: "poor", color: "text-red-600" };
  };

  if (!enabled || !showOverlay) return null;

  const performanceStatus = getPerformanceStatus();

  return (
    <div className="fixed top-4 right-4 z-50 bg-black/80 text-white p-3 rounded-lg text-xs font-mono">
      <div className="space-y-1">
        <div className="flex justify-between gap-4">
          <span>FPS:</span>
          <span className={performanceStatus.color}>{metrics.fps}</span>
        </div>
        <div className="flex justify-between gap-4">
          <span>Frame Time:</span>
          <span>{metrics.frameTime}ms</span>
        </div>
        {metrics.memoryUsage && (
          <div className="flex justify-between gap-4">
            <span>Memory:</span>
            <span>{metrics.memoryUsage}MB</span>
          </div>
        )}
        <div className="flex justify-between gap-4">
          <span>Status:</span>
          <span className={performanceStatus.color}>
            {performanceStatus.status}
          </span>
        </div>
      </div>
    </div>
  );
}

// Performance optimization utilities
export const animationOptimizations = {
  // Reduce motion for users who prefer reduced motion
  respectsReducedMotion: () => {
    if (typeof window === "undefined") return false;
    return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
  },

  // Check if device has limited performance capabilities
  isLowPerformanceDevice: () => {
    if (typeof navigator === "undefined") return false;
    
    // Check for low-end device indicators
    const connection = (navigator as any).connection;
    const hardwareConcurrency = navigator.hardwareConcurrency || 1;
    
    return (
      hardwareConcurrency <= 2 ||
      (connection && connection.effectiveType === "slow-2g") ||
      (connection && connection.effectiveType === "2g")
    );
  },

  // Get optimized animation settings based on device capabilities
  getOptimizedSettings: () => {
    const isLowPerformance = animationOptimizations.isLowPerformanceDevice();
    const prefersReducedMotion = animationOptimizations.respectsReducedMotion();

    return {
      enableAnimations: !prefersReducedMotion,
      reducedAnimations: isLowPerformance || prefersReducedMotion,
      staggerDelay: isLowPerformance ? 0.05 : 0.1,
      animationDuration: isLowPerformance ? 0.2 : 0.3,
      enableComplexAnimations: !isLowPerformance && !prefersReducedMotion,
    };
  },
};

// Hook for using optimized animation settings
export function useOptimizedAnimations() {
  const [settings, setSettings] = useState(animationOptimizations.getOptimizedSettings());

  useEffect(() => {
    // Update settings if user changes preferences
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    const handleChange = () => {
      setSettings(animationOptimizations.getOptimizedSettings());
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  return settings;
}
