"use client";
import { useAuthStore } from "@/store/authStore";
import {
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import React from "react";
import { showToast } from "@/lib/toast";

export default function ProviderQuery({
  children,
}: {
  children: React.ReactNode;
}) {
  const route = useRouter();
  const logout = useAuthStore((state) => state.logout);

  const query = new QueryClient({
    queryCache: new QueryCache({
      onError: (err: any) => {
        if (
          err?.response.data.detail ===
          "Given token not valid for any token type"
        ) {
          showToast.error("Please log in again!");
          // Use optimistic logout and redirect immediately
          logout();
          route.replace("/login");
        } else {
          console.log(err)
        }
      },
    }),
  });
  return <QueryClientProvider client={query}>{children}</QueryClientProvider>;
}
