"use client";

import React from "react";
import {
  Dialog,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import ButtenSubmit from "@/components/shared/ButtenSubmit";

interface SaveExitModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirmExit: () => void;
}

export default function SaveExitModal({
  open,
  onOpenChange,
  onConfirmExit,
}: SaveExitModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md w-full mx-4">
        <DialogHeader className="text-center pb-2">
          <DialogTitle className="flex flex-col items-center gap-3">
            <div className="w-14 h-14 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center shadow-sm">
              <svg
                className="w-7 h-7 text-purple-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
            </div>
            <div className="text-center">
              <h3 className="text-lg font-bold text-gray-900">
                Save Your Progress?
              </h3>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* What happens next section */}
          <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl p-4 border border-purple-200/50">
            <div className="text-center space-y-3">
              <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mx-auto shadow-sm">
                <svg
                  className="w-5 h-5 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="space-y-2">
                <h4 className="text-sm font-semibold text-gray-900">
                  What happens when you save:
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-left">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full flex-shrink-0"></div>
                    <span className="text-xs text-gray-700">
                      Progress saved automatically
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-left">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full flex-shrink-0"></div>
                    <span className="text-xs text-gray-700">
                      Return to dashboard
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-left">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full flex-shrink-0"></div>
                    <span className="text-xs text-gray-700">
                      Resume anytime
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="space-y-2 pt-2">
            {/* Primary action - Save & Exit */}
            <ButtenSubmit
              text="Save & Exit"
              onClick={onConfirmExit}
              classPluss="w-full h-10 text-sm font-semibold shadow-sm !bg-primary !border-primary !text-white hover:!bg-primary hover:!opacity-100 focus:!bg-primary active:!scale-100"
              icon={
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              }
            />

            {/* Secondary action - Continue */}
            <ButtenSubmit
              onClick={() => onOpenChange(false)}
              text="Continue Working"
              color="primary__outline_hover"
              classPluss="w-full h-10 text-sm font-medium shadow-sm border-2"
              prevIcon={
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 19l-7-7m0 0l7-7m-7 7h18"
                  />
                </svg>
              }
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
