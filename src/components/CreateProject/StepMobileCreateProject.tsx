"use client";
import React from "react";
import BoxCreateProject from "./BoxCreateProject";
import StepProgressBar from "@/ui/StepProgressBar";
import { CiMonitor } from "react-icons/ci";
import { BsSpeedometer2 } from "react-icons/bs";
import { IoKeyOutline } from "react-icons/io5";
import { <PERSON><PERSON>ie<PERSON><PERSON> } from "react-icons/fi";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { useRouter } from "next/navigation";

// Custom Competitor Icon Component
const CompetitorIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 512 512"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M464.57,274a5.69,5.69,0,0,0,5.69-5.69V208.54a5.69,5.69,0,0,0-5.69-5.69H404.8a5.69,5.69,0,0,0,0,11.38h46l-67,67,0,0-52.95,52.94,0,0-30.88,30.88L225.26,290.5l225.05-225v46a5.69,5.69,0,0,0,11.38,0V51.71A5.68,5.68,0,0,0,456,46H396.24a5.69,5.69,0,1,0,0,11.37h46l-225,225.06-51.81-51.81a5.69,5.69,0,0,0-8,0l-114,114a5.69,5.69,0,0,0,8,8.05L161.39,242.72l47.78,47.78L43.41,456.26a5.68,5.68,0,0,0,4,9.71H464.57a5.69,5.69,0,1,0,0-11.37H393.51v-167l65.37-65.38v46A5.69,5.69,0,0,0,464.57,274ZM299.91,378.89a5.69,5.69,0,0,0,4-1.67L329.14,352V454.6h-41.6V368.87l8.35,8.35A5.68,5.68,0,0,0,299.91,378.89ZM276.16,454.6H234.55V315.88l41.61,41.61Zm-53-150.1V454.6h-41.6V334.19l35.65-35.65Zm-53,150.1H128.58V387.18l41.61-41.61Zm-53-56.05V454.6h-56Zm264.92-60.28V454.6H340.52v-114L382.13,299Z" />
    <path d="M256,57.4h84.05a5.69,5.69,0,1,0,0-11.37H256a5.69,5.69,0,1,0,0,11.37Z" />
    <path d="M340.05,83H298a5.69,5.69,0,1,0,0,11.38h42a5.69,5.69,0,0,0,0-11.38Z" />
  </svg>
);
import { usePathname } from "next/navigation";
import { useEditNavigation } from "@/hooks/useEditProject";

const stepsSettingBase = [
  {
    step: "project-information",
    label: "Project information",
    icon: <CiMonitor className="text-xl" />,
  },
  {
    step: "search-engines",
    label: "Search engines",
    icon: <BsSpeedometer2 className="text-xl" />,
  },
  {
    step: "keywords",
    label: "Keywords",
    icon: <IoKeyOutline className="text-xl" />,
  },
  {
    step: "competitors",
    label: "Competitors",
    icon: <CompetitorIcon className="text-xl" />,
  },
  {
    step: "analytics-services",
    label: "Statistics and Analytics services",
    icon: <FiPieChart className="text-xl" />,
  },
];

export default function StepMobileCreateProject() {
  const path = usePathname();
  const router = useRouter();
  const { urls, buildUrl } = useEditNavigation();
  const { canAccessStep } = useCreateProjectStore();

  // Build steps with proper URLs (including project_id in edit mode)
  const stepsSetting = stepsSettingBase.map((step) => {
    let id: string;
    switch (step.step) {
      case "project-information":
        id = urls.projectInformation;
        break;
      case "search-engines":
        id = urls.searchEngines;
        break;
      case "keywords":
        id = urls.keywords;
        break;
      case "competitors":
        id = urls.competitors;
        break;
      case "analytics-services":
        id = urls.analyticsServices;
        break;
      default:
        id = buildUrl(`/create-project/${step.step}`);
    }
    return {
      ...step,
      id,
    };
  });

  // Determine which steps are clickable based on accessibility
  const clickableSteps = stepsSetting
    .filter((step) => canAccessStep(step.id))
    .map((step) => step.id);

  // Handle step click navigation
  const handleStepClick = (stepId: string) => {
    if (canAccessStep(stepId)) {
      router.push(stepId);
    }
  };

  return (
    <BoxCreateProject classPlus="w-full block lg:hidden overflow-hidden">
      <StepProgressBar
        steps={stepsSetting}
        currentStepId={path || ""}
        clickableSteps={clickableSteps}
        onStepClick={handleStepClick}
      />
    </BoxCreateProject>
  );
}
