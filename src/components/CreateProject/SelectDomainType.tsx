import React, { useState, useCallback, memo, useEffect } from "react";
import { BsExclamationCircle } from "react-icons/bs";
import TooltipPortal from "../ui/TooltipPortal";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { IoIosArrowDown } from "react-icons/io";
import { Button } from "@/components/ui/button";

const dataDomainType = [
  { name: "*.domain/*", guide: "Domain with subdomains" },
  { name: "domain/* (Recommended)", guide: "Domain only" },
];

type SelectDomainTypeProps = {
  title?: string;
  setValue?: (val: { name: string; guide: string }) => void;
  value?: { name: string; guide: string };
  guideText?: string;
  placeHolder?: string;
  classNameBox?: string;
};

const SelectDomainType = memo(function SelectDomainType({
  title = "Domain Type",
  guideText = "You can add a domain with or without subdomains, select the needed directory on the domain, or add an exact URL.",
  setValue,
  value,
  placeHolder = "Select Domain Type",
  classNameBox,
}: SelectDomainTypeProps) {
  const [open, setOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<{
    name: string;
    guide: string;
  } | null>(value || dataDomainType[1]); // Default to "domain/* (Recommended)"

  // Set default value on mount if no value is provided
  useEffect(() => {
    if (!value && setValue && selectedValue) {
      setValue(selectedValue);
    }
  }, [value, setValue, selectedValue]);

  const handleSelect = useCallback(
    (item: { name: string; guide: string }) => {
      setSelectedValue(item);
      if (setValue) {
        setValue(item);
      }
      setOpen(false);
    },
    [setValue]
  );

  const handleItemClick = useCallback(
    (item: { name: string; guide: string }, e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      handleSelect(item);
    },
    [handleSelect]
  );

  const handleButtonClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setOpen(!open);
    },
    [open]
  );

  return (
    <div className={"w-full " + classNameBox}>
      {title ? (
        <div className="flex mb-2 items-center gap-2 text-gray-600">
          <span className="text-sm lg:text-base">{title}</span>
          {guideText ? (
            <TooltipPortal width="xl" content={<span>{guideText}</span>}>
              <BsExclamationCircle />
            </TooltipPortal>
          ) : null}
        </div>
      ) : null}

      <div className="flex w-full bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors duration-200 overflow-hidden">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <div className="flex h-[45px] w-full">
              <Button
                type="button"
                variant="outline"
                role="combobox"
                aria-expanded={open}
                className="w-full focus:!border rounded-none shadow-none h-full justify-between border-none bg-white placeholder:text-gray-400 px-4 py-3"
                onClick={handleButtonClick}
              >
                {selectedValue ? (
                  <div className="flex items-center gap-2 min-w-4">
                    <span>{selectedValue.name}</span>
                  </div>
                ) : (
                  <span>{placeHolder}</span>
                )}
                <IoIosArrowDown className="ml-2 opacity-50" />
              </Button>
            </div>
          </PopoverTrigger>
          <PopoverContent
            align="start"
            sideOffset={4}
            className="p-0 min-w-64 max-h-[300px]"
            style={{ width: "var(--radix-popover-trigger-width)" }}
          >
            <Command className="p-1" shouldFilter={false}>
              <CommandList className="max-h-[250px] overflow-auto">
                <CommandEmpty>No domain types found.</CommandEmpty>
                <CommandGroup>
                  {dataDomainType.map((item) => (
                    <CommandItem
                      key={item.name}
                      value={item.name}
                      className="w-full flex items-center justify-between cursor-pointer hover:bg-gray-100 transition-colors duration-100"
                      onSelect={() => handleSelect(item)}
                      onClick={(e) => handleItemClick(item, e)}
                    >
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <span className="truncate">{item.name}</span>
                      </div>
                      <div className="ml-2">
                        <TooltipPortal content={item.guide}>
                          <BsExclamationCircle className="text-gray-400" />
                        </TooltipPortal>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
});

export default SelectDomainType;
