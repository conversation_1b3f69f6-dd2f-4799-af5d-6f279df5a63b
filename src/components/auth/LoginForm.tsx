"use client";
import { useState, useEffect } from "react";
import RHTextBox from "@/ui/RHTextBox";
import { useAuthStore } from "@/store/authStore";
import { motion } from "framer-motion";
import { LockIcon } from "@/ui/icons/general";
import { EmailIcon } from "@/ui/icons/socialMedia";
import useFormState from "@/hooks/useFormState";

type LoginFormProps = {
  onSuccess: () => void;
  onRegisterClick: () => void;
  onFormStateChange?: (shouldPreventClose: () => boolean) => void;
};

export default function LoginForm({
  onSuccess,
  onRegisterClick,
  onFormStateChange,
}: LoginFormProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [fieldErrors, setFieldErrors] = useState<Record<string, string[]>>({});

  // Form state management
  const [formState, formActions] = useFormState();

  // Get login function and loading/error states from auth store
  const login = useAuthStore((state) => state.login);
  const isLoading = useAuthStore((state) => state.isLoading);
  const error = useAuthStore((state) => state.error);

  // Update form state when any field has content
  useEffect(() => {
    const hasContent = !!(email || password);
    formActions.setHasTypedContent(hasContent);
    formActions.setIsDirty(hasContent);

    // Notify parent component about form state changes
    if (onFormStateChange) {
      onFormStateChange(formActions.shouldPreventModalClose);
    }
  }, [email, password, formActions, onFormStateChange]);

  // Validate email format
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Validate password (minimum 6 characters)
  const validatePassword = (password: string): boolean => {
    return password.length >= 6;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent multiple submissions
    if (isLoading) return;

    // Reset field errors
    setFieldErrors({});

    // Validate form fields
    const errors: Record<string, string[]> = {};

    // Check for empty fields
    if (!email) {
      errors.email = ["Email is required"];
    } else if (!validateEmail(email)) {
      errors.email = ["Please enter a valid email address"];
    }

    if (!password) {
      errors.password = ["Password is required"];
    } else if (!validatePassword(password)) {
      errors.password = ["Password must be at least 6 characters"];
    }

    // If there are validation errors, show them and stop
    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      return;
    }

    // Call the login function from auth store
    const success = await login({
      email,
      password,
    });

    // If login was successful, call the success callback with a small delay
    // to ensure profile data is fully loaded
    if (success) {
      console.log("Login successful, calling success callback with delay");

      // Small delay to ensure profile data is loaded
      setTimeout(() => {
        console.log("Executing login success callback");
        onSuccess();
      }, 300);
    }
  };

  return (
    <div className="px-2 lg:px-4 pt-1 pb-3 flex-1">
      <form
        onSubmit={handleSubmit}
        className="h-full flex flex-col"
        autoComplete="off"
        method="post"
        action="javascript:void(0);"
        data-form-type="login"
      >
        <div className="flex flex-col gap-2 mb-2 overflow-y-auto">
          {/* CSRF token input removed */}

          <div className="flex flex-col gap-1">
            <RHTextBox
              label="Email Address"
              placeholder="Enter your email address"
              type="email"
              name="email"
              value={email}
              onChange={(value) => {
                // Debounce the state update to reduce renders
                if (value !== email) {
                  setEmail(value);
                }
              }}
              onFocus={() => formActions.setHasActiveInput(true)}
              onBlur={() => formActions.setHasActiveInput(false)}
              required
              autoComplete="username email"
              className="!py-2 text-xs lg:text-sm mx-1"
              icon={<EmailIcon className="w-4 h-4" />}
            />
            {fieldErrors.email && fieldErrors.email.length > 0 && (
              <div className="text-primary-red text-xs">
                {fieldErrors.email[0]}
              </div>
            )}
          </div>

          <div className="flex flex-col gap-1 mb-1">
            <RHTextBox
              label="Password"
              placeholder="Enter your password"
              type="password"
              name="password"
              value={password}
              onChange={(value) => {
                // Debounce the state update to reduce renders
                if (value !== password) {
                  setPassword(value);
                }
              }}
              onFocus={() => formActions.setHasActiveInput(true)}
              onBlur={() => formActions.setHasActiveInput(false)}
              required
              autoComplete="current-password"
              className="!py-2 text-xs lg:text-sm mx-1"
              icon={<LockIcon className="w-4 h-4" />}
            />
            {fieldErrors.password && fieldErrors.password.length > 0 && (
              <div className="text-primary-red text-xs">
                {fieldErrors.password[0]}
              </div>
            )}
          </div>

          {error && (
            <div className="text-primary-red text-xs md:text-sm mt-2">
              {error}
            </div>
          )}
        </div>

        <div className="mt-auto flex flex-col items-center">
          <div className="w-full max-w-sm">
            <motion.button
              type="submit"
              className={`btn btn--primary !w-full !py-3 md:!py-4 mt-4 !text-sm md:!text-base font-semibold flex items-center justify-center gap-2 ${
                isLoading ? "opacity-70" : ""
              }`}
              disabled={isLoading}
              whileHover={{
                scale: 1.005,
                boxShadow: "0 4px 12px rgba(145, 74, 196, 0.25)",
                backgroundColor: "rgba(155, 84, 206, 1)",
              }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.15 }}
            >
            {isLoading ? (
              <span className="flex items-center justify-center gap-2">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>Logging in</span>
                <motion.span
                  animate={{ opacity: [0, 1, 0] }}
                  transition={{ repeat: Infinity, duration: 1.5 }}
                >
                  ...
                </motion.span>
              </span>
            ) : (
              <>
                <span>Login to Your Account</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-4 h-4 md:w-5 md:h-5"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M13.8 12H3" />
                </svg>
              </>
            )}
            </motion.button>
          </div>

          <div className="mt-3 md:mt-4 text-center">
            <p className="text-secondary text-xs md:text-sm">
              Don&apos;t have an account?{" "}
              <button
                type="button"
                onClick={onRegisterClick}
                className="text-primary font-semibold hover:underline transition-colors duration-200 hover:text-primary/80"
              >
                Register
              </button>
            </p>
          </div>
        </div>
      </form>
    </div>
  );
}
