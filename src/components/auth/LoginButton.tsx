"use client";
import { useAuth } from "@/providers/AuthProvider";
import UserProfileDropdown from "./UserProfileDropdown";
import { motion } from "framer-motion";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import storageService from "@/services/storageService";

interface LoginButtonProps {
  className?: string;
  buttonText?: string;
}

export default function LoginButton({
  buttonText = "Login / Signup",
}: LoginButtonProps) {
  const { isAuthenticated, hasTokens, isHydrated, redirectToLogin } = useAuth();
  const router = useRouter();
  const [hasStoredTokens, setHasStoredTokens] = useState(false);

  // Check for stored tokens immediately on mount for instant UI
  useEffect(() => {
    if (typeof window !== "undefined") {
      const token = storageService.getToken();
      // Only check for token, not user data - be more optimistic
      setHasStoredTokens(!!token);
    }
  }, []);

  // Show profile dropdown immediately if we have any indication of authentication
  // Be more optimistic - show profile if we have any token
  if (hasStoredTokens || hasTokens || isAuthenticated) {
    return <UserProfileDropdown />;
  }

  // Show login button by default
  const handleClick = (e: React.MouseEvent) => {
    // Prevent default Link navigation so we can record redirect first
    e.preventDefault();
    // Store the current URL for post-login redirect, then go to /login
    redirectToLogin();
  };

  return (
    <Link href="/login" onClick={handleClick}>
      <motion.button
        className="bg-secondary/5 px-6 py-2.5 text-primary rounded-lg tracking-wide border font-bold border-primary relative overflow-hidden login-btn"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ 
          opacity: 1, 
          scale: 1,
          transition: {
            duration: 0.2,
            ease: "easeOut",
          },
        }}
        whileHover={{
          scale: 1.005,
          backgroundColor: "var(--color-primary)",
          color: "white",
          boxShadow: "0 2px 6px rgba(145, 74, 196, 0.2)",
          transition: {
            duration: 0.2,
            ease: [0.25, 0.1, 0.25, 1.0],
          },
        }}
        whileTap={{
          scale: 0.98,
          boxShadow: "0 2px 6px rgba(145, 74, 196, 0.2)",
        }}
      >
        <motion.span
          className="relative z-20"
          initial={{ opacity: 1 }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          {buttonText}
        </motion.span>

        {/* Background animation element */}
        <motion.div
          className="absolute inset-0 bg-primary rounded-lg"
          initial={{ opacity: 0, scale: 0 }}
          whileHover={{
            opacity: 1,
            scale: 1,
            transition: { duration: 0.3 },
          }}
          style={{ originX: 0.5, originY: 0.5 }}
        />
      </motion.button>
    </Link>
  );
}
