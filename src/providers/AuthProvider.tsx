"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
  ReactNode,
} from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import storageService from "@/services/storageService";
import authErrorHandler from "@/utils/authErrorHandler";
import { UserProfile } from "@/services/profileService";
import { isProtectedRoute } from "@/utils/authUtils";

// Auth context types
interface AuthContextType {
  // Authentication state
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserProfile | null;
  error: string | null;

  // Authentication actions
  login: (data: LoginData) => Promise<boolean>;
  register: (data: RegisterData) => Promise<{
    success: boolean;
    email?: string;
    fieldErrors?: Record<string, string[]> | null;
  }>;
  verifyEmail: (data: VerifyEmailData) => Promise<boolean>;
  logout: () => void;
  refreshProfile: (force?: boolean) => Promise<boolean>;

  // Utility functions
  requireLogin: (callback: () => void, returnUrl?: string) => boolean;
  redirectToLogin: (returnUrl?: string) => void;

  // Optimistic auth state
  hasTokens: boolean;
  isInitialized: boolean;
  isHydrated: boolean;
}

// Auth data types
interface LoginData {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  confirm_password: string;
  first_name: string;
  last_name: string;
}

interface VerifyEmailData {
  email: string;
  otp: string;
}

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook to use the auth context with error boundary
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    // During SSR, provide a default context to prevent errors
    if (typeof window === "undefined") {
      return {
        isAuthenticated: false,
        isLoading: false,
        user: null,
        error: null,
        login: async () => false,
        register: async () => ({ success: false }),
        verifyEmail: async () => false,
        logout: () => {},
        refreshProfile: async () => false,
        requireLogin: () => false,
        redirectToLogin: () => {},
        hasTokens: false,
        isInitialized: false,
        isHydrated: false,
      };
    }
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Centralized Authentication Provider
 *
 * This provider consolidates all authentication logic and provides:
 * - Optimistic UI (show authenticated state immediately when tokens exist)
 * - Background token validation
 * - Centralized error handling
 * - Consistent redirect logic
 * - Single source of truth for auth state
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const router = useRouter();
  const pathname = usePathname();

  // Use refs to prevent unnecessary re-renders
  const initializationRef = useRef(false);
  const authValidationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Get auth state and actions from Zustand store with selective subscriptions
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const isLoading = useAuthStore((state) => state.isLoading);
  const user = useAuthStore((state) => state.user);
  const error = useAuthStore((state) => state.error);

  // Get actions (these don't change, so we can get them once)
  const storeLogin = useAuthStore((state) => state.login);
  const storeRegister = useAuthStore((state) => state.register);
  const storeVerifyEmail = useAuthStore((state) => state.verifyEmail);
  const storeLogout = useAuthStore((state) => state.logout);
  const fetchProfile = useAuthStore((state) => state.fetchProfile);

  // Local state for optimistic UI and initialization
  const [hasTokens, setHasTokens] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  // Enhanced initialization effect with proper hydration
  useEffect(() => {
    if (typeof window !== "undefined" && !initializationRef.current) {
      initializationRef.current = true;

      const token = storageService.getToken();
      const user = storageService.getUser();
      const hasValidTokens = !!(token && user);

      setHasTokens(hasValidTokens);
      setIsHydrated(true);

      // If we have tokens but auth state is false, set optimistic state
      if (hasValidTokens && !isAuthenticated) {
        useAuthStore.setState({
          isAuthenticated: true,
          user: user as any, // Cast to UserProfile - will be updated by refreshProfile
          isLoading: false, // Set to false for optimistic UI
        });
      }

      setIsInitialized(true);

      // Log initialization for debugging
      console.log("AuthProvider initialized:", {
        hasValidTokens,
        isAuthenticated,
        hasUser: !!user,
      });
    }
  }, [isAuthenticated]);

  // Optimized background auth validation with debouncing
  useEffect(() => {
    const validateAuth = async () => {
      if (hasTokens && isInitialized) {
        try {
          // Validate tokens with server in background
          await fetchProfile(true);
        } catch (error) {
          console.error("Background auth validation failed:", error);
          // Don't logout immediately, let the user continue with cached data
          // Auth errors will be handled by the error handler
        }
      }
    };

    // Clear any existing timeout
    if (authValidationTimeoutRef.current) {
      clearTimeout(authValidationTimeoutRef.current);
    }

    // Only validate if we have tokens and are initialized
    if (hasTokens && isInitialized) {
      // Debounce validation to prevent excessive API calls
      authValidationTimeoutRef.current = setTimeout(validateAuth, 100);
    }

    return () => {
      if (authValidationTimeoutRef.current) {
        clearTimeout(authValidationTimeoutRef.current);
      }
    };
  }, [hasTokens, isInitialized, fetchProfile]);

  // Set up auth error handler
  useEffect(() => {
    const handleAuthError = () => {
      console.log("Auth error detected, redirecting to login");
      // Store current URL for post-login redirect (include full href with query/hash)
      if (typeof window !== "undefined") {
        const currentUrl = window.location.href;
        if (!currentUrl.endsWith("/login") && !currentUrl.endsWith("/signup")) {
          sessionStorage.setItem("postLoginRedirect", currentUrl);
        }
      }
      router.push("/login");
    };

    // Register auth error handler
    authErrorHandler.addAuthErrorListener(handleAuthError);

    return () => {
      authErrorHandler.removeAuthErrorListener(handleAuthError);
    };
  }, [router]);

  // Enhanced login function with redirect handling
  const login = useCallback(
    async (data: LoginData): Promise<boolean> => {
      const success = await storeLogin(data);

      if (success) {
        // Handle post-login redirect
        if (typeof window !== "undefined") {
          const postLoginRedirect = sessionStorage.getItem("postLoginRedirect");
          const postLoginCallback = sessionStorage.getItem("postLoginCallback");

          // Clear stored redirects
          sessionStorage.removeItem("postLoginRedirect");
          sessionStorage.removeItem("postLoginCallback");

          if (postLoginRedirect) {
            router.push(postLoginRedirect);
          } else if (postLoginCallback) {
            try {
              const callbackData = JSON.parse(postLoginCallback);
              // Handle callback if needed
              router.push("/my-projects");
            } catch {
              router.push("/my-projects");
            }
          } else {
            router.push("/my-projects");
          }
        }
      }

      return success;
    },
    [storeLogin, router]
  );

  // Enhanced logout function with smart redirect logic and browser history management
  const logout = useCallback(() => {
    storeLogout();
    setHasTokens(false);

    // Clear any stored redirects and authentication-related session data
    if (typeof window !== "undefined") {
      sessionStorage.removeItem("postLoginRedirect");
      sessionStorage.removeItem("postLoginCallback");

      // Clear any other auth-related session storage
      sessionStorage.removeItem("auth-storage");

      // Clear localStorage auth data if any
      localStorage.removeItem("auth-storage");
    }

    // Always redirect to main page for protected routes to prevent back button issues
    if (isProtectedRoute(pathname)) {
      // Use replace to prevent users from going back to authenticated pages
      router.replace("/");

      // Additional browser history management - clear forward history
      if (typeof window !== "undefined") {
        // This helps prevent back button navigation to protected pages
        window.history.replaceState(null, "", "/");
      }
    }
    // If not on a protected route, user stays on current page after logout
  }, [storeLogout, router, pathname]);

  // Enhanced redirect to login with return URL and context preservation
  const redirectToLogin = useCallback(
    (returnUrl?: string, preserveShareContext?: boolean) => {
      if (typeof window !== "undefined") {
        let redirectUrl = returnUrl || window.location.href;

        // If preserveShareContext is true and we have URL parameters, ensure they're preserved
        if (preserveShareContext && !returnUrl) {
          const currentUrl = window.location.href;
          const urlParams = new URLSearchParams(window.location.search);

          // If there's a share parameter, ensure it's in the redirect URL
          if (urlParams.get("share")) {
            redirectUrl = currentUrl;
          }
        }

        if (redirectUrl !== "/login" && redirectUrl !== "/signup") {
          sessionStorage.setItem("postLoginRedirect", redirectUrl);
        }
      }
      router.push("/login");
    },
    [router]
  );

  // Enhanced require login function with context preservation
  const requireLogin = useCallback(
    (
      callback: () => void,
      returnUrl?: string,
      preserveShareContext?: boolean
    ) => {
      if (isAuthenticated || hasTokens) {
        callback();
        return true;
      } else {
        redirectToLogin(returnUrl, preserveShareContext);
        return false;
      }
    },
    [isAuthenticated, hasTokens, redirectToLogin]
  );

  // Enhanced refresh profile function
  const refreshProfile = useCallback(
    async (force?: boolean): Promise<boolean> => {
      try {
        const success = await fetchProfile(force);
        if (success) {
          // Update hasTokens state if successful
          setHasTokens(true);
        }
        return success;
      } catch (error) {
        console.error("Profile refresh failed:", error);
        return false;
      }
    },
    [fetchProfile]
  );

  // Memoized context value to prevent unnecessary re-renders
  const contextValue = useMemo<AuthContextType>(
    () => ({
      // Authentication state
      isAuthenticated: isAuthenticated || hasTokens, // Optimistic auth state
      isLoading,
      user,
      error,

      // Authentication actions
      login,
      register: storeRegister,
      verifyEmail: storeVerifyEmail,
      logout,
      refreshProfile,

      // Utility functions
      requireLogin,
      redirectToLogin,

      // Optimistic auth state
      hasTokens,
      isInitialized,
      isHydrated,
    }),
    [
      isAuthenticated,
      hasTokens,
      isLoading,
      user,
      error,
      login,
      storeRegister,
      storeVerifyEmail,
      logout,
      refreshProfile,
      requireLogin,
      redirectToLogin,
      isInitialized,
      isHydrated,
    ]
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
}

// useAuth hook is defined above near the context creation

export default AuthProvider;
