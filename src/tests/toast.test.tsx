/**
 * @jest-environment jsdom
 */

import React from "react";
import { toast } from "react-hot-toast";
import { showToast, toastOptions } from "@/lib/toast";

// Mock react-hot-toast
jest.mock("react-hot-toast", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    loading: jest.fn(),
    custom: jest.fn(),
    dismiss: jest.fn(),
  },
  Toaster: jest.fn(),
  ToastBar: jest.fn(),
}));

describe("Toast Utilities", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should call toast.success with correct arguments", () => {
    const message = "Success message";
    showToast.success(message);
    expect(toast.success).toHaveBeenCalledWith(message, toastOptions);
  });

  it("should call toast.error with correct arguments", () => {
    const message = "Error message";
    showToast.error(message);
    expect(toast.error).toHaveBeenCalledWith(message, toastOptions);
  });

  it("should call toast.loading with correct arguments", () => {
    const message = "Loading message";
    showToast.loading(message);
    expect(toast.loading).toHaveBeenCalledWith(message, toastOptions);
  });

  it("should call toast.custom with correct arguments", () => {
    const content = <div>Custom content</div>;
    showToast.custom(content);
    expect(toast.custom).toHaveBeenCalledWith(content, toastOptions);
  });

  it("should call toast.dismiss with no arguments when no ID provided", () => {
    showToast.dismiss();
    expect(toast.dismiss).toHaveBeenCalledWith();
  });

  it("should call toast.dismiss with toast ID when provided", () => {
    const toastId = "test-toast-id";
    showToast.dismiss(toastId);
    expect(toast.dismiss).toHaveBeenCalledWith(toastId);
  });

  it("should merge custom options with default options", () => {
    const message = "Custom options message";
    const customOptions = { duration: 10000 };

    showToast.success(message, customOptions);

    expect(toast.success).toHaveBeenCalledWith(
      message,
      expect.objectContaining({
        ...toastOptions,
        ...customOptions,
      })
    );
  });
});
