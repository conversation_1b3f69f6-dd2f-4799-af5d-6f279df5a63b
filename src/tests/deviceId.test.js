/**
 * Device ID Test
 * 
 * This is a simple test to verify that the device ID implementation works correctly.
 * You can run this test in the browser console to check if the device ID is being
 * generated and stored correctly.
 */

// Import the device service
import deviceService from '../services/deviceService';

// Test the device ID generation and storage
const testDeviceId = () => {
  console.log('Testing device ID functionality...');
  
  // Get the device ID
  const deviceId = deviceService.getDeviceId();
  console.log('Current device ID:', deviceId);
  
  // Verify that the device ID is stored in localStorage
  const storedDeviceId = localStorage.getItem('seo_device_id');
  console.log('Device ID from localStorage:', storedDeviceId);
  
  // Verify that the IDs match
  if (deviceId === storedDeviceId) {
    console.log('✅ Test passed: Device IDs match');
  } else {
    console.error('❌ Test failed: Device IDs do not match');
  }
  
  // Get the device ID again to verify it's the same
  const deviceId2 = deviceService.getDeviceId();
  if (deviceId === deviceId2) {
    console.log('✅ Test passed: Device ID is consistent');
  } else {
    console.error('❌ Test failed: Device ID changed on second call');
  }
  
  return {
    deviceId,
    storedDeviceId,
    match: deviceId === storedDeviceId,
    consistent: deviceId === deviceId2
  };
};

// Export the test function
export default testDeviceId;

// Instructions for manual testing:
/*
To test the device ID functionality in the browser console:

1. Import the test:
   import testDeviceId from './tests/deviceId.test.js';

2. Run the test:
   testDeviceId();

3. Check the console output to verify that:
   - A device ID is generated and stored in localStorage
   - The device ID is consistent across multiple calls
   - The device ID persists across page refreshes
*/
