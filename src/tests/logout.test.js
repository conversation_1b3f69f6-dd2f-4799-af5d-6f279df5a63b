/**
 * Logout Functionality Test
 *
 * This is a comprehensive test to verify that the optimistic logout functionality works correctly:
 * - Clears cache immediately without waiting for API response
 * - Redirects user instantly
 * - Prevents back button navigation to protected dashboard pages
 * - Sends logout request to server in background
 *
 * Run this test in the browser console after implementing the optimistic logout fixes.
 */

/**
 * Test the logout functionality and browser history management
 */
const testLogoutFunctionality = () => {
  console.log("🧪 Testing logout functionality...");

  const results = {
    authStateCleared: false,
    sessionStorageCleared: false,
    localStorageCleared: false,
    redirectedToHome: false,
    backButtonPrevented: false,
    profileDropdownHidden: false,
  };

  // Test 1: Check if authentication state is cleared
  console.log("📋 Test 1: Checking authentication state clearing...");

  // Check if tokens are cleared from localStorage
  const token = localStorage.getItem("token");
  const refreshToken = localStorage.getItem("refresh_token");
  const user = localStorage.getItem("user");
  const authStorage = localStorage.getItem("auth-storage");

  if (!token && !refreshToken && !user && !authStorage) {
    console.log("✅ Authentication state cleared from localStorage");
    results.authStateCleared = true;
  } else {
    console.log(
      "❌ Authentication state not properly cleared from localStorage"
    );
    console.log("Remaining items:", { token, refreshToken, user, authStorage });
  }

  // Test 2: Check if session storage is cleared
  console.log("📋 Test 2: Checking session storage clearing...");

  const postLoginRedirect = sessionStorage.getItem("postLoginRedirect");
  const postLoginCallback = sessionStorage.getItem("postLoginCallback");
  const sessionAuthStorage = sessionStorage.getItem("auth-storage");

  if (!postLoginRedirect && !postLoginCallback && !sessionAuthStorage) {
    console.log("✅ Session storage cleared properly");
    results.sessionStorageCleared = true;
  } else {
    console.log("❌ Session storage not properly cleared");
    console.log("Remaining items:", {
      postLoginRedirect,
      postLoginCallback,
      sessionAuthStorage,
    });
  }

  // Test 3: Check if user is redirected to home page
  console.log("📋 Test 3: Checking redirect to home page...");

  if (window.location.pathname === "/") {
    console.log("✅ User redirected to home page");
    results.redirectedToHome = true;
  } else {
    console.log(
      "❌ User not redirected to home page. Current path:",
      window.location.pathname
    );
  }

  // Test 4: Check browser history management
  console.log("📋 Test 4: Checking browser history management...");

  // This test checks if the current history entry is the home page
  // In a real scenario, you would test by trying to navigate back
  if (window.location.pathname === "/" && window.history.length >= 1) {
    console.log("✅ Browser history appears to be managed correctly");
    results.backButtonPrevented = true;
  } else {
    console.log("⚠️ Browser history management needs manual verification");
  }

  // Test 5: Check if profile dropdown is hidden (requires DOM inspection)
  console.log("📋 Test 5: Checking profile dropdown visibility...");

  const profileDropdown =
    document.querySelector('[data-testid="profile-dropdown"]') ||
    document.querySelector(".profile-dropdown") ||
    document.querySelector('[class*="profile"]');

  if (
    !profileDropdown ||
    profileDropdown.style.display === "none" ||
    !profileDropdown.offsetParent
  ) {
    console.log("✅ Profile dropdown is hidden");
    results.profileDropdownHidden = true;
  } else {
    console.log("⚠️ Profile dropdown visibility needs manual verification");
    console.log("Found element:", profileDropdown);
  }

  // Test 6: Check localStorage clearing
  console.log("📋 Test 6: Checking localStorage clearing...");

  const localStorageKeys = Object.keys(localStorage);
  const authRelatedKeys = localStorageKeys.filter(
    (key) =>
      key.includes("auth") ||
      key.includes("token") ||
      key.includes("user") ||
      key.includes("refresh")
  );

  if (authRelatedKeys.length === 0) {
    console.log("✅ No auth-related items found in localStorage");
    results.localStorageCleared = true;
  } else {
    console.log(
      "❌ Auth-related items still in localStorage:",
      authRelatedKeys
    );
  }

  // Summary
  console.log("\n📊 Test Results Summary:");
  console.log("========================");

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? "✅" : "❌";
    const testName = test.replace(/([A-Z])/g, " $1").toLowerCase();
    console.log(`${status} ${testName}`);
  });

  console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log(
      "🎉 All tests passed! Logout functionality is working correctly."
    );
  } else {
    console.log(
      "⚠️ Some tests failed. Please review the logout implementation."
    );
  }

  return {
    results,
    score: `${passedTests}/${totalTests}`,
    allPassed: passedTests === totalTests,
  };
};

/**
 * Test fast redirection functionality
 * This tests the new fast token-based redirection
 */
const testFastRedirection = () => {
  console.log("⚡ Testing fast redirection functionality...");

  const results = {
    fastAuthCheckWorks: false,
    immediateRedirectWorks: false,
    noTokensDetected: false,
  };

  // Test 1: Check if fast auth check utility works
  try {
    // Import the fast auth check (this would need to be available globally)
    if (typeof window.fastAuthCheck !== "undefined") {
      const authStatus = window.fastAuthCheck.getImmediateAuthStatus();
      console.log("Fast auth status:", authStatus);

      if (authStatus && typeof authStatus.hasTokens === "boolean") {
        console.log("✅ Fast auth check utility works");
        results.fastAuthCheckWorks = true;

        if (!authStatus.hasTokens) {
          console.log("✅ No tokens detected correctly");
          results.noTokensDetected = true;
        }
      }
    } else {
      console.log("⚠️ Fast auth check utility not available globally");
    }
  } catch (error) {
    console.log("❌ Error testing fast auth check:", error);
  }

  // Test 2: Test immediate redirect behavior
  const currentPath = window.location.pathname;
  if (currentPath === "/" && !localStorage.getItem("token")) {
    console.log("✅ User redirected to home page without tokens");
    results.immediateRedirectWorks = true;
  }

  return results;
};

/**
 * Test back button behavior after logout
 * This should be run after logout to verify back button doesn't work
 */
const testBackButtonBehavior = () => {
  console.log("🔙 Testing back button behavior...");

  const currentPath = window.location.pathname;
  console.log("Current path:", currentPath);

  // Try to go back
  try {
    window.history.back();

    // Check after a short delay if we're still on the same page
    setTimeout(() => {
      const newPath = window.location.pathname;

      if (newPath === currentPath) {
        console.log("✅ Back button prevented navigation to protected page");
      } else {
        console.log("❌ Back button allowed navigation. New path:", newPath);

        // If we navigated to a dashboard page, that's a problem
        if (
          newPath.startsWith("/dashboard") ||
          newPath.startsWith("/my-projects") ||
          newPath.startsWith("/profile")
        ) {
          console.log(
            "🚨 SECURITY ISSUE: Back button allowed access to protected page!"
          );
        }
      }
    }, 100);
  } catch (error) {
    console.log("⚠️ Error testing back button:", error);
  }
};

/**
 * Manual test instructions
 */
const printTestInstructions = () => {
  console.log(`
🧪 LOGOUT FUNCTIONALITY TEST INSTRUCTIONS
=========================================

To properly test the logout functionality:

1. First, log in to the application and navigate to a dashboard page
2. Open the browser console and run: testLogoutFunctionality()
3. Click the logout button in the profile dropdown
4. After logout, run: testLogoutFunctionality() again
5. Try the back button test: testBackButtonBehavior()
6. Manually try to navigate back using browser back button
7. Verify that you cannot access dashboard pages without re-authentication

Expected Results:
- All authentication data should be cleared
- User should be redirected to home page (/)
- Profile dropdown should not be visible
- Back button should not allow access to dashboard pages
- Attempting to access dashboard URLs should redirect to login

Run these functions in the console:
- testLogoutFunctionality()
- testFastRedirection()
- testBackButtonBehavior()
- printTestInstructions()
  `);
};

// Export functions for use
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    testLogoutFunctionality,
    testFastRedirection,
    testBackButtonBehavior,
    printTestInstructions,
  };
}

// Auto-run instructions when script is loaded
if (typeof window !== "undefined") {
  console.log(
    "🧪 Logout test script loaded. Run printTestInstructions() for guidance."
  );
}
