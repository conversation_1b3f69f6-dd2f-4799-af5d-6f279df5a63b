'use client'

import React, { FormEvent, useState } from 'react'
import ButtenSubmit from '@/components/shared/ButtenSubmit'
import { forgotPassword } from '@/services/authService'
import EmailInput from '../components/input/EmailInput'
import BoxForm from '../components/BoxForm'
import Link from 'next/link'

export default function ForgotPassword() {
    const [email, setEmail] = useState('')
    const [isLoading, setIsLoadin] = useState<boolean>(false)
    const [error, setError] = useState('')
    const [response, setResponse] = useState({
        err: '',
        sendEmail: false
    })
    const forgotHandler = async (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        if (!email) return setError("Please fill in all fields")
        setIsLoadin(true)
        try {
            const response = await forgotPassword({ email })
            if (
                response.statusCode &&
                response.statusCode >= 200 &&
                response.statusCode < 300
            ) {
                setResponse({
                    err: '',
                    sendEmail: true
                })
            }
        } catch (err) {
            setResponse({
                err: 'error',
                sendEmail: false
            })
        } finally {
            setIsLoadin(false);
        }
    }
    return (
        <BoxForm>
            <form onSubmit={(e) => forgotHandler(e)} className='flex flex-col gap-6'>
                {response.sendEmail ?
                    <div>
                        <p className='mb-8'>
                            We've sent an email to {email} with further instructions on resetting your password.
                        </p>
                        <Link href={'/login'} className='btn btn--primary'>Back</Link>
                    </div>
                    : (
                        <>
                            <EmailInput onChange={setEmail} placeholder="Type Your Email here." />
                            {response.err && (
                                <div className="text-primary-red text-xs">
                                    {response.err}
                                </div>
                            )}
                            {error && (<div className="text-primary-red text-xs">{error}</div>)}
                            <ButtenSubmit text='Send Mail' textloading='Sending...' type='submit' isLoading={isLoading} />
                            <Link href={'/login'} className='text-gray-700 mx-auto block hover:text-primary duration-200'>Back</Link>
                        </>
                    )}
            </form>
        </BoxForm>
    )
}
