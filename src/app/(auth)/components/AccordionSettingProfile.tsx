import React, { useEffect, useRef, useState } from "react";
import UpArrow from "@/ui/icons/general/UpArrow";
type ChoiceType = Record<number, string[]>;
export type FaqsType = {
  title: string;
  name: string;
  faqs: { name: string; field: string }[];
  type: "single" | "multiple";
};
type AccordionSettingProfileType = {
  faqsData?: FaqsType[];
  choice: ChoiceType;
  setChoice: React.Dispatch<React.SetStateAction<ChoiceType>>;
  classPlus?: string;
  classBoxGrid?: string;
  errors?: Record<string, string>;
  showValidation?: boolean;
};
export const textSettingProfile: FaqsType[] = [
  {
    name: "business_type",
    type: "single",
    title: "Which of these describes your business best?",
    faqs: [
      { name: "Digital Agency", field: "digital_agency" },
      { name: "SaaS", field: "saas" },
      { name: "Freelance", field: "freelance" },
      { name: "IT Services", field: "it_services" },
      { name: "Franchise", field: "franchise" },
      { name: "E-Commerce", field: "e_commerce" },
      { name: "Multi-location business", field: "multi_location" },
      { name: "Other", field: "other" },
    ],
  },
  {
    name: "company_size",
    type: "single",
    title: "What’s the size of your company or team?",
    faqs: [
      { name: "Just me", field: "just_me" },
      { name: "2–10", field: "2_10" },
      { name: "11–50", field: "11_50" },
      { name: "51–200", field: "51_200" },
      { name: "201–500", field: "201_500" },
      { name: "500+", field: "500_plus" },
    ],
  },
  {
    name: "user_role",
    type: "single",
    title: "What's your role?",
    faqs: [
      { name: "Head of SEO", field: "head_of_seo" },
      { name: "Marketing Manager", field: "marketing_manager" },
      { name: "Owner/VP", field: "owner_vp" },
      { name: "Developer", field: "developer" },
      { name: "SEO Specialist", field: "seo_specialist" },
      { name: "Content Manager", field: "content_manager" },
      { name: "Analyst", field: "analyst" },
      { name: "Other", field: "other" },
    ],
  },
  {
    name: "offers_seo_services",
    type: "single",
    title: "Do you offer SEO services to clients?",
    faqs: [
      { name: "Yes, I run an SEO agency", field: "agency" },
      {
        name: "Yes, but it’s part of a larger service offering",
        field: "part_of_service",
      },
      {
        name: "No, I’m working on my own websites only",
        field: "own_websites_only",
      },
    ],
  },
  {
    name: "help_areas",
    type: "multiple",
    title: "What areas do you want help with?",
    faqs: [
      { name: "On-page SEO", field: "on_page_seo" },
      { name: "Technical SEO", field: "technical_seo" },
      { name: "Backlink analysis", field: "backlink_analysis" },
      { name: "Local SEO (Google Business)", field: "local_seo_gbp" },
      { name: "Content optimization", field: "content_analysis" },
      { name: "Competitor tracking", field: "competitor_tracking" },
    ],
  },
  {
    name: "interested_features",
    type: "multiple",
    title: "Which features are you most interested in using?",
    faqs: [
      { name: "Full SEO audit reports", field: "full_seo_audit" },
      {
        name: "White-label reporting for clients",
        field: "whitelabel_reporting",
      },
      { name: "Competitor benchmarking", field: "competitor_benchmarking" },
      { name: "Google Business Profile insights", field: "gbp_insights" },
      { name: "Content recommendations", field: "content_recommendations" },
      { name: "Backlink monitoring", field: "backlink_monitoring" },
      { name: "AI-generated SEO tasks", field: "ai_seo_tasks" },
    ],
  },
];
export default function AccordionSettingProfile({
  choice,
  setChoice,
  classPlus,
  classBoxGrid,
  errors,
  showValidation,
}: AccordionSettingProfileType) {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const contentRefs = useRef<(HTMLDivElement | null)[]>([]);
  const toggleAccordion = (index: number) => {
    setOpenIndex((prevIndex) => (prevIndex === index ? null : index));
  };
  const classBoxDiv = classBoxGrid || "grid grid-cols-2 mt-4 mb-3 gap-4";
  const handleSelect = (
    stepIndex: number,
    item: string,
    type: "single" | "multiple"
  ) => {
    setChoice((prev) => {
      const prevChoices = prev[stepIndex] || [];

      if (type === "single") {
        return { ...prev, [stepIndex]: [item] };
      }

      if (prevChoices.includes(item)) {
        // remove
        return {
          ...prev,
          [stepIndex]: prevChoices.filter((faq) => faq !== item),
        };
      } else {
        // add
        return {
          ...prev,
          [stepIndex]: [...prevChoices, item],
        };
      }
    });
  };
  useEffect(() => {
    setOpenIndex(0);
  }, []);
  if (!textSettingProfile) return;
  return (
    <div
      className={`w-full flex flex-col gap-3 md:gap-6 my-2 md:my-5 max-w-xl mx-auto ${classPlus}`}
    >
      {textSettingProfile?.map((item, index) => {
        const isOpen = openIndex === index;
        const selected = choice[index] || [];
        const hasError = showValidation && errors?.[item.name];
        const isRequired = true; // All sections are required
        const hasSelection = selected.length > 0;

        return (
          <div
            key={index}
            className={`bg-white rounded-xl border transition-all shadow-sm ${
              hasError ? "border-red-300 bg-red-50/30" : "border-gray-200"
            }`}
          >
            <div
              onClick={() => toggleAccordion(index)}
              className="flex items-center justify-between cursor-pointer text-gray-800 p-4"
            >
              <div className="flex items-center hover:text-primary duration-300 ease-in-out gap-3 text-sm md:text-base">
                <span
                  className={`px-3 py-2 flex items-center justify-center rounded-lg btn font-bold text-sm transition-colors ${
                    hasError
                      ? "bg-red-500 text-white border-red-500"
                      : hasSelection
                      ? "btn--primary"
                      : "border text-gray-600"
                  }`}
                >
                  {index + 1}
                </span>
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    {item?.title}
                    {isRequired && (
                      <span className="text-red-500 text-xs">*</span>
                    )}
                  </div>
                </div>
              </div>
              <span
                className={`transition-transform duration-300 md:text-xl ${
                  isOpen ? "rotate-180" : "rotate-0"
                }`}
              >
                <UpArrow />
              </span>
            </div>
            <div
              ref={(el) => {
                contentRefs.current[index] = el;
              }}
              style={{
                maxHeight: isOpen
                  ? `${contentRefs.current[index]?.scrollHeight || 0}px`
                  : "0px",
              }}
              className="overflow-hidden transition-all duration-500 ease-in-out"
            >
              <div className="p-4">
                <div className={classBoxDiv}>
                  {item.faqs.map((i) => (
                    <span
                      key={i.name}
                      onClick={() => handleSelect(index, i.field, item.type)}
                      className={`${
                        selected.includes(i.field)
                          ? "bg-[#A76BD0] text-white"
                          : " hover:text-primary duration-300 ease-in-out"
                      } cursor-pointer text-sm text-gray-700 p-2 md:p-3 rounded-md md:rounded-lg border transition-colors`}
                    >
                      {i.name}
                    </span>
                  ))}
                </div>
                {hasError && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600 text-sm flex items-center gap-2">
                      <span className="text-red-500">⚠</span>
                      {errors?.[item.name] || "This field is required"}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
