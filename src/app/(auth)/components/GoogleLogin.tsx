"use client";
import Image from "next/image";
import React, { useState } from "react";
import { showToast } from "@/lib/toast";

export default function GoogleLogin() {
  const [isLoading, setIsLoading] = useState(false);

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);

      // Redirect to Google OAuth endpoint
      window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/api/accounts/google/oauth-redirect/`;
    } catch (error) {
      setIsLoading(false);
      showToast.error("Failed to connect with Google. Please try again later.");
      console.error("Google login error:", error);
    }
  };

  return (
    <button
      onClick={handleGoogleLogin}
      disabled={isLoading}
      className="flex items-center justify-center gap-3 py-3 px-4 w-full border border-gray-200 rounded-lg text-gray-700 font-medium transition-all hover:bg-gray-50 hover:shadow-sm disabled:opacity-60 disabled:cursor-not-allowed"
    >
      {isLoading ? (
        <div className="w-5 h-5 border-2 border-t-transparent border-gray-600 rounded-full animate-spin"></div>
      ) : (
        <Image
          src={`/images/googleIcon.png`}
          width={20}
          height={20}
          alt="Sign in with Google"
          className="w-5 h-5"
        />
      )}
      <span>{isLoading ? "Connecting..." : "Continue with Google"}</span>
    </button>
  );
}
