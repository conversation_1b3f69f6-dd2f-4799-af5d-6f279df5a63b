// components/input/passwordYum.ts
"use client";
import * as Yup from "yup";

const schemaPassword = Yup.object().shape({
  password: Yup.string()
    // Allow spaces anywhere, but require at least one non-space character in rules
    .min(8, "Password must be at least 8 characters")
    .matches(/[a-z]/, "Password must contain at least one lowercase letter")
    .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
    .matches(/\d/, "Password must contain at least one number")
    .matches(
      /[!@#$%^&*(){}\[\]:;<>,.?~_+\-=/\\|`]/,
      "Password must contain at least one special character"
    )
    .required("Password is required"),
});
  
export default schemaPassword;
