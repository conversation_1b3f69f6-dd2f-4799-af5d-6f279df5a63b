import { ComponentProps } from "react";
import CustomCheckbox from "./CustomCheckbox";

type KeepLoggedCheckProps = {
  onChange: (value: boolean) => void;
  keepLoggedIn: boolean;
} & Omit<ComponentProps<"input">, "onChange">;

const KeepLoggedCheck: React.FC<KeepLoggedCheckProps> = ({
  keepLoggedIn,
  onChange,
}) => {
  return (
    <CustomCheckbox
      id="keepLoggedIn"
      label="Stay logged in"
      checked={keepLoggedIn}
      onChange={onChange}
    />
  );
};

export default KeepLoggedCheck;
