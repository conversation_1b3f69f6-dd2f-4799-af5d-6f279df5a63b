"use client";
import EyeClose from "@/ui/icons/input/EyeClose";
import <PERSON><PERSON><PERSON> from "@/ui/icons/input/EyeOpen";
import { LockClosedIcon } from "@heroicons/react/24/outline";
import { ComponentProps, useState, useCallback, memo } from "react";

type PassInputProps = {
  onChange: (value: string) => void;
  confirmPassword?: boolean;
} & Omit<ComponentProps<"input">, "onChange">;

const PassInputComponent: React.FC<PassInputProps> = ({
  onChange,
  className,
  confirmPassword,
  ...rest
}) => {
  const [showPassword, setShowPassword] = useState(false);
  // Optimized onChange handler to prevent unnecessary re-renders
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    },
    [onChange]
  );

  // Optimized toggle handler
  const togglePassword = useCallback(() => {
    setShowPassword((prev) => !prev);
  }, []);

  return (
    <div className="flex flex-col gap-2">
      <label
        className="text-secondary font-medium text-sm"
        htmlFor={confirmPassword ? `confirmPassword` : `password`}
      >
        <span className="text-red-500">*</span>{" "}
        {confirmPassword ? "Confirm Password" : "Password"}
      </label>
      <div className="flex relative items-center">
        <div className="absolute left-3 z-10 text-gray-500 pointer-events-none">
          <LockClosedIcon className="w-5 h-5 stroke-2" />
        </div>
        <input
          type={showPassword ? "text" : "password"}
          required
          name={confirmPassword ? `confirmPassword` : `password`}
          autoComplete={confirmPassword ? "new-password" : "current-password"}
          {...rest}
          onChange={handleChange}
          className={`textField__input pl-10 pr-12 w-full relative z-0 ${className}`}
          // Add performance attributes and autofill prevention
          data-lpignore="true" // Disable LastPass autofill
          data-form-type="other" // Prevent browser autofill detection
          data-1p-ignore="true" // Disable 1Password autofill
          data-bwignore="true" // Disable Bitwarden autofill
        />
        <div
          onClick={togglePassword}
          className="absolute transform -translate-y-1/2 right-3 z-20 top-1/2 cursor-pointer text-gray-500 hover:text-gray-700 transition-colors"
        >
          {showPassword ? <EyeOpen /> : <EyeClose />}
        </div>
      </div>
    </div>
  );
};

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(PassInputComponent);
