import { ComponentProps, memo, useCallback } from "react";
import { UserIcon } from "@heroicons/react/24/outline";

type FirstNameInputProps = {
  onChange: (value: string) => void;
} & Omit<ComponentProps<"input">, "onChange">;

const FirstNameInputComponent: React.FC<FirstNameInputProps> = ({
  onChange,
  className,
  ...rest
}) => {
  // Optimized onChange handler to prevent unnecessary re-renders
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    },
    [onChange]
  );

  return (
    <div className="flex flex-col gap-2">
      <label className="text-secondary font-medium text-sm" htmlFor="firstName">
        <span className="text-red-500">*</span> First Name
      </label>
      <div className="flex relative items-center">
        <div className="absolute left-3 z-10 text-gray-500 pointer-events-none">
          <UserIcon className="w-5 h-5 stroke-2" />
        </div>
        <input
          type="text"
          name="firstName"
          required
          autoComplete="given-name"
          {...rest}
          onChange={handleChange}
          className={`textField__input pl-10 w-full relative z-0 ${className}`}
          // Add performance attributes and autofill prevention
          data-lpignore="true" // Disable LastPass autofill
          data-form-type="other" // Prevent browser autofill detection
          data-1p-ignore="true" // Disable 1Password autofill
          data-bwignore="true" // Disable Bitwarden autofill
        />
      </div>
    </div>
  );
};

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(FirstNameInputComponent);
