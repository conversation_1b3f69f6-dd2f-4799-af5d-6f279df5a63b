import ProviderQuery from "@/components/CreateProject/ProviderQuery";
import "./../globals.css";
import nunitoSansFont from "@/constants/localFont";
import { Metadata } from "next";
import { ToastProvider } from "@/lib/ToastProvider";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "SEO Analyser | Create Project",
  description:
    "Our all-in-one SEO Analyser uncovers hidden issues, delivers actionable insights, and helps you rank higher, faster.",
};
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="create-project-layout no-scrollbar-gutter">
      <body
        className={`${nunitoSansFont.variable} font-[family-name:var(--font-nunito-sans)] antialiased relative min-h-screen bg-[#F4F4F4] create-project-page no-scrollbar-gutter`}
      >
        <Suspense fallback={null}>
          <ProviderQuery>{children}</ProviderQuery>
        </Suspense>
        <ToastProvider />
      </body>
    </html>
  );
}
