import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { projectAPI } from "@/services/projectService";

// Mock the project API
jest.mock("@/services/projectService");
const mockedProjectAPI = projectAPI as jest.Mocked<typeof projectAPI>;

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
  }),
  usePathname: () => "/create-project/project-information",
}));

// Mock components that might have complex dependencies
jest.mock("@/components/CreateProject/PageTransition", () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  AnimatedElement: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

describe("Create Project Integration Tests", () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
    jest.clearAllMocks();
    // Reset store state
    useCreateProjectStore.getState().resetAll();
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  describe("Project Information Step", () => {
    it("should create project successfully and store project ID", async () => {
      const mockResponse = {
        data: {
          id: "project-123",
          url: "https://example.com",
          domain_type: "*.example.com",
          project_name: "Test Project",
          project_color: "#FF6B35",
          status: "active",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
      };

      mockedProjectAPI.createProject.mockResolvedValue(mockResponse);

      // Import the page component dynamically to avoid import issues
      const ProjectInformationPage = (
        await import("../(Information)/project-information/page")
      ).default;

      renderWithProviders(<ProjectInformationPage />);

      // Fill in the form
      const domainInput = screen.getByPlaceholderText(/domain/i);
      const nameInput = screen.getByPlaceholderText(/project name/i);

      fireEvent.change(domainInput, {
        target: { value: "https://example.com" },
      });
      fireEvent.change(nameInput, { target: { value: "Test Project" } });

      // Submit the form
      const submitButton = screen.getByRole("button", { name: /continue/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockedProjectAPI.createProject).toHaveBeenCalledWith({
          url: "https://example.com",
          domain_type: "*.example.com",
          project_name: "Test Project",
          project_color: expect.any(String),
        });
      });

      // Verify project ID is stored
      const store = useCreateProjectStore.getState();
      expect(store.projectInfo?.id).toBe("project-123");
    });

    it("should update existing project when project ID exists", async () => {
      // Set up existing project in store
      const existingProject = {
        id: "project-456",
        name: "Existing Project",
        domain: "https://existing.com",
        color: "#FF6B35",
      };

      useCreateProjectStore.getState().setProjectInfo(existingProject);

      const mockUpdateResponse = {
        data: {
          id: "project-456",
          url: "https://updated.com",
          domain_type: "*.updated.com",
          project_name: "Updated Project",
          project_color: "#3498DB",
          status: "enabled",
          updated_at: "2024-01-01T00:00:00Z",
        },
      };

      mockedProjectAPI.updateProject.mockResolvedValue(mockUpdateResponse);

      const ProjectInformationPage = (
        await import("../(Information)/project-information/page")
      ).default;

      renderWithProviders(<ProjectInformationPage />);

      // Fill in the form with updated data
      const domainInput = screen.getByPlaceholderText(/domain/i);
      const nameInput = screen.getByPlaceholderText(/project name/i);

      fireEvent.change(domainInput, {
        target: { value: "https://updated.com" },
      });
      fireEvent.change(nameInput, { target: { value: "Updated Project" } });

      // Submit the form
      const submitButton = screen.getByRole("button", { name: /update/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockedProjectAPI.updateProject).toHaveBeenCalledWith(
          "project-456",
          {
            url: "https://updated.com",
            domain_type: "*.updated.com",
            project_name: "Updated Project",
            project_color: expect.any(String),
            status: "enabled",
            primary_search_engines: [],
            keywords: [],
            competitors: [],
          }
        );
      });

      // Verify project info is updated in store
      const store = useCreateProjectStore.getState();
      expect(store.projectInfo?.id).toBe("project-456");
      expect(store.projectInfo?.name).toBe("Updated Project");
      expect(store.projectInfo?.domain).toBe("https://updated.com");
    });

    it("should handle API errors gracefully", async () => {
      const mockError = {
        response: {
          data: {
            message: "Invalid domain format",
          },
        },
      };

      mockedProjectAPI.createProject.mockRejectedValue(mockError);

      const ProjectInformationPage = (
        await import("../(Information)/project-information/page")
      ).default;

      renderWithProviders(<ProjectInformationPage />);

      // Fill in invalid data
      const domainInput = screen.getByPlaceholderText(/domain/i);
      const nameInput = screen.getByPlaceholderText(/project name/i);

      fireEvent.change(domainInput, { target: { value: "invalid-domain" } });
      fireEvent.change(nameInput, { target: { value: "Test Project" } });

      // Submit the form
      const submitButton = screen.getByRole("button", { name: /continue/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/invalid domain format/i)).toBeInTheDocument();
      });

      // Verify error state is set
      const store = useCreateProjectStore.getState();
      expect(store.error).toBe("Invalid domain format");
    });
  });

  describe("Keywords Step", () => {
    beforeEach(() => {
      // Set up initial state for keywords step
      const store = useCreateProjectStore.getState();
      store.setProjectInfo({
        id: "project-123",
        name: "Test Project",
        domain: "https://example.com",
        color: "#FF6B35",
      });
      store.addSearchEngineConfig({
        searchEngine: { name: "Google", image: "/google.svg" },
        country: { name: "United States", code: "US", image: "/us.svg" },
        language: { name: "English", code: "en" },
        location: null,
      });
    });

    it("should fetch keyword suggestions successfully", async () => {
      const mockSuggestions = {
        data: {
          keywords: ["seo", "marketing", "digital"],
          project_id: "project-123",
        },
      };

      mockedProjectAPI.getKeywordSuggestions.mockResolvedValue(mockSuggestions);

      const KeywordsPage = (await import("../(Information)/keywords/page"))
        .default;

      renderWithProviders(<KeywordsPage />);

      await waitFor(() => {
        expect(mockedProjectAPI.getKeywordSuggestions).toHaveBeenCalledWith({
          project_id: "project-123",
          location: "US",
          language_code: "en",
        });
      });

      // Verify suggestions are displayed
      await waitFor(() => {
        expect(screen.getByText("seo")).toBeInTheDocument();
        expect(screen.getByText("marketing")).toBeInTheDocument();
        expect(screen.getByText("digital")).toBeInTheDocument();
      });
    });

    it("should add suggested keywords to the project", async () => {
      const mockSuggestions = {
        data: {
          url: "https://example.com",
          keywords: [
            {
              keyword: "seo",
              frequency: 5,
              in_title: true,
              in_meta_description: true,
              in_headings: false,
              in_footer: false,
            },
            {
              keyword: "marketing",
              frequency: 3,
              in_title: false,
              in_meta_description: true,
              in_headings: true,
              in_footer: false,
            },
          ],
          phrases: [],
          total_words: 150,
        },
      };

      mockedProjectAPI.getKeywordSuggestions.mockResolvedValue(mockSuggestions);

      const KeywordsPage = (await import("../(Information)/keywords/page"))
        .default;

      renderWithProviders(<KeywordsPage />);

      await waitFor(() => {
        expect(screen.getByText("seo")).toBeInTheDocument();
      });

      // Click on a suggested keyword's Add button
      const addButtons = screen.getAllByText("Add");
      const seoAddButton = addButtons.find((button) =>
        button.closest("div")?.textContent?.includes("seo")
      );

      if (seoAddButton) {
        fireEvent.click(seoAddButton);
      }

      // Verify keyword is added to store
      const store = useCreateProjectStore.getState();
      expect(store.keywords).toHaveLength(1);
      expect(store.keywords[0].keyword).toBe("seo");
    });
  });

  describe("Analytics Services Step (Final)", () => {
    beforeEach(() => {
      // Set up complete state for final step
      const store = useCreateProjectStore.getState();
      store.setProjectInfo({
        id: "project-123",
        name: "Test Project",
        domain: "https://example.com",
        color: "#FF6B35",
      });
      store.addSearchEngineConfig({
        searchEngine: { name: "Google", image: "/google.svg" },
        country: { name: "United States", code: "US", image: "/us.svg" },
        language: { name: "English", code: "en" },
        location: null,
      });
      store.addKeyword("test keyword", ["config-1"]);
    });

    it("should finalize project creation successfully", async () => {
      const mockResponse = {
        data: {
          id: "project-123",
          url: "https://example.com",
          project_name: "Test Project",
          status: "active",
        },
      };

      mockedProjectAPI.createFullProject.mockResolvedValue(mockResponse);

      const AnalyticsServicesPage = (
        await import("../(Information)/analytics-services/page")
      ).default;

      renderWithProviders(<AnalyticsServicesPage />);

      // Submit the final form
      const finishButton = screen.getByRole("button", { name: /finish/i });
      fireEvent.click(finishButton);

      await waitFor(() => {
        expect(mockedProjectAPI.createFullProject).toHaveBeenCalledWith({
          url: "https://example.com",
          domain_type: "*.example.com",
          project_name: "Test Project",
          project_color: "#FF6B35",
          primary_search_engines: expect.any(Array),
          keywords: expect.any(Array),
          competitors: [],
        });
      });
    });
  });
});
