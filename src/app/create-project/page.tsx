"use client";
import ShortcutPage from "@/components/CreateProject/ShortcutPage";
import React, { Suspense, useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useSearchParams, useRouter } from "next/navigation";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { useEditNavigation } from "@/hooks/useEditProject";

function CreateProjectClient() {
  const [step, setStep] = useState("welcome");
  const [hasAdvanced, setHasAdvanced] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const { setCreateFromScratch } = useCreateProjectStore();
  const { urls } = useEditNavigation();

  // Handle mode parameter for direct create mode
  useEffect(() => {
    const modeParam = searchParams?.get("mode");
    if (modeParam === "create") {
      // Set create mode flag and redirect to project information
      setCreateFromScratch();
      router.replace(`${urls.projectInformation}?mode=create`);
    }
  }, [searchParams, router, setCreateFromScratch, urls.projectInformation]);

  useEffect(() => {
    let isMounted = true;

    if (step === "welcome" && !hasAdvanced) {
      const timeout = setTimeout(() => {
        if (isMounted) {
          setStep("shortcut");
          setHasAdvanced(true);
        }
      }, 2000);

      return () => {
        isMounted = false;
        clearTimeout(timeout);
      };
    }
  }, [step, hasAdvanced]);
  // Animation variants for welcome screen
  const welcomeVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.4 },
    },
  };

  const elementVariants = {
    initial: { opacity: 0, y: 30 },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.1, 0.25, 1.0],
      },
    },
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [-2, 2, -2],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  const decorativeVariants = {
    initial: { scale: 0, opacity: 0, willChange: "transform, opacity" },
    animate: {
      scale: 1,
      opacity: [0.6, 0.8, 0.6],
      willChange: "transform, opacity",
      transition: {
        duration: 0.8,
        opacity: {
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
        },
      },
    },
  };

  let content: React.ReactNode;
  switch (step) {
    case "welcome":
      content = (
        <AnimatePresence mode="wait">
          <motion.div
            key="welcome"
            variants={welcomeVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="min-h-screen relative overflow-hidden bg-gray-50"
          >
            {/* Enhanced Purple decorative elements with framer-motion */}
            <motion.div
              variants={decorativeVariants}
              className="absolute top-0 right-0 w-96 h-96 bg-purple-500 rounded-full opacity-80 transform translate-x-48 -translate-y-48"
            />
            <motion.div
              variants={decorativeVariants}
              transition={{ delay: 0.2 }}
              className="absolute bottom-0 left-0 w-80 h-80 bg-purple-600 rounded-full opacity-70 transform -translate-x-40 translate-y-40"
            />
            <motion.div
              variants={decorativeVariants}
              transition={{ delay: 0.4 }}
              className="absolute bottom-0 right-0 w-64 h-64 bg-indigo-500 rounded-full opacity-75 transform translate-x-32 translate-y-32"
            />

            {/* Additional floating circles with enhanced animations */}
            <motion.div
              variants={decorativeVariants}
              transition={{ delay: 0.6 }}
              className="absolute top-1/4 left-10 w-32 h-32 bg-purple-400 rounded-full opacity-70"
            />
            <motion.div
              variants={decorativeVariants}
              transition={{ delay: 0.8 }}
              className="absolute top-3/4 right-20 w-24 h-24 bg-violet-500 rounded-full opacity-80"
            />

            {/* Main content with staggered animations */}
            <motion.div
              variants={elementVariants}
              className="relative z-10 min-h-screen flex flex-col items-center justify-center px-4"
            >
              {/* SEO Logo with entrance animation */}
              <motion.div variants={elementVariants} className="mb-16">
                <motion.div
                  variants={floatingVariants}
                  animate="animate"
                  className="w-[140px] h-[55px] relative"
                >
                  <img
                    src="/images/appLogo.svg"
                    alt="SEO Analyser Logo"
                    className="w-full h-full object-contain"
                  />
                </motion.div>
              </motion.div>

              {/* Kangaroo Illustration with floating animation */}
              <motion.div variants={elementVariants} className="mb-12">
                <motion.img
                  variants={floatingVariants}
                  animate="animate"
                  src="/images/dashboard/kangaroo.svg"
                  alt="kangaroo"
                  className="w-[293px] h-[243px] object-contain"
                />
              </motion.div>

              {/* Welcome Text with subtle animation */}
              <motion.div variants={elementVariants} className="text-center">
                <motion.h1
                  className="text-xl font-semibold text-[#344054]"
                  animate={{
                    opacity: [0.8, 1, 0.8],
                    transition: {
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    },
                  }}
                >
                  Let's Create A Project
                </motion.h1>
              </motion.div>
            </motion.div>
          </motion.div>
        </AnimatePresence>
      );
      break;
    case "shortcut":
      content = (
        <AnimatePresence mode="wait">
          <motion.div
            key="shortcut"
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.5,
                ease: [0.25, 0.1, 0.25, 1.0],
                staggerChildren: 0.1,
                delayChildren: 0.1,
              },
            }}
            exit={{
              opacity: 0,
              y: -10,
              transition: { duration: 0.3 },
            }}
            className="min-h-screen bg-[#F4F4F4]"
          >
            {/* Enhanced Container with Better Spacing */}
            <motion.div
              variants={elementVariants}
              className="w-full max-w-6xl mx-auto px-4 xl:px-2 sm:px-6 lg:px-8 py-4"
            >
              {/* Main Content Card */}
              <motion.div variants={elementVariants} className="w-full mx-auto">
                <motion.div
                  variants={elementVariants}
                  className="overflow-hidden"
                >
                  <motion.div variants={elementVariants} className="p-6 lg:p-8">
                    <ShortcutPage />
                  </motion.div>
                </motion.div>
              </motion.div>

              {/* Footer Info */}
              <motion.div
                variants={elementVariants}
                className="text-center mt-8 lg:mt-12"
              >
                <motion.div
                  className="inline-flex items-center gap-2 text-sm text-[#344054]"
                  whileHover={{
                    scale: 1.02,
                    transition: { duration: 0.2 },
                  }}
                >
                  <motion.svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    animate={{
                      rotate: [0, 5, -5, 0],
                      transition: {
                        duration: 4,
                        repeat: Infinity,
                        ease: "easeInOut",
                      },
                    }}
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </motion.svg>
                  Need help? Check our documentation or contact support
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>
        </AnimatePresence>
      );
      break;
  }

  return content;
}

export default function Page() {
  return (
    <Suspense fallback={null}>
      <CreateProjectClient />
    </Suspense>
  );
}
