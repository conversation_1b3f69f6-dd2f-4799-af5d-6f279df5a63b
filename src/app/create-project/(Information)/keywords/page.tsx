"use client";

import React, { useEffect, useState } from "react";
import BoxCreateProject from "@/components/CreateProject/BoxCreateProject";
import KeywordLimitCard from "@/components/CreateProject/KeywordLimitCard";
import NavbarCreateProject from "@/components/CreateProject/NavbarCreateProject";
import TitleCreateProject from "@/components/CreateProject/TitleCreateProject";
import StepMobileCreateProject from "@/components/CreateProject/StepMobileCreateProject";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import { useRouter } from "next/navigation";
import { useEditProject, useEditNavigation } from "@/hooks/useEditProject";
import Link from "next/link";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useCreateProjectStore } from "@/store/createProjectStore";
import {
  projectAPI,
  ProjectSearchEngine,
  ProjectKeyword,
} from "@/services/projectService";
import PageTransition, {
  AnimatedElement,
} from "@/components/CreateProject/PageTransition";
import {
  SearchEngineConfigurationStep,
  KeywordsInputStep,
  KeywordsList,
  useFileImport,
  KeywordTableRow,
} from "@/components/CreateProject/keywords";
import createProjectToast from "@/lib/createProjectToast";

export default function Page() {
  const route = useRouter();
  const { isEditMode, isProjectDataReady } = useEditProject();
  const { urls } = useEditNavigation();

  // Store hooks
  const {
    searchEngineConfigs,
    selectedConfigIds,
    setSelectedConfigIds,
    setCurrentStep,
    keywords,
    addKeyword,
    removeKeyword,
    getTotalKeywordCount,
    getConfigById,
    projectInfo,
    markStepComplete,
  } = useCreateProjectStore();

  // Local state
  const [keywordInput, setKeywordInput] = useState("");
  const [error, setError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [configDropdownOpen, setConfigDropdownOpen] = useState(false);
  const [autoFill, setAutoFill] = useState<boolean>(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // Track which location/language combinations have been fetched
  const [fetchedCombinations, setFetchedCombinations] = useState<Set<string>>(
    new Set()
  );

  // Helper function to create a unique key for location/language combination
  const createLocationLanguageKey = (
    countryCode: string,
    languageCode: string
  ) => {
    return `${countryCode}-${languageCode}`;
  };

  // Check if there are any non-ChatGPT configurations selected
  const hasNonChatGPTConfigurations = () => {
    return selectedConfigIds.some((configId) => {
      const config = getConfigById(configId);
      return config && config.searchEngine.name.toLowerCase() !== "chatgpt";
    });
  };

  // File import hook
  const { handleFileChange } = useFileImport({
    selectedConfigIds,
    addKeyword,
    setError,
  });

  // Generate table rows from keywords and configurations
  const generateTableRows = (): KeywordTableRow[] => {
    const rows: KeywordTableRow[] = [];

    keywords.forEach((keywordData) => {
      keywordData.configIds.forEach((configId) => {
        const config = getConfigById(configId);
        if (config) {
          // Format volume display
          let volumeDisplay = "N/A";
          let numericVolume = 0;
          if (keywordData.search_volume) {
            numericVolume = keywordData.search_volume;
            if (keywordData.search_volume >= 1000) {
              volumeDisplay = `${(keywordData.search_volume / 1000).toFixed(
                1
              )}k`;
            } else {
              volumeDisplay = keywordData.search_volume.toString();
            }
          }

          rows.push({
            id: `${keywordData.keyword}-${configId}`,
            keyword: keywordData.keyword,
            searchEngine: config.searchEngine,
            country: config.country,
            language: config.language,
            location: config.location,
            volume: volumeDisplay,
            numericVolume: numericVolume, // Store numeric value for sorting
            configId: configId, // Add configId for removal
            isSuggested: keywordData.isSuggested, // Include suggested flag
          });
        }
      });
    });

    // Sort rows: manual keywords first (newest first), then suggested keywords by search volume (highest first)
    const manualRows = rows.filter((row) => !row.isSuggested);
    const suggestedRows = rows.filter((row) => row.isSuggested);

    // Sort suggested rows by search volume (highest first)
    suggestedRows.sort(
      (a, b) => (b.numericVolume || 0) - (a.numericVolume || 0)
    );

    // Manual keywords maintain their order (newest first from store)
    // Combine: manual first, then suggested sorted by volume
    return [...manualRows, ...suggestedRows];
  };

  const tableRows = generateTableRows();
  const totalRows = tableRows.length;
  const totalPages = Math.ceil(totalRows / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentRows = tableRows.slice(startIndex, endIndex);

  // Configuration selection handlers
  const handleConfigToggle = (configId: string) => {
    const newSelectedIds = selectedConfigIds.includes(configId)
      ? selectedConfigIds.filter((id) => id !== configId)
      : [...selectedConfigIds, configId];
    setSelectedConfigIds(newSelectedIds);
  };

  const handleSelectAll = () => {
    setSelectedConfigIds(searchEngineConfigs.map((config) => config.id));
  };

  const handleDeselectAll = () => {
    setSelectedConfigIds([]);
  };

  // Keyword handlers
  const handleAddKeyword = () => {
    if (!keywordInput.trim()) {
      setError("Please enter a keyword");
      return;
    }

    if (selectedConfigIds.length === 0) {
      setError(
        "Please select at least one search engine configuration to track this keyword"
      );
      return;
    }

    addKeyword(keywordInput.trim(), selectedConfigIds);
    setKeywordInput("");
    setError("");
    // Reset to first page to show newly added keyword
    setCurrentPage(1);
  };

  const handleRemoveKeywordConfig = (keyword: string, configId: string) => {
    const keywordData = keywords.find((k) => k.keyword === keyword);
    if (keywordData) {
      const updatedConfigIds = keywordData.configIds.filter(
        (id) => id !== configId
      );
      if (updatedConfigIds.length === 0) {
        // Remove the entire keyword if no configs left
        removeKeyword(keyword);
      } else {
        // Update the keyword with remaining configs
        // For now, we'll remove and re-add the keyword
        removeKeyword(keyword);
        addKeyword(keyword, updatedConfigIds, keywordData.isSuggested, {
          search_volume: keywordData.search_volume,
          competition: keywordData.competition,
          cpc: keywordData.cpc,
          difficulty: keywordData.difficulty,
          relevance_score: keywordData.relevance_score,
        });
      }
    }
  };

  const handleFileImport = () => {
    fileInputRef.current?.click();
  };

  // Handle adding suggested keywords directly to table
  const handleAddSuggestedKeywords = (
    suggestedKeywords: any[],
    locationLanguageKey: string
  ) => {
    if (selectedConfigIds.length === 0) {
      setError("Please select at least one search engine configuration first");
      return 0;
    }

    // Extract country and language codes from the locationLanguageKey
    const [countryCode, languageCode] = locationLanguageKey.split("-");

    // Find only the configurations that match this specific country/language combination
    const matchingConfigIds = selectedConfigIds.filter((configId) => {
      const config = getConfigById(configId);
      const isMatch =
        config &&
        config.country.code === countryCode &&
        config.language.code === languageCode &&
        config.searchEngine.name.toLowerCase() !== "chatgpt";

      console.log(
        `Config ${configId}: ${config?.searchEngine.name}-${config?.country.code}-${config?.language.code}, matches: ${isMatch}`
      );
      return isMatch;
    });

    console.log(
      `Found ${matchingConfigIds.length} matching configurations for ${countryCode}-${languageCode}`
    );
    console.log(`Matching config IDs:`, matchingConfigIds);

    if (matchingConfigIds.length === 0) {
      console.log(
        `No matching configurations found for ${countryCode}-${languageCode}`
      );
      return 0;
    }

    let addedCount = 0;

    // Add each suggested keyword to the table with isSuggested flag
    suggestedKeywords.forEach((keywordData) => {
      // Check if keyword already exists
      const existingKeyword = keywords.find(
        (k) => k.keyword.toLowerCase() === keywordData.keyword.toLowerCase()
      );

      if (!existingKeyword) {
        // Extract additional data from the suggestion
        const additionalData = {
          search_volume: keywordData.search_volume,
          competition: keywordData.competition,
          cpc: keywordData.cpc,
          difficulty: keywordData.difficulty,
          relevance_score: keywordData.relevance_score,
        };

        // Only add to configurations that match the API call's country/language
        addKeyword(
          keywordData.keyword,
          matchingConfigIds,
          true,
          additionalData
        ); // true for isSuggested
        addedCount++;
      } else {
        // If keyword exists, check if any of the matching configs are missing
        const missingConfigIds = matchingConfigIds.filter(
          (configId) => !existingKeyword.configIds.includes(configId)
        );

        if (missingConfigIds.length > 0) {
          // Add the keyword to the missing configurations only
          // We need to merge carefully to avoid adding to ChatGPT configs
          const updatedConfigIds = [
            ...existingKeyword.configIds,
            ...missingConfigIds,
          ];

          // Extract additional data from the suggestion
          const additionalData = {
            search_volume: keywordData.search_volume,
            competition: keywordData.competition,
            cpc: keywordData.cpc,
            difficulty: keywordData.difficulty,
            relevance_score: keywordData.relevance_score,
          };

          // Remove the existing keyword and re-add with updated configs
          removeKeyword(existingKeyword.keyword);
          addKeyword(
            keywordData.keyword,
            updatedConfigIds,
            existingKeyword.isSuggested || true, // Keep existing suggested status or mark as suggested
            additionalData
          );
          addedCount++;
        }
      }
    });

    // Reset to first page to show newly added keywords
    if (addedCount > 0) {
      setCurrentPage(1);
    }

    setError("");
    return addedCount;
  };

  // Get unique location/language combinations from selected configurations (excluding ChatGPT)
  const getUniqueLocationLanguageCombinations = () => {
    const combinations = new Set<string>();
    selectedConfigIds.forEach((configId) => {
      const config = getConfigById(configId);
      if (config && config.searchEngine.name.toLowerCase() !== "chatgpt") {
        const key = createLocationLanguageKey(
          config.country.code,
          config.language.code
        );
        combinations.add(key);
      }
    });
    return Array.from(combinations);
  };

  // Check if there are new combinations that haven't been fetched
  const hasNewCombinations = () => {
    const currentCombinations = getUniqueLocationLanguageCombinations();
    return currentCombinations.some((combo) => !fetchedCombinations.has(combo));
  };

  // Fetch keyword suggestions - only when autoFill is enabled and there are new combinations
  const {
    isLoading: isLoadingSuggestions,
    error: queryError,
    refetch: refetchSuggestions,
  } = useQuery({
    queryKey: [
      "keyword-suggestions",
      projectInfo?.id,
      autoFill,
      selectedConfigIds,
      Array.from(fetchedCombinations), // Include fetched combinations in query key
    ],
    queryFn: async () => {
      if (!projectInfo?.id || selectedConfigIds.length === 0) return null;

      // Get unique location/language combinations that haven't been fetched
      const currentCombinations = getUniqueLocationLanguageCombinations();
      const newCombinations = currentCombinations.filter(
        (combo) => !fetchedCombinations.has(combo)
      );

      console.log("Current combinations:", currentCombinations);
      console.log(
        "Already fetched combinations:",
        Array.from(fetchedCombinations)
      );
      console.log("New combinations to fetch:", newCombinations);

      if (newCombinations.length === 0) {
        console.log("No new combinations to fetch");
        return null;
      }

      // Process each new combination
      const results = [];
      for (const combination of newCombinations) {
        const [countryCode, languageCode] = combination.split("-");

        try {
          console.log(
            `Fetching suggestions for ${countryCode}-${languageCode}`
          );

          const response = await projectAPI.getKeywordSuggestions({
            project_id: projectInfo.id,
            location: countryCode,
            language_code: languageCode,
          });

          // The API returns { data: { status: "success", data: { keywords: [...], phrases: [...] } } }
          const actualData = (response.data as any).data || response.data;

          if (actualData) {
            // Use keywords if available, otherwise use phrases
            const suggestionsToAdd =
              actualData.keywords && actualData.keywords.length > 0
                ? actualData.keywords
                : actualData.phrases || [];

            // Convert phrases to keyword format if needed
            const formattedSuggestions = suggestionsToAdd.map((item: any) => {
              if (item.phrase) {
                return {
                  keyword: item.phrase,
                  search_volume: item.search_volume,
                  competition: item.competition,
                  cpc: item.cpc,
                  difficulty: item.difficulty,
                  relevance_score: item.relevance_score,
                };
              }
              return item;
            });

            // Add suggested keywords for this combination
            let addedCount = 0;
            if (
              selectedConfigIds.length > 0 &&
              formattedSuggestions.length > 0
            ) {
              addedCount = handleAddSuggestedKeywords(
                formattedSuggestions,
                combination
              );
            }

            // Show success toast only if keywords were actually added
            if (addedCount > 0) {
              createProjectToast.success.keywordsSuggested(addedCount);
            }

            results.push({
              combination,
              data: actualData,
              count: addedCount,
            });
          }
        } catch (error) {
          console.error(
            `Error fetching suggestions for ${combination}:`,
            error
          );
          createProjectToast.handleError(
            error as any,
            `get keyword suggestions for ${countryCode}-${languageCode}`
          );
        }
      }

      // Mark all processed combinations as fetched
      newCombinations.forEach((combination) => {
        setFetchedCombinations((prev) => new Set(prev).add(combination));
      });

      return results;
    },
    enabled:
      !!projectInfo?.id &&
      autoFill &&
      selectedConfigIds.length > 0 &&
      hasNewCombinations() &&
      getUniqueLocationLanguageCombinations().length > 0, // Ensure there are non-ChatGPT configurations
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      if (!projectInfo?.id) {
        throw new Error("Project ID is missing");
      }

      if (keywords.length === 0) {
        createProjectToast.warning.missingConfiguration("keywords");
        throw new Error("Please add at least one keyword");
      }

      // Transform search engine configs to API format
      const primarySearchEngines: ProjectSearchEngine[] = searchEngineConfigs.map(
        (config) => ({
          search_engine: config.searchEngine.name.toLowerCase(),
          countries: [config.country.code],
          languages: [config.language.code],
        })
      );

      // Transform keywords to API format based on selected configIds
      const projectKeywords: ProjectKeyword[] = keywords.map((keyword) => {
        const keywordConfigs = keyword.configIds
          .map((id) => searchEngineConfigs.find((config) => config.id === id))
          .filter(Boolean);

        const searchEngines = [
          ...new Set(
            (keywordConfigs as typeof searchEngineConfigs).map((config) =>
              config?.searchEngine.name.toLowerCase()
            )
          ),
        ].filter(Boolean) as string[];

        const countries = [
          ...new Set(
            (keywordConfigs as typeof searchEngineConfigs).map(
              (config) => config?.country.code
            )
          ),
        ].filter(Boolean) as string[];

        const languages = [
          ...new Set(
            (keywordConfigs as typeof searchEngineConfigs).map(
              (config) => config?.language.code
            )
          ),
        ].filter(Boolean) as string[];

        return {
          keyword: keyword.keyword,
          search_engines: searchEngines,
          countries,
          languages,
        };
      });

      // Send PATCH with only keywords (and ensure search engines persisted separately)
      return await createProjectToast.apiCall(
        projectAPI.partialUpdateProject(projectInfo.id, {
          keywords: projectKeywords,
        }),
        {
          loadingMessage: "Saving keywords...",
          successMessage: `Saved ${projectKeywords.length} keyword${
            projectKeywords.length > 1 ? "s" : ""
          } successfully!`,
          errorContext: "update project keywords",
        }
      );
    },
    onSuccess: () => {
      // Mark keywords step as complete and navigate next
      markStepComplete("keywords");
      setCurrentStep("competitors");
      route.push(urls.competitors);
    },
    onError: (error: any) => {
      console.error(error?.message || "Failed to save keywords");
    },
  });

  // Effect to trigger refetch when new configurations with different locations/languages are selected
  useEffect(() => {
    if (
      autoFill &&
      selectedConfigIds.length > 0 &&
      hasNewCombinations() &&
      getUniqueLocationLanguageCombinations().length > 0 // Ensure there are non-ChatGPT configurations
    ) {
      console.log(
        "New location/language combinations detected, triggering refetch"
      );
      refetchSuggestions();
    }
  }, [selectedConfigIds, autoFill, refetchSuggestions]); // Removed fetchedCombinations to prevent infinite loop

  // Effect to reset fetched combinations when suggest is turned off
  useEffect(() => {
    if (!autoFill) {
      setFetchedCombinations(new Set());
    }
  }, [autoFill]);

  useEffect(() => {
    // Set current step
    setCurrentStep("keywords");

    // In edit mode, wait for project data to be ready before any validation
    if (isEditMode && !isProjectDataReady) {
      return;
    }

    // Only redirect in create mode if no configurations exist
    // In edit mode, allow access even without search engine configs
    if (!isEditMode && searchEngineConfigs.length === 0) {
      route.push("/create-project/search-engines");
      return;
    }
  }, [
    setCurrentStep,
    searchEngineConfigs,
    route,
    isEditMode,
    isProjectDataReady,
  ]);

  return (
    <PageTransition>
      <div className="flex justify-between flex-col gap-6 lg:gap- h-full min-h-[calc(100vh-2rem)] max-w-8xl mx-auto">
        <div className="flex flex-col gap-3 ">
          <AnimatedElement variant="child">
            <NavbarCreateProject>Keywords</NavbarCreateProject>
          </AnimatedElement>
          <AnimatedElement variant="child" delay={0.1}>
            <StepMobileCreateProject />
          </AnimatedElement>

          <AnimatedElement variant="card" delay={0.2}>
            <BoxCreateProject classPlus="relative">
              {/* Header Section */}
              <AnimatedElement variant="child" delay={0.3}>
                <div className="mb-4">
                  <TitleCreateProject
                    head="Add target keywords"
                    description="Select your search engine configurations and add keywords to track across different markets and regions."
                    classHead="text-lg lg:text-xl font-semibold text-[#344054]"
                    classP="text-[#344054] mt-3 leading-relaxed"
                  />
                </div>
              </AnimatedElement>

              {/* Error Display */}
              {error && (
                <AnimatedElement variant="child" delay={0.4}>
                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-[#344054] text-sm">{error}</p>
                  </div>
                </AnimatedElement>
              )}

              {/* Step 1: Search Engine Configuration Selection */}
              <SearchEngineConfigurationStep
                searchEngineConfigs={searchEngineConfigs}
                selectedConfigIds={selectedConfigIds}
                configDropdownOpen={configDropdownOpen}
                setConfigDropdownOpen={setConfigDropdownOpen}
                onConfigToggle={handleConfigToggle}
                onSelectAll={handleSelectAll}
                onDeselectAll={handleDeselectAll}
              />

              {/* Step 2: Keywords Input and List Section */}
              <AnimatedElement variant="child" delay={0.7}>
                <div className="space-y-8 mt-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <span className="text-primary font-semibold text-sm">
                        2
                      </span>
                    </div>
                    <h3 className="text-base font-semibold text-[#344054]">
                      Add Your Keywords
                    </h3>
                  </div>

                  <AnimatedElement variant="form" delay={0.8}>
                    <div className="bg-gray-50 rounded-xl p-6">
                      <KeywordsInputStep
                        keywordInput={keywordInput}
                        setKeywordInput={setKeywordInput}
                        autoFill={autoFill}
                        setAutoFill={setAutoFill}
                        selectedConfigIds={selectedConfigIds}
                        onAddKeyword={handleAddKeyword}
                        onFileImport={handleFileImport}
                        fileInputRef={fileInputRef}
                        onFileChange={handleFileChange}
                      />

                      {/* Loading state for suggestions */}
                      {autoFill &&
                        isLoadingSuggestions &&
                        hasNonChatGPTConfigurations() && (
                          <div className="mt-4 p-3 bg-primary/5 border border-primary/20 rounded-lg">
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 border border-primary border-t-transparent rounded-full animate-spin"></div>
                              <span className="text-primary text-sm">
                                Analyzing domain for keyword suggestions...
                              </span>
                            </div>
                          </div>
                        )}

                      {/* ChatGPT-only configuration message */}
                      {autoFill &&
                        selectedConfigIds.length > 0 &&
                        !hasNonChatGPTConfigurations() && (
                          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <div className="flex items-center gap-2">
                              <span className="text-blue-700 text-sm">
                                Keyword suggestions are not available for
                                ChatGPT configurations. Please select other
                                search engines to get AI-powered keyword
                                suggestions.
                              </span>
                            </div>
                          </div>
                        )}

                      {/* Error state */}
                      {autoFill &&
                        queryError &&
                        hasNonChatGPTConfigurations() && (
                          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                            <div className="flex items-center gap-2">
                              <span className="text-red-700 text-sm">
                                Error loading suggestions: {queryError.message}
                              </span>
                            </div>
                          </div>
                        )}

                      <KeywordsList
                        currentRows={currentRows}
                        startIndex={startIndex}
                        totalRows={totalRows}
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onRemoveKeywordConfig={handleRemoveKeywordConfig}
                        onPageChange={setCurrentPage}
                        isLoading={autoFill && isLoadingSuggestions}
                      />
                    </div>

                    {/* Keyword Limit Information */}
                    <AnimatedElement variant="child" delay={0.9}>
                      <KeywordLimitCard
                        keywordCount={getTotalKeywordCount()}
                        searchEngineCount={selectedConfigIds.length}
                        countryCount={
                          new Set(
                            searchEngineConfigs
                              .filter((config) =>
                                selectedConfigIds.includes(config.id)
                              )
                              .map((config) => config.country.code)
                          ).size
                        }
                        totalUsed={totalRows}
                        totalLimit={750}
                      />
                    </AnimatedElement>
                  </AnimatedElement>
                </div>
              </AnimatedElement>
            </BoxCreateProject>
          </AnimatedElement>
        </div>
        {/* Action Buttons */}
        <AnimatedElement variant="card" delay={0.9}>
          <div className="flex flex-col sm:flex-row gap-3 justify-end bg-white p-6 rounded-xl border border-gray-100 shadow-sm">
            <Link href={urls.searchEngines}>
              <ButtenSubmit
                text="Back"
                color="primary__outline_hover"
                classPluss="sm:w-auto w-full order-2 sm:order-1"
              />
            </Link>
            <ButtenSubmit
              text="Continue to Competitors"
              textloading="Setting up..."
              isLoading={isPending}
              onClick={() => mutate()}
              disabled={keywords.length === 0}
              classPluss="sm:w-auto w-full order-1 sm:order-2"
            />
          </div>
        </AnimatedElement>
      </div>
    </PageTransition>
  );
}
