"use client";
import BoxCreateProject from "@/components/CreateProject/BoxCreateProject";
import InfoCard from "@/components/CreateProject/InfoCard";
import CompetitorInput from "@/components/CreateProject/CompetitorInput";
import NavbarCreateProject from "@/components/CreateProject/NavbarCreateProject";
import StepMobileCreateProject from "@/components/CreateProject/StepMobileCreateProject";
import TitleCreateProject from "@/components/CreateProject/TitleCreateProject";
import ButtenSubmit from "@/components/shared/ButtenSubmit";
import { useMutation, useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEditProject, useEditNavigation } from "@/hooks/useEditProject";
import React, { useEffect, useState } from "react";
import { useCreateProjectStore } from "@/store/createProjectStore";
import PageTransition, {
  AnimatedElement,
} from "@/components/CreateProject/PageTransition";
import createProjectToast from "@/lib/createProjectToast";
import { projectAPI, ProjectCompetitor } from "@/services/projectService";

export default function Page() {
  const route = useRouter();
  const { isEditMode, isProjectDataReady } = useEditProject();
  const { urls } = useEditNavigation();

  const {
    setCurrentStep,
    projectInfo,
    competitors,
    addCompetitor,
    removeCompetitor,
    markStepComplete,
    validateCompetitors,
    setSupportedLocations,
  } = useCreateProjectStore();
  const [validationError, setValidationError] = useState("");

  // Load supported locations on page mount
  useQuery({
    queryKey: ["supported-locations"],
    queryFn: async () => {
      try {
        const response = await projectAPI.getSupportedLocations();
        setSupportedLocations(response.data.locations);
        return response.data;
      } catch (error) {
        console.error("Failed to load supported locations:", error);
        return { locations: [] };
      }
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      // Validate that at least one competitor is added
      if (!validateCompetitors()) {
        createProjectToast.warning.missingConfiguration("competitors");
        throw new Error(
          "Please add at least one competitor before continuing."
        );
      }

      const projectId = useCreateProjectStore.getState().projectInfo?.id;
      if (!projectId) {
        throw new Error("Project ID is missing");
      }

      // Transform competitors to API format
      const payloadCompetitors: ProjectCompetitor[] = competitors.map(
        (competitor) => ({
          url: competitor.domain.startsWith("http")
            ? competitor.domain
            : `https://${competitor.domain}`,
          search_engines: competitor.searchEngines,
          countries: competitor.countries,
        })
      );

      // Send PATCH with only competitors
      return await createProjectToast.apiCall(
        projectAPI.partialUpdateProject(projectId, {
          competitors: payloadCompetitors,
        }),
        {
          loadingMessage: "Saving competitors...",
          successMessage: `${competitors.length} competitor${
            competitors.length > 1 ? "s" : ""
          } added successfully!`,
          errorContext: "update competitors",
        }
      );
    },
    onSuccess: () => {
      setValidationError("");
      // Mark competitors step as complete
      markStepComplete("competitors");
      setCurrentStep("analytics-services");

      // Navigate to analytics services with proper URL handling
      route.push(urls.analyticsServices);
    },
    onError: (error: any) => {
      // Error handling is now done by createProjectToast
      console.error(error.message || "An error occurred. Please try again.");
    },
  });

  useEffect(() => {
    setCurrentStep("competitors");

    // In edit mode, wait for project data to be ready before any validation
    if (isEditMode && !isProjectDataReady) {
      return;
    }

    // Only redirect in create mode if no project ID exists
    // In edit mode, allow access to competitors page regardless of project state
    if (!isEditMode && !projectInfo?.id) {
      route.push("/create-project/project-information");
      return;
    }
  }, [setCurrentStep, projectInfo, route, isEditMode, isProjectDataReady]);
  return (
    <PageTransition>
      <div className="flex justify-between flex-col gap-6 lg:gap-8 h-full min-h-[calc(100vh-2rem)]">
        <div className="flex flex-col gap-3">
          <AnimatedElement variant="child">
            <NavbarCreateProject>Competitors</NavbarCreateProject>
          </AnimatedElement>
          <AnimatedElement variant="child" delay={0.1}>
            <StepMobileCreateProject />
          </AnimatedElement>

          {/* Main Content Card */}
          <AnimatedElement variant="card" delay={0.2}>
            <BoxCreateProject classPlus="relative">
              {/* Header Section */}
              <AnimatedElement variant="child" delay={0.3}>
                <div className="mb-4">
                  <TitleCreateProject
                    head="Add competitor analysis"
                    description="Monitor up to 5 competitors to track their ranking positions and promotional strategies. Compare their performance with your site to identify opportunities and stay ahead of the competition."
                    classHead="text-lg lg:text-xl font-semibold text-[#344054]"
                    classP="text-[#344054] mt-3 leading-relaxed"
                  />
                </div>
              </AnimatedElement>

              {/* Competitor Management Section */}
              <AnimatedElement variant="child" delay={0.4}>
                <div className="space-y-8">
                  {/* Competitor Input Section */}
                  <AnimatedElement variant="form" delay={0.5}>
                    <div className="bg-gray-50 rounded-xl p-6">
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-[#344054] mb-2">
                          Add Competitor Websites
                        </h4>
                        <p className="text-xs text-[#344054]">
                          Enter competitor domain URLs to track their SEO
                          performance and ranking positions
                        </p>
                      </div>

                      {/* Validation Error Notification */}
                      {validationError && (
                        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-600 font-medium">
                            {validationError}
                          </p>
                        </div>
                      )}

                      <CompetitorInput
                        maxList={5}
                        classPlusBox="!border-none !p-0 bg-transparent"
                        placeHolder="Enter competitor URL (e.g., example.com)"
                        importButtonText="Import Competitors"
                        competitorNames={competitors.map((comp) => comp.domain)}
                        setData={(
                          competitorData: Array<{
                            domain: string;
                            searchEngines: string[];
                            countries: string[];
                            languages: string[];
                            intersections?: number;
                            avg_position?: number;
                            sum_position?: number;
                            organic_keywords?: number;
                            organic_traffic?: number;
                            organic_cost?: number;
                            competitor_score?: number;
                            strength_level?: string;
                            isSuggested?: boolean;
                          }>
                        ) => {
                          // Clear existing competitors and add new ones
                          competitors.forEach((comp) =>
                            removeCompetitor(comp.id)
                          );
                          competitorData.forEach((comp) =>
                            addCompetitor(
                              comp.domain,
                              comp.searchEngines,
                              comp.countries,
                              comp.isSuggested,
                              undefined, // title
                              undefined, // description
                              undefined, // relevanceScore
                              comp.intersections,
                              comp.avg_position,
                              comp.sum_position,
                              comp.organic_keywords,
                              comp.organic_traffic,
                              comp.organic_cost,
                              comp.competitor_score,
                              comp.strength_level
                            )
                          );

                          // Clear validation error when competitors are updated
                          setValidationError("");
                        }}
                      />
                    </div>

                    {/* Info Cards */}
                    <div className="grid gap-4 md:grid-cols-2 mt-6">
                      <InfoCard
                        variant="info"
                        title="Competitor Limit"
                        description={
                          <>
                            You can add up to{" "}
                            <span className="font-semibold">5 competitors</span>{" "}
                            per project for comprehensive analysis
                          </>
                        }
                      />

                      <InfoCard
                        variant="tips"
                        title="Best Practices"
                        description={
                          <ul className="space-y-1">
                            <li>
                              • Choose direct competitors in your industry
                            </li>
                            <li>• Include both large and niche competitors</li>
                            <li>
                              • Use root domains (example.com) not full URLs
                            </li>
                          </ul>
                        }
                      />
                    </div>
                  </AnimatedElement>
                </div>
              </AnimatedElement>
            </BoxCreateProject>
          </AnimatedElement>
        </div>

        {/* Action Buttons */}
        <AnimatedElement variant="card" delay={0.6}>
          <div className="flex flex-col sm:flex-row gap-3 justify-end bg-white p-6 rounded-xl border border-gray-100 shadow-sm">
            <Link href={urls.keywords}>
              <ButtenSubmit
                text="Back"
                color="primary__outline_hover"
                classPluss="sm:w-auto w-full order-2 sm:order-1"
              />
            </Link>
            <ButtenSubmit
              text="Continue to Analytics"
              textloading="Setting up..."
              isLoading={isPending}
              onClick={() => mutate()}
              classPluss="sm:w-auto w-full order-1 sm:order-2"
            />
          </div>
        </AnimatedElement>
      </div>
    </PageTransition>
  );
}
