"use client";

import { useAuth } from "@/providers/AuthProvider";
import { useEffect, Suspense, ReactNode, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import NavbarDashboard from "@/components/dashboard/NavbarDashboard";
import {
  fastRedirectIfNoAuth,
  hasAccessToken,
  getImmediateAuthStatus,
} from "@/utils/fastAuthCheck";
import ChartPopup from "./project/[projectId]/analytics-traffics/overview/(overview)/audience-overview/_components/ChartPopup";
interface DashboardContentProps {
  children: ReactNode;
}

export default function DashboardContent({ children }: DashboardContentProps) {
  const router = useRouter();
  const pathname = usePathname();
  const {
    isAuthenticated,
    refreshProfile,
    redirectToLogin,
    isHydrated,
    hasTokens,
  } = useAuth();

  // Check if current route is my-projects
  const isMyProjectsRoute = pathname === "/my-projects";

  // State to track if we've performed fast redirect
  const [hasPerformedFastRedirect, setHasPerformedFastRedirect] =
    useState(false);
  
  // State to track if we have immediate tokens (for optimistic rendering)
  const [hasImmediateTokens, setHasImmediateTokens] = useState(false);

  // Check for tokens immediately on mount (synchronous)
  useEffect(() => {
    if (typeof window !== "undefined") {
      const hasTokensNow = hasAccessToken();
      setHasImmediateTokens(hasTokensNow);
      
      // Only redirect if we definitely have no tokens
      if (!hasTokensNow) {
        const authStatus = getImmediateAuthStatus();
        if (authStatus.shouldRedirect) {
          console.log("Fast redirect: No tokens found for protected route");
          router.replace("/");
          setHasPerformedFastRedirect(true);
          return;
        }
      }
    }
  }, [router, pathname]);

  // Background authentication check (non-blocking)
  useEffect(() => {
    const checkAuthInBackground = async () => {
      try {
        // Only run background check if we have tokens
        if (hasTokens || hasImmediateTokens) {
          // Fire and forget - don't block UI
          refreshProfile(false).catch((error) => {
            console.error("Background auth check failed:", error);
            // Only redirect on critical auth failures, not network issues
            if (error?.response?.status === 401) {
              router.replace("/");
            }
          });
        }
      } catch (error) {
        console.error("Background auth setup failed:", error);
      }
    };

    // Run background check after component is mounted and rendered
    const timeoutId = setTimeout(checkAuthInBackground, 100);
    
    return () => clearTimeout(timeoutId);
  }, [hasTokens, hasImmediateTokens, refreshProfile, router]);

  // Early return if we've redirected to prevent any rendering
  if (hasPerformedFastRedirect) {
    return null;
  }

  // If we have immediate tokens, render optimistically (don't wait for API)
  const shouldRenderOptimistically = hasImmediateTokens || hasTokens || isAuthenticated;

  // Only show loading if we have no tokens AND not hydrated yet
  if (!shouldRenderOptimistically && !isHydrated) {
    const containerClass = isMyProjectsRoute
      ? "mx-auto h-screen flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5"
      : "mx-auto min-h-screen flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5";

    return (
      <div className={containerClass}>
        <div className="max-w-7xl mx-auto w-full">
          <div className="h-16 bg-white rounded-xl shadow animate-pulse flex-shrink-0" />
        </div>
        <div className="flex-1 max-w-7xl mx-auto w-full">
          <div className="h-full bg-white rounded-xl shadow animate-pulse" />
        </div>
      </div>
    );
  }

  // If hydrated and no tokens, redirect
  if (isHydrated && !shouldRenderOptimistically) {
    router.replace("/");
    return null;
  }

  // Render dashboard content for authenticated users
  if (isMyProjectsRoute) {
    // For my-projects: use full available height without scrolling
    return (
      <div className="mx-auto flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5 h-full overflow-hidden">
        <div className="w-full flex-shrink-0">
          <NavbarDashboard />
        </div>
        <div className="flex-1 min-h-0 w-full overflow-hidden">
          {children}
        </div>
      </div>
    );
  }

  // For other routes: allow whole page to scroll when content overflows
  return (
    <div className="mx-auto min-h-screen flex flex-col w-full max-w-8xl px-4 xl:px-2 lg:pt-6 py-4 gap-3 lg:gap-5">
      <div className="max-w-8xl mx-auto w-full">
        <NavbarDashboard />
      </div>
      <ChartPopup />
      <div className="flex-1 max-w-8xl mx-auto w-full">{children}</div>
    </div>
  );
}
