"use client";
import React, { Suspense, useState, useMemo, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import BoxDashboard from "@/components/dashboard/BoxDashboard";
import CurrentPlan from "@/components/dashboard/CurrentPlan";
import Table from "@/ui/Table";
import { HiOutlineDocumentText } from "react-icons/hi2";
import TitlePageDashboard from "@/components/dashboard/TitlePageDashboard";
import { FiCommand, FiKey, FiLink } from "react-icons/fi";
import {
  LuMonitorPlay,
  LuMonitor,
  LuSquareDashed,
  LuShoppingBag,
} from "react-icons/lu";
import { RiDoubleQuotesR } from "react-icons/ri";
import { IoAtSharp } from "react-icons/io5";
import { GiSettingsKnobs } from "react-icons/gi";
import { CiBookmark } from "react-icons/ci";
import { PiChatTeardropText } from "react-icons/pi";
import { FaArrowTrendUp } from "react-icons/fa6";
import { FaRegCircleUser } from "react-icons/fa6";
import "react-day-picker/style.css";
import { DatePickerRange } from "@/components/dashboard/DatePickerRange";
import { useQuery } from "@tanstack/react-query";
import dashbordService, { InvoiceItem } from "@/services/dashboardServices";

// Import the InvoicesType for proper typing
type InvoicesType = {
  count: number;
  message: string;
  next: null | number;
  previous: null | number;
  results: InvoiceItem[];
};
import { PaginationBox } from "@/components/shared/PaginationBox";
import FetchLoadingBox from "@/components/shared/FetchLoadingBox";
import ErrorFetch from "@/components/shared/ErrorFetch";

import { motion, AnimatePresence } from "framer-motion";
import { getCurrencySymbol, formatCurrencyCode } from "@/utils/currencyUtils";

// Flag to enable/disable mock data
// Set to true to use mock data instead of API calls for testing/development
const USE_MOCK_DATA = false;

// Mock data for invoices
const mockInvoicesData: InvoicesType = {
  count: 3,
  next: null,
  previous: null,
  results: [
    {
      payment_id: "fb43f417-3a63-4ab3-a519-ae2cc223f8c8",
      payment_amount: {
        amount: 99,
        formatted: "99.00 USD",
      },
      discount_amount: {
        amount: 0,
        formatted: "0.00 USD",
      },
      discount_details: [],
      payment_status: "Succeeded",
      invoice_pdf_url: "https://pay.stripe.com/invoice/test/pdf",
      invoice_number: "T5FIGW6L-0001",
      product_name: "Pro Plan",
      billing_period: {
        period_start: "2024-01-15T10:30:00Z",
        period_end: "2024-02-15T10:30:00Z",
        interval: "month",
      },
      created_date: "2024-01-15T10:30:00Z",
      paid_date: null,
      currency: "usd",
    },
    {
      payment_id: "fb43f417-3a63-4ab3-a519-ae2cc223f8c9",
      payment_amount: {
        amount: 999,
        formatted: "999.00 USD",
      },
      discount_amount: {
        amount: 0,
        formatted: "0.00 USD",
      },
      discount_details: [],
      payment_status: "Succeeded",
      invoice_pdf_url: "https://pay.stripe.com/invoice/test/pdf",
      invoice_number: "T5FIGW6L-0002",
      product_name: "Business Plan",
      billing_period: {
        period_start: "2024-01-10T14:22:00Z",
        period_end: "2024-02-10T14:22:00Z",
        interval: "month",
      },
      created_date: "2024-01-10T14:22:00Z",
      paid_date: null,
      currency: "usd",
    },
    {
      payment_id: "fb43f417-3a63-4ab3-a519-ae2cc223f8c0",
      payment_amount: {
        amount: 29,
        formatted: "29.00 USD",
      },
      discount_amount: {
        amount: 0,
        formatted: "0.00 USD",
      },
      discount_details: [],
      payment_status: "Failed",
      invoice_pdf_url: "https://pay.stripe.com/invoice/test/pdf",
      invoice_number: "T5FIGW6L-0003",
      product_name: "Starter Plan",
      billing_period: null,
      created_date: "2024-01-05T09:15:00Z",
      paid_date: null,
      currency: "usd",
    },
  ],
  message: "Invoices retrieved successfully",
};

type TableCurrentPlanType = {
  name: string;
  numbers: string;
  width: number;
  icon?: React.ReactNode;
  child?: TableCurrentPlanType[];
};

const classIcons = "text-sm lg:text-lg ";
const dataTable = [
  {
    name: "Keywords added",
    numbers: "51 / 750",
    width: 40,
    icon: <FiKey className={classIcons} />,
  },
  {
    name: "Websites added",
    numbers: "51 / 750",
    width: 70,
    icon: <LuMonitorPlay className={classIcons} />,
  },
  {
    name: "Backlink monitor",
    numbers: "51 / 750",
    width: 60,
    icon: <LuSquareDashed className={classIcons} />,
  },
  {
    name: "Website audit (per account)",
    numbers: "51 / 750",
    width: 20,
    icon: <RiDoubleQuotesR className={classIcons} />,
  },
  {
    name: "Website audit (per site)",
    numbers: "51 / 750",
    width: 30,
    icon: <IoAtSharp className={classIcons} />,
  },
  {
    name: "Page changes monitoring",
    numbers: "51 / 750",
    width: 20,
    icon: <LuMonitor className={classIcons} />,
  },
  {
    name: "On-page seo checker",
    numbers: "51 / 750",
    width: 35,
    icon: <FiCommand className={classIcons} />,
  },
  {
    name: "SERP Analyzer",
    numbers: "51 / 750",
    width: 0,
    icon: <GiSettingsKnobs className={classIcons} />,
  },
  {
    name: "Competitive research",
    numbers: "51 / 750",
    width: 30,
    icon: <CiBookmark className={classIcons} />,
  },
  {
    name: "Scheduled reporting",
    numbers: "51 / 750",
    width: 15,
    icon: <PiChatTeardropText className={classIcons} />,
  },
  {
    name: "Content Marketing",
    numbers: "51 / 750",
    width: 30,
    icon: <FaArrowTrendUp className={classIcons} />,
    childrens: [
      {
        name: "AI Writer",
        numbers: "0 / 2,000",
        width: 0,
      },
      {
        name: "Plagiarism Checker",
        numbers: "0 / 2,000",
        width: 0,
      },
    ],
  },
  {
    name: "Backlink Checker / Backlink Gap Analyzer",
    numbers: "51 / 750",
    width: 60,
    icon: <FiLink className={classIcons} />,
  },
  {
    name: "Competitors (per site)",
    numbers: "51 / 750",
    width: 0,
    icon: <IoAtSharp className={classIcons} />,
  },
  {
    name: "Local Marketing (per account)",
    numbers: "51 / 750",
    width: 70,
    icon: <LuShoppingBag className={classIcons} />,
  },
  {
    name: "User seats",
    numbers: "51 / 750",
    width: 0,
    icon: <FaRegCircleUser className={classIcons} />,
  },
];
// Helper function to format date for display
const formatInvoiceDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } catch (error) {
    console.error("Error formatting date:", dateString, error);
    return dateString;
  }
};

// Helper function to format billing period for display
const formatBillingPeriod = (
  billingPeriod: {
    period_start: string;
    period_end: string;
    interval: string | null;
  } | null
): string => {
  if (!billingPeriod) {
    return "N/A";
  }

  try {
    const startDate = new Date(billingPeriod.period_start);
    const endDate = new Date(billingPeriod.period_end);

    const formatDate = (date: Date) =>
      date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });

    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  } catch (error) {
    console.error("Error formatting billing period:", billingPeriod, error);
    return "Invalid period";
  }
};

// Component that uses useSearchParams
function BillingContent() {
  const query = useSearchParams();
  const searchQuery = useSearchParams();
  const pageQuery = Number(searchQuery?.get("page")) || 1;
  const isStepInvoices = query?.get("step") === "invoices";

  // State for date range filtering - initialize as undefined to ensure clean state on page reload
  const [reactDateRange, setReactDateRange] = useState<
    { startDate: Date; endDate: Date } | undefined
  >(undefined);

  // Ensure date range is unset on component mount (page reload)
  useEffect(() => {
    console.log("🔄 Component mounted - resetting date range to undefined");
    setReactDateRange(undefined);
  }, []);

  const {
    data: invoicesData,
    isFetching,
    isError,
  } = useQuery({
    queryKey: ["GetInvoices"],
    queryFn: USE_MOCK_DATA
      ? () => {
          console.log("🔧 Using mock data for invoices");
          return Promise.resolve(mockInvoicesData);
        }
      : dashbordService.getInvoices,
    staleTime: 1000 * 60 * 60 * 24,
    gcTime: 1000 * 60 * 60 * 24,
    enabled: isStepInvoices,
  });

  // Function to parse date string for filtering
  const parseInvoiceDate = (dateString: string): Date | null => {
    try {
      return new Date(dateString);
    } catch (error) {
      console.error("Error parsing date:", dateString, error);
      return null;
    }
  };

  // Filter invoices based on selected date range
  const filteredInvoices = useMemo(() => {
    const invoices = invoicesData?.results || [];

    console.log("🔍 Filtering invoices:", {
      totalInvoices: invoices.length,
      dateRange: reactDateRange,
      hasDateRange: !!reactDateRange,
    });

    // If no date range is selected (undefined or no dates), return all invoices
    if (
      !reactDateRange ||
      (!reactDateRange.startDate && !reactDateRange.endDate)
    ) {
      console.log(
        "✅ No date filter applied - returning all invoices:",
        invoices.length
      );
      return invoices;
    }

    const filtered = invoices.filter((invoice: InvoiceItem) => {
      const invoiceDate = parseInvoiceDate(invoice.created_date);
      if (!invoiceDate) return false;

      // Check if invoice date is within the selected range
      const isAfterStart =
        !reactDateRange.startDate || invoiceDate >= reactDateRange.startDate;
      const isBeforeEnd =
        !reactDateRange.endDate || invoiceDate <= reactDateRange.endDate;

      return isAfterStart && isBeforeEnd;
    });

    console.log("✅ Date filter applied - filtered invoices:", filtered.length);
    return filtered;
  }, [reactDateRange, invoicesData?.results]);

  // Handle date range change
  const handleReactDateRangeChange = (
    range: { startDate: Date; endDate: Date } | undefined
  ) => {
    // Log the selected date range for debugging
    if (range) {
      console.log("📅 Date range selected:", {
        startDate: range.startDate.toLocaleDateString(),
        endDate: range.endDate.toLocaleDateString(),
        startDateISO: range.startDate.toISOString(),
        endDateISO: range.endDate.toISOString(),
      });
    } else {
      console.log("📅 Date range cleared/unset");
    }

    setReactDateRange(range);
    // Reset to page 1 when filter changes
    const params = new URLSearchParams(window.location.search);
    params.set("page", "1");
    window.history.replaceState(
      {},
      "",
      `${window.location.pathname}?${params.toString()}`
    );
  };

  // Calculate total pages based on filtered results
  const itemsPerPage = 10;
  const totalPages = Math.max(
    1,
    Math.ceil(filteredInvoices.length / itemsPerPage)
  );

  // Ensure current page doesn't exceed total pages
  useEffect(() => {
    if (totalPages > 0 && pageQuery > totalPages) {
      const params = new URLSearchParams(window.location.search);
      params.set("page", totalPages.toString());
      window.history.replaceState(
        {},
        "",
        `${window.location.pathname}?${params.toString()}`
      );
    }
  }, [totalPages, pageQuery]);

  switch (query?.get("step")) {
    case "invoices": {
      return (
        <div className="lg:h-auto w-full min-h-[500px]">
          <ErrorFetch isError={isError}>
            <FetchLoadingBox
              classPlus="min-h-[500px] shadow"
              isFetching={isFetching}
            >
              <TitlePageDashboard value="Invoices" />
              {invoicesData?.count ? (
                <BoxDashboard classPlus="mt-4">
                  <div className="flex-col lg:flex-row items-start gap-4 lg:gap-0 lg:items-center flex justify-between">
                    <TitlePageDashboard value="Transaction Summary" />
                    <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                        className="flex flex-col gap-2"
                      >
                        <label className="text-sm font-medium text-muted-foreground">
                          Date Range Filter
                        </label>
                        <DatePickerRange
                          value={reactDateRange}
                          onChange={handleReactDateRangeChange}
                        />
                      </motion.div>
                    </div>
                  </div>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.2 }}
                    className="w-full"
                    style={{
                      overflow: "hidden",
                      minHeight: "400px",
                      position: "relative",
                    }}
                  >
                    <Table>
                      <Table.Header>
                        <th className="pb-2 pr-[52px] lg:pr-0 whitespace-nowrap">
                          Invoice Number
                        </th>
                        <th className="text-center pb-2">Product Name</th>
                        <th className="text-center pb-2 px-4 lg:px-0 whitespace-nowrap">
                          Amount
                        </th>
                        <th className="text-center pb-2 whitespace-nowrap">
                          Billing Period
                        </th>
                        <th className="text-center pb-2 whitespace-nowrap">
                          Created Date
                        </th>
                        <th className="text-center pb-2 px-4 lg:px-0 whitespace-nowrap">
                          View Invoice
                        </th>
                        <th className="text-center pb-2 px-4 lg:px-0 whitespace-nowrap">
                          Status
                        </th>
                      </Table.Header>
                      <Table.Body>
                        <AnimatePresence mode="wait" initial={false}>
                          {filteredInvoices.length === 0 ? (
                            <motion.tr
                              key="empty-state"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <td
                                colSpan={7}
                                className="text-center p-8 text-gray-500"
                              >
                                {reactDateRange &&
                                (reactDateRange.startDate ||
                                  reactDateRange.endDate)
                                  ? "No invoices found for the selected date range."
                                  : "No invoices available."}
                              </td>
                            </motion.tr>
                          ) : (
                            filteredInvoices.map(
                              (item: InvoiceItem, index: number) => {
                                if (pageQuery === 1 && index + 1 <= 10) {
                                  return (
                                    <motion.tr
                                      key={`invoice-${item.payment_id}-${index}`}
                                      initial={{ opacity: 0 }}
                                      animate={{ opacity: 1 }}
                                      exit={{ opacity: 0 }}
                                      transition={{
                                        duration: 0.3,
                                        delay: index * 0.05,
                                      }}
                                      className="border-b border-gray-200 hover:bg-gray-50/50 transition-colors"
                                    >
                                      <td className="text-center min-w-24 p-3.5">
                                        <p className=" cutline cutline-1">
                                          {item.invoice_number}
                                        </p>
                                      </td>
                                      <td className="text-center min-w-xs p-3.5">
                                        <p className=" cutline cutline-1">
                                          {item.product_name}
                                        </p>
                                      </td>
                                      <td className="text-center p-3.5">
                                        {getCurrencySymbol(item.currency)}
                                        {item.payment_amount.amount.toLocaleString(
                                          "en"
                                        )}{" "}
                                        {formatCurrencyCode(item.currency)}
                                      </td>
                                      <td className="text-center whitespace-nowrap p-3.5">
                                        {formatBillingPeriod(
                                          item.billing_period
                                        )}
                                      </td>
                                      <td className="text-center whitespace-nowrap p-3.5">
                                        {formatInvoiceDate(item.created_date)}
                                      </td>
                                      <td className="text-center p-3.5">
                                        <a
                                          href={item.invoice_pdf_url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="inline-block"
                                        >
                                          <HiOutlineDocumentText className="text-xl cursor-pointer mx-auto hover:text-primary transition-colors" />
                                        </a>
                                      </td>
                                      <td className="text-center p-3.5">
                                        {item.payment_status === "Succeeded" ? (
                                          <span className="bg-[#34C7591A] inline-block text-[#34C759] border border-[#34C759] w-32 py-1 px-4 rounded-sm">
                                            Successful
                                          </span>
                                        ) : (
                                          <span className="bg-[#FF3B301A] inline-block text-[#FF3B30] border border-[#FF3B30] w-32 py-1 rounded-sm">
                                            Failed
                                          </span>
                                        )}
                                      </td>
                                    </motion.tr>
                                  );
                                }
                                if (
                                  pageQuery === 2 &&
                                  index + 1 > 10 &&
                                  index + 1 <= 20
                                ) {
                                  return (
                                    <motion.tr
                                      key={`invoice-${item.payment_id}-${index}`}
                                      initial={{ opacity: 0 }}
                                      animate={{ opacity: 1 }}
                                      exit={{ opacity: 0 }}
                                      transition={{
                                        duration: 0.3,
                                        delay: (index - 10) * 0.05,
                                      }}
                                      className="border-b border-gray-200 hover:bg-gray-50/50 transition-colors"
                                    >
                                      <td className="text-center min-w-24 p-3.5">
                                        <p className=" cutline cutline-1">
                                          {item.invoice_number}
                                        </p>
                                      </td>
                                      <td className="text-center min-w-xs p-3.5">
                                        <p className=" cutline cutline-1">
                                          {item.product_name}
                                        </p>
                                      </td>
                                      <td className="text-center p-3.5">
                                        {getCurrencySymbol(item.currency)}
                                        {item.payment_amount.amount.toLocaleString(
                                          "en"
                                        )}{" "}
                                        {formatCurrencyCode(item.currency)}
                                      </td>
                                      <td className="text-center whitespace-nowrap p-3.5">
                                        {formatBillingPeriod(
                                          item.billing_period
                                        )}
                                      </td>
                                      <td className="text-center whitespace-nowrap p-3.5">
                                        {formatInvoiceDate(item.created_date)}
                                      </td>
                                      <td className="text-center p-3.5">
                                        <a
                                          href={item.invoice_pdf_url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="inline-block"
                                        >
                                          <HiOutlineDocumentText className="text-xl mx-auto cursor-pointer hover:text-primary transition-colors" />
                                        </a>
                                      </td>
                                      <td className="text-center !pr-4 lg:pr-0 p-3.5">
                                        {item.payment_status === "Succeeded" ? (
                                          <span className="bg-[#34C7591A] inline-block text-[#34C759] border border-[#34C759] w-32 py-1 px-4 rounded-sm">
                                            Successful
                                          </span>
                                        ) : (
                                          <span className="bg-[#FF3B301A] inline-block text-[#FF3B30] border border-[#FF3B30] w-32 py-1 rounded-sm">
                                            Failed
                                          </span>
                                        )}
                                      </td>
                                    </motion.tr>
                                  );
                                }
                                if (pageQuery === 3 && index + 1 > 20) {
                                  return (
                                    <motion.tr
                                      key={`invoice-${item.payment_id}-${index}`}
                                      initial={{ opacity: 0 }}
                                      animate={{ opacity: 1 }}
                                      exit={{ opacity: 0 }}
                                      transition={{
                                        duration: 0.3,
                                        delay: (index - 20) * 0.05,
                                      }}
                                      className="border-b border-gray-200 hover:bg-gray-50/50 transition-colors"
                                    >
                                      <td className="text-center min-w-24 p-3.5">
                                        <p className=" cutline cutline-1">
                                          {item.invoice_number}
                                        </p>
                                      </td>
                                      <td className="text-center min-w-xs p-3.5">
                                        <p className=" cutline cutline-1">
                                          {item.product_name}
                                        </p>
                                      </td>
                                      <td className="text-center p-3.5">
                                        {getCurrencySymbol(item.currency)}
                                        {item.payment_amount.amount.toLocaleString(
                                          "en"
                                        )}{" "}
                                        {formatCurrencyCode(item.currency)}
                                      </td>
                                      <td className="text-center whitespace-nowrap p-3.5">
                                        {formatBillingPeriod(
                                          item.billing_period
                                        )}
                                      </td>
                                      <td className="text-center whitespace-nowrap p-3.5">
                                        {formatInvoiceDate(item.created_date)}
                                      </td>
                                      <td className="text-center p-3.5">
                                        <a
                                          href={item.invoice_pdf_url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="inline-block"
                                        >
                                          <HiOutlineDocumentText className="text-xl mx-auto cursor-pointer hover:text-primary transition-colors" />
                                        </a>
                                      </td>
                                      <td className="text-center pr-0 p-3.5">
                                        {item.payment_status === "Succeeded" ? (
                                          <span className="bg-[#34C7591A] inline-block text-[#34C759] border border-[#34C759] w-32 py-1 px-4 rounded-sm">
                                            Successful
                                          </span>
                                        ) : (
                                          <span className="bg-[#FF3B301A] inline-block text-[#FF3B30] border border-[#FF3B30] w-32 py-1 rounded-sm">
                                            Failed
                                          </span>
                                        )}
                                      </td>
                                    </motion.tr>
                                  );
                                }
                              }
                            )
                          )}
                        </AnimatePresence>
                      </Table.Body>
                    </Table>
                  </motion.div>
                  <div className="order-3 lg:order-3 col-span-12 -mt-8 lg:mt-0">
                    <PaginationBox
                      currentPage={pageQuery}
                      totalPages={totalPages}
                      boundaryCount={1}
                      nextPageUrl={"false"}
                      previousPageUrl={"false"}
                    />
                  </div>
                </BoxDashboard>
              ) : (
                <BoxDashboard classPlus="mt-4">
                  <div className="text-center p-8 text-gray-500">
                    <p className="text-lg font-medium mb-2">
                      No Invoices Available
                    </p>
                    <p className="text-sm">
                      {invoicesData?.message ||
                        "Invoices are only available for successful payments."}
                    </p>
                  </div>
                </BoxDashboard>
              )}
            </FetchLoadingBox>
          </ErrorFetch>
        </div>
      );
    }
    case "current-plan":
      return (
        <div>
          <TitlePageDashboard value="Current Plan" />
          <div className="grid grid-cols-12 lg:order-1 order-2 items-start gap-3 lg:gap-6 mt-4">
            <BoxDashboard classPlus="col-span-12 lg:col-span-8 order-2 lg:order-1">
              <TitlePageDashboard value="Your Limits" />
              <div className="w-full overflow-x-auto custome-scrollbar pb-3 lg:pb-0">
                <div className="min-w-[600px] flex flex-col gap-4">
                  {dataTable.map((item, index) => (
                    <TableCurrentPlan
                      key={index}
                      child={item.childrens}
                      icon={item.icon}
                      name={item.name}
                      numbers={item.numbers}
                      width={item.width}
                    />
                  ))}
                </div>
              </div>
            </BoxDashboard>
            <BoxDashboard classPlus="lg:mt-0 col-span-12 order-1 lg:order-2 lg:order-2 w-full flex lg:col-span-4">
              <CurrentPlan />
            </BoxDashboard>
          </div>
        </div>
      );
  }
}

const TableCurrentPlan = ({
  name,
  numbers,
  width,
  icon,
  child,
}: TableCurrentPlanType) => {
  const ChildComponent = ({ name, numbers, width }: TableCurrentPlanType) => {
    return (
      <div className="p-3 relative rounded-b-md flex justify-between w-full border border-gray-200">
        <div
          style={{ width: `${width}%` }}
          className="top-0 absolute left-0 h-full bg-purple-100"
        ></div>
        <p className="text-black z-10 whitespace-nowrap text-xs lg:text-base">
          {name}
        </p>
        <span className="text-black z-10 whitespace-nowrap ml-5 text-xs lg:text-base">
          {numbers}
        </span>
      </div>
    );
  };

  return (
    <div className="flex gap-2 items-start w-full ">
      <i
        className={`${
          width > 0
            ? "text-primary bg-[#914AC41A]"
            : "bg-[#F4F4F4] text-gray-700"
        } p-3 py-[15px]  flex items-center w-12 justify-center rounded-md  border border-gray-200`}
      >
        {icon}
      </i>
      <div className="flex flex-col w-full">
        <div
          className={`p-3 relative ${width > 0 ? "" : "bg-[#F4F4F4]"} ${
            child ? "rounded-t-md" : "rounded-md"
          }  w-full flex justify-between w-full border border-gray-200`}
        >
          <div
            style={{ width: `${width}%` }}
            className="top-0 absolute left-0 h-full bg-[#914AC41A]"
          ></div>
          <p className="text-black z-10 whitespace-nowrap text-sm lg:text-base">
            {name}
          </p>
          <span className="text-black z-10  whitespace-nowrap ml-5 text-sm lg:text-base">
            {numbers}
          </span>
        </div>
        {child && child.length ? (
          <div className=" flex gap-2">
            {child.map((i, index) => (
              <ChildComponent
                key={index}
                name={i.name}
                numbers={i.numbers}
                width={i.width}
              />
            ))}
          </div>
        ) : null}
      </div>
    </div>
  );
};

// Main component with Suspense boundary
export default function Billing() {
  return (
    <Suspense
      fallback={<div className="animate-pulse bg-gray-200 rounded-xl h-96" />}
    >
      <BillingContent />
    </Suspense>
  );
}
