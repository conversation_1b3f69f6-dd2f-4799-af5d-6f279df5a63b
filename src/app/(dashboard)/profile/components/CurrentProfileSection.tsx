"use client";

import React, { useState } from "react";
import Image from "next/image";
import {
  UserIcon,
  AtSymbolIcon,
  CameraIcon,
} from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";
import FetchLoadingBox from "@/components/shared/FetchLoadingBox";
import ErrorFetch from "@/components/shared/ErrorFetch";
import SelectAvatarComponent from "./SelectAvatarComponent";

interface CurrentProfileSectionProps {
  dataUserProfile: any;
  dataAvatars: any;
  fetchAvatar: boolean;
  isErrorAvatar: boolean;
  selectAvatar: string;
  setSelectAvatar: (val: string) => void;
}

// Simple animation variants using only opacity for better performance
const avatarSectionVariants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
  exit: {
    opacity: 0,
    transition: {
      duration: 0.15,
      ease: "easeIn",
    },
  },
};

export default function CurrentProfileSection({
  dataUserProfile,
  dataAvatars,
  fetchAvatar,
  isErrorAvatar,
  selectAvatar,
  setSelectAvatar,
}: CurrentProfileSectionProps) {
  const [showAvatarSelection, setShowAvatarSelection] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  // Get the selected avatar URL from dataAvatars if selectAvatar is set
  const getDisplayAvatarUrl = () => {
    if (selectAvatar && dataAvatars?.avatars) {
      const selectedAvatarData = dataAvatars.avatars.find(
        (avatar: any) => avatar.id === selectAvatar
      );
      if (selectedAvatarData) {
        return selectedAvatarData.image_url;
      }
    }
    return dataUserProfile?.data?.avatar_url;
  };

  const displayAvatarUrl = getDisplayAvatarUrl();

  return (
    <div className="mb-8 bg-white rounded-xl p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-secondary">Current Profile</h3>
      </div>
      <div className="flex gap-4 items-center">
        <figure className="min-w-[60px] relative">
          {displayAvatarUrl && !imageError ? (
            <Image
              alt="Current profile avatar"
              src={displayAvatarUrl}
              width={60}
              height={60}
              className="rounded-full h-[60px] w-[60px] object-cover"
              onError={handleImageError}
            />
          ) : (
            <div className="rounded-full w-[60px] h-[60px] flex items-center justify-center">
              <UserIcon className="w-6 h-6 text-gray-600" />
            </div>
          )}
        </figure>
        <div className="flex flex-col gap-1 flex-1">
          <span className="text-lg font-semibold text-secondary">
            {`${dataUserProfile?.data?.first_name || "User"} ${
              dataUserProfile?.data?.last_name || ""
            }`.trim()}
          </span>
          <p className="text-gray-600 text-sm flex items-center gap-2">
            Email: {dataUserProfile?.data?.email || "No email available"}
          </p>
        </div>
        <button
          onClick={() => setShowAvatarSelection(!showAvatarSelection)}
          className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2 text-sm"
          type="button"
          title="Choose from Avatars"
        >
          <CameraIcon className="w-4 h-4" />
          {showAvatarSelection ? "Hide Avatars" : "Choose Avatar"}
        </button>
      </div>

      {/* Avatar Selection Section */}
      <AnimatePresence mode="wait">
        {showAvatarSelection && (
          <motion.div
            className="pt-6 border-t border-gray-200 mt-6"
            variants={avatarSectionVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <FetchLoadingBox isFetching={fetchAvatar}>
              <ErrorFetch isError={isErrorAvatar} nameError="Avatar">
                <div className="mb-8 pb-6">
                  <div className="flex items-center gap-3 mb-6">
                    <CameraIcon className="w-5 h-5 text-primary" />
                    <span className="text-lg font-semibold text-secondary">
                      Choose Your Avatar
                    </span>
                  </div>
                  <div className="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6 justify-items-center max-w-full px-2 py-4">
                    {dataAvatars?.avatars.length
                      ? dataAvatars?.avatars.map(
                          (avatar: any, index: number) => (
                            <SelectAvatarComponent
                              key={avatar.id || index}
                              setSelect={setSelectAvatar}
                              select={selectAvatar}
                              image={avatar}
                            />
                          )
                        )
                      : null}
                  </div>
                </div>
              </ErrorFetch>
            </FetchLoadingBox>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
