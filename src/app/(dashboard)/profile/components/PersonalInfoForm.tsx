"use client";

import React from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { UserIcon, PhoneIcon, AtSymbolIcon } from "@heroicons/react/24/outline";
import CustomInput from "@/components/shared/CustomInput";

interface FormProfileType {
  first_name: string;
  last_name: string;
  phone_number: string;
}

interface PersonalInfoFormProps {
  register: UseFormRegister<FormProfileType>;
  errors: FieldErrors<FormProfileType>;
  dataUserProfile: any;
}

export default function PersonalInfoForm({
  register,
  errors,
  dataUserProfile,
}: PersonalInfoFormProps) {
  return (
    <div className="mb-8 bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-primary rounded-lg">
          <UserIcon className="w-5 h-5 text-white" />
        </div>
        <h3 className="text-xl font-bold text-secondary">
          Personal Information
        </h3>
      </div>
      <form className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 max-w-full overflow-hidden p-2">
        <CustomInput
          error={errors.first_name?.message}
          required
          {...register("first_name")}
          label="First Name"
          placeholder="Enter your first name"
          startIcon={<UserIcon className="w-5 h-5 text-gray-500" />}
        />
        <CustomInput
          error={errors.last_name?.message}
          required
          {...register("last_name")}
          label="Last Name"
          placeholder="Enter your last name"
          startIcon={<UserIcon className="w-5 h-5 text-gray-500" />}
        />
        <CustomInput
          error={errors.phone_number?.message}
          required
          {...register("phone_number")}
          label="Phone Number"
          placeholder="Enter your phone number"
          startIcon={<PhoneIcon className="w-5 h-5 text-gray-500" />}
        />
        <CustomInput
          disabled
          value={dataUserProfile?.data?.email}
          label="Email Address"
          placeholder="Your email address"
          startIcon={<AtSymbolIcon className="w-5 h-5 text-gray-500" />}
        />
      </form>
    </div>
  );
}
