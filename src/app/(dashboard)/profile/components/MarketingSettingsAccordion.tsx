"use client";

import React, { useState } from "react";
import { ChevronDownIcon, ChartBarIcon } from "@heroicons/react/24/outline";
import { motion, AnimatePresence } from "framer-motion";
import AccordionSettingProfile from "@/app/(auth)/components/AccordionSettingProfile";

interface MarketingSettingsAccordionProps {
  choice: Record<number, string[]>;
  setChoice: (choice: Record<number, string[]>) => void;
  errors?: Record<string, string>;
  showValidation?: boolean;
}

export default function MarketingSettingsAccordion({
  choice,
  setChoice,
  errors,
  showValidation,
}: MarketingSettingsAccordionProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Count total selections across all sections
  const totalSelections = Object.values(choice).reduce((total, selections) => {
    return (
      total +
      (Array.isArray(selections) ? selections.length : selections ? 1 : 0)
    );
  }, 0);

  // Check if there are any errors
  const hasErrors = showValidation && errors && Object.keys(errors).length > 0;

  return (
    <div className="mb-8 bg-white rounded-xl border border-gray-200 shadow-sm">
      <div
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center justify-between cursor-pointer p-6 transition-colors ${
          hasErrors ? "bg-red-50/30 border-red-200" : "hover:bg-gray-50"
        }`}
      >
        <div className="flex items-center gap-3">
          <div
            className={`p-2 rounded-lg ${
              hasErrors ? "bg-red-500" : "bg-primary"
            }`}
          >
            <ChartBarIcon className="w-5 h-5 text-white" />
          </div>
          <div className="flex flex-col">
            <h3 className="text-xl font-bold text-secondary">
              Marketing & Business Settings
            </h3>
            <div className="flex items-center gap-4 mt-1">
              {hasErrors && (
                <span className="text-sm text-red-600 flex items-center gap-1">
                  <span>⚠</span>
                  {Object.keys(errors).length} field(s) need attention
                </span>
              )}
              {!isOpen && !hasErrors && (
                <span className="text-sm text-gray-500">
                  Click to configure your business settings
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {!isOpen && hasErrors && (
            <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              Required
            </div>
          )}
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            <ChevronDownIcon className="w-5 h-5 text-gray-500" />
          </motion.div>
        </div>
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: "easeInOut",
              opacity: { duration: 0.2 },
            }}
            className="overflow-hidden"
          >
            <div className="border-t border-gray-200 p-6">
              <div className="mb-4">
                <p className="text-gray-600 text-sm">
                  Help us personalize your experience by providing information
                  about your business and marketing needs.
                </p>
              </div>
              <AccordionSettingProfile
                classBoxGrid="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6 max-w-full overflow-hidden"
                choice={choice}
                setChoice={setChoice}
                classPlus="!max-w-full gap-4 lg:gap-6 overflow-hidden !mx-0"
                errors={errors}
                showValidation={showValidation}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
