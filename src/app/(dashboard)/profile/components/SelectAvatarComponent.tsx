"use client";

import React, { useState } from "react";
import Image from "next/image";
import { FaCheck } from "react-icons/fa";
import { UserIcon } from "@heroicons/react/24/outline";
import { motion } from "framer-motion";

interface SelectAvatarComponentProps {
  image: any;
  setSelect: (val: string) => void;
  select: string;
}

// Simple animation variants using only opacity for better performance
const overlayVariants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
};

export default function SelectAvatarComponent({
  image,
  setSelect,
  select,
}: SelectAvatarComponentProps) {
  const isSelected = select === image.id;
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <button
      onClick={() => setSelect(image.id)}
      type="button"
      title="Select avatar"
      className={`relative h-[80px] w-[80px] lg:h-[100px] lg:w-[100px] mx-auto rounded-full focus:outline-none hover:opacity-90 transition-opacity ${
        isSelected
          ? "ring-4 ring-primary ring-offset-2"
          : "focus:ring-2 focus:ring-primary focus:ring-offset-2"
      }`}
    >
      {!imageError ? (
        <div className="rounded-full h-[80px] w-[80px] lg:h-[100px] lg:w-[100px] overflow-hidden">
          <Image
            alt="Avatar option"
            src={image.image_url}
            width={100}
            height={100}
            className="rounded-full h-[80px] w-[80px] lg:h-[100px] lg:w-[100px] object-cover"
            onError={handleImageError}
          />
        </div>
      ) : (
        <div className="rounded-full h-[80px] w-[80px] lg:h-[100px] lg:w-[100px] flex items-center justify-center bg-gray-100">
          <UserIcon className="w-8 h-8 lg:w-12 lg:h-12 text-gray-600" />
        </div>
      )}
      {isSelected && (
        <motion.div
          className="absolute inset-0 bg-primary/20 rounded-full flex items-center justify-center"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="bg-primary rounded-full p-2">
            <FaCheck className="w-4 h-4 text-white" />
          </div>
        </motion.div>
      )}
    </button>
  );
}
