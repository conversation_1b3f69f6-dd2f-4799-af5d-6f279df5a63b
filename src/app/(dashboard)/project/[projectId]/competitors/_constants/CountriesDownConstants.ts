import googleLogo from "@/../public/images/dashboard/google.svg";
import yahooLogo from "@/../public/images/dashboard/yahoo.svg";
import bingLogo from "@/../public/images/dashboard/bing.svg";

import { StaticImageData } from "next/image";

export type CountryOption = {
  engine: StaticImageData;
  value: string;
  code: string;
};

export const countriesDropdown: CountryOption[] = [
  {
    engine: googleLogo,
    value: "France",
    code: "FR",
  },
  {
    engine: bingLogo,
    value: "Germany",
    code: "DE",
  },
  {
    engine: yahooLogo,
    value: "Italy",
    code: "IT",
  },
];
export const keywordsDropdown = ["All keywords", "Hello", "World", "keyword"];
export const topFilters = ["All", "Top 50", "Top 25", "Top 10"];
