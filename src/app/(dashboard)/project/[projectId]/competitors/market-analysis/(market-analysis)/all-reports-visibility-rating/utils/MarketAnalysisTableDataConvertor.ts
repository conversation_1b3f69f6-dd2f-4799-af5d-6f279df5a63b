import { TableData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";

export interface CompetitorStats {
  domain: string;
  visibility_rating: number;
  traffic_forecast: number;
  keywords_ranking: number;
  pct_in_top10: number;
  domain_authority: number;
  backlinks: number;
  referring_domains: number;

  previous_visibility_rating: number;
  previous_traffic_forecast: number;
  previous_keywords_ranking: number;
  previous_pct_in_top10: number;
  previous_domain_authority: number;
  previous_backlinks: number;
  previous_referring_domains: number;
}

export type CompetitorStatsList = CompetitorStats[];

export function MarketAnalysisTableDataConvertor(input: CompetitorStatsList) {
  const firstObject = input[0];

  const data: TableData = {
    tableHeadings: Object.keys(firstObject)
      .filter((key) => !key.startsWith("previous_"))
      .map((key) =>
        key
          .replace(/_/g, " ")
          .replace("pct", "%")
          .replace(/\b\w/g, (char) => char.toUpperCase())
      ),
    tableBody: input.map((row) => {
      const bodyRow = Object.entries(row)
        .filter(([key]) => !key.startsWith("previous_"))
        .map(([key, value]) => {
          const prevKey = `previous_${key}`;
          const prevValue = (row as any)[prevKey];

          let growth: string;
          if (typeof prevValue !== "number") {
            growth = "";
          } else if (prevValue === 0) {
            growth = "0";
          } else {
            const diff = Math.round(Number(value) - Number(prevValue));
            if (diff > 0) {
              growth = `+${diff}`;
            } else if (diff < 0) {
              growth = `${diff}`;
            } else {
              growth = "0";
            }
          }

          return {
            value: String(value),
            growth,
          };
        });

      return bodyRow;
    }),
  };

  return data;
}
