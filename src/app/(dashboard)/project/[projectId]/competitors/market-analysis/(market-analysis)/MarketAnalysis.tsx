"use client";
/* ================================== RECT ================================== */
import React, { useCallback, useEffect, useRef, useState } from "react";

/* ============================== MANTINE HOOKS ============================= */
import { useClickOutside } from "@mantine/hooks";

/* ================================= ZUSTAND ================================ */
import { useCompetitorsStore } from "@/store/competitors/useCompetitorsAdded";

/* =============================== COMPONENTS =============================== */
import CompetitorAnalysisHeader from "../../competitor-analysis/(competitor-analysis)/_components/CompetitorAnalysisHeader";
import DateRangePicker from "../../../analytics-traffics/_components/date-range-picker/DateRangePicker";
import NoCompetitorsAdded from "../../_components/NoCompetitorsAdded";
import SearchVisibility from "./search-visibility/SearchVisibility";
import AllReportsVisibilityRating from "./all-reports-visibility-rating/AllReportsVisibilityRating";

/* ================================ TYPES ================================ */
import { GroupByOption } from "./search-visibility/constants";
import { PeriodOption } from "./search-visibility/constants";
import { useSearchVisibilityCharts } from "./search-visibility/SearchVisibility.hooks";
import { countriesDropdown } from "../../_constants/CountriesDownConstants";
import { useProjectContext } from "@/contexts/ProjectContext";
import { lineChartConvertor } from "@/utils/api-response-convertors/lineChartDataConvertor";
import { MarketAnalysisResponse } from "./search-visibility/SearchVisibility.types";
import { CompetitorsLineChartDataConvertor } from "../../competitor-analysis/(competitor-analysis)/competitors/utils/lineChartDataConvertor";

/* ================================== MAIN ================================== */
const MarketAnalysis = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const charts = ["Search Visibility", "Competitor Distribution"];
  const { projectName } = useProjectContext();

  /* ========================================================================== */
  /*                                   STATES                                   */
  /* ========================================================================== */
  const [selectedChart, setSelectedChart] = useState<string>(charts[0]);
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const [selectedTableItems, setSelectedTableItems] = useState<string[]>([]);
  const [selectedExtraColumn, setSelectedExtraColumn] = useState<string[]>([]);
  const [activeGroupBy, setActiveGroupBy] = useState<GroupByOption>("Days");
  const [activeCard, setActiveCard] = useState<string>("visibility_rating");
  const [activePeriod, setActivePeriod] = useState<PeriodOption>("7D");
  const [selectedCountry, setSelectedCountry] = useState(countriesDropdown[0]);
  const [convertedLineChartData, setConvertedLineChartData] = useState<
    MarketAnalysisResponse | undefined
  >(undefined);

  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */
  const handleDateRangeClick = useCallback(() => {
    setShowDateRangePicker((prev) => !prev);
  }, []);

  /* ========================================================================== */
  /*                                  HOOKS                                     */
  /* ========================================================================== */
  const toggleButtonRef = useRef<HTMLDivElement>(null);
  const dateRangePickerRef = useClickOutside(() => {
    if (showDateRangePicker) {
      const isClickInsideToggle = toggleButtonRef.current?.contains(
        document.activeElement
      );

      if (!isClickInsideToggle) {
        setShowDateRangePicker(false);
      }
    }
  });

  /* ========================================================================== */
  /*                                  EFFECTS                                   */
  /* ========================================================================== */

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (showDateRangePicker) {
        if (
          toggleButtonRef.current?.contains(event.target as Node) ||
          dateRangePickerRef.current?.contains(event.target as Node)
        ) {
          return;
        }
        setShowDateRangePicker(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showDateRangePicker, dateRangePickerRef]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  const { addedCompetitors } = useCompetitorsStore();
  const noData = addedCompetitors.length <= 0;

  return (
    <div className="space-y-6">
      <DateRangePicker
        ShowDateRangePicker={showDateRangePicker}
        ref={dateRangePickerRef}
      />
      <CompetitorAnalysisHeader
        title={`${projectName} Market Analysis`}
        variant="selectRange"
        onDateRangeClick={handleDateRangeClick}
        ref={toggleButtonRef}
      />
      {noData ? (
        <NoCompetitorsAdded />
      ) : (
        <SearchVisibility
          charts={charts}
          selectedChart={selectedChart}
          setSelectedChart={setSelectedChart}
          activePeriod={activePeriod}
          setActivePeriod={setActivePeriod}
          activeGroupBy={activeGroupBy}
          setActiveGroupBy={setActiveGroupBy}
          activeCard={activeCard}
          setActiveCard={setActiveCard}
          selectedCountry={selectedCountry}
          setSelectedCountry={setSelectedCountry}
        />
      )}
      <AllReportsVisibilityRating
        selectedExtraColumn={selectedExtraColumn}
        setSelectedExtraColumn={setSelectedExtraColumn}
        selectedChart={selectedChart}
        selectedTableItems={selectedTableItems}
        setSelectedTableItems={setSelectedTableItems}
        refetch={() => void 0}
      />
    </div>
  );
};

export default MarketAnalysis;
