import { useQuery } from "@tanstack/react-query";
import http from "@/services/httpService";
import type { CompetitorApiResponse } from "@/utils/api-response-convertors/lineChartDataConvertor";

import { useProjectId } from "@/hooks/useProjectId";
import { BubbleChartDataResponse } from "../../../competitor-analysis/(competitor-analysis)/seo-competitor-landscape/_components/BubbleChartComponent.types";
import { bubbleChartDataConvertor } from "./utils/bubbleChartDataConvertor";
import { CompetitorLineChartApiResponse } from "../../../competitor-analysis/(competitor-analysis)/competitors/utils/lineChartDataConvertor";
import { CompetitorSearchVisibilityResponse } from "./utils/lineChartDataConvertor";

export const useSearchVisibilityCharts = (
  period: string,
  groupBy: string,
  // selectedChart: string,
  // activeCard: string,
  // selectedTableItems: string,
  // selectedCountry: string
) => {
  const { projectId } = useProjectId();
  return useQuery({
    queryKey: [
      "useSearchVisibilityCharts",
      groupBy,
      period,
      // selectedChart,
      // activeCard,
      // selectedCountry,
    ],
    queryFn: async (): Promise<CompetitorSearchVisibilityResponse> => {
      const { data } = await http.get(
        `/api/projects/${projectId}/competitors/search-visibility`,
        {
          params: {
            // groupBy,
            period,
            // selectedChart,
            // activeCard,
            // selectedTableItems,
            // selectedCountry,
            force_refresh: false,
          },
        }
      );
      return data;
    },
    enabled: !!projectId,
    refetchOnWindowFocus: false,
  });
};

export const useCompetitorDistribution = (
  period: string,
  groupBy: string,
  selectedChart: string
  // activeCard: string,
  // selectedCountry: string
) => {
  const { projectId } = useProjectId();
  return useQuery({
    queryKey: ["useSearchVisibilityCharts", groupBy, period, selectedChart],
    queryFn: async (): Promise<BubbleChartDataResponse[]> => {
      const { data } = await http.get(
        `/api/projects/${projectId}/competitors/distribution-serp/`,
        {
          params: {
            // groupBy,
            period,
            // selectedChart,
            // activeCard,
            // selectedTableItems,
            // selectedCountry,
            force_refresh: false,
          },
        }
      );

      return bubbleChartDataConvertor(data);
    },
    enabled: !!projectId,
    refetchOnWindowFocus: false,
  });
};
