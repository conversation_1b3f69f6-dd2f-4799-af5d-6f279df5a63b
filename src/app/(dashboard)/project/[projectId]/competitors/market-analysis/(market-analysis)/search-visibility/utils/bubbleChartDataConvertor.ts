import type { BubbleChartDataResponse } from "../../../../competitor-analysis/(competitor-analysis)/seo-competitor-landscape/_components/BubbleChartComponent.types";

export type CompetitorDistributionApiResponse = {
  results: Array<{
    domain: string;
    keywords_ranked: number;
    average_position: number;
    top_100_count: number;
  }>;
};
const colors = [
  "#FF00C3",
  "#00BBEC",
  "#F57D37",
  "#31D37A",
  "#3C0866",
  "#FFA5DA",
];

export function bubbleChartDataConvertor(
  input: CompetitorDistributionApiResponse
): BubbleChartDataResponse[] {
  return input.results.map((item, index) => ({
    x: item.keywords_ranked,
    y: item.average_position,
    z: item.top_100_count,
    color: colors[index % colors.length],
    title: item.domain,
    keywordRanked: item.keywords_ranked,
    ABGPosition: item.average_position,
    keywordCountInTop100: item.top_100_count,
    growth: "", // empty for now
  }));
}
