import { useQuery } from "@tanstack/react-query";
import http from "@/services/httpService";
import { TableData } from "../../../../analytics-traffics/_components/data-table/DataTable.types";
import { useProjectId } from "@/hooks/useProjectId";
import {
  CompetitorStatsList,
  MarketAnalysisTableDataConvertor,
} from "./utils/MarketAnalysisTableDataConvertor";

export const useMarketAnalysisTable = (
  columns: string,
  currentPage: number
) => {
  const { projectId } = useProjectId();

  return useQuery({
    queryKey: ["useMarketAnalysisTable", columns, currentPage],
    queryFn: async (): Promise<TableData> => {
      const { data } = await http.get(
        `/api/projects/${projectId}/competitors/visibility-rating`,
        {
          params: {
            force_refresh: false,
          },
        }
      );
      return MarketAnalysisTableDataConvertor(data.results);
    },
    refetchOnWindowFocus: false,
  });
};
