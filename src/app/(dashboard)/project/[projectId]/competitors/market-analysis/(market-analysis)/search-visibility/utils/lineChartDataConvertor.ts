import { LineChartDataResponse } from "../../MarketAnalysis.types";

export type CompetitorSearchVisibilityResponse = {
  status: string; // being safe here
  project_id: string;
  period: {
    start_date: string; // e.g. "2025-08-26"
    end_date: string; // e.g. "2025-09-02"
    days_count: number;
  };
  data: {
    search_visibility: {
      competitors: {
        domain: string;
        totals: {
          visibility_rating: number;
          traffic_forecast: number;
          pct_in_top10: number;
        };
        daily_metrics: {
          date: string; // "YYYYMMDD"
          visibility_rating: number;
          traffic_forecast: number;
          pct_in_top10: number;
        }[];
      }[];
    };
  };
};

export type MultiMetricLineChartDataApiResponse = {
  visibility_rating: LineChartDataResponse;
  traffic_forecast: LineChartDataResponse;
  pct_in_top10: LineChartDataResponse;
};

// {
//     "status": "success",
//     "project_id": "202c4198-c34f-4f52-906c-7d467cee1bdc",
//     "period": {
//         "start_date": "2025-08-26",
//         "end_date": "2025-09-02",
//         "days_count": 8
//     },
//     "data": {
//         "search_visibility": {
//             "competitors": [
//                 {
//                     "domain": "seoanalyser.com.au",
//                     "totals": {
//                         "visibility_rating": 23.9,
//                         "traffic_forecast": 9.93,
//                         "pct_in_top10": 1.25
//                     },
//                     "daily_metrics": [
//                         {
//                             "date": "20250826",
//                             "visibility_rating": 0.0,
//                             "traffic_forecast": 0.0,
//                             "pct_in_top10": 0.0
//                         },
/* ========================================================================== */
// const LINE_CHART_DATA: LineChartDataResponse = {
//   cardsData: {
//     digikala_com: { amount: 12000, growth: "+21.2%" },
//     myescape_ir: { amount: 12000, growth: "0" },
//     website1_ir: { amount: 12000, growth: "+21.2%" },
//     website2_ir: { amount: 12000, growth: "+16%" },
//     website3_com: { amount: 12000, growth: "-5%" },
//   },
//   colors: [
//     { name: "digikala_com", color: "#ff00ff" },
//     { name: "myescape_ir", color: "#31D37A" },
//     { name: "website1_ir", color: "#00BBEC" },
//     { name: "website2_ir", color: "#3C0866" },
//     { name: "website3_com", color: "#F57D37" },
//   ],
//   selectedLines: [
//     "digikala_com",
//     "myescape_ir",
//     "website1_ir",
//     "website2_ir",
//     "website3_com",
//   ],
//   lineChartData: [
//     {
//       name: "May",
//       digikala_com: 3000,
//       myescape_ir: 1398,
//       website1_ir: 1400,
//       website2_ir: 3110,
//       website3_com: 3510,
//     },

export function MarketAnalysisLineChartDataConvertor(
  input: CompetitorSearchVisibilityResponse
): MultiMetricLineChartDataApiResponse {
  const colorsPalette = ["#ff00ff", "#31D37A", "#00BBEC", "#3C0866", "#F57D37"];

  // normalize domain names → replace dots with underscores
  const formatDomain = (domain: string) => domain.replace(/\./g, "_");

  // format date like "Sep 02"
  const formatDate = (dateStr: string) => {
    const year = dateStr.slice(0, 4);
    const month = dateStr.slice(4, 6);
    const day = dateStr.slice(6, 8);
    const d = new Date(`${year}-${month}-${day}`);
    return d.toLocaleDateString("en-US", {
      month: "short",
      day: "2-digit",
    });
  };

  // prepare common colors + selectedLines
  const colors = input.data.search_visibility.competitors.map(
    (competitor, idx) => ({
      name: formatDomain(competitor.domain),
      color: colorsPalette[idx % colorsPalette.length],
    })
  );

  const selectedLines = colors.map((c) => c.name);

  // all dates (assuming they’re aligned across competitors)
  const dates =
    input.data.search_visibility.competitors[0]?.daily_metrics.map(
      (m) => m.date
    ) || [];

  // helper to build response for a specific metric
  const buildResponse = (
    metricKey: "visibility_rating" | "traffic_forecast" | "pct_in_top10"
  ): LineChartDataResponse => {
    // cardsData
    const cardsData: LineChartDataResponse["cardsData"] = {};
    input.data.search_visibility.competitors.forEach((competitor) => {
      const key = formatDomain(competitor.domain);
      cardsData[key] = {
        amount: competitor.totals[metricKey],
        growth: "", // always empty string
      };
    });

    // lineChartData
    const lineChartData: {
      [key: string]: string | number;
      name: string;
    }[] = dates.map((date) => {
      const row: { [key: string]: string | number; name: string } = {
        name: formatDate(date),
      };
      input.data.search_visibility.competitors.forEach((competitor) => {
        const key = formatDomain(competitor.domain);
        const metric = competitor.daily_metrics.find((m) => m.date === date);
        row[key] = metric ? metric[metricKey] : 0;
      });
      return row;
    });

    return {
      cardsData,
      colors,
      selectedLines,
      lineChartData,
    };
  };

  return {
    visibility_rating: buildResponse("visibility_rating"),
    traffic_forecast: buildResponse("traffic_forecast"),
    pct_in_top10: buildResponse("pct_in_top10"),
  };
}
