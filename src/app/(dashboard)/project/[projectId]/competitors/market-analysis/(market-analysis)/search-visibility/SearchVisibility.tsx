"use client";
import React, { useState, useEffect, SetStateAction } from "react";
import Image from "next/image";

/* ============================= COMPONENTS ============================== */
import Card from "@/components/ui/card";
import Dropdown from "@/components/ui/Dropdown";
import Title from "@/components/ui/Title";
import DateRange from "../../../../analytics-traffics/_components/date-range/DateRange";
import BubbleChartComponent from "../../../competitor-analysis/(competitor-analysis)/seo-competitor-landscape/_components/BubbleChartComponent";
import LineChartCard from "../../../../analytics-traffics/overview/_components/LineChartCard";
import GSCLineChart from "../../../../analytics-traffics/analytic-insight/_components/line-chart/LineChart";

/* ============================ LIBRARY NAME ============================= */
import Flag from "react-world-flags";

/* ================================ UTILS ================================ */
import { cn } from "@/utils/cn";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

/* ================================ HOOKS ================================ */
import LineChartSkeleton from "../../../../analytics-traffics/_components/line-chart-skeleton/LineChartSkeleton";
import {
  GROUP_BY_OPTIONS,
  GroupByOption,
  PERIOD_OPTIONS,
  PeriodOption,
} from "./constants";
import { AnimatePresence, motion } from "framer-motion";
import SmallChartSkeleton from "../../../../analytics-traffics/_components/small-chart-skeleton/SmallChartSkeleton";
import {
  countriesDropdown,
  CountryOption,
} from "../../../_constants/CountriesDownConstants";
import { useProjectContext } from "@/contexts/ProjectContext";
import {
  useCompetitorDistribution,
  useSearchVisibilityCharts,
} from "./SearchVisibility.hooks";
import ErrorSection from "@/ui/ErrorSection";
import {
  MarketAnalysisLineChartDataConvertor,
  MultiMetricLineChartDataApiResponse,
} from "./utils/lineChartDataConvertor";
import { LineChartDataItems } from "@/types/LineChartCard.type";

interface SearchVisibilityProps {
  selectedChart: string;
  setSelectedChart: (chart: string) => void;
  charts: string[];
  activePeriod: PeriodOption;
  setActivePeriod: (period: PeriodOption) => void;
  activeGroupBy: GroupByOption;
  setActiveGroupBy: (groupBy: GroupByOption) => void;
  activeCard: string;
  setActiveCard: (card: string) => void;
  selectedCountry: CountryOption;
  setSelectedCountry: React.Dispatch<SetStateAction<CountryOption>>;
}

/* ================================== MAIN ================================== */
const SearchVisibility = ({
  selectedChart,
  setSelectedChart,
  charts,
  activePeriod,
  setActivePeriod,
  activeGroupBy,
  setActiveGroupBy,
  activeCard,
  setActiveCard,
  selectedCountry,
  setSelectedCountry,
}: SearchVisibilityProps) => {
  /* ========================================================================== */
  /*                                   STATES                                   */
  /* ========================================================================== */

  const [previousChartCards, setPreviousChartCards] = useState<
    MultiMetricLineChartDataApiResponse | undefined
  >(undefined);
  const themeColor = useProjectThemeColor((state) => state.themeColor);
  const { projectName } = useProjectContext();
  const [convertedLineChartData, setConvertedLineChartData] = useState<
    MultiMetricLineChartDataApiResponse | undefined
  >(undefined);

  /* ========================================================================== */
  /*                                   HOOKS                                    */
  /* ========================================================================== */

  const {
    data: competitorDistributionData,
    isLoading: competitorDistributionIsLoading,
    isPending: competitorDistributionIsPending,
    isFetching: competitorDistributionIsFetching,
    refetch: competitorDistributionRefetch,
  } = useCompetitorDistribution(activePeriod, activeGroupBy, activeCard);

  const {
    data: LineChartsData,
    isLoading: LineChartsIsLoading,
    isPending: LineChartsIsPending,
    isError: LineChartsIsError,
    error: LineChartsError,
    refetch: LineChartsRefetch,
    isFetching: LineChartsIsFetching,
  } = useSearchVisibilityCharts(
    activePeriod,
    activeGroupBy
    // activeCard,
    // selectedChart,
    // selectedCountry.code
  );

  /* ========================================================================== */
  /*                                FUNCTIONS                                  */
  /* ========================================================================== */
  const handlePeriodChange = (period: PeriodOption) => {
    setActivePeriod(period);
  };

  const handleGroupByChange = (groupBy: GroupByOption) => {
    setActiveGroupBy(groupBy);
  };

  useEffect(() => {
    if (!LineChartsData) return;
    setConvertedLineChartData(
      MarketAnalysisLineChartDataConvertor(LineChartsData)
    );
    setPreviousChartCards(MarketAnalysisLineChartDataConvertor(LineChartsData));
    console.log(
      "LineChartsData",
      MarketAnalysisLineChartDataConvertor(LineChartsData)
    );
    console.log("projectName", projectName);
  }, [LineChartsData]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  if (LineChartsIsError) console.error(LineChartsError);
  return (
    <Card className="space-y-4">
      {/* =============================== HEADER ============================== */}
      <div className="flex items-center justify-between">
        <Title>
          {projectName} {selectedChart}
        </Title>
        <Dropdown>
          <Dropdown.Button>{selectedChart}</Dropdown.Button>
          <Dropdown.Options>
            {charts.map((chart) => (
              <Dropdown.Option
                key={chart}
                onClick={() => setSelectedChart(chart)}
              >
                {chart}
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
      </div>

      {/* =============================== DATE RANGE ============================== */}
      <DateRange variation="short" />

      {/* =============================== LOCATION DROPDOWN ============================== */}
      <Dropdown className="bg-white h-fit">
        <Dropdown.Button className="bg-white border pl-1 pr-4 w-48 py-1 h-fit">
          <div className="flex items-center gap-2">
            <div className="border p-1 rounded-sm">
              <Image
                className="h-5"
                src={selectedCountry.engine}
                alt="search engine logo"
                width={20}
                height={20}
              />
            </div>
            <Flag className="w-5 rounded-xs" code={selectedCountry.code} />
            <span>{selectedCountry.value}</span>
          </div>
        </Dropdown.Button>
        <Dropdown.Options className="border border-t-0">
          {countriesDropdown.map((country) => (
            <Dropdown.Option
              key={country.code}
              onClick={() => setSelectedCountry(country)}
              className="py-1 bg-white flex items-center gap-5 border-t-0 pl-1 pr-4 h-fit"
            >
              <div className="flex items-center gap-2">
                <div className="border p-1 rounded-sm">
                  <Image
                    className="h-5"
                    src={country.engine}
                    alt="search engine logo"
                    width={20}
                    height={20}
                  />
                </div>
                <Flag className="w-5 rounded-xs" code={country.code} />
                <span>{country.value}</span>
              </div>
            </Dropdown.Option>
          ))}
        </Dropdown.Options>
      </Dropdown>

      {/* =============================== CHART CONTENT ============================== */}
      <div>
        {selectedChart === "Competitor Distribution" ? (
          <div>
            {competitorDistributionIsLoading ||
            competitorDistributionIsPending ||
            competitorDistributionIsFetching ? (
              <BubbleChartComponent isLoading />
            ) : competitorDistributionData ? (
              <BubbleChartComponent data={competitorDistributionData} />
            ) : (
              <ErrorSection
                message={LineChartsError?.message}
                onRetry={competitorDistributionRefetch}
              />
            )}

            <div className="flex gap-4 pt-6">
              {competitorDistributionData &&
                competitorDistributionData.map(({ title, color }) => (
                  <div key={title} className="flex items-center gap-2">
                    <div
                      className="w-2 h-2 rounded-[4px]"
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-xs text-secondary">
                      {title.replace("_", ".")}
                    </span>
                  </div>
                ))}
            </div>
          </div>
        ) : (
          <div className="flex flex-col gap-4">
            {/* =============================== CHART CARDS ============================== */}
            <AnimatePresence mode="wait">
              {false ? (
                <motion.div
                  className="flex gap-4"
                  key={"loading"}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {Array.from({ length: 3 }).map((_, i) => (
                    <SmallChartSkeleton key={i} className="w-full" />
                  ))}
                </motion.div>
              ) : (
                LineChartsData &&
                convertedLineChartData && (
                  <motion.div
                    key={"data"}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="flex gap-4"
                  >
                    {Object.entries(convertedLineChartData).map(
                      ([metricKey, metricData]) => (
                        <LineChartCard
                          onClick={() => setActiveCard(metricKey)}
                          key={metricKey}
                          className={cn("w-full border cursor-pointer", {
                            "border-primary": activeCard === metricKey,
                          })}
                          bigNumber={
                            // first competitor’s total for *this metric*
                            LineChartsData.data.search_visibility.competitors[0].totals[
                              metricKey as keyof (typeof LineChartsData.data.search_visibility.competitors)[0]["totals"]
                            ].toString()
                          }
                          smallNumber=""
                          title={metricKey
                            .replace(/_/g, " ")
                            .replace("pct", "%")
                            .replace(/\b\w/g, (c) => c.toUpperCase())}
                          data={metricData.lineChartData.map((row) => {
                            // if you want all competitors plotted, you can map differently,
                            // but here I’ll just stick with the first competitor for clarity
                            const firstCompetitorDomain = projectName.replace(
                              /\./g,
                              "_"
                            );

                            return {
                              name: row.name,
                              value: row[firstCompetitorDomain] as number,
                            } as LineChartDataItems;
                          })}
                        />
                      )
                    )}
                  </motion.div>
                )
              )}
            </AnimatePresence>

            {/* =============================== CHART CONTROLS ============================== */}
            <div className="flex gap-4 items-center px-2">
              {/* =============================== PERIOD SELECTOR ============================== */}
              <div className="text-[10px] text-secondary flex items-center gap-2">
                <span>Period :</span>
                {PERIOD_OPTIONS.map((option) => (
                  <span
                    key={option}
                    onClick={() => handlePeriodChange(option)}
                    className={cn("cursor-pointer font-bold")}
                    style={activePeriod === option ? { color: themeColor } : {}}
                  >
                    {option}
                  </span>
                ))}
              </div>

              <div className="h-[14px] w-[1px] bg-[#E0E0E0]" />

              {/* =============================== GROUP BY SELECTOR ============================== */}
              <div className="text-[10px] text-secondary flex items-center gap-2">
                <span>Group by :</span>
                <Dropdown className="bg-white">
                  <Dropdown.Button
                    className="bg-white text-[10px]"
                    chevronClassName="text-xs"
                  >
                    {activeGroupBy}
                  </Dropdown.Button>
                  <Dropdown.Options className="bg-white text-[10px] shadow-md font-bold">
                    {GROUP_BY_OPTIONS.map((option) => (
                      <Dropdown.Option
                        key={option}
                        onClick={() => handleGroupByChange(option)}
                        className={cn(
                          "cursor-pointer",
                          activeGroupBy === option && "text-primary"
                        )}
                      >
                        {option}
                      </Dropdown.Option>
                    ))}
                  </Dropdown.Options>
                </Dropdown>
              </div>
            </div>

            {/* =============================== LINE CHART ============================== */}
            <div className="flex items-center gap-2">
              <span
                className="text-[#6C757D] text-sm"
                style={{ writingMode: "vertical-rl", rotate: "180deg" }}
              >
                visibility rating
              </span>
              {LineChartsIsLoading ||
              LineChartsIsPending ||
              LineChartsIsFetching ? (
                <LineChartSkeleton />
              ) : LineChartsData && convertedLineChartData ? (
                <GSCLineChart
                  lineChartData={
                    convertedLineChartData[activeCard].lineChartData
                  }
                  colors={convertedLineChartData[activeCard].colors}
                  selectedLines={
                    convertedLineChartData[activeCard].selectedLines
                  }
                  cardsData={convertedLineChartData[activeCard].cardsData}
                  tooltipItemsClassName="grid-flow-row"
                />
              ) : (
                <ErrorSection
                  message={LineChartsError?.message}
                  onRetry={LineChartsRefetch}
                />
              )}
            </div>

            {/* =============================== CHART LEGEND ============================== */}
            <div className="flex gap-4 pt-6">
              {convertedLineChartData?.visibility_rating.colors.map(
                ({ name, color }) => (
                  <div key={name} className="flex items-center gap-2">
                    <div
                      className="w-2 h-2 rounded-[4px]"
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-xs text-secondary">
                      {name.replace(/_/g, ".")}
                    </span>
                  </div>
                )
              )}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default SearchVisibility;
