import { useMutation, useQueryClient } from "@tanstack/react-query";
import http from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { showToast } from "@/lib/toast";

/* ============================= ADD COMPETITOR ============================= */
type AddCompetitorPayload = {
  url: string;
  search_engines: string[];
  countries: string[];
};

export const useAddCompetitor = () => {
  const { projectId } = useProjectId();

  return useMutation({
    mutationFn: async (payload: AddCompetitorPayload) => {
      const response = await http.post(
        `/api/project/${projectId}/competitors/`,
        payload
      );
      return response.data;
    },
    onSuccess: () => {
      showToast.success("Competitor added successfully!");
    },
    onError: () => {
      showToast.error("Failed to add competitor.");
    },
  });
};

/* ============================ DELETE COMPETITOR =========================== */
export const useDeleteCompetitor = () => {
  const { projectId } = useProjectId();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (url: string) => {
      const response = await http.delete(
        `/api/project/${projectId}/competitors/`,
        {
          data: { url }, // DELETE in axios uses `data`
        }
      );
      return response.data;
    },
    onSuccess: () => {
      showToast.success("Competitor removed successfully!");
    },
    onError: () => {
      showToast.error("Failed to remove competitor.");
    },
  });
};
