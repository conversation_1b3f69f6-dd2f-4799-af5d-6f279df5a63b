/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";

/* ================================== ICONS ================================= */
import { CgInfo } from "react-icons/cg";
const NoCompetitorsAdded = () => {
  return (
    <Card className="flex flex-col justify-start items-center h-[500px] gap-4">
      <div className="flex gap-4 items-center text-secondary pt-24">
        <CgInfo className="scale-y-[-1] size-6" />
        <h4 className="text-xl font-extrabold">No Competitor Added Yet</h4>
      </div>
      <span className="text-secondary/40 text-xl">
        connect to google analytics and search console to get fully report
      </span>
    </Card>
  );
};

export default NoCompetitorsAdded;
