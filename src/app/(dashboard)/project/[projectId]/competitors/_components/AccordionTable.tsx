import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { createPortal } from "react-dom";
import { cn } from "@/utils/cn";
import Title from "@/components/ui/Title";

/* ================================== COMPONENTS ============================== */
import Card from "@/components/ui/card";

/* ================================== TYPES ================================= */
type TableBody = { value: string; growth?: string };

export type AccordionTableData = {
  tableHeadings: string[];
  tableBody: {
    data: TableBody[];
    accordionData: TableBody[];
  }[];
};

type AccordionTableProps = {
  tableData: AccordionTableData;
  title?: string;
  showDateRange?: boolean;
};

type TooltipState = {
  isVisible: boolean;
  position: { x: number; y: number };
};

/* ================================== TOOLTIP =============================== */
const ToolTip = ({ isVisible, position }: TooltipState) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!isVisible || !mounted) return null;

  const tooltipElement = (
    <Card
      className="fixed border py-6 rounded-lg shadow-lg h-[56px] w-[150px] flex justify-center items-center z-50 pointer-events-none"
      style={{
        left: Math.min(position.x + 10, window.innerWidth - 170),
        top: Math.max(position.y - 10, 10),
      }}
    >
      <p className="text-secondary text-xs text-center">
        Double Click To See All Details
      </p>
    </Card>
  );

  return createPortal(tooltipElement, document.body);
};

/* ================================== MAIN ================================== */
const AccordionTable = ({ tableData, title }: AccordionTableProps) => {
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());
  const [tooltipState, setTooltipState] = useState<TooltipState>({
    isVisible: false,
    position: { x: 0, y: 0 },
  });

  const toggleRow = (rowIndex: number) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(rowIndex)) {
      newExpandedRows.delete(rowIndex);
    } else {
      newExpandedRows.add(rowIndex);
    }
    setExpandedRows(newExpandedRows);

    // Hide tooltip when expanding a row
    if (newExpandedRows.has(rowIndex)) {
      setTooltipState((prev) => ({ ...prev, isVisible: false }));
    }
  };

  const handleRowMouseEnter = (
    event: React.MouseEvent,
    hasAccordionData: boolean,
    rowIndex: number,
  ) => {
    if (hasAccordionData && !expandedRows.has(rowIndex)) {
      setTooltipState({
        isVisible: true,
        position: { x: event.clientX, y: event.clientY },
      });
    }
  };

  const handleRowMouseLeave = () => {
    setTooltipState((prev) => ({ ...prev, isVisible: false }));
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (tooltipState.isVisible) {
      setTooltipState((prev) => ({
        ...prev,
        position: { x: event.clientX, y: event.clientY },
      }));
    }
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="space-y-4" onMouseMove={handleMouseMove}>
      {title && <Title>{title}</Title>}

      <div className="overflow-x-auto">
        <table
          className="w-full text-secondary text-xs overflow-hidden"
          style={{ tableLayout: "fixed" }}
        >
          <thead>
            <tr>
              {tableData.tableHeadings.map((heading, index) => (
                <th
                  style={{ userSelect: "none" }}
                  key={index}
                  className={cn(
                    "px-2 text-center h-16 bg-[#F4F4F4] border-[#E0E0E0] border-b-2",
                    index === 0 && "rounded-tl-md",
                    index === tableData.tableHeadings.length - 1 &&
                      "rounded-tr-md",
                  )}
                >
                  {heading}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="overflow-hidden">
            {tableData.tableBody.map((row, rowIndex) => {
              const isExpanded = expandedRows.has(rowIndex);
              const isLastRow = rowIndex === tableData.tableBody.length - 1;
              const hasAccordionData = row.accordionData.length > 0;

              return (
                <React.Fragment key={rowIndex}>
                  {/* ============================ MAIN ROW =========================== */}
                  <motion.tr
                    style={{ userSelect: "none" }}
                    layout
                    transition={{
                      duration: 0.1,
                      ease: "easeInOut",
                      layout: { duration: 0.1 },
                    }}
                    onDoubleClick={() => toggleRow(rowIndex)}
                    onMouseEnter={(e) =>
                      handleRowMouseEnter(e, hasAccordionData, rowIndex)
                    }
                    onMouseLeave={handleRowMouseLeave}
                    className={cn(
                      "hover:bg-gray-50 transition-colors",
                      rowIndex % 2 !== 0 && "bg-[#F4F4F4]",
                      hasAccordionData && "border-b border-primary",
                      hasAccordionData && "cursor-pointer",
                    )}
                  >
                    {row.data.map((cell, cellIndex) => (
                      <td
                        key={cellIndex}
                        className={cn(
                          "px-2 py-4 text-sm text-center border-0 rounded-none",
                          isLastRow && "border-b-0",
                        )}
                      >
                        <span
                          className={cn(
                            "text-sm",
                            hasAccordionData && "text-primary",
                          )}
                        >
                          {cell.value}
                        </span>
                        {cell.growth && (
                          <span
                            className={cn(
                              "block text-[10px] mt-1",
                              cell.growth.includes("+") && "text-primary-green",
                              cell.growth.includes("-") && "text-primary-red",
                            )}
                          >
                            {cell.growth}
                          </span>
                        )}
                      </td>
                    ))}
                  </motion.tr>

                  {/* ============================ ACCORDION CONTENT =========================== */}
                  <AnimatePresence mode="popLayout">
                    {isExpanded && (
                      <motion.tr
                        style={{ userSelect: "none" }}
                        initial={{
                          height: 0,
                          opacity: 0,
                          scaleY: 0,
                          transformOrigin: "top",
                        }}
                        animate={{
                          height: "auto",
                          opacity: 1,
                          scaleY: 1,
                        }}
                        exit={{
                          height: 0,
                          opacity: 0,
                          scaleY: 0,
                        }}
                        transition={{
                          duration: 0.1,
                          ease: "easeInOut",
                        }}
                        className="bg-white overflow-hidden"
                      >
                        {row.accordionData.map((cell, cellIndex) => (
                          <motion.td
                            layout
                            transition={{
                              duration: 0.3,
                              ease: "easeInOut",
                              layout: { duration: 0.3 },
                            }}
                            key={cellIndex}
                            className={cn(
                              "px-2 py-4 text-sm text-center border-0 rounded-none",
                              cellIndex === 0 && "text-left pl-4 truncate",
                              "border-t border-gray-200",
                            )}
                          >
                            <span className="text-sm">{cell.value}</span>
                            {cell.growth && (
                              <span
                                className={cn(
                                  "block text-[10px] mt-1",
                                  cell.growth.includes("+") &&
                                    "text-primary-green",
                                  cell.growth.includes("-") &&
                                    "text-primary-red",
                                )}
                              >
                                {cell.growth}
                              </span>
                            )}
                          </motion.td>
                        ))}
                      </motion.tr>
                    )}
                  </AnimatePresence>
                </React.Fragment>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* =============================== TOOLTIP ============================== */}
      <ToolTip
        isVisible={tooltipState.isVisible}
        position={tooltipState.position}
      />
    </div>
  );
};

export default AccordionTable;
