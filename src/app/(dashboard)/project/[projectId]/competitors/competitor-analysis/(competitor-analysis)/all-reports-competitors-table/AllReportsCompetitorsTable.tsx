"use client";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Pagination from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/Pagination";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import React, { useEffect, useMemo, useState } from "react";
import AddColumns from "../../_components/add-columns/AddColumns";
// import Dropdown from "@/components/ui/Dropdown";
import { useCompetitorsTableData } from "./AllReportsCompetitorsTable.hooks";
import TableSkeleton from "../../../../analytics-traffics/_components/data-table/TableSkeleton";
import { useProjectContext } from "@/contexts/ProjectContext";
import type { TableDataRequest } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";
import { competitorsTableDataConverter } from "./utils/competitorsTableDataConverter";

const AllReportsCompetitorsTable = () => {
  const [tableData, setTableData] = useState<TableDataRequest | undefined>(
    undefined
  );
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  // const numberOfRows = [10, 25, 50];
  // const [selectedRows, setSelectedRows] = useState<number>(10);
  const [page, setPage] = useState<number>(1);
  const columns = useMemo(
    () => ["Domain Trust", "Referral Domain", "Backlinks", "Indexed"],
    []
  );
  const [checkedColumns, setCheckedColumns] = useState<string[]>([]);
  const { projectName } = useProjectContext();

  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */

  const {
    data,
    isLoading: tableLoading,
    isPending: tablePending,
  } = useCompetitorsTableData();

  useEffect(() => {
    if (!data || !Array.isArray(data)) return;

    const tableHeadings = [
      "URL",
      "KEYWORDS",
      "Top5 / Top10 / Top20",
      "AVG POSITION",
      "RF",
      "SEARCH VISIBILITY",
      "TRAFFIC FORECAST",
      ...columns,
    ];

    /* ===================== BUILD THE CONVERTED DATA FIRST ===================== */
    const convertedTableData = competitorsTableDataConverter(
      data,
      tableHeadings
    );

    /* ============ FIGURE OUT WHICH "EXTRA" COLUMNS WE *DON’T* WANT ============ */
    const uncheckedColumns = columns.filter(
      (col) => !checkedColumns.includes(col)
    );

    /* ========= GET INDEXES OF THOSE COLUMNS INSIDE THE HEADINGS ARRAY ========= */
    const indexesToRemove = uncheckedColumns
      .map((col) => tableHeadings.indexOf(col))
      .filter((i) => i !== -1);

    /* ======================== FILTER OUT FROM HEADINGS ======================== */
    const filteredHeadings = tableHeadings.filter(
      (_, i) => !indexesToRemove.includes(i)
    );

    /* ====================== FILTER OUT FROM EACH ROW BODY ===================== */
    const filteredBody = convertedTableData.tableData.tableBody.map((row) =>
      row.filter((_, i) => !indexesToRemove.includes(i))
    );

    setTableData({
      ...convertedTableData,
      tableData: {
        tableHeadings: filteredHeadings,
        tableBody: filteredBody,
      },
    });
  }, [data, checkedColumns, columns]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Card className="space-y-4">
      <div className="flex justify-between items-center relative">
        <Title className="text-lg font-bold">
          {projectName} All reports-competitors
        </Title>
        <AddColumns
          isDisabled={tableLoading || tablePending}
          sendColumns={() => void 0}
          columns={columns}
          checkedColumns={checkedColumns}
          setCheckedColumns={setCheckedColumns}
        />
      </div>
      {tableLoading || tablePending ? (
        <TableSkeleton />
      ) : (
        tableData && <DataTable tableData={tableData.tableData} />
      )}
      <div className="mt-6 flex items-center">
        {tableData && tableData?.pagination.totalPages > 1 && (
          <Pagination
            totalPages={10}
            page={page}
            onPageChange={(page) => setPage(page)}
          />
        )}
        {/* backend guy said we don't need this part but I'm keeping it just in case */}
        {/* <Dropdown className="rotate-180">
          <Dropdown.Button className="flex-row-reverse bg-[#914AC41A]">
            <span className="rotate-180">{selectedRows}</span>
          </Dropdown.Button>
          <Dropdown.Options>
            {numberOfRows.map((num, index) => (
              <Dropdown.Option key={index} onClick={() => setSelectedRows(num)}>
                <div className="rotate-180">{num}</div>
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown> */}
      </div>
    </Card>
  );
};

export default AllReportsCompetitorsTable;
