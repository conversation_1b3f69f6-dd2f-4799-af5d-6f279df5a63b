import { CompetitorLineChartApiResponse } from "@/app/(dashboard)/project/[projectId]/competitors/competitor-analysis/(competitor-analysis)/competitors/utils/lineChartDataConvertor";
import { CompetitorApiResponse } from "../../../../../../../../../utils/api-response-convertors/lineChartDataConvertor";

type BarChartDataResponse = {
  cardsData: Record<
    string,
    {
      amount: number;
      growth: string;
    }
  >;
  colors: { name: string; color: string }[];
  barChartData: { name: string; bar: number }[];
};

export function barChartDataConvertor(
  inputData: CompetitorLineChartApiResponse,
  title: string,
  themeColor?: string
): BarChartDataResponse {
  const barChartData = inputData.data.competitors.map((c) => ({
    name: c.domain,
    bar: Math.round(c.totals[title] ?? 0),
  }));

  const colors = inputData.data.competitors.map((c) => ({
    name: c.domain,
    color: themeColor ?? "#914ac4",
  }));

  const cardsData: Record<string, { amount: number; growth: string }> = {};
  inputData.data.competitors.forEach((c) => {
    const key = c.domain.replace(/\./g, "_");
    const value = Math.round(c.totals[title] ?? 0);
    cardsData[key] = {
      amount: Math.round(c.totals[title] ?? 0),
      growth: `${value > 0 ? "+" : ""}${value}%`,
    };
  });

  const keys = Object.keys(cardsData);
  if (keys.length > 1) {
    const firstKey = keys.shift()!;
    keys.push(firstKey);
  }

  const orderedCardsData: Record<string, { amount: number; growth: string }> =
    {};
  keys.forEach((key) => {
    orderedCardsData[key] = cardsData[key];
  });

  return {
    cardsData: orderedCardsData,
    colors,
    barChartData,
  };
}
