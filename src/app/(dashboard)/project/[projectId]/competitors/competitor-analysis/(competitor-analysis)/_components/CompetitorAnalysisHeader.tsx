import Card from "@/components/ui/card";
import React, { forwardRef, useRef, useState } from "react";
import ShareAndSettings from "@/ui/ShareAndSettings";
import { Button } from "@/components/ui/button";
import { FiCalendar } from "react-icons/fi";
import { LuRefreshCw } from "react-icons/lu";
import SharePopup from "@/app/(dashboard)/project/_components/SharePopup";
import { AnimatePresence, motion } from "framer-motion";
import AddColumns from "../../_components/add-columns/AddColumns";
import toast from "react-hot-toast";

/* ================================= ZUSTAND ================================ */
import { useCompetitorPopupStore } from "@/store/competitors/useAddCompetitorsPopup";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import DateRangePicker from "../../../../analytics-traffics/_components/date-range-picker/DateRangePicker";
import { useClickOutside } from "@mantine/hooks";
import Title from "@/components/ui/Title";
import { cn } from "@/utils/cn";

/* ================================== MAIN ================================== */
const CompetitorAnalysisHeader = forwardRef(
  ({
    variant = "default",
    title,
  }: {
    title: string;
    variant?: "default" | "selectRange";
    onDateRangeClick?: () => void;
  }) => {
    /* ========================================================================== */
    /*                                   STATES                                   */
    /* ========================================================================== */
    const [isSharePopupOpen, setIsSharePopupOpen] = useState(false);

    const columns = ["Refresh Ranking", "Refresh Search Volume"];
    const [checkedColumns, setCheckedColumns] = useState<string[]>([]);
    const [isSending, setIsSending] = useState(false);
    const toggleButtonRef = useRef<HTMLDivElement>(null);
    const [showDateRangePicker, setShowDateRangePicker] = useState(false);

    /* ========================================================================== */
    /*                                  HANDLERS                                  */
    /* ========================================================================== */
    const handleSendColumns = () => {
      setIsSending(true);
      setTimeout(() => {
        setIsSending(false);
      }, 1000);
      toast.success(`${checkedColumns} Columns Refreshing`);
    };
    const handleDateRangeClick = () => {
      setShowDateRangePicker((prev) => !prev);
    };

    /* ========================================================================== */
    /*                                  FUNCTIONS                                 */
    /* ========================================================================== */
    const dateRangePickerRef = useClickOutside(() => {
      if (showDateRangePicker) {
        const isClickInsideToggle = toggleButtonRef.current?.contains(
          document.activeElement
        );

        if (!isClickInsideToggle) {
          setShowDateRangePicker(false);
        }
      }
    });

    const formatDate = (date: Date) => {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    };

    const getPrimaryDateRange = () => {
      if (!selectedRange?.from || !selectedRange?.to) {
        return "Select date range";
      }
      return `${formatDate(selectedRange.from)} - ${formatDate(
        selectedRange.to
      )}`;
    };

    const getComparisonDateRange = () => {
      if (!comparisonRange?.from || !comparisonRange?.to) {
        return "Select comparison dates";
      }
      return `${formatDate(comparisonRange.from)} - ${formatDate(
        comparisonRange.to
      )}`;
    };

    /* ========================================================================== */
    /*                                    HOOKS                                   */
    /* ========================================================================== */
    const { show } = useCompetitorPopupStore();
    // Get selected date range from store
    const { selectedRange, comparisonRange, shouldShowComparison } =
      useDateRangeStore();
    /* ========================================================================== */
    /*                                   RENDER                                   */
    /* ========================================================================== */
    return (
      <Card
        className={cn("flex justify-between", {
          "items-end": variant === "selectRange",
          "items-start": variant !== "selectRange",
        })}
      >
        <DateRangePicker
          ShowDateRangePicker={showDateRangePicker}
          ref={dateRangePickerRef}
        />
        <AnimatePresence>
          {isSharePopupOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.1 }}
              className="fixed top-0 left-0 w-screen h-screen bg-black/10 backdrop-blur-[1px] z-50 flex justify-center items-center"
            >
              <SharePopup
                onClose={() => {
                  setIsSharePopupOpen(false);
                }}
              />
            </motion.div>
          )}
        </AnimatePresence>

        <div className="space-y-4">
          <div>
            <Title>{title}</Title>
          </div>
          {variant === "selectRange" && (
            <div className="flex gap-4 mt-3" ref={toggleButtonRef}>
              {/* Primary Date Range Container */}
              <motion.div
                className="cursor-pointer"
                onClick={handleDateRangeClick}
                whileTap={{ scale: 0.98 }}
                whileHover={{ opacity: 0.8 }}
              >
                <div className="text-sm font-medium text-secondary mb-1">
                  Date Range
                </div>
                <div className="bg-gray-100 px-3 py-2 rounded-md min-w-[160px] flex items-center gap-2 relative">
                  <div className="w-1 h-6 bg-primary rounded-sm flex-shrink-0"></div>
                  <FiCalendar className="text-gray-500" />
                  <span className="text-sm text-gray-800 font-medium">
                    {getPrimaryDateRange()}
                  </span>
                </div>
              </motion.div>
              {/* Comparison Date Range Container - Only show when user has selected comparison dates */}
              {shouldShowComparison && (
                <motion.div
                  className="cursor-pointer"
                  onClick={handleDateRangeClick}
                  whileTap={{ scale: 0.98 }}
                  whileHover={{ opacity: 0.8 }}
                >
                  <div className="text-sm font-medium text-secondary mb-1">
                    Compare To:
                  </div>
                  <div className="bg-gray-100 px-3 py-2 rounded-md min-w-[160px] flex items-center gap-2 relative">
                    <div className="w-1 h-6 bg-primary-yellow rounded-sm flex-shrink-0"></div>
                    <FiCalendar className="text-gray-500" />
                    <span className="text-sm text-gray-800 font-medium">
                      {getComparisonDateRange()}
                    </span>
                  </div>
                </motion.div>
              )}
            </div>
          )}
        </div>
        <div className="flex flex-col items-end gap-4">
          <ShareAndSettings
            onShareClick={() => setIsSharePopupOpen(!isSharePopupOpen)}
          />
          <div className="flex items-center gap-2">
            {variant === "default" && (
              <AddColumns
                classNames={{
                  buttonContainer: "pt-5 pb-3",
                  options: "text-xs",
                  optionsContainer: "w-[198px]",
                  optionsTitle: "text-sm font-semibold text-secondary/80",
                }}
                title="Refresh Data"
                className="hover:bg-primary hover:text-white text-primary border-primary"
                icon={<LuRefreshCw className="text-lg" />}
                isDisabled={isSending}
                sendColumns={handleSendColumns}
                columns={columns}
                checkedColumns={checkedColumns}
                setCheckedColumns={setCheckedColumns}
              />
            )}

            <Button
              onClick={show}
              variant={"default"}
              className="text-white py-2 px-4"
            >
              Add Your Competitors
            </Button>
          </div>
        </div>
      </Card>
    );
  }
);

CompetitorAnalysisHeader.displayName = "CompetitorAnalysisHeader";

export default CompetitorAnalysisHeader;
