import { BubbleChartDataResponse } from "../_components/BubbleChartComponent.types";
import { SEOCompetitorPieChartResponse } from "../_components/SEOCompetitorPieChart.type";

const colors = [
  "#FF00C3",
  "#00BBEC",
  "#F57D37",
  "#31D37A",
  "#3C0866",
  "#FFA5DA",
];

/* ================= COMPETITOR DISTRIBUTION CONVERTOR TYPES ================ */
type Results = {
  domain: string;
  keywords_ranked: number;
  average_position: number;
  top_100_count: number;
};

export type CompetitorDistributionApiResponse = {
  results: Array<Results>;
};

/* ==================== COMPETITOR DISTRIBUTION CONVERTOR =================== */
export function competitorDistributionConvertor(
  input: CompetitorDistributionApiResponse
): BubbleChartDataResponse[] {
  const { results } = input;
  const data: BubbleChartDataResponse[] = results.map((item, index) => ({
    color: colors[index % colors.length],
    growth: "",
    title: item.domain,
    keywordRanked: item.keywords_ranked,
    ABGPosition: item.average_position,
    keywordCountInTop100: item.top_100_count,
    x: item.keywords_ranked,
    y: item.average_position,
    z: item.top_100_count,
  }));

  return data;
}

/* ==================== CONTENT COVERAGE CONVERTOR TYPES ==================== */

type Competitors = {
  domain: string;
  share_of_serp_keywords: number;
  topical_authority_score: number;
  content_gap_count: number;
  content_ratio: number;
  traffic_share: number;
  ranking_score: number;
};

export type ContentCoverageBubbleApiResponse = {
  competitors: Array<Competitors>;
};

/* ======================= CONTENT COVERAGE CONVERTOR ======================= */

export function contentCoverageConvertor(
  input: ContentCoverageBubbleApiResponse
): BubbleChartDataResponse[] {
  const data = input.competitors.map((item, index) => ({
    x: item.share_of_serp_keywords,
    y: item.topical_authority_score,
    z: item.content_gap_count,
    color: colors[index % colors.length],
    title: item.domain,
    keywordRanked: item.share_of_serp_keywords,
    ABGPosition: item.topical_authority_score,
    keywordCountInTop100: item.content_gap_count,
    growth: "",
  }));
  return data;
}

type ShareOfVoiceResults = {
  domain: string;
  share_of_voice_pct: number;
  share_of_voice_value: number;
  keyword_count: number;
};
export type ShareOfVoiceApiResponse = {
  total_keywords: number;
  total_TF_of_all_keywords: number;
  results: Array<ShareOfVoiceResults>;
};

export function seoCompetitorPieConvertor(
  input: ShareOfVoiceApiResponse
): SEOCompetitorPieChartResponse {
  const { total_TF_of_all_keywords, results } = input;

  const data: SEOCompetitorPieChartResponse = {
    total: total_TF_of_all_keywords,
    growth: "",
    data: results.map((item) => ({
      name: item.domain,
      value: item.share_of_voice_value,
      color: colors[results.indexOf(item) % colors.length],
      shareOfVoice: item.share_of_voice_pct,
      keywordCount: item.keyword_count,
      shareOfVoiceGrowth: "",
      keywordCountGrowth: "",
    })),
  };

  return data;
}
