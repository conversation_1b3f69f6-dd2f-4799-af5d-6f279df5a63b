import type {
  CardsData,
  ColorConfig,
} from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";

type BarChartComponentPropsLoading = {
  isLoading: true;
};

type BarChartComponentPropsLoaded = {
  cardsData: CardsData;
  colors: ColorConfig[];
  barChartData: BarChartData[];
  isLoading?: false;
};

export type BarChartComponentProps =
  | BarChartComponentPropsLoading
  | BarChartComponentPropsLoaded;

export type BarChartData = {
  name: string;
  bar: number;
};

export type BarChartDataResponse = {
  cardsData: CardsData;
  colors: ColorConfig[];
  barChartData: BarChartData[];
};

export type BarChartDataRequest = {
  groupBy: string;
  period: string;
  activeTab: string;
};
