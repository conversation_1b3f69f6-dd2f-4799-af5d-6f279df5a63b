import type {
  TableDataRequest,
  TableBody,
} from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";

export const competitorsTableDataConverter = (
  result: any,
  tableHeading: string[]
): TableDataRequest => {
  const tableBody: TableBody[][] = result.map((item) => {
    const growth = (current: number, previous: number) => {
      const diff = Math.round(current - previous);
      return diff === 0 || null ? "0" : `${diff > 0 ? "+" : ""}${diff}`;
    };

    /* ========================= START ROW WITH THE URL ========================= */
    const row: TableBody[] = [{ value: item.url }];

    /* ============== LOOP OVER ALL METRICS EXCEPT *_PREVIOUS KEYS ============== */
    Object.keys(item.metrics)
      .filter((key) => !key.endsWith("_previous"))
      .forEach((key) => {
        const prevKey = `${key}_previous`;
        row.push({
          value: String(
            item.metrics[key] === null ? "0" : Math.round(item.metrics[key])
          ),
          growth:
            prevKey in item.metrics
              ? growth(item.metrics[key], item.metrics[prevKey])
              : undefined,
        });
      });

    return row;
  });

  return {
    tableData: {
      tableHeadings: tableHeading,
      tableBody,
    },
    pagination: {
      totalPages: 1,
    },
  };
};
