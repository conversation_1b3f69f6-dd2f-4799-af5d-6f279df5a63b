import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Car<PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from "recharts";
import Card from "@/components/ui/card";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { AnimatePresence, motion } from "framer-motion";
import { BubbleChartDataResponse } from "./BubbleChartComponent.types";
/* ================================== TYPES ================================= */

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const TooltipContent = ({ payload }: { payload?: any[] }) => {
  if (!payload || !payload.length || !payload[0].payload) return null;
  const { title, color, keywordRanked, ABGPosition, keywordCountIn<PERSON>op<PERSON> } =
    payload[0].payload;
  return (
    <Card className="shadow-xl p-4 text-xs grid gap-3 text-secondary font-bold border">
      <div className="flex items-center gap-3.5">
        <div
          className="w-2 h-2 rounded-full"
          style={{ backgroundColor: color }}
        />
        <span className="-translate-y-[0.8px]">{title}</span>
      </div>
      <div className="flex flex-col gap-3.5">
        <div className="flex items-center gap-3.5 justify-between font-normal">
          <span className="font-semibold">keyword ranked:</span>
          <span>{keywordRanked}</span>
        </div>
        <div className="flex items-center gap-3.5 justify-between font-normal">
          <span className="font-semibold">ABG Position:</span>
          <span>{ABGPosition}</span>
        </div>
        <div className="flex items-center gap-3.5 justify-between font-normal">
          <span className="font-semibold">keyword count in top 100:</span>
          <span>{keywordCountInTop100}</span>
        </div>
      </div>
    </Card>
  );
};

const BubbleChartSkeleton = () => {
  return (
    <div className="w-full h-full flex items-center justify-center px-4 overflow-hidden">
      <div className="w-full h-full relative">
        {/* Grid lines skeleton */}
        <div className="absolute inset-0 flex flex-col">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="flex-1 border-b border-gray-200" />
          ))}
        </div>
        <div className="absolute inset-0 flex border">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="flex-1 border-l border-gray-200" />
          ))}
        </div>

        {/* Bubble skeletons */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4">
            <Skeleton circle width={60} height={60} />
          </div>
          <div className="absolute top-1/3 right-1/3">
            <Skeleton circle width={40} height={40} />
          </div>
          <div className="absolute bottom-1/3 left-1/2">
            <Skeleton circle width={50} height={50} />
          </div>
          <div className="absolute bottom-1/4 right-1/4">
            <Skeleton circle width={70} height={70} />
          </div>
        </div>
      </div>
    </div>
  );
};

const BubbleChartComponent = ({
  isLoading = false,
  data,
}: {
  isLoading?: boolean;
  data?: BubbleChartDataResponse[];
}) => {
  const maxX = data ? Math.max(...data.map((d) => d.x)) : 0;
  const stepX = Math.ceil(maxX / 10);
  const lastTick = Math.ceil(maxX / stepX) * stepX;
  const ticksX = Array.from(
    { length: lastTick / stepX + 1 },
    (_, i) => i * stepX
  );

  const domainMax = lastTick + (stepX + 1) / 2;

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return (
    <div className="w-full h-[300px]">
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="skeleton"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
            className="w-full h-full"
          >
            <BubbleChartSkeleton />
          </motion.div>
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
            className="w-full h-full"
          >
            <ResponsiveContainer width="100%" height="100%">
              <ScatterChart
                margin={{ top: 20, right: 30, bottom: 20, left: 0 }}
              >
                {/* <CartesianGrid /> */}
                <XAxis
                  type="number"
                  dataKey="x"
                  domain={[0, domainMax]}
                  ticks={[...ticksX, Math.ceil(domainMax)]}
                  interval={0}
                  tickLine={false}
                  axisLine={false}
                  tick={{
                    fill: "#6C757D",
                    fontSize: 12,
                    fontWeight: "bold",
                  }}
                  tickMargin={16}
                />

                <YAxis
                  interval={0}
                  type="number"
                  tickLine={false}
                  axisLine={false}
                  dataKey="y"
                  domain={[0, 100]}
                  tick={{
                    fill: "#6C757D",
                    fontSize: 12,
                    fontWeight: "bold",
                  }}
                  tickMargin={16}
                />
                <ZAxis dataKey="z" range={[50, 100]} />
                <Tooltip content={TooltipContent} cursor={false} />
                <CartesianGrid stroke="#EAEAEA" />
                <Scatter
                  data={data}
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  shape={(props: any) => {
                    const { cx, cy, size, payload } = props;
                    const r = size / 2;
                    return (
                      <g>
                        <circle
                          cx={cx}
                          cy={cy}
                          r={r}
                          fill={payload.color}
                          stroke="#fff"
                          strokeWidth={1}
                        />
                        <text
                          x={cx}
                          y={!!payload.growth ? cy - 4 : cy + 4}
                          textAnchor="middle"
                          fontSize={18}
                          fontWeight="bold"
                          fill="#fff"
                          pointerEvents="none"
                        >
                          {payload.keywordCountInTop100}
                        </text>
                        <text
                          x={cx}
                          y={cy + 18}
                          textAnchor="middle"
                          fontSize={11}
                          fontWeight="normal"
                          fill="#fff"
                          pointerEvents="none"
                        >
                          {payload.growth}
                        </text>
                      </g>
                    );
                  }}
                />
              </ScatterChart>
            </ResponsiveContainer>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BubbleChartComponent;
