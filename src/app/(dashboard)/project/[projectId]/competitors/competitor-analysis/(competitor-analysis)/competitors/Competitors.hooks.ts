import { useQuery } from "@tanstack/react-query";
import http from "@/services/httpService";
import type { BarChartDataRequest } from "./_components/BarChartComponent.types";
import { useProjectId } from "@/hooks/useProjectId";
import { CompetitorApiResponse } from "@/utils/api-response-convertors/lineChartDataConvertor";
import { CompetitorLineChartApiResponse } from "./utils/lineChartDataConvertor";

/* =========================== GET LINE CHART DATA ========================== */

export const useCompetitorsSection = ({
  period,
  groupBy,
  activeTab,
}: BarChartDataRequest) => {
  const { projectId } = useProjectId();

  return useQuery({
    queryKey: ["useLineChartData", period, projectId],
    queryFn: async (): Promise<CompetitorLineChartApiResponse> => {
      const { data } = await http.get(
        `/api/projects/${projectId}/competitors/overview/`,
        {
          params: {
            period,
            force_refresh: false,
            // group_by: groupBy,
            // active_tab: activeTab,
          },
        }
      );
      return data;
    },
    enabled: projectId !== null,
  });
};
