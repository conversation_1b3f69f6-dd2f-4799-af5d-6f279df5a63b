type SEOCompetitorPieChartData = {
  name: string;
  value: number;
  color: string;
  shareOfVoice: number;
  keywordCount: number;
  shareOfVoiceGrowth: string;
  keywordCountGrowth: string;
};

export type SEOCompetitorPieChartResponse = {
  total: number;
  growth: string;
  data: SEOCompetitorPieChartData[];
};

export type SEOCompetitorPieChartProps = {
  domains?: SEOCompetitorPieChartResponse;
  isLoading?: boolean;
};
