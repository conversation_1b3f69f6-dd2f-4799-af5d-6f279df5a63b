import { CompetitorApiResponse } from "@/utils/api-response-convertors/lineChartDataConvertor";

export type TabsType = {
  title: string;
  value: string;
  changeValue: string;
};

function titlePreparation(title: string): string {
  return title
    .replace(/_/g, " ")
    .replace("pct", "%")
    .replace(
      /\w\S*/g,
      (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    );
}

export function cardTabsConvertor(input: CompetitorApiResponse): TabsType[] {
  const tabsData = input.competitors[0].totals;
  return Object.entries(tabsData).map(([key, value]) => ({
    title: titlePreparation(key),
    value: value === null ? "0" : Math.round(value).toString(),
    changeValue: "",
  }));
}
