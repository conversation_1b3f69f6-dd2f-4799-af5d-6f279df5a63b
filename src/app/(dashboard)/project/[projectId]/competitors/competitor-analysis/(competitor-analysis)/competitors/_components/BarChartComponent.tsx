/* =============================== REACT NEXT =============================== */
import React from "react";

/* ================================== TYPES ================================= */
import type { BarChartComponentProps } from "./BarChartComponent.types";

/* ================================ RECHARTS ================================ */
import {
  BarChart,
  Bar,
  ResponsiveContainer,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  type TooltipProps,
} from "recharts";

/* ================================= ZUSTAND ================================ */
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

/* ============================== LOADING SKELETON ============================= */
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

/* ================================== UTILS ================================= */
import abbreviateNumber from "@/utils/abbreviateNumber";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import { motion, AnimatePresence } from "framer-motion";

/* ========================================================================== */
/*                             COMPONENT: tooltip                             */
/* ========================================================================== */
const TooltipContent = ({
  payload,
  colors,
}: {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  payload?: TooltipProps<any, any>["payload"];
  colors: { name: string; color: string }[];
  cardsData: { [key: string]: { amount: number; growth: string } };
}) => {
  if (!payload || !payload.length) return null;

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Card className="shadow-xl p-4 text-xs grid gap-3 text-secondary font-bold border">
      <div className="flex items-center gap-2">
        <div
          className="w-2 h-2 rounded-full"
          style={{
            backgroundColor: colors.find(
              (color) =>
                color.name.replace("_", ".") === payload[0]?.payload?.name
            )?.color,
          }}
        />
        <span className="-translate-y-[0.8px]">{payload[0]?.payload.name}</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-2">
          <span className="text-base font-bold">
            {abbreviateNumber(payload[0].value)}
          </span>
          {/* <span
            style={
              cardsData[
                payload[0]?.payload.name.replace(".", "_")
              ]?.growth?.includes("+")
                ? { color: "#319F43" }
                : cardsData[
                    payload[0]?.payload.name.replace(".", "_")
                  ]?.growth?.includes("-")
                ? { color: "#FD0004" }
                : { color: "#344054" }
            }
          >
            {cardsData[payload[0]?.payload.name.replace(".", "_")]?.growth}
          </span> */}
        </div>
      </div>
    </Card>
  );
};

/* ================================== MAIN ================================== */

const BarChartComponent = (props: BarChartComponentProps) => {
  const { themeColor } = useProjectThemeColor();

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  if (props.isLoading) {
    return (
      <AnimatePresence mode="wait">
        <motion.div
          key="loading"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
          className="w-[95%] h-[90%] flex items-end justify-center px-6 pb-6 gap-32 relative"
        >
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex flex-col items-center z-10">
              <Skeleton
                height={100 + Math.random() * 60}
                width={60}
                borderRadius={8}
              />
            </div>
          ))}
          <div className="h-full w-full absolute flex flex-col justify-between animate-pulse">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="h-[1px] w-full bg-[#ccc]/40" />
            ))}
          </div>
          <div className="h-full w-full absolute flex justify-between animate-pulse">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="h-full w-[1px] bg-[#ccc]/50" />
            ))}
          </div>
        </motion.div>
      </AnimatePresence>
    );
  }

  // Loaded state
  if (
    "barChartData" in props &&
    "cardsData" in props &&
    "colors" in props &&
    props.barChartData &&
    props.cardsData &&
    props.colors
  ) {
    const { barChartData, cardsData, colors } = props;
    return (
      <AnimatePresence mode="wait">
        <motion.div
          key="chart"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
          className="w-full h-full"
        >
          <ResponsiveContainer width="100%" height="100%">
            <BarChart width={150} height={40} data={barChartData}>
              <Tooltip
                content={(props) => (
                  <TooltipContent
                    {...props}
                    colors={colors}
                    cardsData={cardsData}
                  />
                )}
                cursor={false}
              />
              <CartesianGrid stroke="#ccc" strokeDasharray="0" />
              <Bar
                dataKey="bar"
                fill={themeColor}
                radius={[8, 8, 8, 8]}
                barSize={60}
              />
              <Tooltip />
              <XAxis
                dataKey="name"
                tickLine={false}
                axisLine={false}
                tick={{ fontSize: 12, fill: "#6C757D", fontWeight: 700 }}
                tickMargin={16}
                tickFormatter={(value) =>
                  value.length > 15 ? value.slice(0, 15) + "…" : value
                }
              />
              <YAxis
                // domain={[0, (dataMax: number) => dataMax * 1.2]}
                tickLine={false}
                axisLine={false}
                tick={{ fontSize: 12, fill: "#6C757D", fontWeight: 700 }}
                tickMargin={16}
                tickCount={6}
                interval={0}
              />
            </BarChart>
          </ResponsiveContainer>
        </motion.div>
      </AnimatePresence>
    );
  }

  // Fallback
  return null;
};

export default BarChartComponent;
