/* =============================== REACT NEXT =============================== */
import React from "react";

/* ================================== UTILS ================================= */
import abbreviateNumber from "@/utils/abbreviateNumber";
import { cn } from "@/utils/cn";

/* =============================== COMPONENTS =============================== */
import CustomActiveShapePieChart from "./CustomActiveShapePieChart";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { AnimatePresence, motion } from "framer-motion";
import { SEOCompetitorPieChartProps } from "./SEOCompetitorPieChart.type";

/* ========================================================================== */
const SEOCompetitorPieChart = ({
  domains,
  isLoading = false,
}: SEOCompetitorPieChartProps) => {
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <AnimatePresence mode="wait">
      {isLoading ? (
        <motion.div
          key="skeleton"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
          className="flex gap-4 px-8 pb-4 h-[232px]"
        >
          <div className="flex flex-col justify-center gap-4 py-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center gap-2 text-[#5F666C]">
                <Skeleton circle width={16} height={16} />
                <Skeleton width={60} height={16} />
              </div>
            ))}
          </div>
          <div className="w-full relative flex items-center justify-center">
            <Skeleton circle width={180} height={180} />
            <div className="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] flex flex-col items-center gap-2">
              <div className="flex items-center gap-2">
                <Skeleton width={40} height={28} />
                <Skeleton width={32} height={20} />
              </div>
              <Skeleton width={110} height={16} />
            </div>
          </div>
          <div className="flex gap-4 text-secondary text-start justify-between w-[85%] items-center">
            <div className="flex flex-col items-center gap-2 ">
              <div className="mb-2 font-medium">
                <Skeleton width={80} height={16} />
              </div>
              {[...Array(4)].map((_, i) => (
                <div key={i} className="flex items-center gap-2">
                  <Skeleton circle width={8} height={8} />
                  <Skeleton width={32} height={16} />
                  <Skeleton width={28} height={16} />
                </div>
              ))}
            </div>
            <div className="flex flex-col items-center gap-2 ">
              <div className="mb-2 font-medium">
                <Skeleton width={80} height={16} />
              </div>
              {[...Array(4)].map((_, i) => (
                <div key={i} className="flex items-center gap-2 text-[#5F666C]">
                  <Skeleton circle width={8} height={8} />
                  <Skeleton width={32} height={16} />
                  <Skeleton width={48} height={16} />
                  <Skeleton width={28} height={16} />
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      ) : (
        <motion.div
          key="content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
          className="flex gap-4 px-8 pb-4"
        >
          <div className="flex flex-col justify-center gap-4 py-4 ">
            {domains?.data.map(({ name, color }) => (
              <div key={name} className="flex items-center gap-2">
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: color }}
                />
                <span className="text-[#5F666C]">{name.replace("_", ".")}</span>
              </div>
            ))}
          </div>
          <div className="w-full relative">
            {domains && <CustomActiveShapePieChart data={domains.data} />}
            <div className="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] flex flex-col items-center gap-2">
              <div className="flex items-center gap-2">
                <span className="text-secondary text-xl font-bold">
                  {domains && abbreviateNumber(domains.total)}
                </span>
                <span
                  className={cn(
                    "text-secondary text-sm font-semibold",
                    domains?.growth.includes("+")
                      ? "text-green-500"
                      : domains?.growth.includes("-")
                      ? "text-red-500"
                      : "text-secondary"
                  )}
                >
                  {domains?.growth}
                </span>
              </div>
              <span className="text-center text-sm text-[#5F666C] max-w-[110px]">
                Total TF Of All Keywords
              </span>
            </div>
          </div>
          <div className="flex gap-4 text-secondary text-start justify-between w-[85%] items-center">
            <div className="flex flex-col items-center gap-2 ">
              <div className="mb-2 font-medium">
                <span className="!text-secondary">share of voice</span>
              </div>
              {domains?.data.map(
                ({ shareOfVoice, shareOfVoiceGrowth, color }, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-[#5F666C]">
                      {abbreviateNumber(shareOfVoice)}
                    </span>
                    <span
                      className={cn(
                        "text-secondary text-xs font-bold",
                        shareOfVoiceGrowth.includes("-")
                          ? "text-primary-red"
                          : shareOfVoiceGrowth.includes("+")
                          ? "text-primary-green"
                          : "text-secondary"
                      )}
                    >
                      {shareOfVoiceGrowth}
                    </span>
                  </div>
                )
              )}
            </div>
            <div className="flex flex-col items-center gap-2">
              <div className="mb-2 font-medium">
                <span>keyword count</span>
              </div>
              {domains?.data.map(
                ({ keywordCount, color, keywordCountGrowth }, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-2 text-[#5F666C]"
                  >
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-secondary">{keywordCount}</span>
                    <span className="text-[#5F666C]">keywords</span>
                    <span
                      className={cn(
                        "text-secondary text-xs font-bold",
                        keywordCountGrowth.includes("-")
                          ? "text-primary-red"
                          : keywordCountGrowth.includes("+")
                          ? "text-primary-green"
                          : "text-secondary"
                      )}
                    >
                      {keywordCountGrowth}
                    </span>
                  </div>
                )
              )}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SEOCompetitorPieChart;
