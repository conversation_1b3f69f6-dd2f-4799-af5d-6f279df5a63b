import { useQuery } from "@tanstack/react-query";
import http from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";

/* ============================= GET TABLE DATA ============================= */
export const useCompetitorsTableData = () => {
  const { projectId } = useProjectId();

  return useQuery({
    queryKey: ["competitorsTableData", projectId],
    queryFn: async (): Promise<any> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await http.get(
        `/api/projects/${projectId}/competitors/reports`,
        {
          params: {
            force_refresh: false,
          },
        }
      );
      return data.frame_a.results;
    },
    enabled: projectId !== null,
  });
};
