import { useQuery } from "@tanstack/react-query";
import http from "@/services/httpService";
import { BubbleChartDataRequest } from "./_components/BubbleChartComponent.types";

import {
  CompetitorDistributionApiResponse,
  ContentCoverageBubbleApiResponse,
  ShareOfVoiceApiResponse,
} from "./utils/seoCompetitorsConvertors";

import { useProjectId } from "@/hooks/useProjectId";

/* ========================== GET BUBBLE CHART DATA ========================= */
export const useBubbleChartData = ({
  selectedBadge,
}: BubbleChartDataRequest) => {
  const { projectId } = useProjectId();
  // console.log("selectedBadge", selectedBadge);

  return useQuery<
    CompetitorDistributionApiResponse | ContentCoverageBubbleApiResponse
  >({
    queryKey: ["useBubbleChartData", selectedBadge, projectId],
    queryFn:
      selectedBadge === "Competitor Distribution"
        ? async (): Promise<CompetitorDistributionApiResponse> => {
            const { data } = await http.get(
              `/api/projects/${projectId}/competitors/distribution/`,
              {
                params: {
                  force_refresh: false,
                  keywords: "all",
                  search_engine: "google",
                },
              }
            );
            return data;
          }
        : async (): Promise<ContentCoverageBubbleApiResponse> => {
            const { data } = await http.get(
              `/api/projects/${projectId}/competitors/content-coverage/`,
              {
                params: {
                  force_refresh: false,
                  keywords: "all",
                  search_engine: "google",
                },
              }
            );
            return data;
          },
  });
};

/* =========================== GET DOUGHNUT CHART =========================== */
export const useDoughnutChartData = ({
  selectedBadge,
}: BubbleChartDataRequest) => {
  const { projectId } = useProjectId();
  return useQuery({
    queryKey: ["useDoughnutChartData", selectedBadge],
    queryFn: async (): Promise<ShareOfVoiceApiResponse> => {
      const { data } = await http.get(
        `/api/projects/${projectId}/competitors/share-of-voice/`,
        {
          params: {
            force_refresh: false,
          },
        }
      );
      return data;
    },
  });
};
