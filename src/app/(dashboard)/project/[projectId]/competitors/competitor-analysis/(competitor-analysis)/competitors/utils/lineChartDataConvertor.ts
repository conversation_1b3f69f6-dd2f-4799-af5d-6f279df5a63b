import { LineChartDataResponse } from "../../../../market-analysis/(market-analysis)/MarketAnalysis.types";

const colors = ["#ff00ff", "#31D37A", "#00BBEC", "#3C0866", "#F57D37"];

export type CompetitorLineChartApiResponse = {
  status: string;
  project_id: string;
  period: {
    days_count: number;
  };
  data: {
    competitors: {
      domain: string;
      totals: {
        avg_keyword_position: number | null;
        pct_in_top10: number;
        search_visibility: number;
        da_score: number;
        backlinks: number;
        referring_domains: number;
        traffic_forecast: number;
      };
      daily_metrics: Array<{
        date: string; // e.g., "20250825"
        avg_keyword_position: number | null;
        pct_in_top10: number;
        search_visibility: number;
        da_score: number;
        backlinks: number;
        referring_domains: number;
        traffic_forecast: number;
      }>;
    }[];
  };
};

export function CompetitorsLineChartDataConvertor(
  input: CompetitorLineChartApiResponse,
  title: string
): LineChartDataResponse {
  const colorsPalette = ["#ff00ff", "#31D37A", "#00BBEC", "#3C0866", "#F57D37"];

  const cardsData: Record<string, { amount: number; growth: string }> = {};
  const colors: { name: string; color: string }[] = [];
  const selectedLines: string[] = [];

  // Temporary map to build lineChartData keyed by date
  const lineChartDataMap: Record<string, any> = {};

  input.data.competitors.forEach((comp, idx) => {
    const domainKey = comp.domain.replace(/\./g, "_"); // e.g., hubspot.com -> hubspot_com

    // cardsData
    cardsData[domainKey] = {
      amount: comp.totals[title],
      growth: "", // growth is empty string
    };

    // colors
    colors.push({
      name: domainKey,
      color: colorsPalette[idx % colorsPalette.length],
    });

    // selectedLines
    selectedLines.push(domainKey);

    // lineChartData: iterate daily_metrics
    comp.daily_metrics.forEach((metric) => {
      // convert date string YYYYMMDD to readable format
      const year = Number(metric.date.slice(0, 4));
      const month = Number(metric.date.slice(4, 6)) - 1; // JS Date months are 0-indexed
      const day = Number(metric.date.slice(6, 8));
      const dateObj = new Date(year, month, day);

      const dateLabel = dateObj.toLocaleString("default", {
        month: "short",
        day: "numeric",
      }); // e.g., "Aug 25"

      if (!lineChartDataMap[metric.date]) {
        lineChartDataMap[metric.date] = { name: dateLabel };
      }

      lineChartDataMap[metric.date][domainKey] = metric[title]; // or another metric if needed
    });
  });

  // Convert map to sorted array
  const lineChartData = Object.keys(lineChartDataMap)
    .sort()
    .map((key) => lineChartDataMap[key]);

  return {
    cardsData,
    colors,
    selectedLines,
    lineChartData,
  };
}
