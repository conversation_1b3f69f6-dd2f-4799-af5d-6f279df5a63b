"use client";
/* =============================== REACT NEXT =============================== */
import React, { useCallback, useEffect, useRef, useState } from "react";

/* =============================== COMPONENTS =============================== */
import CompetitorAnalysisHeader from "../../competitor-analysis/(competitor-analysis)/_components/CompetitorAnalysisHeader";
import ShareOfVoiceOverview from "./share-of-voice-overview/ShareOfVoiceOverview";
import ShareOfVoiceTable from "./share-of-voice-table/ShareOfVoiceTable";
import DateRangePicker from "../../../analytics-traffics/_components/date-range-picker/DateRangePicker";
import { useClickOutside } from "@mantine/hooks";
import { useCompetitorsStore } from "@/store/competitors/useCompetitorsAdded";
import NoCompetitorsAdded from "../../_components/NoCompetitorsAdded";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ================================== MAIN ================================== */
const ShareOfVoice = () => {
  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */
  const handleDateRangeClick = useCallback(() => {
    setShowDateRangePicker((prev) => !prev);
  }, []);

  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const { projectName } = useProjectContext();

  /* ========================================================================== */
  /*                                  HOOKS                                     */
  /* ========================================================================== */
  const toggleButtonRef = useRef<HTMLDivElement>(null);
  const dateRangePickerRef = useClickOutside(() => {
    if (showDateRangePicker) {
      const isClickInsideToggle = toggleButtonRef.current?.contains(
        document.activeElement
      );

      if (!isClickInsideToggle) {
        setShowDateRangePicker(false);
      }
    }
  });

  /* ========================================================================== */
  /*                                  EFFECTS                                   */
  /* ========================================================================== */

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (showDateRangePicker) {
        if (
          toggleButtonRef.current?.contains(event.target as Node) ||
          dateRangePickerRef.current?.contains(event.target as Node)
        ) {
          return;
        }
        setShowDateRangePicker(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showDateRangePicker, dateRangePickerRef]);
  // TODO: Replace this with backend logic. if no competitors added noData = true else false
  const { addedCompetitors } = useCompetitorsStore();
  const noData = addedCompetitors.length <= 0;

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-full max-w-full overflow-hidden space-y-6">
      <DateRangePicker
        ShowDateRangePicker={showDateRangePicker}
        ref={dateRangePickerRef}
      />
      <CompetitorAnalysisHeader
        title={`${projectName} Share of Voice`}
        variant="selectRange"
        onDateRangeClick={handleDateRangeClick}
        ref={toggleButtonRef}
      />
      {noData ? (
        <NoCompetitorsAdded />
      ) : (
        <>
          <ShareOfVoiceOverview />
          <ShareOfVoiceTable />
        </>
      )}
    </div>
  );
};

export default ShareOfVoice;
