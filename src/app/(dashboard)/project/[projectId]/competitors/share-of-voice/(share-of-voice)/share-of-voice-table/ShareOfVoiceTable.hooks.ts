import { useQuery } from "@tanstack/react-query";
import { ShareOfVoiceTableApiResponse } from "./utils/shareOfVoiceTableDataConvertor";
import { useProjectId } from "@/hooks/useProjectId";
import http from "@/services/httpService";

export const useShareOfVoiceTable = () => {
  const { projectId } = useProjectId();
  return useQuery({
    queryKey: ["useShareOfVoiceTable"],
    queryFn: async (): Promise<ShareOfVoiceTableApiResponse> => {
      const { data } = await http.post(
        `/api/projects/${projectId}/competitors/sov/report`,
        {
          keyword_scope: "all",
        },
        {
          params: {
            start_date: "2025-08-15",
            end_date: "2025-08-20",
            force_refresh: true,
          },
        }
      );
      return data;
    },
    enabled: !!projectId,
  });
};
