import { useQuery } from "@tanstack/react-query";
import http from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { ShareOfVoiceOverviewApiResponse } from "./utils/sovLineChartConvertor";

export const useShareOfVoiceOverview = (
  selectedKeyword: string,
  selectedCountry: string
) => {
  const { projectId } = useProjectId();
  return useQuery({
    queryKey: ["useShareOfVoiceOverview", selectedKeyword, selectedCountry],
    queryFn: async ():Promise<ShareOfVoiceOverviewApiResponse> => {
      const { data } = await http.post(
        `/api/projects/${projectId}/competitors/sov/overview`,
        {
          keyword_scope: selectedKeyword,
        },
        {
          params: {
            start_date: "2025-08-15",
            end_date: "2025-08-20",
            force_refresh: false,
          },
        }
      );
      return data;
    },
  });
};
