import abbreviateNumber from "@/utils/abbreviateNumber";
import { format, parseISO } from "date-fns";
import type { ChartResponse } from "../../../../../analytics-traffics/overview/(overview)/audience-overview/AudienceOverview.types";
export type ShareOfVoiceOverviewApiResponse = {
  project_id: string;
  engine: string;
  location: string;
  rank_filter: string;
  range: {
    start_date: string; // ISO date string
    end_date: string; // ISO date string
  };
  keywords_total: number;
  metrics: {
    total_traffic_forecast: number;
    traffic_forecast: number;
    share_of_voice: number;
    total_search_volume: number;
  };
  trend: Array<{
    date: string; // ISO date string
    share_of_voice: number;
    traffic_forecast: number;
    total_traffic_forecast: number;
  }>;
};

// (alias) type ChartResponse = {
//     id: number;
//     title: string;
//     bigNumber: string;
//     smallNumber: string;
//     data: ChartDataPoint[];
//     hasComparison?: boolean;
// }
// import ChartResponse

// {
//     "project_id": "202c4198-c34f-4f52-906c-7d467cee1bdc",
//     "engine": "google",
//     "location": "default",
//     "rank_filter": "top20",
//     "range": {
//         "start_date": "2025-08-15",
//         "end_date": "2025-08-20"
//     },
//     "keywords_total": 1,
//     "metrics": {
//         "total_traffic_forecast": 120,
//         "traffic_forecast": 0,
//         "share_of_voice": 0.0,
//         "total_search_volume": 0
//     },
//     "trend": [
//         {
//             "date": "2025-08-15",
//             "share_of_voice": 0.0,
//             "traffic_forecast": 0,
//             "total_traffic_forecast": 20
//         },

export function sovLineChartConvertor(
  input: ShareOfVoiceOverviewApiResponse
): ChartResponse[] {
  const chartKeys: Array<keyof ShareOfVoiceOverviewApiResponse["metrics"]> = [
    "total_traffic_forecast",
    "traffic_forecast",
    "share_of_voice",
    "total_search_volume",
  ];

  return chartKeys.map((key, index) => ({
    id: index + 1, // simple incrementing
    title: formatKeyTitle(key),
    bigNumber: abbreviateNumber(input.metrics[key]),
    smallNumber: "", // leave empty for now
    data: input.trend.map((trendItem) => ({
      name: format(parseISO(trendItem.date), "MMM dd"), // e.g., "Aug 15"
      value: trendItem[key] ?? 0,
    })),
  }));
}

// helper to turn metrics key into nice title
function formatKeyTitle(key: string) {
  return key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase());
}
