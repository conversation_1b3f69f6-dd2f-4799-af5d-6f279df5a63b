import abbreviateNumber from "@/utils/abbreviateNumber";
import type { AccordionTableData } from "../../../../_components/AccordionTable";

export type ShareOfVoiceTableApiResponse = {
  project_id: string;
  engine: string;
  location: string;
  rank_filter: string;
  range: {
    start_date: string; // ISO date string
    end_date: string; // ISO date string
  };
  keywords_total: number;
  data: Array<{
    target: string;
    share_of_voice: number;
    keywords_in_top: number;
    urls_in_top: number;
    total_traffic_forecast: number;
    traffic_forecast: number;
  }>;
};

export function ShareOfVoiceTableDataConvertor(
  input: ShareOfVoiceTableApiResponse
): AccordionTableData {
  const tableHeadings = [
    "DOMAIN",
    "SHARE OF VOICE",
    "KEYWORDS IN THE TOP 20",
    "URL IN THE TOP 20",
    "TOTAL TRAFFIC FORECAST",
    "TRAFFIC FORECAST",
  ];

  const tableBody = input.data.map((item) => ({
    data: [
      { value: item.target }, // DOMAIN
      { value: abbreviateNumber(item.share_of_voice), growth: "" },
      { value: abbreviateNumber(item.keywords_in_top), growth: "" },
      { value: abbreviateNumber(item.urls_in_top), growth: "" },
      { value: abbreviateNumber(item.total_traffic_forecast), growth: "" },
      { value: abbreviateNumber(item.traffic_forecast), growth: "" },
    ],
    accordionData: [], // always empty for now
  }));

  return {
    tableHeadings,
    tableBody,
  };
}
