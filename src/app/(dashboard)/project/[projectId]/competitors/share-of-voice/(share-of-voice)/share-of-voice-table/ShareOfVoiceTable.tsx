/* =============================== REACT NEXT =============================== */
import React, { useEffect, useState } from "react";

/* =============================== COMPONENTS =============================== */
import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import AccordionTable, {
  AccordionTableData,
} from "../../../_components/AccordionTable";
import Pagination from "../../../../analytics-traffics/_components/Pagination";

/* ================================ SKELETON ================================ */
import TableSkeleton from "../../../../analytics-traffics/_components/data-table/TableSkeleton";

/* =============================== REACT QUERY ============================== */
import { useShareOfVoiceTable } from "./ShareOfVoiceTable.hooks";
import { AnimatePresence, motion } from "framer-motion";
import { useProjectContext } from "@/contexts/ProjectContext";
import { ShareOfVoiceTableDataConvertor } from "./utils/shareOfVoiceTableDataConvertor";
import NoData from "../../../../analytics-traffics/analytic-insight/_components/NoData";
/* ================================== MAIN ================================== */
const ShareOfVoiceTable = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const { data, isLoading, isPending, isError, error } = useShareOfVoiceTable();
  const { projectName } = useProjectContext();
  const [convertedData, setConvertedData] = useState<
    AccordionTableData | undefined
  >(undefined);

  useEffect(() => {
    if (!data) return;
    setConvertedData(ShareOfVoiceTableDataConvertor(data));
  }, [data]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  if (isError) console.error(error);

  return (
    <Card className="space-y-6 min-h-fit transition-all duration-300">
      <div className="space-y-4">
        <Title>{projectName} All Reports-Share of Voice</Title>
        <DateRange variation="range" />
      </div>
      <AnimatePresence mode="wait">
        {isLoading || isPending ? (
          <motion.div
            key={"loading"}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
          >
            <TableSkeleton />
          </motion.div>
        ) : convertedData && convertedData?.tableBody.length > 0 ? (
          <motion.div
            key={"data"}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
          >
            <AccordionTable tableData={convertedData} />
          </motion.div>
        ) : <NoData title="No Data Available for All Reports-Share of Voice" />}
      </AnimatePresence>
      {/* <Pagination totalPages={10} page={1} className="py-1" /> */}
    </Card>
  );
};

export default ShareOfVoiceTable;
