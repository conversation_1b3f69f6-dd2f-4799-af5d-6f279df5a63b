import { useQuery } from "@tanstack/react-query";
import http from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import {
  serpCompetitorConvertor,
  serpCompetitorConvertorSingleDate,
} from "./utils/SerpCompetitorsConvertor";
import { TableData } from "../../../../analytics-traffics/_components/data-table/DataTable.types";

export const useSerpCompetitorsAllReports = (
  selectedDays: Date[],
  selectedBadge: string
) => {
  const { projectId } = useProjectId();
  const formatted = new Date(selectedDays[0]).toLocaleDateString("en-CA");

  return useQuery({
    queryKey: ["competitorsTableData", projectId, selectedDays, selectedBadge],
    queryFn: async (): Promise<TableData> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      if (selectedDays.length === 0) {
        const { data } = await http.get(
          `/api/projects/${projectId}/competitors/visibility-rating`,
          {
            params: {
              force_refresh: false,
              keywords: selectedBadge,
            },
          }
        );
        return serpCompetitorConvertor(data);
      } else {
        const { data } = await http.post(
          `/api/projects/${projectId}/competitors/serp/report/`,
          {
            rank_filter: selectedBadge,
            date: formatted,
          },
          {
            params: {
              force_refresh: false,
            },
          }
        );
        return serpCompetitorConvertorSingleDate(data);
      }
    },
    refetchOnWindowFocus: false,
    enabled: projectId !== null,
  });
};
