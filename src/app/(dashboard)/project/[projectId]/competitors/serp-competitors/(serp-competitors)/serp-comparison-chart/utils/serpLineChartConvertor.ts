import { ChartData, SerpChartResponse } from "../SerpComparisonChart.types";

export interface SerpDomainData {
  domain: string;
  domain_authority: number;
  ranked_keywords: number;
  referral_domains: number;
}

export interface SerpCompetitorReport {
  project_id: string;
  engine: string; // e.g. "google"
  locale: string; // e.g. "en-AU"
  keywords_total: number;
  keywords_filter: string; // e.g. "all"
  rank_filter: string; // e.g. "all"
  dates: string[]; // ["2025-09-02", "2025-09-03"]
  data: Record<string, SerpDomainData[]>;
  // keys like "20250902", "20250903"
}

// {
//     "project_id": "202c4198-c34f-4f52-906c-7d467cee1bdc",
//     "engine": "google",
//     "locale": "en-AU",
//     "keywords_total": 4,
//     "keywords_filter": "all",
//     "rank_filter": "all",
//     "dates": [
//         "2025-09-02",
//         "2025-09-03"
//     ],
//     "data": {
//         "20250902": [
//             {
//                 "domain": "moz.com",
//                 "domain_authority": 88,
//                 "ranked_keywords": 0,
//                 "referral_domains": 83287
//             },
/* ========================================================================== */
// {
//   colors: {
//     digikala_com: "#E0E0E0",
//     digikala_com2: "#E0E0E0",
//     digikala_com3: "#E0E0E0",
//   },
//   chartData: [
//     {
//       name: "11 Jan",
//       digikala_com: 30,
//       digikala_com2: 20,
//       digikala_com3: 50,
//     },

const colorsPalette = ["#ff00ff", "#31D37A", "#00BBEC", "#3C0866", "#F57D37"];
function formatDate(dateKey: string): string {
  // dateKey = "YYYYMMDD"
  const year = parseInt(dateKey.slice(0, 4), 10);
  const month = parseInt(dateKey.slice(4, 6), 10) - 1;
  const day = parseInt(dateKey.slice(6, 8), 10);

  const date = new Date(year, month, day);
  return date.toLocaleDateString("en-US", {
    day: "2-digit",
    month: "short",
  }); // "02 Sep"
}

function normalizeDomain(domain: string): string {
  return domain.replace(/\./g, "_");
}

export function serpLineChartConvertor(
  report: SerpCompetitorReport
): SerpChartResponse {
  const allDomains = Array.from(
    new Set(
      Object.values(report.data)
        .flat()
        .map((d) => d.domain)
    )
  );

  // assign colors to domains
  const colors: Record<string, string> = {};
  allDomains.forEach((domain, idx) => {
    colors[normalizeDomain(domain)] = colorsPalette[idx % colorsPalette.length];
  });

  // sort date keys so chartData is chronological
  const sortedDates = Object.keys(report.data).sort();

  const chartData: ChartData[] = sortedDates.map((dateKey) => {
    const row: ChartData = {
      name: formatDate(dateKey),
    };

    // fill values from report
    report.data[dateKey].forEach((d) => {
      row[normalizeDomain(d.domain)] = d.ranked_keywords;
    });

    // ensure all domains exist in every row (fill missing with 0)
    allDomains.forEach((domain) => {
      const key = normalizeDomain(domain);
      if (!(key in row)) {
        row[key] = 0;
      }
    });

    return row;
  });

  return { colors, chartData };
}
