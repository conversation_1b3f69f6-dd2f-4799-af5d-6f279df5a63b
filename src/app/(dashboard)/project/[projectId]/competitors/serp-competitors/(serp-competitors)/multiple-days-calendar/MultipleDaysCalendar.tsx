/* ============================= REACT AND NEXT ============================= */
import React, { forwardRef, useMemo, useState } from "react";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";

/* =============================== COMPONENTS =============================== */
import { getPastTenYears } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range-picker/Constants";
import { months } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range-picker/Constants";
import Card from "@/components/ui/card";
import { MultiDayPickerCalendar } from "@/components/ui/range-picker-calendar/RangePickerCalendar";
import { AnimatePresence } from "framer-motion";

/* ================================== MAIN ================================== */
const MultipleDaysCalendar = forwardRef<
  HTMLDivElement,
  {
    selected: Date[];
    setSelected: (dates: Date[]) => void;
    isOpen: boolean;
  }
>(
  (
    {
      selected,
      setSelected,
      isOpen,
    }: {
      selected: Date[];
      setSelected: (dates: Date[]) => void;
      isOpen: boolean;
    },
    ref,
  ) => {
    /* ========================================================================== */
    /*                                    HOOKS                                   */
    /* ========================================================================== */
    const monthsDropdownData = useMemo(
      () => ({
        button: months[0],
        options: months,
      }),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [months],
    );

    const yearsDropdownData = useMemo(
      () => ({
        button: getPastTenYears()[0],
        options: getPastTenYears(),
      }),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [getPastTenYears],
    );

    const [month, setMonth] = useState(new Date());

    /* ========================================================================== */
    /*                                   RENDER                                   */
    /* ========================================================================== */
    return (
      <AnimatePresence>
        {isOpen && (
          <Card
            ref={ref}
            key={isOpen ? "open" : "close"}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
            className={cn(
              "w-fit absolute z-40 border shadow-lg right-4 top-[25%] scale-90",
            )}
          >
            <MultiDayPickerCalendar
              selectedColor="bg-primary"
              yearDropdown={yearsDropdownData}
              monthDropdown={monthsDropdownData}
              month={month}
              setMonth={setMonth}
              selected={selected}
              setSelected={setSelected}
            />
          </Card>
        )}
      </AnimatePresence>
    );
  },
);
MultipleDaysCalendar.displayName = "MultipleDaysCalendar";

export default MultipleDaysCalendar;
