import React from "react";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import Ser<PERSON><PERSON><PERSON><PERSON><PERSON> from "./SerpLineC<PERSON>";
import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import { FiBarChart2 } from "react-icons/fi";
import { Button } from "@/components/ui/button";
import { Payload } from "../../../types/serpTable.types";
import { useSerpComparisonChart } from "./SerpComparisonChart.hooks";
import { useProjectContext } from "@/contexts/ProjectContext";
import { CHART_PLACEHOLDER_DATA } from "./constants";

const SerpComparisonChart = ({
  selectedDate,
  serpTableData,
}: {
  selectedDate: Date[];
  serpTableData?: Payload;
}) => {
  const { projectName } = useProjectContext();

  const { mutate, isPending, data, error } = useSerpComparisonChart();

  return (
    <Card className="space-y-4">
      <div className="flex justify-between items-center">
        <Title>{projectName} Comparison Chart</Title>
        <Button
          onClick={() => mutate(selectedDate)}
          disabled={selectedDate.length < 2}
          variant="outline"
          className="gap-4 text-[#344054]/70 border-[#344054]/70 px-2 py-2.5 h-auto"
        >
          <div
            className="h-5 w-5 border-2 border-[#344054]/70"
            style={{ borderRadius: "7px" }}
          >
            <FiBarChart2 className="scale-x-[-1]" />
          </div>
          {isPending && !error ? <span>loading...</span> : <span>compare</span>}
        </Button>
      </div>
      <DateRange variation="range" />
      <div className="flex items-center justify-around px-4">
        <span
          className="text-[#6C757D] text-sm font-semibold"
          style={{ writingMode: "vertical-rl", rotate: "180deg" }}
        >
          ranking
        </span>
        {isPending || !data ? (
          <SerpLineChart
            colors={CHART_PLACEHOLDER_DATA.colors}
            chartData={CHART_PLACEHOLDER_DATA.chartData}
          />
        ) : (
          data && (
            <SerpLineChart colors={data.colors} chartData={data.chartData} />
          )
        )}
      </div>
    </Card>
  );
};

export default SerpComparisonChart;
