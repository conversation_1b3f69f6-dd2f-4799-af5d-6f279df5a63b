"use client";
/* =============================== REACT NEXT =============================== */
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";

/* =============================== HEADLESS UI ============================== */
import { Switch } from "@headlessui/react";

/* ============================ REACT WORLD FLAGS =========================== */
import Flag from "react-world-flags";

/* =============================== UTILS =============================== */
import { cn } from "@/utils/cn";
import { FiCalendar } from "react-icons/fi";

/* =============================== MANTINE HOOKS ============================ */
import { useClickOutside } from "@mantine/hooks";

/* ================================ CONSTANTS =============================== */
import {
  countriesDropdown,
  keywordsDropdown,
  topFilters,
} from "../../../_constants/CountriesDownConstants";

/* =============================== COMPONENTS =============================== */
import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import Card from "@/components/ui/card";
import Dropdown from "@/components/ui/Dropdown";
import Title from "@/components/ui/Title";
import Badge, {
  BadgeWithCheckbox,
} from "@/app/(dashboard)/project/[projectId]/analytics-traffics/analytic-insight/_components/Badge";
import { Button } from "@/components/ui/button";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import MultipleDaysCalendar from "../multiple-days-calendar/MultipleDaysCalendar";
import type { Payload } from "../../../types/serpTable.types";
import { useSerpCompetitorsAllReports } from "./SerpCompetitorsAllReports.hooks";
import TableSkeleton from "../../../../analytics-traffics/_components/data-table/TableSkeleton";
import { useProjectContext } from "@/contexts/ProjectContext";
import ErrorSection from "@/ui/ErrorSection";
/* ================================== MAIN ================================== */
const SerpCompetitorsTable = ({
  selectedDate,
  setSelectedDate,
  setSerpTableData,
}: {
  selectedDate: Date[];
  setSelectedDate: (date: Date[]) => void;
  setSerpTableData: (data: Payload) => void;
}) => {
  /* ========================================================================== */
  /*                                   states                                   */
  /* ========================================================================== */
  const [highlightCompetitors, setHighlightCompetitors] = useState(false);
  const [showUrlEnabled, setShowUrlEnabled] = useState(false);
  const [selectedBadge, setSelectedBadge] = useState("all"); // this is sort by top 10, top50 and ...
  const [selectedDays, setSelectedDays] = useState<Date[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTableItems, setSelectedTableItems] = useState<string[]>([]);
  const [selectedCountry, setSelectedCountry] = useState(countriesDropdown[0]);
  const [selectedKeyword, setSelectedKeyword] = useState(keywordsDropdown[0]);

  const toggleButtonRef = useRef<HTMLDivElement>(null);
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const { data, isLoading, error, isFetching, refetch } =
    useSerpCompetitorsAllReports(selectedDate, selectedBadge);
  const { projectName } = useProjectContext();

  useEffect(() => {
    console.log("selectedDays", selectedDays);
  }, [selectedDays]);

  /* ========================================================================== */
  /*                                 USE EFFECTS                                */
  /* ========================================================================== */
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (isOpen) {
        if (
          toggleButtonRef.current?.contains(event.target as Node) ||
          calendarRef.current?.contains(event.target as Node)
        ) {
          return;
        }
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  useEffect(() => {
    const payload: Payload = {
      selectedDays: selectedDays.map((day) => Math.floor(day.getTime() / 1000)),
      selectedTableItems,
      selectedCountry: selectedCountry.code,
      selectedKeyword,
    };
    setSerpTableData(payload);
  }, [selectedDays, selectedTableItems, selectedCountry, selectedKeyword]);

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */
  function DateFormatter(date: Date) {
    const day = date.getDate().toString().padStart(2, "0");
    const month = date
      .toLocaleString("en-US", { month: "short" })
      .toUpperCase();
    const year = date.getFullYear();
    return `${day} ${month}-${year}`;
  }

  function BadgeWithCheckboxIsChecked(date: Date) {
    return selectedDate.some((d) => d.getTime() === date.getTime());
  }

  function BadgeWithCheckboxOnChange(date: Date) {
    if (BadgeWithCheckboxIsChecked(date)) {
      setSelectedDate(
        selectedDate.filter((d) => d.getTime() !== date.getTime())
      );
    } else {
      if (selectedDate.length < 2) {
        console.log("selectedDate", selectedDate.length);

        setSelectedDate([...selectedDate, date]);
      }
    }
  }

  const calendarRef = useClickOutside(() => {
    if (isOpen) {
      const isClickInsideToggle = toggleButtonRef.current?.contains(
        document.activeElement
      );

      if (!isClickInsideToggle) {
        setIsOpen(false);
      }
    }
  });

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <Card className="space-y-6 relative pb-[64px]">
      <MultipleDaysCalendar
        ref={calendarRef}
        isOpen={isOpen}
        selected={selectedDays}
        setSelected={setSelectedDays}
      />
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Title>{projectName} All Reports- SERP Competitors</Title>
          <div className="flex items-center gap-2">
            <label
              htmlFor="highlight-competitors"
              className="text-sm cursor-pointer"
            >
              Highlight competitors
            </label>
            <Switch
              id="highlight-competitors"
              checked={highlightCompetitors}
              onChange={setHighlightCompetitors}
              className="group inline-flex h-5 w-10 items-center rounded-full bg-gray-200 transition data-checked:bg-primary"
            >
              <span className="size-3 translate-x-1 rounded-full bg-white transition group-data-checked:translate-x-6" />
            </Switch>
          </div>
        </div>
        <DateRange variation="range" />
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 h-[40px]">
          <Dropdown className="bg-white h-full">
            <Dropdown.Button className="bg-white border pl-1 pr-4 w-48 h-full">
              <div className="flex items-center gap-2">
                <div className="border p-1 rounded-sm">
                  <Image
                    className="h-5"
                    src={selectedCountry.engine}
                    alt="search engine logo"
                    width={20}
                    height={20}
                  />
                </div>
                <Flag className="w-5 rounded-xs" code={selectedCountry.code} />
                <span>{selectedCountry.value}</span>
              </div>
            </Dropdown.Button>
            <Dropdown.Options className="border border-t-0">
              {countriesDropdown.map((country) => (
                <Dropdown.Option
                  key={country.code}
                  onClick={() => setSelectedCountry(country)}
                  className="py-1 bg-white flex items-center just gap-5 border-t-0 pl-1 pr-4 h-fit"
                >
                  <div className="flex items-center gap-2">
                    <div className="border p-1 rounded-sm">
                      <Image
                        className="h-5"
                        src={country.engine}
                        alt="search engine logo"
                        width={20}
                        height={20}
                      />
                    </div>
                    <Flag className="w-5 rounded-xs" code={country.code} />
                    <span>{country.value}</span>
                  </div>
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
          <Dropdown className="h-full">
            <Dropdown.Button className="bg-white border h-full px-4 w-48">
              <span>{selectedKeyword}</span>
            </Dropdown.Button>
            <Dropdown.Options>
              {keywordsDropdown.map((keyword) => (
                <Dropdown.Option
                  className="bg-white border border-t-0"
                  key={keyword}
                  onClick={() => setSelectedKeyword(keyword)}
                >
                  {keyword}
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
        </div>
        <div className="flex items-center gap-2">
          <label htmlFor="show-url" className="text-sm cursor-pointer">
            Show URL
          </label>
          <Switch
            id="show-url"
            checked={showUrlEnabled}
            onChange={setShowUrlEnabled}
            className="group inline-flex h-5 w-10 items-center rounded-full bg-gray-200 transition data-checked:bg-primary"
          >
            <span className="size-3 translate-x-1 rounded-full bg-white transition group-data-checked:translate-x-6" />
          </Switch>
        </div>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {topFilters.map((badge) => (
            <Badge
              key={badge}
              onSelect={() =>
                setSelectedBadge(badge.toLocaleLowerCase().replace(" ", ""))
              }
              selected={selectedBadge === badge.toLocaleLowerCase().replace(" ", "")}
            >
              {badge}
            </Badge>
          ))}
        </div>
        <div ref={toggleButtonRef}>
          <Button
            disabled={!data}
            variant={"secondary"}
            className={"bg-[#F4F4F4] text-secondary hover:text-white gap-5"}
            onClick={() => setIsOpen((prev) => !prev)}
          >
            <FiCalendar />

            <span style={{ userSelect: "none" }}>Select Date</span>
          </Button>
        </div>
      </div>
      <div className="flex items-start justify-between gap-9">
        <div className="flex justify-start flex-col gap-2">
          {selectedDays.length > 0 ? (
            selectedDays.map((day) => (
              <BadgeWithCheckbox
                checkboxId={DateFormatter(day)}
                key={day.toString()}
                className={cn(
                  "text-nowrap border-0",
                  BadgeWithCheckboxIsChecked(day) && "text-primary"
                )}
                checked={BadgeWithCheckboxIsChecked(day)}
                onChange={() => BadgeWithCheckboxOnChange(day)}
              >
                <label htmlFor={DateFormatter(day)} className="cursor-pointer">
                  {DateFormatter(day)}
                </label>
              </BadgeWithCheckbox>
            ))
          ) : (
            <div className="text-sm text-secondary bg-[#F4F4F4] rounded-md py-2.5 px-6 w-[105px]">
              No Date selected
            </div>
          )}
        </div>
        <div className="w-full">
          {isLoading || isFetching ? (
            <TableSkeleton />
          ) : data ? (
            // <></>
            <DataTable
              // highlightCompetitors={highlightCompetitors}
              // isUrl={showUrlEnabled}
              showDateRange={false}
              // checkbox
              tableData={data}
              selectedItems={selectedTableItems}
              setSelectedItems={setSelectedTableItems}
            />
          ) : (
            <ErrorSection onRetry={refetch} message={error?.message} />
          )}
        </div>
      </div>
      {/* <div className="mt-10">
        <Pagination totalPages={10} page={1} />
      </div> */}
    </Card>
  );
};

export default SerpCompetitorsTable;
