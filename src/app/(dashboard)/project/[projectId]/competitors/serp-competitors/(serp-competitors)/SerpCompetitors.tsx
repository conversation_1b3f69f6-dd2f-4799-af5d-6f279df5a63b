"use client";
import React, { useEffect, useState } from "react";
import CompetitorAnalysisHeader from "../../competitor-analysis/(competitor-analysis)/_components/CompetitorAnalysisHeader";
import SerpComparisonChart from "./serp-comparison-chart/SerpComparisonChart";
import SerpCompetitorsTable from "./serp-competitors-all-reports/SerpCompetitorsAllReports";
import type { Payload } from "../../types/serpTable.types";
import NoCompetitorsAdded from "../../_components/NoCompetitorsAdded";
import { useCompetitorsStore } from "@/store/competitors/useCompetitorsAdded";
import { useProjectContext } from "@/contexts/ProjectContext";

const SerpCompetitors = () => {
  const [selectedDate, setSelectedDate] = useState<Date[]>([]);
  // TODO: remove this comment after api integration
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [serpTableData, setSerpTableData] = useState<Payload | undefined>(
    undefined
  );

  useEffect(() => {
    console.log("serpTableData", serpTableData);
  }, [serpTableData]);

  // TODO: Replace this with backend logic. if no competitors added noData = true else false
  const { addedCompetitors } = useCompetitorsStore();
  const noData = addedCompetitors.length <= 0;
  const { projectName } = useProjectContext();
  return (
    <div className="w-full max-w-full overflow-hidden space-y-6">
      <CompetitorAnalysisHeader title={`${projectName} SERP Competitors`} />
      {noData ? (
        <NoCompetitorsAdded />
      ) : (
        <>
          <SerpComparisonChart
            selectedDate={selectedDate}
            serpTableData={serpTableData}
          />
          <SerpCompetitorsTable
            setSerpTableData={setSerpTableData}
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
          />
        </>
      )}
    </div>
  );
};

export default SerpCompetitors;
