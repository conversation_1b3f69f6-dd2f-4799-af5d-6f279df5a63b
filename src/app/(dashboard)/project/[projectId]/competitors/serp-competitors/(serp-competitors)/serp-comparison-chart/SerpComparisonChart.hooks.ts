import { useMutation } from "@tanstack/react-query";
import http from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { SerpChartResponse } from "./SerpComparisonChart.types";
import { serpLineChartConvertor } from "./utils/serpLineChartConvertor";

export const useSerpComparisonChart = () => {
  const { projectId } = useProjectId();

  return useMutation<SerpChartResponse, Error, Date[]>({
    mutationFn: async (selectedDays) => {
      const [firstDay, secondDay] = selectedDays.map((d) =>
        new Date(d).toLocaleDateString("en-CA")
      );

      console.log("fetching chart…", firstDay, secondDay);

      const { data } = await http.post(
        `/api/projects/${projectId}/competitors/serp/report/`,
        {
          rank_filter: "all",
          date: [firstDay, secondDay],
        },
        { params: { force_refresh: false } }
      );

      return serpLineChartConvertor(data);
    },
    retry: 3,
  });
};
