import { TableData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";

type Results = {
  domain: string;
  visibility_rating: number;
  traffic_forecast: number;
  keywords_ranking: number;
  pct_in_top10: number;
  domain_authority: number;
  backlinks: number;
  referring_domains: number;
};
export type TableDataApiResponse = {
  results: Array<Results>;
};

function formatString(str: string): string {
  return str
    .trim()
    .replace("pct", "%")
    .replace(/\_/g, " ")
    .replace(/\b\w/g, (c) => c.toUpperCase());
}

/* ============================== INITIAL CALL ============================== */
export function serpCompetitorConvertor(
  input: TableDataApiResponse
): TableData {
  if (!input.results.length) {
    return { tableHeadings: [], tableBody: [] };
  }

  const sample = input.results[0];

  const tableHeadings = Object.keys(sample)
    .filter((key) => !key.startsWith("previous_"))
    .map((item) => formatString(item));

  const tableBody = input.results.map((item) =>
    tableHeadings.map((headingKey) => {
      const originalKey = headingKey
        .toLowerCase()
        .replace(/ /g, "_")
        .replace("%", "pct");

      const value = (item as any)[originalKey];
      const prev = (item as any)[`previous_${originalKey}`];

      let growth: string | undefined;
      if (typeof prev === "number" && typeof value === "number") {
        const diff = Math.round(value - prev);
        growth = `${diff > 0 ? "+" : ""}${diff}`;
      }

      return {
        value:
          typeof value === "number"
            ? value === null
              ? "0"
              : Math.round(value).toString()
            : value === null
            ? "0"
            : value,
        ...(growth ? { growth } : {}),
      };
    })
  );

  return {
    tableHeadings,
    tableBody,
  };
}

/* =============================== SINGLE DATE ============================== */

export type TableDataSingleDateApiResponse = {
  project_id: string;
  engine: string;
  locale: string;
  keywords_total: number;
  rank_filter: string;
  dates: string; // e.g. "2025-08-15"
  data: {
    [dateKey: string]: Array<Record<string, any>>; // dynamic keys per domain
  };
};

export function serpCompetitorConvertorSingleDate(
  input: TableDataSingleDateApiResponse
): TableData {
  const dateKey = Object.keys(input.data)[0]; // e.g., "20250815"
  const results = input.data[dateKey];

  if (!results || results.length === 0) {
    return { tableHeadings: [], tableBody: [] };
  }

  const sample = results[0];

  // dynamically create table headings, ignoring "previous_" keys
  const tableHeadings = Object.keys(sample)
    .filter((key) => !key.startsWith("previous_"))
    .map(formatString);

  // create table body
  const tableBody = results.map((item) =>
    tableHeadings.map((headingKey) => {
      const originalKey = headingKey
        .toLowerCase()
        .replace(/ /g, "_")
        .replace("%", "pct");

      const value = (item as any)[originalKey];

      return {
        value:
          typeof value === "number"
            ? value === null
              ? "0"
              : Math.round(value).toString()
            : value === null
            ? "0"
            : value,
        growth: "", // empty string for single-date
      };
    })
  );

  return {
    tableHeadings,
    tableBody,
  };
}
