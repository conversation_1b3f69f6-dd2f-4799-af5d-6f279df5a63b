"use client";

import React, { Suspense } from "react";
import { ProjectProvider } from "@/contexts/ProjectContext";
import { useProjectId } from "@/hooks/useProjectId";

interface ProjectLayoutProps {
  children: React.ReactNode;
}

/**
 * Error boundary component for project-related errors
 */
function ProjectErrorBoundary({
  children,
  error,
}: {
  children: React.ReactNode;
  error?: string | null;
}) {
  if (error) {
    return (
      <div className="w-full max-w-8xl mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Project Error
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

/**
 * Loading component for project pages
 */
function ProjectLoading() {
  return (
    <div className="w-full max-w-8xl mx-auto px-4 py-8">
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
        <div className="space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    </div>
  );
}

/**
 * Project validation component
 * Validates project ID and shows appropriate loading/error states
 */
function ProjectValidator({ children }: { children: React.ReactNode }) {
  const { projectId, isValidProjectId, projectIdError } = useProjectId();

  // Show error if project ID is invalid
  if (!isValidProjectId) {
    return (
      <ProjectErrorBoundary error={projectIdError}>
        {children}
      </ProjectErrorBoundary>
    );
  }

  // Show loading if project ID is not yet available (shouldn't happen with URL params, but good to have)
  if (!projectId) {
    return <ProjectLoading />;
  }

  return <>{children}</>;
}

/**
 * Main project layout component
 * Provides project context to all child pages and handles error states
 */
export default function ProjectLayout({ children }: ProjectLayoutProps) {
  return (
    <ProjectProvider>
      <Suspense fallback={<ProjectLoading />}>
        <ProjectValidator>{children}</ProjectValidator>
      </Suspense>
    </ProjectProvider>
  );
}
