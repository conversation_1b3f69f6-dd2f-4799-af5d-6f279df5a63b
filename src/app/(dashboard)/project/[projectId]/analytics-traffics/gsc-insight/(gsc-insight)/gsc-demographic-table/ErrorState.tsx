import React from "react";
import { motion } from "framer-motion";

interface ErrorStateProps {
  message: string;
  onRetry: () => void;
  type: "primary" | "comparison" | null;
}

const ErrorState: React.FC<ErrorStateProps> = ({ message, onRetry, type }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center h-full min-h-[300px] space-y-4"
    >
      <div className="text-center space-y-2">
        <div className="text-6xl">😕</div>
        <h3 className="text-lg font-semibold text-gray-700">
          {type === "primary" ? "Unable to Load Data" : "Comparison Data Unavailable"}
        </h3>
        <p className="text-sm text-gray-500 max-w-md">
          {message}
        </p>
      </div>
      
      <button
        onClick={onRetry}
        className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
      >
        Try Again
      </button>
    </motion.div>
  );
};

export default ErrorState;