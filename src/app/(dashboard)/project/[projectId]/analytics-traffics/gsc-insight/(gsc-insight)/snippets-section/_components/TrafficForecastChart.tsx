import React, { useState, useRef } from "react";
import { format, parseISO, isAfter, startOfDay } from "date-fns";
import { cn } from "@/utils/cn";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";
import { TrafficForecastDailyMetric } from "../_hooks/useTrafficForecastData";

// Helper function to convert YYYYMMDD format to ISO format
const convertDateFormat = (dateStr: string): string => {
  // If already in ISO format (contains hyphens), return as is
  if (dateStr.includes("-")) {
    return dateStr;
  }

  // Convert YYYYMMDD to YYYY-MM-DD
  if (dateStr.length === 8) {
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    return `${year}-${month}-${day}`;
  }

  return dateStr;
};

/* ================================== TYPES ================================= */
type TrafficForecastChartProps = {
  data: TrafficForecastDailyMetric[];
  className?: string;
  period?: string;
  onPeriodChange?: (period: string) => void;
  activePeriod?: string;
  selectedKeyword?: string | null;
  keywordData?: TrafficForecastDailyMetric[];
  title?: string;
  showDateRangeButtons?: boolean;
};

const trafficForecastBadges = ["Week", "Month", "3 Month"];

/* ========================================================================== */
const TrafficForecastChart = ({
  data,
  className = "",
  period = "Month",
  onPeriodChange,
  activePeriod = "Month",
  selectedKeyword = null,
  keywordData = undefined,
  title,
  showDateRangeButtons = true,
}: TrafficForecastChartProps) => {
  const { themeColor } = useAppThemeColor();
  const [activeBadge, setActiveBadge] = useState(
    trafficForecastBadges.indexOf(activePeriod)
  );
  const [hoveredPoint, setHoveredPoint] = useState<{
    index: number;
    x: number;
    y: number;
    value: number;
    date: string;
  } | null>(null);
  const chartRef = useRef<HTMLDivElement>(null);

  // Use keyword-specific data if available, otherwise use overall data
  const chartData = selectedKeyword && keywordData ? keywordData : data;
  const _chartTitle = selectedKeyword
    ? `Traffic Forecast - "${selectedKeyword}"`
    : title || "Traffic Forecast";

  // Determine if we have data to display
  const hasNoData = !chartData || chartData.length === 0;
  
  return (
    <div className={cn("w-full", className)}>
      <div className={cn("w-full p-4", className)}>
        {/* Optional date range buttons */}
        {showDateRangeButtons && (
          <div className="flex flex-wrap gap-2 mb-4">
            {trafficForecastBadges.map((item, index) => (
              <button
                key={index}
                onClick={() => {
                  setActiveBadge(index);
                  onPeriodChange?.(item);
                }}
                className={cn(
                  "py-2 px-3 text-xs rounded-md text-secondary border transition-colors duration-300",
                  index === activeBadge
                    ? "border-[#914AC4] text-[#914AC4]"
                    : "border-gray-200 hover:border-[#914AC4]/50"
                )}
              >
                {item}
              </button>
            ))}
          </div>
        )}
        
        {/* Show no data message if there's no data */}
        {hasNoData ? (
          <div className="w-full h-64 flex items-center justify-center">
            <div className="text-center space-y-1 mt-8">
              <p className="text-sm text-secondary/50">
                {selectedKeyword
                  ? `No data available for "${selectedKeyword}"`
                  : "No data available"}
              </p>
              <p className="text-xs text-secondary/40">
                {selectedKeyword
                  ? "Try selecting a different period"
                  : "Traffic forecast data not found"}
              </p>
            </div>
          </div>
        ) : (
          <>
            {/* Chart content */}
            {/* Get today's date for comparison */}
            {(() => {
              const today = startOfDay(new Date());

              // Separate current and future data
              const currentData: TrafficForecastDailyMetric[] = [];
              const futureData: TrafficForecastDailyMetric[] = [];
              let pivotIndex = -1;

              chartData.forEach((item, index) => {
                const itemDate = startOfDay(parseISO(convertDateFormat(item.date)));
                if (isAfter(itemDate, today)) {
                  if (pivotIndex === -1) {
                    pivotIndex = index;
                    // Include the last current data point in future data for continuity
                    if (currentData.length > 0) {
                      futureData.push(currentData[currentData.length - 1]);
                    }
                  }
                  futureData.push(item);
                } else {
                  currentData.push(item);
                }
              });

              // Find min and max values for scaling with padding
              const values = chartData.map((d) => d.value);
              const minValue = Math.min(...values);
              const maxValue = Math.max(...values);
              const valueRange = maxValue - minValue || 1;

              // Add padding to the range for better visualization
              // Important: allow 0 as the minimum when present
              const paddedMin = minValue === 0 ? 0 : Math.max(0, minValue - valueRange * 0.1);
              const paddedMax = maxValue + valueRange * 0.1;
              const paddedRange = Math.max(paddedMax - paddedMin, 0.0001);

              // Chart dimensions with proper aspect ratio and padding
              const chartWidth = 400;
              const chartHeight = 100;
              const squarePadding = 12;
              const chartAreaWidth = chartWidth - squarePadding * 2;

              // Generate path for the line chart
              const generatePath = (
                dataPoints: TrafficForecastDailyMetric[],
                startIndex = 0
              ) => {
                if (dataPoints.length === 0) return "";

                const points = dataPoints.map((item, index) => {
                  const globalIndex = startIndex + index;
                  const x =
                    squarePadding + (globalIndex / (chartData.length - 1)) * chartAreaWidth;
                  const y = ((paddedMax - item.value) / paddedRange) * chartHeight;
                  return `${x},${y}`;
                });

                return `M ${points.join(" L ")}`;
              };

              const currentPath = generatePath(currentData, 0);
              const futurePath = generatePath(
                futureData,
                pivotIndex === -1 ? 0 : pivotIndex - 1
              );

              // Calculate total traffic and trend
              const totalTraffic = values.reduce((a, b) => a + b, 0);
              const _averageTraffic = (totalTraffic / values.length).toFixed(1);

              // Determine date format based on period and data length
              const getDateFormat = () => {
                if (period === "Month" || period === "3 Month") {
                  return chartData.length > 15 ? "MMM d" : "MMM d";
                }
                return "MMM d";
              };

              // Determine which dates to show based on period
              const shouldShowDate = (index: number) => {
                const totalDates = chartData.length;

                if (period === "Week") {
                  return true; // Show all dates for week view
                } else if (period === "Month") {
                  // For month view, show every 5th date or key points
                  return index % 5 === 0 || index === 0 || index === totalDates - 1;
                } else if (period === "3 Month") {
                  // For 3 month view, show every 15th date or key points
                  return index % 15 === 0 || index === 0 || index === totalDates - 1;
                }

                return (
                  index === 0 ||
                  index === totalDates - 1 ||
                  index === Math.floor(totalDates / 2)
                );
              };

              // Determine which data points should show tooltips and diamond indicators (separately)
              const shouldShowTooltip = (_index: number) => {
                // For 3 month view, show tooltip for every day
                if (period === "3 Month") return true;
                // For other periods, show for all points as well
                return true;
              };

              const shouldShowIndicator = (index: number) => {
                const totalDates = chartData.length;
                if (period === "3 Month") {
                  // Keep indicators sparse (weekly) while tooltips are available for all
                  return index % 7 === 0 || index === 0 || index === totalDates - 1;
                }
                // For Week/Month show indicators for all points
                return true;
              };

              /* ========================================================================== */
              /*                                   RENDER                                   */
              /* ========================================================================== */
              
              // Return the chart JSX
               return (
      <div className={cn("w-full p-4", className)}>
        {/* Optional date range buttons */}
        {showDateRangeButtons && (
          <div className="flex flex-wrap gap-2 mb-4">
            {trafficForecastBadges.map((item, index) => (
              <button
                key={index}
                onClick={() => {
                  setActiveBadge(index);
                  onPeriodChange?.(item);
                }}
                className={cn(
                  "py-2 px-3 text-xs rounded-md text-secondary border transition-colors duration-300",
                  index === activeBadge
                    ? "border-[#914AC4] text-[#914AC4]"
                    : "border-gray-200 hover:border-[#914AC4]/50"
                )}
              >
                {item}
              </button>
            ))}
          </div>
        )}
        
        {/* Header with total traffic */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-sm font-medium text-secondary/80 mb-1">
              {selectedKeyword
                ? `"${selectedKeyword}" Traffic Forecast`
                : "Keywords Traffic Forecast"}
            </h3>
            <div className="text-2xl font-semibold text-secondary/90">
              {totalTraffic}
            </div>
            <div className="text-xs text-secondary/60">
              {selectedKeyword
                ? "Keyword expected traffic"
                : "Total expected traffic"}
            </div>
          </div>
          <div className="text-right">
          
          </div>
        </div>

        {/* Chart Container with proper aspect ratio */}
        <div className="relative w-full h-32 mb-6" ref={chartRef}>
          <div className="absolute inset-0 flex">
            {/* Y-axis labels */}
            <div className="flex flex-col justify-between py-1 pr-3 text-xs text-secondary/50 w-10">
              <span>{paddedMax.toFixed(1)}</span>
              <span>{(((paddedMin + paddedMax) / 2)).toFixed(1)}</span>
              <span>{paddedMin.toFixed(1)}</span>
            </div>

            {/* Chart area */}
            <div className="flex-1 relative">
              <svg
                width="100%"
                height="100%"
                viewBox={`0 0 ${chartWidth} ${chartHeight}`}
                preserveAspectRatio="xMidYMid meet"
                className="block"
              >
                {/* Subtle grid lines */}
                <defs>
                  <pattern
                    id="traffic-grid"
                    width="80"
                    height="25"
                    patternUnits="userSpaceOnUse"
                  >
                    <path
                      d={`M 80 0 L 0 0 0 25`}
                      fill="none"
                      stroke="#f8fafc"
                      strokeWidth="0.5"
                    />
                  </pattern>
                </defs>
                <rect
                  width={chartWidth}
                  height={chartHeight}
                  fill="url(#traffic-grid)"
                />

                {/* Current data line (solid) */}
                {currentData.length > 0 && (
                  <path
                    d={currentPath}
                    fill="none"
                    stroke={themeColor}
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                )}

                {/* Future data line (dotted) */}
                {futureData.length > 0 && (
                  <path
                    d={futurePath}
                    fill="none"
                    stroke={themeColor}
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeDasharray="4,4"
                    opacity="0.7"
                  />
                )}

                {/* Data points with hover areas */}
                {chartData.map((item, index) => {
                  const x =
                    squarePadding +
                    (index / (chartData.length - 1)) * chartAreaWidth;
                  const y =
                    ((paddedMax - item.value) / paddedRange) * chartHeight;
                  const itemDate = startOfDay(
                    parseISO(convertDateFormat(item.date))
                  );
                  const isFuture = isAfter(itemDate, today);
                  const showTooltip = shouldShowTooltip(index);
                  const showIndicator = shouldShowIndicator(index);

                  return (
                    <g key={index}>
                      {/* Invisible hover area for tooltips */}
                      {showTooltip && (
                        <circle
                          cx={x}
                          cy={y}
                          r="8"
                          fill="transparent"
                          className="cursor-pointer"
                          onMouseEnter={(e) => {
                            const svgRect = e.currentTarget
                              .closest("svg")
                              ?.getBoundingClientRect();
                            if (svgRect) {
                              // Calculate absolute position relative to viewport
                              const relativeX =
                                (x / chartWidth) * svgRect.width;
                              const relativeY =
                                (y / chartHeight) * svgRect.height;

                              // Use absolute positioning relative to viewport
                              const absoluteX = svgRect.left + relativeX;
                              const absoluteY = svgRect.top + relativeY;

                              setHoveredPoint({
                                index,
                                x: absoluteX,
                                y: absoluteY,
                                value: item.value,
                                date: item.date,
                              });
                            }
                          }}
                          onMouseLeave={() => setHoveredPoint(null)}
                        />
                      )}
                      {/* Visible data point - only for indicator points */}
                      {showIndicator && (
                        <rect
                          x={x - 3}
                          y={y - 3}
                          width="6"
                          height="6"
                          rx="1.5"
                          ry="1.5"
                          fill={themeColor}
                          stroke="none"
                          strokeWidth="0"
                          opacity={isFuture ? "0.7" : "1"}
                          transform={`rotate(45 ${x} ${y})`}
                          className="pointer-events-none"
                        />
                      )}
                    </g>
                  );
                })}
              </svg>
            </div>
          </div>

          {/* Tooltip with fixed positioning */}
          {hoveredPoint && (
            <div
              className="fixed z-[9999] pointer-events-none overflow-visible"
              style={{
                left: hoveredPoint.x,
                top: hoveredPoint.y - 85,
                transform: "translateX(-50%)",
              }}
            >
              <div
                className="bg-white border rounded-lg shadow-lg px-4 py-3 space-y-2 overflow-visible"
                style={{ borderColor: "#E0E0E0" }}
              >
                <div className="text-sm text-secondary/80">
                  {format(
                    parseISO(convertDateFormat(hoveredPoint.date)),
                    "MMM d, yyyy"
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: themeColor }}
                  />
                  <span className="text-sm text-secondary font-medium">
                    {hoveredPoint.value} visits
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* X-axis indicator line */}
        <div className="mb-2 px-8">
          <div className="w-full h-px bg-[#E0E0E0]"></div>
        </div>

        {/* X-axis with dates - adaptive display based on period */}
        <div className="flex justify-between items-center mb-4 px-8 min-h-[20px]">
          {chartData.map((item, index) => {
            const itemDate = startOfDay(parseISO(convertDateFormat(item.date)));
            const isFuture = isAfter(itemDate, today);
            const showDate = shouldShowDate(index);

            return (
              <div
                key={index}
                className={cn(
                  "text-xs font-medium text-center",
                  showDate ? "flex-shrink-0 min-w-[50px]" : "flex-1",
                  isFuture ? "text-secondary/70" : "text-secondary/80"
                )}
              >
                {showDate
                  ? format(
                      parseISO(convertDateFormat(item.date)),
                      getDateFormat()
                    )
                  : ""}
              </div>
            );
          })}
        </div>

        {/* Legend - improved visibility and spacing */}
        <div className="flex items-center justify-center space-x-6 pt-2 pb-1">
          <div className="flex items-center space-x-2">
            <div
              className="w-4 h-0.5"
              style={{ backgroundColor: themeColor }}
            ></div>
            <span className="text-xs font-medium text-secondary/80">
              Current Data
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div
              className="w-4 h-0.5 opacity-70"
              style={{
                backgroundColor: themeColor,
                backgroundImage: `repeating-linear-gradient(90deg, transparent, transparent 2px, white 2px, white 4px)`,
              }}
            ></div>
            <span className="text-xs font-medium text-secondary/80">Forecast</span>
          </div>
        </div>
      </div>
              );
            })()}
          </>
        )}
      </div>
    </div>
  );
};

export default TrafficForecastChart;
