import React, { useState, useRef, useEffect } from "react";
import { format, parseISO } from "date-fns";
import { cn } from "@/utils/cn";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";
import { AveragePositionDailyMetric } from "../_hooks/useAveragePositionData";

// Helper function to convert YYYYMMDD format to ISO format
const convertDateFormat = (dateStr: string): string => {
  // If already in ISO format (contains hyphens), return as is
  if (dateStr.includes("-")) {
    return dateStr;
  }

  // Convert YYYYMMDD to YYYY-MM-DD
  if (dateStr.length === 8) {
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    return `${year}-${month}-${day}`;
  }

  return dateStr;
};

/* ================================== TYPES ================================= */
type AveragePositionChartProps = {
  data: AveragePositionDailyMetric[];
  className?: string;
  onPeriodChange?: (period: string) => void;
  activePeriod?: string;
  selectedKeyword?: string | null;
  keywordData?: AveragePositionDailyMetric[];
  keywordAvgPosition?: number;
  overallAvgPosition?: number;
  title?: string;
  showDateRangeButtons?: boolean;
};

const avgPositionBadges = ["Last week", "Month", "3 Month"];

/* ========================================================================== */
const AveragePositionChart = ({
  data,
  className = "",
  onPeriodChange,
  activePeriod = "Last week",
  selectedKeyword = null,
  keywordData = undefined,
  keywordAvgPosition,
  overallAvgPosition,
  title,
  showDateRangeButtons: _showDateRangeButtons = true,
}: AveragePositionChartProps) => {
  const { themeColor } = useAppThemeColor();
  const [activeBadge, setActiveBadge] = useState(
    avgPositionBadges.indexOf(activePeriod)
  );
  const [hoveredPoint, setHoveredPoint] = useState<{
    index: number;
    x: number;
    y: number;
    value: number;
    date: string;
  } | null>(null);
  const chartRef = useRef<HTMLDivElement>(null);

  // If a keyword is selected but has no metrics, do NOT fall back to overall data
  // Show an empty chart state instead so we can display a keyword-specific no-data message
  const chartData = selectedKeyword ? (keywordData ?? []) : data;
  const _chartTitle = selectedKeyword
    ? `Average Position - "${selectedKeyword}"`
    : title || "Average Position";

  // Keep local active badge in sync with parent-provided activePeriod
  useEffect(() => {
    setActiveBadge(avgPositionBadges.indexOf(activePeriod));
  }, [activePeriod]);

  const noData = !chartData || chartData.length === 0;

  // Find min and max values for scaling with padding
  const positions = chartData.length > 0 ? chartData.map((d) => d.avg_position) : [0];
  const minPosition = Math.min(...positions);
  const maxPosition = Math.max(...positions);
  const positionRange = maxPosition - minPosition || 1;

  // Add padding to the range for better visualization
  // Important: allow 0 as the minimum when present, otherwise clamp to >= 1
  const paddedMin =
    minPosition === 0
      ? 0
      : Math.max(1, minPosition - positionRange * 0.1);
  const paddedMax = maxPosition + positionRange * 0.1;
  const paddedRange = Math.max(paddedMax - paddedMin, 0.0001);

  // Chart dimensions with proper aspect ratio and padding
  const chartWidth = 400;
  const chartHeight = 100;
  const squarePadding = 12;
  const chartAreaWidth = chartWidth - squarePadding * 2;

  // Generate path for the line chart with padding
  const generatePath = () => {
    if (chartData.length === 0) return "";

    const points = chartData.map((item, index) => {
      const x =
        squarePadding + (index / (chartData.length - 1)) * chartAreaWidth;
      // Invert Y axis since lower position is better (position 1 is top)
      const y = ((paddedMax - item.avg_position) / paddedRange) * chartHeight;
      return `${x},${y}`;
    });

    return `M ${points.join(" L ")}`;
  };

  const pathData = generatePath();
  const selectedAvg = selectedKeyword ? keywordAvgPosition : overallAvgPosition;
  const displayAverage =
    typeof selectedAvg === "number" ? selectedAvg.toFixed(1) : "0.0";

  // Determine date format based on period and data length
  const getDateFormat = () => {
    if (activePeriod === "Month" || activePeriod === "3 Month") {
      return chartData.length > 15 ? "MMM d" : "MMM d";
    }
    return "MMM d";
  };

  // Determine which dates to show based on period
  const shouldShowDate = (index: number) => {
    const totalDates = chartData.length;

    if (activePeriod === "Last week") {
      return true; // Show all dates for week view
    } else if (activePeriod === "Month") {
      // For month view, show every 5th date or key points
      return index % 5 === 0 || index === 0 || index === totalDates - 1;
    } else if (activePeriod === "3 Month") {
      // For 3 month view, show every 15th date or key points
      return index % 15 === 0 || index === 0 || index === totalDates - 1;
    }

    return (
      index === 0 ||
      index === totalDates - 1 ||
      index === Math.floor(totalDates / 2)
    );
  };

  // Determine which data points should show tooltips and diamond indicators (separately)
  const shouldShowTooltip = (_index: number) => {
    // For 3 month view, show tooltip for every day
    if (activePeriod === "3 Month") return true;
    // For other periods, show for all points as well
    return true;
  };

  const shouldShowIndicator = (index: number) => {
    const totalDates = chartData.length;
    if (activePeriod === "3 Month") {
      // Keep indicators sparse (weekly) while tooltips are available for all
      return index % 7 === 0 || index === 0 || index === totalDates - 1;
    }
    // For Week/Month show indicators for all points
    return true;
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className={cn("w-full", className)}>
      <div className={cn("w-full p-4", className)}>
        {/* Date range buttons - always shown */}
        <div className="flex flex-wrap gap-2 mb-4">
          {avgPositionBadges.map((item, index) => (
            <button
              key={index}
              onClick={() => {
                setActiveBadge(index);
                onPeriodChange?.(item);
              }}
              className={cn(
                "py-2 px-3 text-xs rounded-md text-secondary border transition-colors duration-300",
                index === activeBadge
                  ? "border-[#914AC4] text-[#914AC4]"
                  : "border-gray-200 hover:border-[#914AC4]/50"
              )}
            >
              {item}
            </button>
          ))}
        </div>

        {/* Header with average position */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-sm font-medium text-secondary/90 mb-1">
              {selectedKeyword
                ? `"${selectedKeyword}" Average Position`
                : "Keywrods Average Position"}
            </h3>
            <div className="text-2xl font-semibold text-secondary/90">{displayAverage}</div>
            <div className="text-xs text-secondary/70">
              {selectedKeyword
                ? "Keyword average position"
                : "Average position"}
            </div>
          </div>
          <div className="text-right">
          
          </div>
        </div>

        {/* Chart Container with proper aspect ratio */}
        <div className="relative w-full h-32 mb-6" ref={chartRef}>
          {noData ? (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center space-y-1 mt-2 flex flex-col justify-center items-center h-full">
                <p className="text-sm text-secondary/70">
                  {selectedKeyword ? `No data for "${selectedKeyword}"` : "No data available"}
                </p>
                <p className="text-xs text-secondary/50">Try selecting a different period</p>
              </div>
            </div>
          ) : (
            <>
              <div className="absolute inset-0 flex">
                {/* Y-axis labels (top is max due to inverted Y mapping) */}
                <div className="flex flex-col justify-between py-1 pr-3 text-xs text-secondary/40 w-10">
                  <span>{paddedMax.toFixed(1)}</span>
                  <span>{(((paddedMin + paddedMax) / 2)).toFixed(1)}</span>
                  <span>{paddedMin.toFixed(1)}</span>
                </div>

                {/* Chart area */}
                <div className="flex-1 relative">
                  <svg
                    width="100%"
                    height="100%"
                    viewBox={`0 0 ${chartWidth} ${chartHeight}`}
                    preserveAspectRatio="xMidYMid meet"
                    className="block"
                  >
                    {/* Subtle grid lines */}
                    <defs>
                      <pattern
                        id="position-grid"
                        width="80"
                        height="25"
                        patternUnits="userSpaceOnUse"
                      >
                        <path
                          d={`M 80 0 L 0 0 0 25`}
                          fill="none"
                          stroke="#f8fafc"
                          strokeWidth="0.5"
                        />
                      </pattern>
                    </defs>
                    <rect
                      width={chartWidth}
                      height={chartHeight}
                      fill="url(#position-grid)"
                    />

                    {/* Main chart line */}
                    <path
                      d={pathData}
                      fill="none"
                      stroke={themeColor}
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />

                    {/* Data points with hover areas */}
                    {chartData.map((item, index) => {
                      const x =
                        squarePadding +
                        (index / (chartData.length - 1)) * chartAreaWidth;
                      const y =
                        ((paddedMax - item.avg_position) / paddedRange) *
                        chartHeight;
                      const showTooltip = shouldShowTooltip(index);
                      const showIndicator = shouldShowIndicator(index);

                      return (
                        <g key={index}>
                          {/* Invisible hover area for tooltips */}
                          {showTooltip && (
                            <circle
                              cx={x}
                              cy={y}
                              r="8"
                              fill="transparent"
                              className="cursor-pointer"
                              onMouseEnter={(e) => {
                                const svgRect = e.currentTarget
                                  .closest("svg")
                                  ?.getBoundingClientRect();
                                if (svgRect) {
                                  const relativeX =
                                    (x / chartWidth) * svgRect.width;
                                  const relativeY =
                                    (y / chartHeight) * svgRect.height;

                                  const absoluteX = svgRect.left + relativeX;
                                  const absoluteY = svgRect.top + relativeY;

                                  setHoveredPoint({
                                    index,
                                    x: absoluteX,
                                    y: absoluteY,
                                    value: item.avg_position,
                                    date: item.date,
                                  });
                                }
                              }}
                              onMouseLeave={() => setHoveredPoint(null)}
                            />
                          )}
                          {/* Visible data point - only for indicator points */}
                          {showIndicator && (
                            <rect
                              x={x - 3}
                              y={y - 3}
                              width="6"
                              height="6"
                              rx="1.5"
                              ry="1.5"
                              fill={themeColor}
                              transform={`rotate(45 ${x} ${y})`}
                              className="pointer-events-none"
                            />
                          )}
                        </g>
                      );
                    })}
                  </svg>
                </div>
              </div>

              {/* Tooltip with fixed positioning */}
              {hoveredPoint && (
                <div
                  className="fixed z-[9999] pointer-events-none overflow-visible"
                  style={{
                    left: hoveredPoint.x,
                    top: hoveredPoint.y - 85,
                    transform: "translateX(-50%)",
                  }}
                >
                  <div
                    className="bg-white border rounded-lg shadow-lg px-4 py-3 space-y-2 overflow-visible"
                    style={{ borderColor: "#E0E0E0" }}
                  >
                    <div className="text-sm text-secondary/80">
                      {format(
                        parseISO(convertDateFormat(hoveredPoint.date)),
                        "MMM d, yyyy"
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: themeColor }}
                      />
                      <span className="text-sm text-secondary font-medium">
                        Position {hoveredPoint.value.toFixed(1)}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {!noData && (
          <>
            {/* X-axis indicator line */}
            <div className="mb-2 px-8">
              <div className="w-full h-px bg-[#E0E0E0]"></div>
            </div>

            {/* X-axis with dates - adaptive display based on period */}
            <div className="flex justify-between items-center mb-4 px-8 min-h-[20px]">
              {chartData.map((item, index) => {
                const showDate = shouldShowDate(index);

                return (
                  <div
                    key={index}
                    className={cn(
                      "text-xs font-medium text-center",
                      showDate ? "flex-shrink-0 min-w-[50px]" : "flex-1",
                      "text-secondary/70"
                    )}
                  >
                    {showDate
                      ? format(
                          parseISO(convertDateFormat(item.date)),
                          getDateFormat()
                        )
                      : ""}
                  </div>
                );
              })}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AveragePositionChart;
