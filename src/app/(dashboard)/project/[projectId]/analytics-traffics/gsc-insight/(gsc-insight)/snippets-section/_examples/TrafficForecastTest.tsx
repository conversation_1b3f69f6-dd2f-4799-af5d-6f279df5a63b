import React from "react";
import TrafficForecastChart from "../_components/TrafficForecastChart";

// Sample data from the user's request
const sampleTrafficForecastData = {
  status: "success",
  project_id: "51e74171-0e23-4f7e-ad4d-7ffae8ffdab1",
  metric: "trafficforecast",
  data: {
    daily_metrics: [
      { date: "20250801", value: 1 },
      { date: "20250802", value: 2 },
      { date: "20250803", value: 4 },
      { date: "20250804", value: 0 },
      { date: "20250805", value: 0 },
      { date: "20250806", value: 4 },
      { date: "20250807", value: 0 },
      { date: "20250808", value: 0 },
      { date: "20250809", value: 0 },
      { date: "20250810", value: 2 },
      { date: "20250811", value: 2 },
      { date: "20250812", value: 2 },
      { date: "20250813", value: 2 },
      { date: "20250814", value: 2 },
      { date: "20250815", value: 2 },
      { date: "20250816", value: 2 },
      { date: "20250817", value: 2 },
      { date: "20250818", value: 2 },
      { date: "20250819", value: 2 },
      { date: "20250820", value: 2 },
      { date: "20250821", value: 2 },
      { date: "20250822", value: 2 },
      { date: "20250823", value: 2 },
      { date: "20250824", value: 2 },
      { date: "20250825", value: 2 },
      { date: "20250826", value: 2 },
      { date: "20250827", value: 2 },
      { date: "20250828", value: 2 },
      { date: "20250829", value: 2 },
      { date: "20250830", value: 2 },
    ],
    keywords: {
      "seo kellyville": {
        daily_metrics: [
          { date: "20250801", value: 0 },
          { date: "20250802", value: 0 },
          { date: "20250803", value: 0 },
          { date: "20250804", value: 0 },
          { date: "20250805", value: 0 },
          { date: "20250806", value: 0 },
          { date: "20250807", value: 0 },
          { date: "20250808", value: 0 },
          { date: "20250809", value: 0 },
          { date: "20250810", value: 1 },
          { date: "20250811", value: 1 },
          { date: "20250812", value: 1 },
          { date: "20250813", value: 1 },
          { date: "20250814", value: 1 },
          { date: "20250815", value: 1 },
          { date: "20250816", value: 1 },
          { date: "20250817", value: 1 },
          { date: "20250818", value: 1 },
          { date: "20250819", value: 1 },
          { date: "20250820", value: 1 },
          { date: "20250821", value: 1 },
          { date: "20250822", value: 1 },
          { date: "20250823", value: 1 },
          { date: "20250824", value: 1 },
          { date: "20250825", value: 1 },
          { date: "20250826", value: 1 },
          { date: "20250827", value: 1 },
          { date: "20250828", value: 1 },
          { date: "20250829", value: 1 },
          { date: "20250830", value: 1 },
        ],
      },
      "seo norwest business park": {
        daily_metrics: [
          { date: "20250801", value: 0 },
          { date: "20250802", value: 0 },
          { date: "20250803", value: 0 },
          { date: "20250804", value: 0 },
          { date: "20250805", value: 0 },
          { date: "20250806", value: 0 },
          { date: "20250807", value: 0 },
          { date: "20250808", value: 0 },
          { date: "20250809", value: 0 },
          { date: "20250810", value: 1 },
          { date: "20250811", value: 1 },
          { date: "20250812", value: 1 },
          { date: "20250813", value: 1 },
          { date: "20250814", value: 1 },
          { date: "20250815", value: 1 },
          { date: "20250816", value: 1 },
          { date: "20250817", value: 1 },
          { date: "20250818", value: 1 },
          { date: "20250819", value: 1 },
          { date: "20250820", value: 1 },
          { date: "20250821", value: 1 },
          { date: "20250822", value: 1 },
          { date: "20250823", value: 1 },
          { date: "20250824", value: 1 },
          { date: "20250825", value: 1 },
          { date: "20250826", value: 1 },
          { date: "20250827", value: 1 },
          { date: "20250828", value: 1 },
          { date: "20250829", value: 1 },
          { date: "20250830", value: 1 },
        ],
      },
      "seo-webanalyser.com": {
        daily_metrics: [
          { date: "20250801", value: 0 },
          { date: "20250802", value: 0 },
          { date: "20250803", value: 0 },
          { date: "20250804", value: 0 },
          { date: "20250805", value: 0 },
          { date: "20250806", value: 0 },
          { date: "20250807", value: 0 },
          { date: "20250808", value: 0 },
          { date: "20250809", value: 0 },
          { date: "20250810", value: 0 },
          { date: "20250811", value: 0 },
          { date: "20250812", value: 0 },
          { date: "20250813", value: 0 },
          { date: "20250814", value: 0 },
          { date: "20250815", value: 0 },
          { date: "20250816", value: 0 },
          { date: "20250817", value: 0 },
          { date: "20250818", value: 0 },
          { date: "20250819", value: 0 },
          { date: "20250820", value: 0 },
          { date: "20250821", value: 0 },
          { date: "20250822", value: 0 },
          { date: "20250823", value: 0 },
          { date: "20250824", value: 0 },
          { date: "20250825", value: 0 },
          { date: "20250826", value: 0 },
          { date: "20250827", value: 0 },
          { date: "20250828", value: 0 },
          { date: "20250829", value: 0 },
          { date: "20250830", value: 0 },
        ],
      },
    },
  },
};

/**
 * Test component to verify TrafficForecastChart keyword filtering functionality
 * This component demonstrates:
 * 1. Overall traffic forecast data display
 * 2. Keyword-specific filtering
 * 3. Date format conversion (YYYYMMDD to ISO format)
 */
const TrafficForecastTest = () => {
  const [selectedKeyword, setSelectedKeyword] = React.useState<string | null>(
    null
  );

  const keywords = Object.keys(sampleTrafficForecastData.data.keywords);

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Traffic Forecast Chart Test</h2>

        {/* Keyword Selection */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Select Keyword to Filter:</h3>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedKeyword(null)}
              className={`px-3 py-1 text-xs rounded border transition-colors ${
                selectedKeyword === null
                  ? "border-blue-500 bg-blue-50 text-blue-700"
                  : "border-gray-300 hover:border-blue-300"
              }`}
            >
              All Keywords
            </button>
            {keywords.map((keyword) => (
              <button
                key={keyword}
                onClick={() => setSelectedKeyword(keyword)}
                className={`px-3 py-1 text-xs rounded border transition-colors ${
                  selectedKeyword === keyword
                    ? "border-blue-500 bg-blue-50 text-blue-700"
                    : "border-gray-300 hover:border-blue-300"
                }`}
              >
                {keyword}
              </button>
            ))}
          </div>
        </div>

        {/* Current Selection Info */}
        <div className="p-3 bg-gray-50 rounded">
          <p className="text-sm">
            <strong>Currently showing:</strong>{" "}
            {selectedKeyword
              ? `"${selectedKeyword}" keyword data`
              : "Overall traffic forecast data"}
          </p>
          {selectedKeyword && (
            <p className="text-xs text-gray-600 mt-1">
              Total traffic for "{selectedKeyword}":{" "}
              {sampleTrafficForecastData.data.keywords[
                selectedKeyword
              ].daily_metrics.reduce(
                (sum, metric) => sum + metric.value,
                0
              )}{" "}
              visits
            </p>
          )}
        </div>
      </div>

      {/* Traffic Forecast Chart */}
      <div className="border rounded-lg p-4">
        <TrafficForecastChart
          data={sampleTrafficForecastData.data.daily_metrics}
          selectedKeyword={selectedKeyword}
          keywordData={
            selectedKeyword &&
            sampleTrafficForecastData.data.keywords[selectedKeyword]
              ? sampleTrafficForecastData.data.keywords[selectedKeyword]
                  .daily_metrics
              : undefined
          }
          period="Month"
          className="w-full"
        />
      </div>

      {/* Debug Info */}
      <details className="text-xs">
        <summary className="cursor-pointer font-medium">
          Debug Information
        </summary>
        <div className="mt-2 p-3 bg-gray-100 rounded">
          <p>
            <strong>Selected Keyword:</strong> {selectedKeyword || "None"}
          </p>
          <p>
            <strong>Available Keywords:</strong> {keywords.join(", ")}
          </p>
          <p>
            <strong>Overall Data Points:</strong>{" "}
            {sampleTrafficForecastData.data.daily_metrics.length}
          </p>
          {selectedKeyword && (
            <p>
              <strong>Keyword Data Points:</strong>{" "}
              {
                sampleTrafficForecastData.data.keywords[selectedKeyword]
                  .daily_metrics.length
              }
            </p>
          )}
        </div>
      </details>
    </div>
  );
};

export default TrafficForecastTest;
