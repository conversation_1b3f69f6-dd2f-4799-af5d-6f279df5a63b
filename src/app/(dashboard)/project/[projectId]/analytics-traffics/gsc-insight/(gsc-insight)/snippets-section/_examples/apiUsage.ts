/**
 * Example usage of the Snippets API Service
 * 
 * This file demonstrates how to use the SnippetsApiService
 * to fetch and process keyword snippets data.
 */

import { snippetsApiService, SnippetsApiService } from '../_services/snippetsApi';

// Example 1: Basic API usage
export async function fetchBasicSnippetsData(projectId: string) {
  try {
    // Fetch data for a project
    const response = await snippetsApiService.fetchKeywordSnippets(projectId);
    
    console.log('API Response:', response);
    console.log('Project ID:', response.project_id);
    console.log('Last Sync:', response.last_sync);
    console.log('Period:', response.data.period);
    
    return response;
  } catch (error) {
    console.error('Failed to fetch snippets data:', error);
    throw error;
  }
}

// Example 2: Fetch data with date range
export async function fetchSnippetsWithDateRange(
  projectId: string,
  startDate: string,
  endDate: string
) {
  try {
    const response = await snippetsApiService.fetchKeywordSnippets(
      projectId,
      startDate,
      endDate
    );
    
    console.log(`Data for period ${startDate} to ${endDate}:`, response);
    return response;
  } catch (error) {
    console.error('Failed to fetch snippets data with date range:', error);
    throw error;
  }
}

// Example 3: Process and analyze the data
export async function analyzeKeywordPerformance(projectId: string) {
  try {
    const response = await snippetsApiService.fetchKeywordSnippets(projectId);
    
    // Transform to keyword list
    const keywords = snippetsApiService.transformToKeywordList(response);
    console.log('Keywords:', keywords);
    
    // Get aggregated metrics
    const metrics = snippetsApiService.getAggregatedMetrics(response);
    console.log('Aggregated Metrics:', metrics);
    
    // Get top performing URLs
    const topUrls = snippetsApiService.getTopPerformingUrls(response, 5);
    console.log('Top 5 URLs:', topUrls);
    
    // Find best performing keyword
    const bestKeyword = keywords.reduce((best, current) => 
      current.clicks > best.clicks ? current : best
    );
    console.log('Best performing keyword:', bestKeyword);
    
    // Get detailed data for best keyword
    const keywordData = snippetsApiService.getKeywordData(response, bestKeyword.keyword);
    console.log('Detailed data for best keyword:', keywordData);
    
    return {
      keywords,
      metrics,
      topUrls,
      bestKeyword,
      keywordData
    };
  } catch (error) {
    console.error('Failed to analyze keyword performance:', error);
    throw error;
  }
}

// Example 4: Search functionality
export async function searchKeywords(projectId: string, searchQuery: string) {
  try {
    const response = await snippetsApiService.fetchKeywordSnippets(projectId);
    
    // Search keywords
    const filteredKeywords = snippetsApiService.searchKeywords(response, searchQuery);
    console.log(`Keywords matching "${searchQuery}":`, filteredKeywords);
    
    return filteredKeywords;
  } catch (error) {
    console.error('Failed to search keywords:', error);
    throw error;
  }
}

// Example 5: Custom API service instance
export function createCustomApiService() {
  const customService = new SnippetsApiService();
  
  return {
    async fetchData(projectId: string) {
      return await customService.fetchKeywordSnippets(projectId);
    },
    
    async getKeywordInsights(projectId: string, keyword: string) {
      const response = await customService.fetchKeywordSnippets(projectId);
      const keywordData = customService.getKeywordData(response, keyword);
      
      if (!keywordData) {
        throw new Error(`Keyword "${keyword}" not found`);
      }
      
      return {
        keyword,
        data: keywordData,
        urlCount: keywordData.snippets.length,
        bestPosition: Math.min(...keywordData.snippets.map(s => s.avg_position)),
        totalTraffic: keywordData.clicks,
        ctr: keywordData.impressions > 0 ? (keywordData.clicks / keywordData.impressions) * 100 : 0
      };
    }
  };
}

// Example 6: Batch processing multiple projects
export async function batchProcessProjects(projectIds: string[]) {
  const results = [];
  
  for (const projectId of projectIds) {
    try {
      console.log(`Processing project: ${projectId}`);
      
      const response = await snippetsApiService.fetchKeywordSnippets(projectId);
      const metrics = snippetsApiService.getAggregatedMetrics(response);
      
      results.push({
        projectId,
        success: true,
        metrics,
        keywordCount: metrics.totalKeywords,
        lastSync: response.last_sync
      });
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`Failed to process project ${projectId}:`, error);
      results.push({
        projectId,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
  
  return results;
}

// Example 7: Real-time data monitoring
export class SnippetsDataMonitor {
  private intervalId: NodeJS.Timeout | null = null;
  private callbacks: Array<(data: any) => void> = [];
  
  constructor(
    private projectId: string,
    private pollInterval: number = 60000 // 1 minute
  ) {}
  
  subscribe(callback: (data: any) => void) {
    this.callbacks.push(callback);
  }
  
  unsubscribe(callback: (data: any) => void) {
    this.callbacks = this.callbacks.filter(cb => cb !== callback);
  }
  
  start() {
    if (this.intervalId) return;
    
    this.intervalId = setInterval(async () => {
      try {
        const response = await snippetsApiService.fetchKeywordSnippets(this.projectId);
        const metrics = snippetsApiService.getAggregatedMetrics(response);
        
        this.callbacks.forEach(callback => {
          callback({
            timestamp: new Date(),
            projectId: this.projectId,
            metrics,
            lastSync: response.last_sync
          });
        });
      } catch (error) {
        console.error('Failed to fetch data in monitor:', error);
      }
    }, this.pollInterval);
  }
  
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}

// Usage examples:

/*
// Basic usage
const data = await fetchBasicSnippetsData('project-123');

// With date range
const rangeData = await fetchSnippetsWithDateRange(
  'project-123',
  '2025-01-01',
  '2025-01-31'
);

// Analysis
const analysis = await analyzeKeywordPerformance('project-123');

// Search
const searchResults = await searchKeywords('project-123', 'seo');

// Custom service
const customService = createCustomApiService('https://api.example.com');
const insights = await customService.getKeywordInsights('project-123', 'seo tools');

// Batch processing
const batchResults = await batchProcessProjects(['proj-1', 'proj-2', 'proj-3']);

// Real-time monitoring
const monitor = new SnippetsDataMonitor('project-123', 30000);
monitor.subscribe((data) => {
  console.log('New data received:', data);
});
monitor.start();

// Stop monitoring when done
// monitor.stop();
*/