import React from "react";
import { useParams } from "next/navigation";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../_components/date-range/DateRange";
import CardSection from "./_components/CardSection";
import KeywordsList from "./_components/KeywordsList";
import AnalyticsPanel from "./_components/AnalyticsPanel";
import SerpAnalysis from "./_components/SerpAnalysis";
import LoadingSpinner from "./_components/LoadingSpinner";
import ErrorMessage from "./_components/ErrorMessage";
import { useSnippetsData } from "./_hooks/useSnippetsData";
import { useProjectContext } from "@/contexts/ProjectContext";

interface SnippetsSectionProps {
  startDate?: string;
  endDate?: string;
}

const SnippetsSection = ({ startDate, endDate }: SnippetsSectionProps) => {
  const params = useParams();
  const projectId = params?.projectId as string;
  const { projectName } = useProjectContext();

  const {
    data,
    keywords,
    filteredKeywords,
    selectedKeywordData,
    aggregatedMetrics,
    loading,
    error,
    selectedKeyword,
    searchQuery,
    setSelectedKeyword,
    setSearchQuery,
    refetch,
    clearError,
  } = useSnippetsData({
    projectId,
    startDate,
    endDate,
    autoFetch: true,
  });

  const handleKeywordClick = (keyword: string) => {
    setSelectedKeyword(keyword);
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <Card className="space-y-6 rounded-3xl">
          <div className="space-y-2">
            <div className="flex flex-row justify-between items-center">
              <Title>{projectName} Snippets</Title>
            </div>
            <DateRange />
          </div>
          <LoadingSpinner message="Loading keyword snippets..." />
        </Card>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <Card className="space-y-6 rounded-3xl">
          <div className="space-y-2">
            <div className="flex flex-row justify-between items-center">
              <Title>{projectId} Snippets</Title>
            </div>
            <DateRange />
          </div>
          <ErrorMessage
            message={error}
            onRetry={refetch}
            onDismiss={clearError}
          />
        </Card>
      </div>
    );
  }

  // Show empty state
  if (!data || keywords.length === 0) {
    return (
      <div className="space-y-6">
        <Card className="space-y-6 rounded-3xl">
          <div className="space-y-2">
            <div className="flex flex-row justify-between items-center">
              <Title>{projectName} Snippets</Title>
            </div>
            <DateRange />
          </div>
          <div className="text-center py-12">
            <div className="text-secondary/60 text-sm">
              No keyword snippets data available for the selected period.
            </div>
            <button
              onClick={refetch}
              className="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
            >
              Refresh Data
            </button>
          </div>
        </Card>
      </div>
    );
  }

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="space-y-6">
      <Card className="space-y-6 rounded-3xl">
        <div className="space-y-2">
          <div className="flex flex-row justify-between items-center">
            <Title>{projectName} Snippets</Title>
          </div>
          {/* <DateRange /> */}
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-[400px_1fr] lg:grid-cols-2 gap-6 w-full">
          <CardSection className="w-full">
            <KeywordsList
              keywords={filteredKeywords}
              selectedKeyword={selectedKeyword}
              onKeywordClick={handleKeywordClick}
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
            />
          </CardSection>

          <CardSection className="w-full">
            <AnalyticsPanel
              selectedKeyword={selectedKeyword}
              keywordData={selectedKeywordData}
              aggregatedMetrics={aggregatedMetrics}
              totalKeywords={keywords.length}
            />
          </CardSection>
        </div>
      </Card>

      {/* SERP Analysis Section */}
      <SerpAnalysis
        selectedKeyword={selectedKeyword}
        keywordData={selectedKeywordData}
      />
    </div>
  );
};

export default SnippetsSection;
