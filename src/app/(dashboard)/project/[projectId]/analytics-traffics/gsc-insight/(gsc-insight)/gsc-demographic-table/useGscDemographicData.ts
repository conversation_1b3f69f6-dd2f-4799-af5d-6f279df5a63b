import { useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import {
  fetchGscDemographicData,
  fetchGscDemographicComparisonData,
  processGscDemographicData,
  processGscDemographicComparisonData,
  DemographicType,
  ProcessedGscDemographicData,
} from "./gscDemographicService";

interface UseGscDemographicDataParams {
  page: number;
  demographicType: DemographicType;
  itemsPerPage?: number;
}

interface UseGscDemographicDataReturn {
  data: ProcessedGscDemographicData | undefined;
  isLoading: boolean;
  error: {
    hasError: boolean;
    message: string | null;
    type: "primary" | "comparison" | null;
  };
}

const useGscDemographicData = ({
  page,
  demographicType,
  itemsPerPage = 10,
}: UseGscDemographicDataParams): UseGscDemographicDataReturn => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API calls
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } = getFormattedDates();

  const {
    data,
    isLoading,
    error: queryError,
  } = useQuery({
    queryKey: [
      "gsc-demographic-data",
      projectId,
      demographicType,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
      page,
      itemsPerPage,
    ],
    queryFn: async () => {
      if (!projectId || !startDate || !endDate) {
        throw new Error("Missing required parameters");
      }

      // If comparison is enabled and we have comparison dates, fetch comparison data
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { current, comparison } = await fetchGscDemographicComparisonData({
            projectId,
            demographicType,
            startDate,
            endDate,
            comparisonStartDate,
            comparisonEndDate,
            page,
            limit: itemsPerPage,
          });

          return processGscDemographicComparisonData(current, comparison, demographicType, page, itemsPerPage);
        } catch (comparisonError) {
          // If comparison fails, fall back to current data only
          console.warn("Comparison data fetch failed, falling back to current data:", comparisonError);
          const response = await fetchGscDemographicData({
            projectId,
            demographicType,
            startDate,
            endDate,
            page,
            limit: itemsPerPage,
          });

          return processGscDemographicData(response, demographicType, page, itemsPerPage);
        }
      } else {
        // Fetch current data only
        const response = await fetchGscDemographicData({
          projectId,
          demographicType,
          startDate,
          endDate,
          page,
          limit: itemsPerPage,
        });

        return processGscDemographicData(response, demographicType, page, itemsPerPage);
      }
    },
    enabled: isValidProjectId && !!projectId && !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    throwOnError: false,
  });

  const error = {
    hasError: !!queryError,
    message: queryError?.message || null,
    type: queryError ? ("primary" as const) : null,
  };

  return {
    data,
    isLoading,
    error,
  };
};

export default useGscDemographicData;