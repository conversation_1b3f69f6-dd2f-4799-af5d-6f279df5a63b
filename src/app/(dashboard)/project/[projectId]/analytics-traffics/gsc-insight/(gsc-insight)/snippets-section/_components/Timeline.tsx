import React from "react";

import { format } from "date-fns";
import { cn } from "@/utils/cn";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";

/* ================================== TYPES ================================= */
type TimelineProps = {
  dates: Date[];
  className?: string;
};
/* ========================================================================== */
const Timeline = ({ dates, className = "" }: TimelineProps) => {
  const { themeColor } = useAppThemeColor();
  if (!dates || dates.length === 0) {
    return null;
  }
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className={cn("w-full p-6 h-full ${className} ", className)}>
      <div className="relative h-full flex flex-col-reverse">
        <div className="absolute bottom-6 left-6 right-6 h-[1px] bg-gray-400" />

        <div className="flex justify-between items-center h-[50%] overflow-hidden relative">
          <div
            className="w-[100%] h-[1px] top-1 -translate-y-[.8px] absolute"
            style={{ backgroundColor: themeColor }}
          />
          {dates.map((date, index) => (
            <div
              key={index}
              className={cn(
                "flex flex-col items-center h-full justify-between",
                index === 0 && "items-start",
                index === dates.length - 1 && "items-end"
              )}
            >
              <div
                className="w-2 h-2 rounded-[1px] z-10 rotate-45 border-3 out border-white -translate-y-[0.5px]"
                style={{ backgroundColor: themeColor }}
              />
              <div className="mt-4 text-xs text-gray-500 font-medium whitespace-nowrap">
                {format(date, "MMM dd").toUpperCase()}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Timeline;
