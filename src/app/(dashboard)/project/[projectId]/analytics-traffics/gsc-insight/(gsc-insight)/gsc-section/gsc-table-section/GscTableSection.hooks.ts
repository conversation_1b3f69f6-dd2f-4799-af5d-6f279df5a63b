import httpService from "@/services/httpService";
import { TableDataRequest } from "../../../../_components/data-table/DataTable.types";
import { useQuery } from "@tanstack/react-query";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";

const useGscTable = ({
  page,
  filterBy,
}: {
  page: number;
  filterBy: string;
}) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates } = useDateRangeStore();
  const { startDate, endDate } = getFormattedDates();

  return useQuery({
    queryKey: ["gsc-table-data", projectId, page, filterBy, startDate, endDate],
    queryFn: async (): Promise<TableDataRequest> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const params: Record<string, string> = {
        page: page.toString(),
        filterBy,
      };

      if (startDate && endDate) {
        params.start_date = startDate;
        params.end_date = endDate;
      }

      const response = await httpService.get(
        `/api/project/GSC/table/${projectId}`,
        {
          params,
          useAuth: true,
        }
      );

      return response.data;
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    throwOnError: false,
  });
};

export default useGscTable;
