import { useState, useCallback, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  SnippetsApiResponse, 
  KeywordListItem, 
  KeywordData,
  snippetsApiService 
} from '../_services/snippetsApi';

interface UseSnippetsDataProps {
  projectId: string;
  startDate?: string;
  endDate?: string;
  autoFetch?: boolean;
}

interface UseSnippetsDataReturn {
  // Data
  data: SnippetsApiResponse | undefined;
  keywords: KeywordListItem[];
  filteredKeywords: KeywordListItem[];
  selectedKeywordData: KeywordData | undefined;
  aggregatedMetrics: ReturnType<typeof snippetsApiService.getAggregatedMetrics> | null;
  topUrls: ReturnType<typeof snippetsApiService.getTopPerformingUrls>;
  
  // State
  loading: boolean;
  error: string | null;
  selectedKeyword: string | null;
  searchQuery: string;
  
  // Actions
  setSelectedKeyword: (keyword: string | null) => void;
  setSearchQuery: (query: string) => void;
  refetch: () => void;
  clearError: () => void;
}

export const useSnippetsData = ({
  projectId,
  startDate,
  endDate,
  autoFetch = true,
}: UseSnippetsDataProps): UseSnippetsDataReturn => {
  // Local state for UI interactions
  const [selectedKeyword, setSelectedKeyword] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // React Query for data fetching
  const {
    data,
    isLoading: loading,
    error: queryError,
    refetch: queryRefetch,
  } = useQuery({
    queryKey: ['snippets-keywords', projectId, startDate, endDate],
    queryFn: async (): Promise<SnippetsApiResponse> => {
      if (!projectId) {
        throw new Error('Project ID is required');
      }

      return await snippetsApiService.fetchKeywordSnippets(
        projectId,
        startDate,
        endDate
      );
    },
    enabled: autoFetch && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  // Convert query error to string
  const error = queryError ? 
    (queryError instanceof Error ? queryError.message : 'Failed to fetch snippets data') 
    : null;

  // Derived data using useMemo for performance
  const keywords = useMemo(() => 
    data ? snippetsApiService.transformToKeywordList(data) : [], 
    [data]
  );
  
  const filteredKeywords = useMemo(() => 
    data ? snippetsApiService.searchKeywords(data, searchQuery) : [], 
    [data, searchQuery]
  );

  const selectedKeywordData = useMemo(() => 
    data && selectedKeyword ? snippetsApiService.getKeywordData(data, selectedKeyword) : undefined,
    [data, selectedKeyword]
  );

  const aggregatedMetrics = useMemo(() => 
    data ? snippetsApiService.getAggregatedMetrics(data) : null,
    [data]
  );

  const topUrls = useMemo(() => 
    data ? snippetsApiService.getTopPerformingUrls(data, 10) : [],
    [data]
  );

  // Actions
  const refetch = useCallback(() => {
    queryRefetch();
  }, [queryRefetch]);
  
  const clearError = useCallback(() => {
    // React Query handles errors, so we don't need to clear them manually
    // This is kept for API compatibility
  }, []);

  const handleSetSelectedKeyword = useCallback((keyword: string | null) => {
    setSelectedKeyword(prevSelected => prevSelected === keyword ? null : keyword);
  }, []);

  const handleSetSearchQuery = useCallback((query: string) => {
    setSearchQuery(query);
    // Reset selected keyword when searching if it doesn't match
    if (query.trim() && selectedKeyword) {
      const matchesSearch = selectedKeyword.toLowerCase().includes(query.toLowerCase());
      if (!matchesSearch) {
        setSelectedKeyword(null);
      }
    }
  }, [selectedKeyword]);

  // Reset selected keyword when data changes and keyword no longer exists
  const validSelectedKeyword = useMemo(() => {
    if (!data || !selectedKeyword) return selectedKeyword;
    return data.data.keywords[selectedKeyword] ? selectedKeyword : null;
  }, [data, selectedKeyword]);

  // Update selected keyword if it becomes invalid
  if (validSelectedKeyword !== selectedKeyword) {
    setSelectedKeyword(validSelectedKeyword);
  }

  return {
    // Data
    data,
    keywords,
    filteredKeywords,
    selectedKeywordData,
    aggregatedMetrics,
    topUrls,
    
    // State
    loading,
    error,
    selectedKeyword: validSelectedKeyword,
    searchQuery,
    
    // Actions
    setSelectedKeyword: handleSetSelectedKeyword,
    setSearchQuery: handleSetSearchQuery,
    refetch,
    clearError,
  };
};

// Additional hook for handling date range changes
export const useSnippetsDateRange = (
  projectId: string,
  initialStartDate?: string,
  initialEndDate?: string
) => {
  const [dateRange, setDateRange] = useState({
    startDate: initialStartDate,
    endDate: initialEndDate,
  });

  const snippetsData = useSnippetsData({
    projectId,
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
    autoFetch: true,
  });

  const updateDateRange = useCallback((startDate?: string, endDate?: string) => {
    setDateRange({ startDate, endDate });
  }, []);

  return {
    ...snippetsData,
    dateRange,
    updateDateRange,
  };
};