import httpService from "@/services/httpService";

// Types based on the API response structure
export interface KeywordSnippet {
  url: string;
  clicks: number;
  impressions: number;
  avg_position: number;
}

export interface KeywordData {
  avg_position: number;
  impressions: number;
  clicks: number;
  snippets: KeywordSnippet[];
}

export interface SnippetsApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    keywords: Record<string, KeywordData>;
    period: {
      start_date: string;
      end_date: string;
      days_count: number;
    };
  };
  last_sync: string;
}

export interface KeywordListItem {
  keyword: string;
  avg_position: number;
  impressions: number;
  clicks: number;
}

// Service class for handling snippets API operations
export class SnippetsApiService {
  /**
   * Fetch keyword snippets data from the API
   * @param projectId - The project ID
   * @param startDate - Optional start date filter (YYYY-MM-DD)
   * @param endDate - Optional end date filter (YYYY-MM-DD)
   * @returns Promise with the API response
   */
  async fetchKeywordSnippets(
    projectId: string,
    startDate?: string,
    endDate?: string
  ): Promise<SnippetsApiResponse> {
    try {
      // Build parameters object
      const params: Record<string, string> = {};
      if (startDate) params.start_date = startDate;
      if (endDate) params.end_date = endDate;

      // Use httpService with proper authentication and parameter handling
      const response = await httpService.get(
        `/api/project/GSC/snippet/keywords/${projectId}/`,
        {
          params: Object.keys(params).length > 0 ? params : undefined,
          useAuth: true,
        }
      );

      // Validate response
      if (response.data.status !== "success") {
        throw new Error("API returned error status");
      }

      return response.data;
    } catch (error) {
      console.error("Error fetching keyword snippets:", error);
      throw error;
    }
  }

  /**
   * Transform API response to keyword list format for the UI
   * @param apiResponse - The API response
   * @returns Array of keyword list items sorted by average position (ascending - lowest position first)
   */
  transformToKeywordList(apiResponse: SnippetsApiResponse): KeywordListItem[] {
    return Object.entries(apiResponse.data.keywords)
      .map(([keyword, data]) => ({
        keyword,
        avg_position: data.avg_position,
        impressions: data.impressions,
        clicks: data.clicks,
      }))
      .sort((a, b) => a.avg_position - b.avg_position);
  }

  /**
   * Get specific keyword data for SERP analysis
   * @param apiResponse - The API response
   * @param keyword - The selected keyword
   * @returns Keyword data or undefined if not found
   */
  getKeywordData(
    apiResponse: SnippetsApiResponse,
    keyword: string
  ): KeywordData | undefined {
    return apiResponse.data.keywords[keyword];
  }

  /**
   * Get aggregated metrics for all keywords
   * @param apiResponse - The API response
   * @returns Aggregated metrics
   */
  getAggregatedMetrics(apiResponse: SnippetsApiResponse) {
    const keywords = Object.values(apiResponse.data.keywords);

    const totalImpressions = keywords.reduce(
      (sum, keyword) => sum + keyword.impressions,
      0
    );
    const totalClicks = keywords.reduce(
      (sum, keyword) => sum + keyword.clicks,
      0
    );
    const avgPosition =
      keywords.length > 0
        ? keywords.reduce((sum, keyword) => sum + keyword.avg_position, 0) /
          keywords.length
        : 0;

    return {
      totalKeywords: keywords.length,
      totalImpressions,
      totalClicks,
      avgPosition: Math.round(avgPosition * 10) / 10,
      ctr: totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0,
    };
  }

  /**
   * Get top performing URLs across all keywords
   * @param apiResponse - The API response
   * @param limit - Number of top URLs to return
   * @returns Array of top performing URLs
   */
  getTopPerformingUrls(apiResponse: SnippetsApiResponse, limit: number = 10) {
    const urlMetrics: Record<
      string,
      {
        url: string;
        totalClicks: number;
        totalImpressions: number;
        bestPosition: number;
        keywords: string[];
      }
    > = {};

    // Aggregate metrics for each URL across all keywords
    Object.entries(apiResponse.data.keywords).forEach(
      ([keyword, keywordData]) => {
        keywordData.snippets.forEach((snippet) => {
          if (!urlMetrics[snippet.url]) {
            urlMetrics[snippet.url] = {
              url: snippet.url,
              totalClicks: 0,
              totalImpressions: 0,
              bestPosition: snippet.avg_position,
              keywords: [],
            };
          }

          const urlData = urlMetrics[snippet.url];
          urlData.totalClicks += snippet.clicks;
          urlData.totalImpressions += snippet.impressions;
          urlData.bestPosition = Math.min(
            urlData.bestPosition,
            snippet.avg_position
          );
          urlData.keywords.push(keyword);
        });
      }
    );

    // Sort by total clicks and return top performers
    return Object.values(urlMetrics)
      .sort((a, b) => b.totalClicks - a.totalClicks)
      .slice(0, limit);
  }

  /**
   * Search keywords by query
   * @param apiResponse - The API response
   * @param query - Search query
   * @returns Filtered keyword list
   */
  searchKeywords(
    apiResponse: SnippetsApiResponse,
    query: string
  ): KeywordListItem[] {
    const allKeywords = this.transformToKeywordList(apiResponse);

    if (!query.trim()) {
      return allKeywords;
    }

    const lowercaseQuery = query.toLowerCase();
    return allKeywords.filter((keyword) =>
      keyword.keyword.toLowerCase().includes(lowercaseQuery)
    );
  }
}

// Create a singleton instance
export const snippetsApiService = new SnippetsApiService();

// Hook for using the snippets API service
export const useSnippetsApi = () => {
  return snippetsApiService;
};
