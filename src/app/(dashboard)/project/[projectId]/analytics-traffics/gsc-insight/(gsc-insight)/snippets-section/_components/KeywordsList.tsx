import React, { useState, useMemo } from "react";
import SearchInput from "./SearchInput";
import HighlightText from "./HighlightText";

interface KeywordItem {
  keyword: string;
  avg_position: number;
  impressions: number;
  clicks: number;
}

interface KeywordsListProps {
  keywords: KeywordItem[];
  selectedKeyword: string | null;
  onKeywordClick: (keyword: string) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

const KeywordsList = ({
  keywords,
  selectedKeyword,
  onKeywordClick,
  searchQuery,
  onSearchChange,
}: KeywordsListProps) => {
  const [debouncedQuery, setDebouncedQuery] = useState("");

  // Fast client-side filtering with useMemo
  const filteredKeywords = useMemo(() => {
    if (!debouncedQuery.trim()) return keywords;
    
    const query = debouncedQuery.toLowerCase();
    return keywords.filter(item => 
      item.keyword.toLowerCase().includes(query)
    );
  }, [keywords, debouncedQuery]);

  const showNoResults = debouncedQuery.trim() && filteredKeywords.length === 0;

  return (
    <div className="space-y-4">
      <SearchInput 
        value={searchQuery}
        onChange={onSearchChange}
        onDebouncedChange={setDebouncedQuery}
      />
      
      <div className="flex justify-between text-secondary text-xs font-semibold pr-6">
        <span>KEYWORDS ({filteredKeywords.length})</span>
        <span>AVG POSITION</span>
      </div>
      
      <div className="space-y-2 max-h-80 overflow-y-auto pr-2">
        {showNoResults ? (
          <div className="text-center py-8 text-secondary/60">
            <div className="text-lg mb-2">🔍</div>
            <div className="text-sm">No keywords found matching "{debouncedQuery}"</div>
          </div>
        ) : (
          filteredKeywords.map((item, index) => (
            <div
              key={item.keyword}
              className={`
                border-2 p-4 rounded-lg flex items-center justify-between text-sm cursor-pointer 
                transition-colors duration-150 hover:bg-primary/5
                ${selectedKeyword === item.keyword 
                  ? "border-primary bg-primary/10" 
                  : "border-[#F4F4F4] hover:border-primary/30"
                }
                ${index % 2 === 0 && selectedKeyword !== item.keyword ? "bg-[#F4F4F4]/50" : ""}
              `}
              onClick={() => onKeywordClick(item.keyword)}
            >
              <HighlightText 
                text={item.keyword}
                searchQuery={debouncedQuery}
                className="truncate flex-1 mr-2"
              />
              <span className="font-medium">
                {Math.round(item.avg_position)}
              </span>
            </div>
          ))
        )}
      </div>
      
      {/* Search results summary */}
      {debouncedQuery.trim() && filteredKeywords.length > 0 && (
        <div className="text-xs text-secondary/60 text-center py-2 border-t border-gray-100">
          Found {filteredKeywords.length} keyword{filteredKeywords.length !== 1 ? 's' : ''} matching "{debouncedQuery}"
        </div>
      )}
    </div>
  );
};

export default KeywordsList;
