import React from "react";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  message?: string;
}

const LoadingSpinner = ({ size = "md", message }: LoadingSpinnerProps) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12",
  };

  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <div className="relative">
        <div
          className={`${sizeClasses[size]} border-4 border-secondary/20 border-t-secondary rounded-full animate-spin`}
        />
      </div>
      {message && (
        <div className="text-secondary/60 text-sm text-center">
          {message}
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;