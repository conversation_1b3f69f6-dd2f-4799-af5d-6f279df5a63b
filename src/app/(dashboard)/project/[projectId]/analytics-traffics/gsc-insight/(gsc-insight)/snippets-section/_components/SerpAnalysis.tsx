import React from "react";
import * as Tooltip from "@radix-ui/react-tooltip";
import { useParams } from "next/navigation";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import { useProjectDetails } from "@/hooks/useProjectDetails";
import { useProjectContext } from "@/contexts/ProjectContext";

interface KeywordSnippet {
  url: string;
  clicks: number;
  impressions: number;
  avg_position: number;
}

interface KeywordData {
  avg_position: number;
  impressions: number;
  clicks: number;
  snippets: KeywordSnippet[];
}

interface SerpAnalysisProps {
  selectedKeyword: string | null;
  keywordData: KeywordData | undefined;
}

// Helper function to extract domain name from URL
const getDomainName = (url: string): string => {
  try {
    const domain = new URL(url).hostname.replace("www.", "");
    return (
      domain.split(".")[0].charAt(0).toUpperCase() +
      domain.split(".")[0].slice(1)
    );
  } catch {
    return "Website";
  }
};

const SerpAnalysis = ({ selectedKeyword, keywordData }: SerpAnalysisProps) => {
  const params = useParams();
  const projectId = params?.projectId as string;
  const { projectName } = useProjectContext();
  // Get project details including favicon and name
  const { project } = useProjectDetails({
    projectId: projectId || null,
    enabled: !!projectId,
  });

  // Create project title with favicon for URL Performance section only
  const getProjectTitle = () => {
    if (!project) return "URL Performance";

    return (
      <div className="flex items-center gap-3">
        {project.favicon_url && (
          <img
            src={project.favicon_url}
            alt={`${project.project_name} favicon`}
            className="w-6 h-6 rounded-sm flex-shrink-0"
            onError={(e) => {
              // Hide favicon if it fails to load
              e.currentTarget.style.display = "none";
            }}
          />
        )}
        <span className="text-base font-semibold">{project.project_name}</span>
      </div>
    );
  };

  // Show placeholder when no keyword is selected
  if (!selectedKeyword || !keywordData) {
    return (
      <Card className="space-y-6 rounded-3xl">
        <div className="space-y-2">
          <div className="flex flex-row justify-between items-center">
            <Title>{projectName} Snippets Analysis</Title>
          </div>
        </div>

        <div className="text-center py-12">
          <div className="text-secondary/60 text-sm">
            Select a keyword to view snippets analysis
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="space-y-6 rounded-3xl">
      <div className="space-y-2">
        <div className="flex flex-row justify-between items-center">
          <Title>Snippets for "{selectedKeyword}"</Title>
          <div className="px-3 py-1 bg-secondary/10 text-secondary rounded-full text-xs font-medium">
            {keywordData.snippets.length} URLs
          </div>
        </div>
      </div>

      {/* Overall Metrics */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="  bg-primary/8 rounded-lg p-4">
          <div className="text-xs font-bold text-primary uppercase tracking-wide mb-1">
            Avg Position
          </div>
          <div className="text-xl font-semibold text-secondary">
            {keywordData.avg_position.toFixed(1)}
          </div>
        </div>
        <div className="bg-primary/8 rounded-lg p-4">
          <div className="text-xs font-bold text-primary uppercase tracking-wide mb-1">
            Total Impressions
          </div>
          <div className="text-xl font-semibold text-secondary">
            {keywordData.impressions.toLocaleString()}
          </div>
        </div>
        <div className="bg-primary/8 rounded-lg p-4">
          <div className="text-xs font-bold text-primary uppercase tracking-wide mb-1">
            Total Clicks
          </div>
          <div className="text-xl font-semibold text-secondary">
            {keywordData.clicks.toLocaleString()}
          </div>
        </div>
      </div>

      {/* URL Performance */}
      <div className="space-y-5">
        <h3 className="text-sm font-semibold text-secondary uppercase tracking-wide">
          {getProjectTitle()}
        </h3>
        <Tooltip.Provider delayDuration={150}>
          <div className="space-y-4">
            {keywordData.snippets.map((snippet, index) => (
              <div
                key={index}
                className="border border-secondary/20 rounded-md   p-4 bg-secondary/2"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 max-w-[75%] space-y-2 min-w-0">
                    {/* URL and Domain */}
                    <div className="flex items-center space-x-3 min-w-0">
                      <div className="flex w-full h-full items-center">
                        <Tooltip.Root delayDuration={150}>
                          <Tooltip.Trigger asChild>
                            <a
                              href={snippet.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-secondary/95 block w-full font-bold hover:text-secondary/80 tracking-wide hover:underline  text-sm truncate"
                            >
                              {snippet.url}
                            </a>
                          </Tooltip.Trigger>
                          <Tooltip.Portal>
                            <Tooltip.Content
                              side="top"
                              align="start"
                              sideOffset={6}
                              avoidCollisions
                              collisionPadding={8}
                              className="z-[9999] rounded-md bg-gray-800/95 text-white text-sm px-2 py-1 shadow-lg backdrop-blur-sm max-w-[80vw]"
                            >
                              {snippet.url}
                              <Tooltip.Arrow className="fill-gray-800/95" />
                            </Tooltip.Content>
                          </Tooltip.Portal>
                        </Tooltip.Root>
                      </div>
                    </div>
                  </div>

                  {/* Metrics */}
                  <div className="ml-4 flex space-x-6 text-right">
                    <div>
                      <div className="text-xs text-secondary/70 mb-1">
                        Impressions
                      </div>
                      <div className="text-sm font-semibold text-secondary">
                        {snippet.impressions.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-secondary/70 mb-1">
                        Clicks
                      </div>
                      <div className="text-sm font-semibold text-secondary">
                        {snippet.clicks.toLocaleString()}
                      </div>
                    </div>{" "}
                    <div>
                      <div className="text-xs text-secondary/70 mb-1">
                        Position
                      </div>
                      <div className="text-sm font-semibold text-secondary">
                        {snippet.avg_position.toFixed(1)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Tooltip.Provider>
      </div>
    </Card>
  );
};

export default SerpAnalysis;
