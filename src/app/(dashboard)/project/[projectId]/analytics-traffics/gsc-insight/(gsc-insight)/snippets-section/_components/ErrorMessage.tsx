import React from "react";

interface ErrorMessageProps {
  message: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  showRetry?: boolean;
  showDismiss?: boolean;
}

const ErrorMessage = ({ 
  message, 
  onRetry, 
  onDismiss, 
  showRetry = true, 
  showDismiss = false 
}: ErrorMessageProps) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      {/* Error Icon */}
      <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
        <svg
          className="w-6 h-6 text-red-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      </div>

      {/* Error Message */}
      <div className="text-center space-y-2">
        <h3 className="text-lg font-semibold text-secondary">
          Something went wrong
        </h3>
        <p className="text-secondary/60 text-sm max-w-md">
          {message}
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3">
        {showRetry && onRetry && (
          <button
            onClick={onRetry}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium"
          >
            Try Again
          </button>
        )}
        {showDismiss && onDismiss && (
          <button
            onClick={onDismiss}
            className="px-4 py-2 border border-secondary/20 text-secondary rounded-lg hover:bg-secondary/5 transition-colors text-sm font-medium"
          >
            Dismiss
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorMessage;