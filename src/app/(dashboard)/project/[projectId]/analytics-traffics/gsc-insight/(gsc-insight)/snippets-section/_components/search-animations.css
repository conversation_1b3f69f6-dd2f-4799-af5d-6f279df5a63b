/* Search Animation Styles */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes highlightPulse {
  0%, 100% {
    background-color: rgb(254 240 138);
    transform: scale(1);
  }
  50% {
    background-color: rgb(253 224 71);
    transform: scale(1.05);
  }
}

.search-result-enter {
  animation: fadeInUp 0.3s ease-out forwards;
}

.search-result-highlight {
  animation: highlightPulse 1s ease-in-out;
}

.search-loading-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(145, 74, 196, 0.1),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.search-focus-ring {
  position: relative;
}

.search-focus-ring::after {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(45deg, #914AC4, #B565D8, #914AC4);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-focus-ring:focus-within::after {
  opacity: 1;
}

/* Staggered animation for search results */
.search-results-container .search-result-item:nth-child(1) { animation-delay: 0ms; }
.search-results-container .search-result-item:nth-child(2) { animation-delay: 50ms; }
.search-results-container .search-result-item:nth-child(3) { animation-delay: 100ms; }
.search-results-container .search-result-item:nth-child(4) { animation-delay: 150ms; }
.search-results-container .search-result-item:nth-child(5) { animation-delay: 200ms; }
.search-results-container .search-result-item:nth-child(6) { animation-delay: 250ms; }
.search-results-container .search-result-item:nth-child(7) { animation-delay: 300ms; }
.search-results-container .search-result-item:nth-child(8) { animation-delay: 350ms; }
.search-results-container .search-result-item:nth-child(9) { animation-delay: 400ms; }
.search-results-container .search-result-item:nth-child(10) { animation-delay: 450ms; }

/* Smooth transitions for keyword items */
.keyword-item-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.keyword-item-transition:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(145, 74, 196, 0.15);
}

.keyword-item-selected {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 0 4px 15px rgba(145, 74, 196, 0.2);
}

/* Loading skeleton animation */
.search-skeleton {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Highlight text animation */
.highlight-text {
  background: linear-gradient(
    45deg,
    rgb(254 240 138),
    rgb(253 224 71),
    rgb(254 240 138)
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 3px;
  padding: 1px 3px;
  font-weight: 600;
  color: rgb(133 77 14);
}