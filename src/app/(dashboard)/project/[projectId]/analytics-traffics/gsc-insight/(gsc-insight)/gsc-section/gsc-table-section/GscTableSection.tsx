"use client";
import React, { useRef, useState } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "../../../../_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
import useGscTable from "./GscTableSection.hooks";
import NoData from "../../../../analytic-insight/_components/NoData";

const GscTableSection = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);
  const badges = [
    "Web Pages",
    "Images",
    "Videos",
    "Queries",
    "Countries",
    "Devices",
    "Search Appearance",
  ];
  const [filterBy, setFilterBy] = useState(badges ? badges[0] : "");

  const { data, isLoading, error } = useGscTable({
    page,
    filterBy,
  });

  const lastDataRef = useRef(data);

  if (data) {
    lastDataRef.current = data;
  }

  const stableData = data ?? lastDataRef.current;

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Show no data for errors or when no data is available
  if (error || (!data?.tableData && !isLoading)) {
    return (
      <Card className="w-full space-y-10 min-h-[520px] flex flex-col justify-between">
        <motion.div
          key="nodata"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <DataTable
            title={"All Reports-GSC"}
            tableData={{ tableHeadings: [], tableBody: [] }}
            isLoading={false}
            badges={badges}
            selectedItem={filterBy}
            setSelectedItem={setFilterBy}
          />
        </motion.div>
        <div className="flex items-center justify-center py-8">
          <p className="text-gray-500">No data</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full space-y-10 min-h-[520px] flex flex-col justify-between">
      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <DataTable
          title={"All Reports-GSC"}
          tableData={data?.tableData || { tableHeadings: [], tableBody: [] }}
          isLoading={isLoading}
          badges={badges}
          selectedItem={filterBy}
          setSelectedItem={setFilterBy}
        />
      </motion.div>
      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={stableData?.pagination.initialPage || 1}
        onPageChange={setPage}
      />
    </Card>
  );
};

export default GscTableSection;
