import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";

// Types for the API response
export interface AveragePositionDailyMetric {
  date: string;
  avg_position: number;
}

export interface AveragePositionKeywordData {
  avg_position: number;
  impressions: number;
  clicks: number;
  daily_metrics: AveragePositionDailyMetric[];
}

export interface AveragePositionApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    totals: {
      avg_position: number;
      keywords_count: number;
      total_impressions: number;
      total_clicks: number;
      ctr: number;
    };
    daily_metrics: AveragePositionDailyMetric[];
    keywords: Record<string, AveragePositionKeywordData>;
  };
  period: {
    start_date: string;
    end_date: string;
    days_count: number;
  };
  last_sync: string;
}

// Normalize a date value that may come as number (YYYYMMDD) or string into YYYY-MM-DD
const normalizeDate = (value: string | number): string => {
  const raw = typeof value === "number" ? String(value) : value;
  if (!raw) return "";
  // Already ISO-like
  if (raw.includes("-")) return raw;
  // Expect YYYYMMDD
  if (raw.length === 8) {
    const year = raw.slice(0, 4);
    const month = raw.slice(4, 6);
    const day = raw.slice(6, 8);
    return `${year}-${month}-${day}`;
  }
  return raw;
};

// Ensure keyword daily metrics use avg_position and normalized date
const normalizeKeywordDailyMetrics = (
  metrics: Array<any>
): AveragePositionDailyMetric[] => {
  if (!Array.isArray(metrics)) return [];
  return metrics
    .map((m) => ({
      date: normalizeDate((m as any).date as any),
      avg_position: Number((m as any).avg_position ?? (m as any).position ?? 0),
    }))
    .filter((m) => !Number.isNaN(m.avg_position) && !!m.date)
    .sort((a, b) => (a.date < b.date ? -1 : a.date > b.date ? 1 : 0));
};

// Ensure top-level daily metrics have normalized date and numbers
const normalizeTopLevelDailyMetrics = (
  metrics: Array<any>
): AveragePositionDailyMetric[] => {
  if (!Array.isArray(metrics)) return [];
  return metrics
    .map((m) => ({
      date: normalizeDate((m as any).date as any),
      avg_position: Number((m as any).avg_position ?? (m as any).position ?? 0),
    }))
    .filter((m) => !Number.isNaN(m.avg_position) && !!m.date)
    .sort((a, b) => (a.date < b.date ? -1 : a.date > b.date ? 1 : 0));
};

// Helper function to calculate date ranges based on period
const getDateRange = (period: string) => {
  const today = new Date();
  let startDate: Date;
  let endDate = new Date(today);

  switch (period) {
    case "Last week":
      // Last 7 days
      startDate = new Date(today);
      startDate.setDate(today.getDate() - 7);
      break;
    case "Month":
      // Last 30 days
      startDate = new Date(today);
      startDate.setDate(today.getDate() - 30);
      break;
    case "3 Month":
      // Last 90 days
      startDate = new Date(today);
      startDate.setDate(today.getDate() - 90);
      break;
    default:
      // Default to last week
      startDate = new Date(today);
      startDate.setDate(today.getDate() - 7);
  }

  // Format dates as YYYY-MM-DD
  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  return {
    start_date: formatDate(startDate),
    end_date: formatDate(endDate),
  };
};

export const useAveragePositionData = (period: string = "Last week") => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["average-position", projectId, period],
    queryFn: async (): Promise<AveragePositionApiResponse> => {
      if (!isValidProjectId) {
        throw new Error("Invalid project ID");
      }

      const { start_date, end_date } = getDateRange(period);

      const { data } = await httpService.get<AveragePositionApiResponse>(
        `/api/project/GSC/snippet/averageposition/${projectId}/?start_date=${start_date}&end_date=${end_date}`,
        { useAuth: true }
      );

      // Runtime-normalize response to protect UI from schema drifts
      const normalized: AveragePositionApiResponse = {
        ...data,
        data: {
          ...data.data,
          daily_metrics: normalizeTopLevelDailyMetrics(
            (data as any)?.data?.daily_metrics ?? []
          ),
          keywords: Object.fromEntries(
            Object.entries((data as any)?.data?.keywords ?? {}).map(
              ([keyword, kwData]: [string, any]) => [
                keyword,
                {
                  avg_position: Number(kwData?.avg_position ?? 0),
                  impressions: Number(kwData?.impressions ?? 0),
                  clicks: Number(kwData?.clicks ?? 0),
                  daily_metrics: normalizeKeywordDailyMetrics(
                    kwData?.daily_metrics ?? []
                  ),
                } as AveragePositionKeywordData,
              ]
            )
          ),
        },
        period: {
          start_date: normalizeDate((data as any)?.period?.start_date ?? ""),
          end_date: normalizeDate((data as any)?.period?.end_date ?? ""),
          days_count: Number((data as any)?.period?.days_count ?? 0),
        },
        last_sync: data.last_sync,
      };

      return normalized;
    },
    enabled: isValidProjectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};