import { TableData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";
import httpService from "@/services/httpService";

export type DemographicType =
  | "web-pages"
  | "images"
  | "videos"
  | "queries"
  | "countries"
  | "devices"
  | "search-appears";

export interface GscDemographicParams {
  projectId: string;
  demographicType: DemographicType;
  startDate: string;
  endDate: string;
  page?: number;
  limit?: number;
}

export interface GscDemographicComparisonParams {
  projectId: string;
  demographicType: DemographicType;
  startDate: string;
  endDate: string;
  comparisonStartDate: string;
  comparisonEndDate: string;
  page?: number;
  limit?: number;
}

export interface GscDemographicResponse {
  status: string;
  project_id: string;
  period: {
    start_date: string;
    end_date: string;
    days_count: number;
  };
  data: {
    [key: string]: {
      data: {
        [key: string]: {
          CLICKS: number;
          IMPRESSION: number;
          CTR: number;
          "AVG POSITION": number;
          "BOUNCE RATE": number;
          "EXIT RATE": number;
        };
      };
    };
  };
}

export interface ProcessedGscDemographicData {
  tableData: TableData;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export const fetchGscDemographicData = async (
  params: GscDemographicParams
): Promise<GscDemographicResponse> => {
  const { projectId, demographicType, startDate, endDate, page, limit } =
    params;

  const apiParams: Record<string, string> = {
    start_date: startDate,
    end_date: endDate,
  };

  if (page) {
    apiParams.page = page.toString();
  }

  if (limit) {
    apiParams.limit = limit.toString();
  }

  // Map demographic type to correct API endpoint
  const getApiEndpoint = (type: DemographicType): string => {
    switch (type) {
      case "search-appears":
        return "search-appearance";
      default:
        return type;
    }
  };

  const apiEndpoint = getApiEndpoint(demographicType);

  const response = await httpService.get(
    `/api/project/GSC/demographic/${apiEndpoint}/${projectId}`,
    {
      params: apiParams,
      useAuth: true,
    }
  );

  return response.data;
};

export const fetchGscDemographicComparisonData = async (
  params: GscDemographicComparisonParams
): Promise<{ current: GscDemographicResponse; comparison: GscDemographicResponse }> => {
  const { projectId, demographicType, startDate, endDate, comparisonStartDate, comparisonEndDate, page, limit } = params;

  const apiParams: Record<string, string> = {};

  if (page) {
    apiParams.page = page.toString();
  }

  if (limit) {
    apiParams.limit = limit.toString();
  }

  // Map demographic type to correct API endpoint
  const getApiEndpoint = (type: DemographicType): string => {
    switch (type) {
      case "search-appears":
        return "search-appearance";
      default:
        return type;
    }
  };

  const apiEndpoint = getApiEndpoint(demographicType);

  // Fetch current period data
  const currentResponse = await httpService.get(
    `/api/project/GSC/demographic/${apiEndpoint}/${projectId}`,
    {
      params: {
        ...apiParams,
        start_date: startDate,
        end_date: endDate,
      },
      useAuth: true,
    }
  );

  // Fetch comparison period data
  const comparisonResponse = await httpService.get(
    `/api/project/GSC/demographic/${apiEndpoint}/${projectId}`,
    {
      params: {
        ...apiParams,
        start_date: comparisonStartDate,
        end_date: comparisonEndDate,
      },
      useAuth: true,
    }
  );

  return {
    current: currentResponse.data,
    comparison: comparisonResponse.data,
  };
};

export const processGscDemographicData = (
  response: GscDemographicResponse,
  demographicType: DemographicType,
  page: number = 1,
  itemsPerPage: number = 10
): ProcessedGscDemographicData => {
  // Get the data key based on demographic type
  const dataKey = Object.keys(response.data)[0];
  const rawData = response.data[dataKey]?.data || {};

  // Convert raw data to table format
  const entries = Object.entries(rawData);

  // Sort entries by impression in descending order for queries (keywords)
  if (demographicType === "queries") {
    entries.sort((a, b) => b[1].IMPRESSION - a[1].IMPRESSION);
  }

  // Calculate pagination
  const totalItems = entries.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedEntries = entries.slice(startIndex, endIndex);

  // Define table headings based on demographic type
  const getTableHeadings = (type: DemographicType): string[] => {
    switch (type) {
      case "web-pages":
        return [
          "Page",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "images":
        return [
          "Image",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "videos":
        return [
          "Video",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "queries":
        return [
          "Query",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "countries":
        return [
          "Country",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "devices":
        return [
          "Device",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "search-appears":
        return [
          "Search Appear",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      default:
        return [
          "Item",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
    }
  };

  const tableHeadings = getTableHeadings(demographicType);

  const tableBody = paginatedEntries.map(([key, metrics]) => [
    { value: key, growth: "" }, // Item name (page, image, etc.)
    { value: metrics.CLICKS.toString(), growth: "" },
    { value: metrics.IMPRESSION.toString(), growth: "" },
    { value: metrics.CTR.toFixed(1), growth: "" },
    { value: metrics["AVG POSITION"].toFixed(1), growth: "" },
    { value: metrics["BOUNCE RATE"].toString(), growth: "" },
    { value: metrics["EXIT RATE"].toString(), growth: "" },
  ]);

  return {
    tableData: {
      tableHeadings,
      tableBody,
    },
    pagination: {
      currentPage: page,
      totalPages,
      totalItems,
      itemsPerPage,
    },
  };
};

export const processGscDemographicComparisonData = (
  currentResponse: GscDemographicResponse,
  comparisonResponse: GscDemographicResponse,
  demographicType: DemographicType,
  page: number = 1,
  itemsPerPage: number = 10
): ProcessedGscDemographicData => {
  // Get the data key based on demographic type
  const currentDataKey = Object.keys(currentResponse.data)[0];
  const comparisonDataKey = Object.keys(comparisonResponse.data)[0];
  
  const currentRawData = currentResponse.data[currentDataKey]?.data || {};
  const comparisonRawData = comparisonResponse.data[comparisonDataKey]?.data || {};

  // Convert raw data to table format with comparison
  const currentEntries = Object.entries(currentRawData);

  // Sort entries by impression in descending order for queries (keywords)
  if (demographicType === "queries") {
    currentEntries.sort((a, b) => b[1].IMPRESSION - a[1].IMPRESSION);
  }

  // Calculate pagination based on current data
  const totalItems = currentEntries.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedEntries = currentEntries.slice(startIndex, endIndex);

  // Define table headings based on demographic type
  const getTableHeadings = (type: DemographicType): string[] => {
    switch (type) {
      case "web-pages":
        return [
          "Page",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "images":
        return [
          "Image",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "videos":
        return [
          "Video",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "queries":
        return [
          "Query",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "countries":
        return [
          "Country",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "devices":
        return [
          "Device",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      case "search-appears":
        return [
          "Search Appear",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
      default:
        return [
          "Item",
          "Clicks",
          "Impressions",
          "CTR (%)",
          "Avg Position",
          "Bounce Rate (%)",
          "Exit Rate (%)",
        ];
    }
  };

  const tableHeadings = getTableHeadings(demographicType);

  // Helper function to calculate growth percentage
  const calculateGrowth = (current: number, previous: number): string => {
    if (previous === 0) {
      return current > 0 ? "+100%" : "";
    }
    const growth = ((current - previous) / previous) * 100;
    if (Math.abs(growth) < 0.1) return "";
    return growth > 0 ? `+${growth.toFixed(1)}%` : `${growth.toFixed(1)}%`;
  };

  const tableBody = paginatedEntries.map(([key, currentMetrics]) => {
    const comparisonMetrics = comparisonRawData[key];
    
    return [
      { value: key, growth: "" }, // Item name (page, image, etc.)
      { 
        value: currentMetrics.CLICKS.toString(), 
        growth: comparisonMetrics ? calculateGrowth(currentMetrics.CLICKS, comparisonMetrics.CLICKS) : ""
      },
      { 
        value: currentMetrics.IMPRESSION.toString(), 
        growth: comparisonMetrics ? calculateGrowth(currentMetrics.IMPRESSION, comparisonMetrics.IMPRESSION) : ""
      },
      { 
        value: currentMetrics.CTR.toFixed(1), 
        growth: comparisonMetrics ? calculateGrowth(currentMetrics.CTR, comparisonMetrics.CTR) : ""
      },
      { 
        value: currentMetrics["AVG POSITION"].toFixed(1), 
        growth: comparisonMetrics ? calculateGrowth(comparisonMetrics["AVG POSITION"], currentMetrics["AVG POSITION"]) : "" // Inverted for position (lower is better)
      },
      { 
        value: currentMetrics["BOUNCE RATE"].toString(), 
        growth: comparisonMetrics ? calculateGrowth(comparisonMetrics["BOUNCE RATE"], currentMetrics["BOUNCE RATE"]) : "" // Inverted for bounce rate (lower is better)
      },
      { 
        value: currentMetrics["EXIT RATE"].toString(), 
        growth: comparisonMetrics ? calculateGrowth(comparisonMetrics["EXIT RATE"], currentMetrics["EXIT RATE"]) : "" // Inverted for exit rate (lower is better)
      },
    ];
  });

  return {
    tableData: {
      tableHeadings,
      tableBody,
    },
    pagination: {
      currentPage: page,
      totalPages,
      totalItems,
      itemsPerPage,
    },
  };
};
