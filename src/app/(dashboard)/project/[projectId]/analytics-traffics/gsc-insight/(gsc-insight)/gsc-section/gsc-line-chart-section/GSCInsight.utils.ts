import {
  GSCAPIResponse,
  GSCInsightType,
  CardsData,
} from "./GSCInsight.types";

/**
 * Formats date from API format (YYYYMMDD) to display format (MMM DD)
 */
const formatDateForChart = (dateString: string): string => {
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);

  const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Calculates growth percentage between two values
 */
const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) return current > 0 ? "+100%" : "0%";
  const growth = ((current - previous) / previous) * 100;
  return growth >= 0 ? `+${growth.toFixed(1)}%` : `${growth.toFixed(1)}%`;
};

/**
 * Converts string values to numbers for calculations
 */
const parseMetricValue = (value: string): number => {
  // Handle values like "1498k" or plain numbers
  if (value.includes("k")) {
    return parseFloat(value.replace("k", "")) * 1000;
  }
  return parseFloat(value) || 0;
};

/**
 * Processes GSC API responses into chart-compatible format
 */
export const processGSCInsightData = (
  primaryData: GSCAPIResponse,
  comparisonData?: GSCAPIResponse,
  dataType: string = "web-pages"
): GSCInsightType => {
  const metrics = [
    "Clicks",
    "CTR",
    "Search_Visibility",
    "Impression",
    "Avg_Position",
  ];

  // Get the data for the specific type (web-pages, images, etc.)
  const primaryDataKey = Object.keys(primaryData.data)[0]; // Get the first (and likely only) key
  const primaryMetrics = primaryData.data[primaryDataKey].daily_metrics;
  const primaryTotals = primaryData.data[primaryDataKey].totals;

  // Process comparison data if available
  let comparisonMetrics: any[] = [];
  let comparisonTotals: any = null;
  
  if (comparisonData) {
    const comparisonDataKey = Object.keys(comparisonData.data)[0];
    comparisonMetrics = comparisonData.data[comparisonDataKey].daily_metrics || [];
    comparisonTotals = comparisonData.data[comparisonDataKey].totals;
  }

  // Create line chart data
  const lineChartData: Record<string, string | number>[] = [];

  // Process primary data points
  primaryMetrics.forEach((metric, index) => {
    const chartPoint: Record<string, string | number> = {
      name: formatDateForChart(metric.date),
      // Raw dates for tooltip to distinguish current vs comparison
      primaryDate: metric.date,
    };

    // Add primary metrics (normal lines)
    metrics.forEach((metricName) => {
      const rawValue = metric[metricName as keyof typeof metric];
      const value = parseMetricValue(rawValue);
      chartPoint[metricName] = value;

      if (metricName === "Search_Visibility") {
        console.log(`Search_Visibility processing for ${dataType}:`, {
          date: metric.date,
          rawValue,
          parsedValue: value,
          isValid: !isNaN(value) && isFinite(value),
        });
      }
    });

    // Add comparison data if available (dotted lines) - align by index, not date
    if (comparisonMetrics.length > 0 && comparisonMetrics[index]) {
      const comparisonMetric = comparisonMetrics[index];
      // Attach raw comparison date for tooltip
      (chartPoint as any).comparisonDate = comparisonMetric.date;
      metrics.forEach((metricName) => {
        chartPoint[`dotted_${metricName}`] = parseMetricValue(
          comparisonMetric[metricName as keyof typeof comparisonMetric]
        );
      });
    }

    lineChartData.push(chartPoint);
  });

  // Create cards data with growth calculations
  const cardsData: Record<string, CardsData> = {};

  metrics.forEach((metricName) => {
    const primaryValue = parseMetricValue(
      primaryTotals[metricName as keyof typeof primaryTotals]
    );
    const comparisonValue = comparisonTotals
      ? parseMetricValue(
          comparisonTotals[metricName as keyof typeof comparisonTotals]
        )
      : 0;

    cardsData[metricName] = {
      amount: primaryValue,
      growth: comparisonTotals
        ? calculateGrowth(primaryValue, comparisonValue)
        : "0%",
    };

    // Add dotted line cards data if comparison exists
    if (comparisonTotals) {
      cardsData[`dotted_${metricName}`] = {
        amount: comparisonValue,
        growth: "0%", // Comparison data doesn't show growth
      };
    }
  });

  // Debug: Check data integrity
  const searchVisibilityValues = lineChartData.map(
    (point) => point.Search_Visibility
  );
  console.log(`Search_Visibility values in final ${dataType} data:`, {
    values: searchVisibilityValues,
    validValues: searchVisibilityValues.filter(
      (v) => typeof v === "number" && !isNaN(v) && isFinite(v)
    ),
    invalidValues: searchVisibilityValues.filter(
      (v) => typeof v !== "number" || isNaN(v) || !isFinite(v)
    ),
  });

  return {
    lineChartData,
    cardsData,
  };
};