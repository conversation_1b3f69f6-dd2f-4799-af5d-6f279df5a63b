"use client";
import React, { useRef, useState, useEffect } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import useGscDemographicData from "./useGscDemographicData";
import { DemographicType } from "./gscDemographicService";
import { useProjectId } from "@/hooks/useProjectId";
import { useProjectDetails } from "@/hooks/useProjectDetails";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
const GscDemographicTable = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);
  const { projectName } = useProjectContext();

  // Demographic type filters with user-friendly labels
  const demographicTypeFilters: Array<{
    value: DemographicType;
    label: string;
  }> = [
    { value: "web-pages", label: "Web Pages" },
    { value: "images", label: "Images" },
    { value: "videos", label: "Videos" },
    { value: "queries", label: "Queries" },
    { value: "countries", label: "Countries" },
    { value: "devices", label: "Devices" },
    { value: "search-appears", label: "Search Appears" },
  ];

  const [selectedDemographicType, setSelectedDemographicType] =
    useState<DemographicType>("queries");

  // Reset page to 1 when filter changes
  useEffect(() => {
    setPage(1);
  }, [selectedDemographicType]);

  const { data, isLoading, error } = useGscDemographicData({
    page,
    demographicType: selectedDemographicType,
  });

  // Get project base URL for linking page paths
  const { projectId } = useProjectId();
  const { project, currentProject } = useProjectDetails({
    projectId,
    enabled: !!projectId,
  });
  const projectBaseUrl = (project?.url || currentProject?.url) ?? undefined;
  const linkableTables: Array<DemographicType> = [
    "web-pages",
    "images",
    "videos",
  ];
  const isLinkable = linkableTables.includes(selectedDemographicType);
  const pageLinkBaseUrl = isLinkable ? projectBaseUrl : undefined;

  const lastDataRef = useRef(data);

  if (data && data.tableData.tableBody.length > 0) {
    lastDataRef.current = data;
  }

  const stableData = data ?? lastDataRef.current;

  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */

  const handleDemographicTypeChange = (newDemographicType: string) => {
    setSelectedDemographicType(newDemographicType as DemographicType);
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Show no data state for primary data errors or when no data is available
  if (
    (error.hasError && error.type === "primary" && !isLoading) ||
    (!isLoading && data && data.tableData.tableBody.length === 0)
  ) {
    return (
      <Card className="w-full space-y-4 min-h-[520px] flex flex-col justify-between">
        <div className="flex w-full justify-between items-center">
          <Title>{projectName} GSC Demographics</Title>
        </div>

        <motion.div
          key="nodata"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="flex-1"
        >
          <DataTable
            title=""
            tableData={{ tableHeadings: [], tableBody: [] }}
            isLoading={false}
            badges={demographicTypeFilters.map((filter) => filter.label)}
            selectedItem={
              demographicTypeFilters.find(
                (filter) => filter.value === selectedDemographicType
              )?.label || demographicTypeFilters[0].label
            }
            setSelectedItem={(next) => {
              const currentLabel =
                demographicTypeFilters.find(
                  (filter) => filter.value === selectedDemographicType
                )?.label || demographicTypeFilters[0].label;
              const nextLabel =
                typeof next === "function" ? next(currentLabel) : next;
              const filter = demographicTypeFilters.find(
                (f) => f.label === nextLabel
              );
              if (filter) handleDemographicTypeChange(filter.value);
            }}
            currentPage={page}
            pageLinkBaseUrl={pageLinkBaseUrl}
            openLinksInNewTab={Boolean(pageLinkBaseUrl)}
          />
        </motion.div>

        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-gray-500 text-sm">No data available</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full space-y-4 min-h-[520px] flex flex-col justify-between">
      {/* Header with title */}
      <div className="flex w-full justify-between items-center">
        <Title>{projectName} GSC Demographics</Title>
      </div>

      {/* Show comparison error as a warning banner if main data is available */}
      {error.hasError && error.type === "comparison" && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center">
            <div className="text-yellow-600 mr-2">⚠️</div>
            <p className="text-sm text-yellow-800">
              Comparison data is unavailable, but current period data is shown
              below.
            </p>
          </div>
        </div>
      )}

      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="flex-1"
      >
        <DataTable
          title=""
          tableData={data?.tableData || { tableHeadings: [], tableBody: [] }}
          isLoading={isLoading}
          badges={demographicTypeFilters.map((filter) => filter.label)}
          selectedItem={
            demographicTypeFilters.find(
              (filter) => filter.value === selectedDemographicType
            )?.label || demographicTypeFilters[0].label
          }
          setSelectedItem={(next) => {
            const currentLabel =
              demographicTypeFilters.find(
                (filter) => filter.value === selectedDemographicType
              )?.label || demographicTypeFilters[0].label;
            const nextLabel =
              typeof next === "function" ? next(currentLabel) : next;
            const filter = demographicTypeFilters.find(
              (f) => f.label === nextLabel
            );
            if (filter) handleDemographicTypeChange(filter.value);
          }}
          currentPage={page}
          pageLinkBaseUrl={pageLinkBaseUrl}
          openLinksInNewTab={Boolean(pageLinkBaseUrl)}
        />
      </motion.div>

      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={page}
        onPageChange={setPage}
      />
    </Card>
  );
};

export default GscDemographicTable;
