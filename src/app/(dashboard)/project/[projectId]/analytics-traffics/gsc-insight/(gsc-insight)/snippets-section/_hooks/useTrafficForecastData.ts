import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";

// Types for the API response
export interface TrafficForecastDailyMetric {
  date: string;
  value: number;
}

export interface TrafficForecastKeywordData {
  daily_metrics: TrafficForecastDailyMetric[];
}

export interface TrafficForecastApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    daily_metrics: TrafficForecastDailyMetric[];
    keywords: Record<string, TrafficForecastKeywordData>;
    period: {
      start_date: string;
      end_date: string;
      days_count: number;
    };
    is_forecast: boolean;
    aggregated: {
      clicks: number;
      impressions: number;
      ctr: number;
      average_cpc: number;
      cost: number;
    };
  };
  last_sync: string;
}
export const useTrafficForecastData = () => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["traffic-forecast", projectId],
    queryFn: async (): Promise<TrafficForecastApiResponse> => {
      if (!isValidProjectId) {
        throw new Error("Invalid project ID");
      }

      // API now returns current month by default; do not send date range
      const { data } = await httpService.get<TrafficForecastApiResponse>(
        `/api/project/GSC/snippet/trafficforecast/${projectId}/`,
        { useAuth: true }
      );

      return data;
    },
    enabled: isValidProjectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};
