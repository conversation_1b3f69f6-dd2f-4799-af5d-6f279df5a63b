import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { GSCInsightType, GSCAPIResponse } from "./GSCInsight.types";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { processGSCInsightData } from "./GSCInsight.utils";

/**
 * Validates if the API response has the expected GSC structure
 */
const isValidGSCResponse = (data: any): data is GSCAPIResponse => {
  return (
    data &&
    typeof data === "object" &&
    data.status &&
    data.project_id &&
    data.period &&
    data.data &&
    typeof data.data === "object"
  );
};

export const useGSCInsight = (dataType: string = "web-pages") => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "gsc-insight-data",
      projectId,
      dataType,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<GSCInsightType> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Build API URL with GSC endpoint format
      const apiUrl = `/api/project/GSC/overview/${dataType}/${projectId}`;

      try {
        // Fetch current period data (primary data)
        const currentParams: Record<string, string> = {};
        if (startDate && endDate) {
          currentParams.start_date = startDate;
          currentParams.end_date = endDate;
        }

        console.log("🔄 Fetching GSC insight primary data:", {
          url: apiUrl,
          params: currentParams,
          dataType,
        });

        const primaryResponse = await httpService.get(apiUrl, {
          params:
            Object.keys(currentParams).length > 0 ? currentParams : undefined,
          useAuth: true,
        });

        // Validate primary response
        if (!isValidGSCResponse(primaryResponse.data)) {
          throw new Error("Invalid primary period API response format");
        }

        console.log("✅ Primary GSC insight data fetched successfully");

        let comparisonResponse: { data: GSCAPIResponse } | null = null;

        // If comparison is enabled and we have comparison dates, fetch comparison data
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          const comparisonParams: Record<string, string> = {
            start_date: comparisonStartDate,
            end_date: comparisonEndDate,
          };

          console.log("🔄 Fetching GSC insight comparison data:", {
            url: apiUrl,
            params: comparisonParams,
            dataType,
          });

          try {
            const comparisonResponseData = await httpService.get(apiUrl, {
              params: comparisonParams,
              useAuth: true,
            });

            // Validate comparison response
            if (isValidGSCResponse(comparisonResponseData.data)) {
              comparisonResponse = comparisonResponseData;
              console.log("✅ Comparison GSC insight data fetched successfully");
            } else {
              console.warn(
                "Invalid comparison period API response, using primary data only"
              );
            }
          } catch (comparisonError) {
            console.warn(
              "Failed to fetch comparison data, using primary data only:",
              comparisonError
            );
          }
        }

        // Process and merge the data
        const result = processGSCInsightData(
          primaryResponse.data,
          comparisonResponse?.data,
          dataType
        );

        console.log("✅ Processed GSC insight data:", {
          dataType,
          lineChartDataLength: result.lineChartData.length,
          cardsDataKeys: Object.keys(result.cardsData),
          hasComparisonData: !!comparisonResponse,
          firstDataPoint: result.lineChartData[0],
          sampleDottedKeys: Object.keys(result.lineChartData[0] || {}).filter(
            (k) => k.includes("dotted_")
          ),
        });

        return result;
      } catch (error) {
        console.error("GSC Insight API call failed:", error);

        // Re-throw the error to let React Query handle it
        throw new Error(
          error instanceof Error
            ? `Failed to fetch GSC insight data: ${error.message}`
            : "Failed to fetch GSC insight data"
        );
      }
    },
    enabled: isValidProjectId && !!projectId && !!dataType,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};