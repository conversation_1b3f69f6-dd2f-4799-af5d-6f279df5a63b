import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>earch, LuX } from "react-icons/lu";

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onDebouncedChange: (value: string) => void;
  placeholder?: string;
}

const SearchInput = ({ 
  value, 
  onChange, 
  onDebouncedChange,
  placeholder = "Search keywords..."
}: SearchInputProps) => {
  const [isFocused, setIsFocused] = useState(false);

  // Simple debounce with 500ms delay
  useEffect(() => {
    if (value.trim()) {
      const timer = setTimeout(() => onDebouncedChange(value), 500);
      return () => clearTimeout(timer);
    } else {
      onDebouncedChange("");
    }
  }, [value, onDebouncedChange]);

  return (
    <div className={`
      border-2 p-2 pr-4 rounded-lg flex items-center justify-between transition-colors duration-150
      ${isFocused ? 'border-primary' : 'border-[#F4F4F4] hover:border-primary/30'}
    `}>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className="p-2 focus:outline-none w-full bg-transparent"
        placeholder={placeholder}
      />
      
      <div className="flex items-center space-x-2">
        {value && (
          <button
            onClick={() => onChange("")}
            className="text-secondary/60 hover:text-secondary transition-colors duration-150 p-1 rounded-full hover:bg-gray-100"
          >
            <LuX className="text-lg" />
          </button>
        )}
        
        <LuSearch className={`text-2xl transition-colors duration-150 ${
          isFocused ? 'text-primary' : 'text-secondary/90'
        }`} />
      </div>
    </div>
  );
};

export default SearchInput;
