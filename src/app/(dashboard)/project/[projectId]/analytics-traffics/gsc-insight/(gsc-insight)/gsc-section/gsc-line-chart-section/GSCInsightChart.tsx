"use client";
import React, { useEffect, useMemo, useState } from "react";

/* ================================ API CALLS =============================== */
import { useGSCInsight } from "./GSCInsight.hook";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import GSCLineChart from "../../../../analytic-insight/_components/line-chart/LineChart";
import CheckboxCard from "../../../../overview/(overview)/gsc-overview/_components/CheckboxCard";
import DateRange from "../../../../_components/date-range/DateRange";
import NoData from "../../../../analytic-insight/_components/NoData";
import Skeleton from "react-loading-skeleton";
import LineChartSkeleton from "../../../../_components/line-chart-skeleton/LineChartSkeleton";
import { AnimatePresence, motion } from "framer-motion";
import Dropdown from "@/components/ui/Dropdown";
import { GSC_DATA_TYPE_OPTIONS, GSCDataTypeOption } from "./GSCInsight.types";
import GscTooltip from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/(overview)/gsc-overview/_components/GscTooltip";
import { useProjectContext } from "@/contexts/ProjectContext";

interface GSCInsightChartProps {
  shouldShowComparison: boolean;
}

/* ========================================================================== */
const GSCInsightChart = ({ shouldShowComparison }: GSCInsightChartProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [selectedDataType, setSelectedDataType] = useState<GSCDataTypeOption>(
    GSC_DATA_TYPE_OPTIONS[0]
  );
  const { projectName } = useProjectContext();
  const [selectedLines, setSelectedLines] = useState<string[]>([]);

  const {
    data: gscInsightData,
    isLoading: gscInsightIsLoading,
    error,
  } = useGSCInsight(selectedDataType.apiEndpoint);

  /* ================================== HOOKS ================================= */
  const coloredKeys = useMemo(() => {
    const colors = ["#FF00C3", "#00BBEC", "#31D37A", "#3C0866", "#F57D37"];

    if (!gscInsightData) return [];
    const keys = Object.keys(gscInsightData.lineChartData[0] || {}).filter(
      (k) =>
        k !== "name" &&
        k !== "primaryDate" &&
        k !== "comparisonDate" &&
        !k.includes("dotted_")
    );
    const mappedKeys = keys.map((key, i) => ({
      name: key,
      color: colors[i % colors.length],
    }));
    return mappedKeys;
  }, [gscInsightData]);

  useEffect(() => {
    if (coloredKeys.length > 0) {
      setSelectedLines(coloredKeys.map((item) => item.name));
    }
  }, [coloredKeys]);

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */
  const handleToggleCheck = (title: string) => {
    setSelectedLines((prev) =>
      prev.includes(title)
        ? prev.filter((item) => item !== title)
        : [...prev, title]
    );
  };

  const handleDataTypeChange = (dataType: GSCDataTypeOption) => {
    setSelectedDataType(dataType);
    // Reset selected lines when changing data type
    setSelectedLines([]);
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Show no data state if there's an error or no data
  if (error || (!gscInsightData && !gscInsightIsLoading)) {
    return <NoData title={`${projectName} GSC`} />;
  }

  return (
    <Card className="space-y-4">
      <div className="flex justify-between items-center">
        <Title>{projectName} GSC</Title>
        <Dropdown>
          <Dropdown.Button>
            <span>{selectedDataType.label}</span>
          </Dropdown.Button>
          <Dropdown.Options>
            {GSC_DATA_TYPE_OPTIONS.map((option) => (
              <Dropdown.Option
                key={option.value}
                onClick={() => handleDataTypeChange(option)}
              >
                {option.label}
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
      </div>
      <DateRange />
      {
        <div className="flex justify-between gap-2 mb-9 flex-wrap">
          {gscInsightIsLoading ? (
            <div className="flex justify-between w-full mt-2">
              {Array.from({ length: 5 }).map((_, index) => (
                <Skeleton
                  key={index}
                  height={72}
                  width={150}
                  style={{ borderRadius: "10px" }}
                />
              ))}
            </div>
          ) : (
            coloredKeys.map(({ name, color }, index) => {
              if (gscInsightData) {
                // Skip dotted lines (comparison data)
                if (!name.includes("dotted_")) {
                  // Get card data
                  const cardData = gscInsightData.cardsData[name] ?? {
                    amount: 0,
                    growth: "0%",
                  };

                  // Only show growth value if comparison is enabled
                  const displayCardData = {
                    amount: cardData.amount,
                    growth: shouldShowComparison ? cardData.growth : "",
                  };

                  return (
                    <CheckboxCard
                      title={name}
                      color={color}
                      key={index}
                      selected={selectedLines}
                      cardsData={displayCardData}
                      onToggleCheck={() => handleToggleCheck(name)}
                    />
                  );
                }
              }
              return null;
            })
          )}
        </div>
      }
      <AnimatePresence mode="wait">
        {gscInsightIsLoading ? (
          <motion.div
            key="line-chart-skeleton"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full"
          >
            <LineChartSkeleton />
          </motion.div>
        ) : (
          gscInsightData && (
            <motion.div
              key="line-chart"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="w-full"
            >
              <GSCLineChart
                lineChartData={gscInsightData!.lineChartData}
                colors={coloredKeys}
                selectedLines={
                  shouldShowComparison
                    ? selectedLines
                    : selectedLines.filter((line) => !line.includes("dotted_"))
                }
                cardsData={gscInsightData!.cardsData}
                customTooltipContent={GscTooltip}
                shouldShowComparison={shouldShowComparison}
              />
            </motion.div>
          )
        )}
      </AnimatePresence>
    </Card>
  );
};

export default GSCInsightChart;
