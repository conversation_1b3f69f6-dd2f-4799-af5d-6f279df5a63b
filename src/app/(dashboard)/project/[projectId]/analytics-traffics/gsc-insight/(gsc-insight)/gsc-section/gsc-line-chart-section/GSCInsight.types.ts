// GSC API Response Types
export type GSCDailyMetric = {
  date: string; // Format: "20250601"
  Clicks: string;
  CTR: string;
  Search_Visibility: string;
  Impression: string;
  Avg_Position: string;
};

export type GSCTotals = {
  Clicks: string;
  CTR: string;
  Search_Visibility: string;
  Impression: string;
  Avg_Position: string;
};

export type GSCPeriod = {
  start_date: string; // Format: "2025-06-01"
  end_date: string; // Format: "2025-06-30"
  days_count: number;
};

export type GSCDataTypeData = {
  totals: GSCTotals;
  daily_metrics: GSCDailyMetric[];
};

export type GSCAPIResponse = {
  status: string;
  project_id: string;
  period: GSCPeriod;
  data: {
    [key: string]: GSCDataTypeData; // Dynamic key for different data types (web-pages, images, etc.)
  };
};

// Processed data types for chart consumption
export type GSCInsightType = {
  lineChartData: Record<string, string | number>[];
  cardsData: Record<string, CardsData>;
};

export type CardsData = { amount: number; growth: string };

export type CheckboxCardProps = {
  title: string;
  color: string;
  cardsData: CardsData;
  selected: string[];
  onToggleCheck: () => void;
};

// Data type options for dropdown
export type GSCDataTypeOption = {
  value: string;
  label: string;
  apiEndpoint: string;
};

export const GSC_DATA_TYPE_OPTIONS: GSCDataTypeOption[] = [
  {
    value: "web-pages",
    label: "Web Pages",
    apiEndpoint: "web-pages",
  },
  {
    value: "images",
    label: "Images",
    apiEndpoint: "images",
  },
  {
    value: "videos",
    label: "Videos",
    apiEndpoint: "videos",
  },
];