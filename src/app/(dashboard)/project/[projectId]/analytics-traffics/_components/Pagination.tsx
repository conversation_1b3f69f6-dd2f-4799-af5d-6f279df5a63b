"use client";
import React, { useState, useEffect } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================== ICONS ================================= */
import { IoArrowBackCircleOutline } from "react-icons/io5";

/* ================================== TYPES ================================= */
type PaginationProps = {
  totalPages: number;
  page: number;
  onPageChange?: (page: number) => void;
};

/* ========================================================================== */
const Pagination = ({
  totalPages,
  page = 1,
  onPageChange,
}: PaginationProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activePage, setActivePage] = useState(page);

  // Sync activePage with page prop changes
  useEffect(() => {
    setActivePage(page);
  }, [page]);

  if (!totalPages || totalPages < 2 || page <= 0) return (
    <div className="w-full h-6"></div>
  );

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */
  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setActivePage(page);
      onPageChange?.(page);
    }
  };

  const renderPageNumbers = () => {
    const visiblePages = 4;
    const pages = [];

    const start = Math.max(
      1,
      Math.min(
        activePage - Math.floor(visiblePages / 2),
        Math.max(1, totalPages - visiblePages + 1)
      )
    );

    for (let i = 0; i < visiblePages; i++) {
      const pageNum = start + i;
      if (pageNum > totalPages) break;
      pages.push(
        <motion.span
          key={pageNum}
          whileTap={{ scale: 0.98 }}
          transition={{ duration: 0.1 }}
          onClick={() => handlePageClick(pageNum)}
          className={`${
            activePage === pageNum ? "text-secondary" : "text-secondary/40"
          } cursor-pointer px-1`}
        >
          {pageNum}
        </motion.span>
      );
    }

    return pages;
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-full flex justify-center items-center text-secondary text-sm h-6">
      <motion.div
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.1 }}
      >
        <IoArrowBackCircleOutline
          className={`h-5 w-5 cursor-pointer ${
            activePage === 1 ? "opacity-30 cursor-default" : ""
          }`}
          onClick={() => handlePageClick(activePage - 1)}
        />
      </motion.div>

      <div className="mx-4 space-x-2 flex items-center">
        {renderPageNumbers()}
        {activePage < totalPages - 3 && (
          <>
            <span className="cursor-default">...</span>
            <motion.span
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.1 }}
              className="text-secondary/40 cursor-pointer"
              onClick={() => handlePageClick(totalPages)}
            >
              {totalPages}
            </motion.span>
          </>
        )}
      </div>

      <motion.div
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.1 }}
      >
        <IoArrowBackCircleOutline
          className={`scale-[-1] h-5 w-5 cursor-pointer ${
            activePage === totalPages ? "opacity-30 cursor-default" : ""
          }`}
          onClick={() => handlePageClick(activePage + 1)}
        />
      </motion.div>
    </div>
  );
};

export default Pagination;