"use client";
import React from "react";

const LineChartSkeleton = () => {
  return (
    <div className="w-full h-[300px] animate-pulse relative overflow-hidden">
      <svg
        className="w-full h-full"
        viewBox="0 0 852 300"
        preserveAspectRatio="none"
      >
        <defs>
          <clipPath id="recharts268-clip">
            <rect x="80" y="5" height="260" width="742" />
          </clipPath>
        </defs>

        {/* Grid / Axes lines (if you want them) */}

        {/* Example line */}
        <path
          stroke="#ccc"
          pointerEvents="none"
          fill="none"
          strokeWidth="4"
          strokeDasharray="5 5"
          d="M80,228.652C154.2,228.652,154.2,202.6,228.4,202.6C302.6,202.6,302.6,10.2,376.8,10.2C451,10.2,451,228.652,525.2,228.652C599.4,228.652,599.4,228.652,673.6,228.652C747.8,228.652,747.8,202.6,822,202.6"
        />

        <path
          stroke="#aaa"
          fill="none"
          strokeWidth="4"
          strokeLinecap="round"
          d="M80,184.14C154.2,184.14,154.2,210.4,228.4,210.4C302.6,210.4,302.6,156.06,376.8,156.06C451,156.06,451,236.14,525.2,236.14C599.4,236.14,599.4,184.14,673.6,184.14C747.8,184.14,747.8,236.4,822,236.4"
        />
      </svg>
    </div>
  );
};

export default LineChartSkeleton;
