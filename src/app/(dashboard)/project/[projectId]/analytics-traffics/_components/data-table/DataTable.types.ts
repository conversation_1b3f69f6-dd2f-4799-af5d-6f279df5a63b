export type TableBody = { value: string; growth?: string };
export type TableData = {
  tableHeadings: string[];
  tableBody: TableBody[][];
};

export type DataTableProps = {
  tableData: TableData | undefined;
  badges?: string[];
  selectedItem?: string;
  setSelectedItem?: React.Dispatch<React.SetStateAction<string>>;
  isLoading?: boolean;
  title?: string;
  showDateRange?: boolean;
  checkbox?: boolean;
  setSelectedItems?: React.Dispatch<React.SetStateAction<string[]>>;
  selectedItems?: string[];
  currentPage?: number;
  /**
   * Width of the first column as a percentage of the table width. Example: 20 means 20%.
   * Default: 20
   */
  firstColumnWidthPercent?: number;
  /**
   * Optional base URL used to render the first column (Page) as clickable links.
   * Example: https://injatrip.com
   */
  pageLinkBaseUrl?: string;
  /**
   * When linking the first column, whether to open links in a new tab. Default: true
   */
  openLinksInNewTab?: boolean;
};

type PaginationType = {
  totalPages: number;
  initialPage?: number;
};
export type TableDataRequest = {
  tableData: TableData;
  pagination: PaginationType;
};
