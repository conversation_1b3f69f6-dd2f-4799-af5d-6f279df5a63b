import Title from "@/components/ui/Title";
import React, { useEffect } from "react";
import DateRange from "../date-range/DateRange";
import Badge from "../../analytic-insight/_components/Badge";
import { cn } from "@/utils/cn";
import { DataTableProps } from "./DataTable.types";
import { motion, type Transition } from "framer-motion";
import * as Tooltip from "@radix-ui/react-tooltip";

import TableSkeleton from "./TableSkeleton";
import { GoArrowSwitch } from "react-icons/go";
import { HiPlus } from "react-icons/hi2";
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  horizontalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import Checkbox from "@/components/ui/Checkbox";

/* ================================== MAIN ================================== */
const DataTable = ({
  tableData,
  badges,
  selectedItem,
  setSelectedItem,
  isLoading = false,
  title,
  showDateRange = true,
  checkbox = false,
  setSelectedItems,
  selectedItems,
  currentPage,
  pageLinkBaseUrl,
  openLinksInNewTab = true,
  firstColumnWidthPercent = 25,
}: DataTableProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */

  // Column order state
  const [columnOrder, setColumnOrder] = React.useState<number[]>([]);

  // Initialize column order when tableData is available
  useEffect(() => {
    if (tableData?.tableHeadings) {
      setColumnOrder(tableData.tableHeadings.map((_, i) => i).slice(1));
    }
  }, [tableData]);

  // Cleanup effect to reset drag state and body styles on unmount
  useEffect(() => {
    return () => {
      // Reset body styles on cleanup
      document.body.style.userSelect = "";
      document.body.style.webkitUserSelect = "";
      // Reset drag states
      setActiveId(null);
      setOverId(null);
    };
  }, []);

  // dnd-kit sensors with improved configuration
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
        delay: 100,
        tolerance: 5,
      },
    })
  );
  const [activeId, setActiveId] = React.useState<number | null>(null);
  const [overId, setOverId] = React.useState<number | null>(null);

  // During drag, render remaining columns without the active one
  const renderingColumns = React.useMemo(
    () =>
      activeId !== null
        ? columnOrder.filter((id) => id !== activeId)
        : columnOrder,
    [columnOrder, activeId]
  );

  // Where to show the placeholder highlight column
  const _placeholderIndex = React.useMemo(() => {
    if (activeId === null || overId === null) return -1;
    return renderingColumns.indexOf(overId);
  }, [activeId, overId, renderingColumns]);

  // Calculate drop indicator position
  const dropIndicatorIndex = React.useMemo(() => {
    if (activeId === null || overId === null) return -1;
    const overIndex = renderingColumns.indexOf(overId);
    return overIndex;
  }, [activeId, overId, renderingColumns]);

  // Responsive width allocation that never exceeds container width
  const firstColumnPercent = Math.max(5, Math.min(60, firstColumnWidthPercent));
  const otherColumnPercent = React.useMemo(() => {
    const count =
      (activeId !== null ? renderingColumns.length : columnOrder.length) || 1;
    const remaining = Math.max(0, 100 - firstColumnPercent);
    return remaining / count;
  }, [
    activeId,
    renderingColumns.length,
    columnOrder.length,
    firstColumnPercent,
  ]);

  // Shared framer-motion layout transition for fast, smooth reordering
  const fmLayoutTransition: Transition = {
    duration: 0.1,
    ease: [0.2, 0.8, 0.2, 1],
  };

  // Truncated text with Radix Tooltip to show full text
  function TruncatedCell({
    text,
    maxWidthClass,
    align = "center",
    triggerClassName = "",
  }: {
    text: React.ReactNode;
    maxWidthClass: string;
    align?: "left" | "center" | "right";
    triggerClassName?: string;
  }) {
    return (
      <Tooltip.Root delayDuration={150}>
        <Tooltip.Trigger asChild>
          <span
            className={cn(
              "block truncate whitespace-nowrap overflow-hidden",
              maxWidthClass,
              align === "left" && "text-left",
              align === "center" && "text-center",
              align === "right" && "text-right",
              triggerClassName
            )}
          >
            {text}
          </span>
        </Tooltip.Trigger>
        <Tooltip.Portal>
          <Tooltip.Content
            side="top"
            align="center"
            sideOffset={6}
            avoidCollisions
            collisionPadding={8}
            className="z-[9999] rounded-md bg-gray-800/95 text-white text-xs px-2 py-1 shadow-lg backdrop-blur-sm max-w-[80vw]"
          >
            {typeof text === "string" ? text : String((text as unknown) ?? "")}
            <Tooltip.Arrow className="fill-gray-800/95" />
          </Tooltip.Content>
        </Tooltip.Portal>
      </Tooltip.Root>
    );
  }
  function SortableTh({
    id,
    children,
    isDropTarget: _isDropTarget = false,
    ...props
  }: {
    id: number;
    children: React.ReactNode;
    isDropTarget?: boolean;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any;
  }) {
    const { attributes, listeners, setNodeRef, transform, isDragging } =
      useSortable({ id });
    return (
      <motion.th
        ref={setNodeRef}
        layout
        animate={
          isDragging
            ? { scale: 1.02, boxShadow: "0px 10px 24px rgba(0,0,0,0.12)" }
            : { scale: 1, boxShadow: "0px 0px 0px rgba(0,0,0,0)" }
        }
        style={{
          // dnd-kit drives the drag transform; we avoid CSS transitions to let framer-motion handle layout animations
          transform: CSS.Transform.toString(transform),
          zIndex: isDragging ? 2 : 1,
          background: isDragging ? "#eeeeee" : undefined,
          cursor: isDragging ? "grabbing" : "grab",
          position: "relative",
          overflow: "visible",
          touchAction: "none",
        }}
        transition={{
          layout: fmLayoutTransition,
          type: "tween",
        }}
        {...attributes}
        {...listeners}
        {...props}
      >
        {children}
      </motion.th>
    );
  }

  // Calculate minimum rows needed to maintain consistent height
  const currentRows = tableData?.tableBody?.length || 0;
  // Don't add empty rows - just show the available data rows
  const emptyRowsNeeded = 0;

  // Memoize valid rows once to avoid repeated filtering during render (reduces drag lag)
  const filteredRowsMemo = React.useMemo(() => {
    if (!tableData?.tableBody) return [];
    return tableData.tableBody.filter(
      (row) =>
        row &&
        Array.isArray(row) &&
        row.length > 0 &&
        row[0] &&
        typeof row[0] === "object" &&
        "value" in row[0]
    );
  }, [tableData]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-full space-y-10 overflow-visible ">
      {(title || showDateRange) && (
        <div className="space-y-2">
          {title && (
            <div className="flex w-full justify-between items-center">
              <Title>{title}</Title>
            </div>
          )}
          {showDateRange && <DateRange variation="long" />}
        </div>
      )}
      <div className="space-y-6">
        {badges && setSelectedItem && (
          <div className="flex gap-2 overflow-x-auto">
            {badges.map((badge, index) => (
              <Badge
                selected={selectedItem === badge}
                key={index}
                onSelect={() => setSelectedItem(badge)}
              >
                {badge}
              </Badge>
            ))}
          </div>
        )}
        <div className={cn("overflow-x-auto")}>
          {isLoading ? (
            <motion.div
              key={"loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.12 }}
            >
              <TableSkeleton />
            </motion.div>
          ) : tableData?.tableHeadings &&
            tableData?.tableBody &&
            tableData.tableBody.length > 0 ? (
            <Tooltip.Provider delayDuration={150}>
              <motion.div
                key={"data"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.12 }}
              >
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragStart={(event) => {
                    const { active } = event;
                    const nextId = active.id as number;
                    if (nextId !== activeId) {
                      setActiveId(nextId);
                      // Prevent text selection during drag
                      document.body.style.userSelect = "none";
                      document.body.style.webkitUserSelect = "none";
                    }
                  }}
                  onDragOver={(event) => {
                    const { over } = event;
                    const nextOver = (over?.id as number) ?? null;
                    if (nextOver !== overId) setOverId(nextOver);
                  }}
                  onDragEnd={(event) => {
                    const { active, over } = event;
                    if (over && active.id !== over.id) {
                      const oldIndex = columnOrder.indexOf(active.id as number);
                      const newIndex = columnOrder.indexOf(over.id as number);
                      if (oldIndex !== -1 && newIndex !== -1) {
                        setColumnOrder(
                          arrayMove(columnOrder, oldIndex, newIndex)
                        );
                      }
                    }
                    // Reset drag state and restore text selection
                    setActiveId(null);
                    setOverId(null);
                    document.body.style.userSelect = "";
                    document.body.style.webkitUserSelect = "";
                  }}
                  onDragCancel={() => {
                    setActiveId(null);
                    setOverId(null);
                    // Restore text selection on cancel
                    document.body.style.userSelect = "";
                    document.body.style.webkitUserSelect = "";
                  }}
                >
                  <DragOverlay
                    dropAnimation={{
                      duration: 200,
                      easing: "cubic-bezier(0.18, 0.67, 0.6, 1.22)",
                    }}
                    style={{
                      cursor: "grabbing",
                    }}
                  >
                    {activeId !== null && tableData ? (
                      <motion.table
                        initial={{ scale: 1.02, opacity: 0.85 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{
                          type: "spring",
                          stiffness: 420,
                          damping: 26,
                        }}
                        className="bg-white border border-[#E0E0E0] rounded-md shadow-lg text-xs text-secondary select-none"
                      >
                        <thead>
                          <tr>
                            <th className="px-2 py-3 w-28 bg-[#F4F4F4] border-b-2 border-[#E0E0E0] text-center font-semibold select-none">
                              {tableData.tableHeadings[activeId] === "RF" ? (
                                <GoArrowSwitch
                                  className="rotate-90 mx-auto text-lg"
                                  strokeWidth={1}
                                />
                              ) : (
                                tableData.tableHeadings[activeId]
                              )}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {tableData.tableBody
                            .filter(
                              (row) =>
                                row &&
                                Array.isArray(row) &&
                                row.length > 0 &&
                                row[activeId] &&
                                typeof row[activeId] === "object" &&
                                "value" in row[activeId]
                            )
                            .map((row, rowIndex) => (
                              <tr key={rowIndex}>
                                <td className="px-2 py-3 text-sm text-center border-t-2 border-[#E0E0E0] w-28 bg-white">
                                  <span className="font-medium">
                                    {row[activeId]?.value || "N/A"}
                                  </span>
                                  <br />
                                  <span
                                    className={cn(
                                      "text-[10px] mt-1 inline-block",
                                      row[activeId]?.growth?.includes("+") &&
                                        "text-primary-green font-medium",
                                      row[activeId]?.growth?.includes("-") &&
                                        "text-primary-red font-medium"
                                    )}
                                  >
                                    {row[activeId]?.growth || ""}
                                  </span>
                                </td>
                              </tr>
                            ))}
                        </tbody>
                      </motion.table>
                    ) : null}
                  </DragOverlay>

                  <div
                    className="inline-block min-w-full rounded-lg border border-[#E0E0E0] overflow-visible relative"
                    style={{ touchAction: "pan-y" }}
                  >
                    {/* Continuous Drop Indicator Line */}
                    {activeId !== null && dropIndicatorIndex !== -1 && (
                      <motion.div
                        initial={{ opacity: 0, scaleY: 0 }}
                        animate={{ opacity: 1, scaleY: 1 }}
                        exit={{ opacity: 0, scaleY: 0 }}
                        transition={{
                          opacity: { duration: 0.2 },
                          scaleY: { duration: 0.2, ease: "easeOut" },
                        }}
                        className="absolute top-0 bottom-0 w-1 bg-primary pointer-events-none z-20"
                        style={{
                          left: `${
                            firstColumnPercent +
                            dropIndicatorIndex * otherColumnPercent
                          }%`,
                          transformOrigin: "top",
                        }}
                      >
                        {/* Plus Icon in the middle */}
                        <motion.div
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0 }}
                          transition={{
                            delay: 0.1,
                            duration: 0.2,
                            ease: "easeOut",
                          }}
                          className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-primary rounded-full p-1 shadow-lg"
                        >
                          <HiPlus className="w-3 h-3 text-white" />
                        </motion.div>
                      </motion.div>
                    )}
                    <table
                      className="w-full text-secondary text-xs border-separate border-spacing-0"
                      style={{ tableLayout: "fixed" }}
                    >
                      <thead>
                        <tr>
                          <th
                            className={cn(
                              "px-2 text-center h-12 bg-[#F4F4F4] border-b-2 rounded-tl-lg cursor-default font-semibold border-r border-[#E0E0E0] select-none"
                            )}
                            style={{ width: `${firstColumnPercent}%` }}
                          >
                            <div
                              className={cn(
                                checkbox &&
                                  "flex items-center justify-start w-fit gap-1 pl-2"
                              )}
                            >
                              {checkbox && (
                                <Checkbox
                                  checked={
                                    selectedItems?.includes(
                                      tableData.tableHeadings[0]
                                    ) || false
                                  }
                                  onChange={() => {
                                    if (!setSelectedItems) return;
                                    const allItems = tableData.tableBody
                                      .filter(
                                        (row) =>
                                          row &&
                                          Array.isArray(row) &&
                                          row.length > 0 &&
                                          row[0] &&
                                          typeof row[0] === "object" &&
                                          "value" in row[0]
                                      )
                                      .map((row) => row[0].value)
                                      .concat(tableData.tableHeadings[0]);
                                    const allSelected =
                                      selectedItems?.length ===
                                      tableData.tableBody.length + 1;

                                    setSelectedItems(
                                      allSelected ? [] : allItems
                                    );
                                  }}
                                  id={"checkbox-0"}
                                />
                              )}
                              <div className="mx-auto whitespace-normal break-words">
                                {tableData.tableHeadings[0]}
                              </div>
                            </div>
                          </th>
                          <SortableContext
                            items={columnOrder}
                            strategy={horizontalListSortingStrategy}
                          >
                            {/* Header cells without layout-affecting placeholder; use overlay highlight */}
                            {renderingColumns.map((colIdx, index) => (
                              <SortableTh
                                id={colIdx}
                                key={colIdx}
                                isDropTarget={
                                  activeId !== null &&
                                  dropIndicatorIndex === index
                                }
                                className={cn(
                                  "px-2 text-center h-12 bg-[#F4F4F4] border-[#E0E0E0] cursor-grab font-semibold border-b-2 relative overflow-visible select-none",
                                  index === renderingColumns.length - 1
                                    ? "rounded-tr-lg border-r-0"
                                    : "border-r"
                                )}
                                style={{ width: `${otherColumnPercent}%` }}
                              >
                                {tableData.tableHeadings[colIdx] === "RF" ? (
                                  <GoArrowSwitch className="rotate-90 mx-auto text-lg" />
                                ) : (
                                  tableData.tableHeadings[colIdx]
                                )}
                              </SortableTh>
                            ))}
                          </SortableContext>
                        </tr>
                      </thead>
                      <motion.tbody
                        key={currentPage || "default"}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{
                          duration: 0.15,
                          ease: "easeInOut",
                        }}
                      >
                        {/* Actual data rows */}
                        {filteredRowsMemo.map((row, rowIndex) => {
                          const isLastRow =
                            rowIndex === filteredRowsMemo.length - 1;

                          // Additional safety check for row data
                          if (
                            !row ||
                            !Array.isArray(row) ||
                            row.length === 0 ||
                            !row[0] ||
                            typeof row[0] !== "object" ||
                            !("value" in row[0])
                          ) {
                            return null;
                          }

                          return (
                            <motion.tr
                              key={rowIndex}
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{
                                duration: 0.1,
                                delay: rowIndex * 0.01,
                                ease: "easeOut",
                              }}
                            >
                              <motion.td
                                layout
                                className={`px-2 py-3 text-sm text-center border-0 w-40 max-w-44 rounded-none ${
                                  rowIndex % 2 !== 0
                                    ? "bg-[#F4F4F4] border-b-2 border-[#E0E0E0]"
                                    : "border-b-2 border-[#E0E0E0]"
                                } ${
                                  isLastRow ? "!border-b-0 rounded-bl-lg" : ""
                                } border-r border-[#E0E0E0]`}
                                style={{ width: `${firstColumnPercent}%` }}
                                transition={{ layout: fmLayoutTransition }}
                              >
                                <div
                                  className={cn(
                                    "flex items-center gap-1 justify-start w-full pl-1",
                                    "min-w-0"
                                  )}
                                >
                                  {checkbox && (
                                    <Checkbox
                                      checked={
                                        selectedItems?.includes(
                                          row[0]?.value || ""
                                        ) || false
                                      }
                                      onChange={() => {
                                        if (setSelectedItems && row[0]?.value) {
                                          setSelectedItems((prev) => {
                                            if (prev.includes(row[0].value)) {
                                              return prev.filter(
                                                (item) => item !== row[0].value
                                              );
                                            }
                                            return [...prev, row[0].value];
                                          });
                                        }
                                      }}
                                      id={"checkbox-0"}
                                      className="shrink-0"
                                    />
                                  )}
                                  {(() => {
                                    const textValue =
                                      (row[0]?.value as string) || "N/A";
                                    const isLinkable =
                                      !!pageLinkBaseUrl &&
                                      typeof textValue === "string" &&
                                      textValue !== "(not set)";
                                    if (!isLinkable) {
                                      return (
                                        <div className="flex-1 min-w-0 overflow-hidden">
                                          <TruncatedCell
                                            text={textValue}
                                            maxWidthClass="w-full"
                                            align="left"
                                            triggerClassName="text-xs leading-tight font-medium min-w-0"
                                          />
                                        </div>
                                      );
                                    }
                                    // Build absolute URL safely
                                    const isAbsoluteUrl = /^https?:\/\//i.test(
                                      textValue
                                    );
                                    const base = pageLinkBaseUrl.replace(
                                      /\/$/,
                                      ""
                                    );
                                    const path = textValue.startsWith("/")
                                      ? textValue
                                      : `/${textValue}`;
                                    const href = isAbsoluteUrl
                                      ? textValue
                                      : `${base}${path}`;
                                    return (
                                      <a
                                        href={href}
                                        target={
                                          openLinksInNewTab
                                            ? "_blank"
                                            : undefined
                                        }
                                        rel={
                                          openLinksInNewTab
                                            ? "noopener noreferrer"
                                            : undefined
                                        }
                                        className="text-primary-blue hover:underline block flex-1 min-w-0 overflow-hidden"
                                      >
                                        <TruncatedCell
                                          text={href}
                                          maxWidthClass="w-full"
                                          align="left"
                                          triggerClassName="text-xs leading-tight font-medium min-w-0"
                                        />
                                      </a>
                                    );
                                  })()}
                                </div>
                                <span
                                  className={cn(
                                    "text-[10px] mt-1 inline-block",
                                    row[0]?.growth?.includes("+") &&
                                      "text-primary-green font-medium",
                                    row[0]?.growth?.includes("-") &&
                                      "text-primary-red font-medium"
                                  )}
                                >
                                  {row[0]?.growth || ""}
                                </span>
                              </motion.td>
                              {renderingColumns.map((colIdx, cellIndex) => {
                                const cell = row[colIdx];
                                const isFirst = cellIndex === 0;
                                const isLast =
                                  cellIndex === renderingColumns.length - 1;

                                // Safety check for cell data
                                if (
                                  !cell ||
                                  typeof cell !== "object" ||
                                  !("value" in cell)
                                ) {
                                  return (
                                    <motion.td
                                      key={`row-frag-${rowIndex}-${colIdx}`}
                                      layout
                                      className={`px-2 py-3 text-sm rounded-none border-0 text-center relative overflow-hidden
                                        ${
                                          rowIndex % 2 !== 0
                                            ? "bg-[#F4F4F4] border-b-2 border-[#E0E0E0]"
                                            : "border-b-2 border-[#E0E0E0]"
                                        }
                                        ${
                                          isLastRow && isFirst
                                            ? "rounded-none"
                                            : isLastRow && isLast
                                            ? "rounded-br-lg"
                                            : ""
                                        } ${isLastRow && "!border-b-0"} ${
                                        isLast ? "border-r-0" : "border-r"
                                      } border-[#E0E0E0]`}
                                      style={{
                                        width: `${otherColumnPercent}%`,
                                      }}
                                      transition={{
                                        layout: fmLayoutTransition,
                                      }}
                                    >
                                      {activeId !== null &&
                                        dropIndicatorIndex === cellIndex && (
                                          <motion.div
                                            layout
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 0.3 }}
                                            exit={{ opacity: 0 }}
                                            transition={{
                                              layout: fmLayoutTransition,
                                            }}
                                            className="absolute inset-0 bg-primary/10 pointer-events-none"
                                          />
                                        )}
                                      <span className="text-sm text-gray-500 block mx-auto whitespace-normal break-words">
                                        N/A
                                      </span>
                                    </motion.td>
                                  );
                                }

                                return (
                                  <React.Fragment
                                    key={`row-frag-${rowIndex}-${colIdx}`}
                                  >
                                    <motion.td
                                      layout
                                      className={`px-2 py-3 text-sm rounded-none border-0 text-center relative overflow-hidden
                                        ${
                                          rowIndex % 2 !== 0
                                            ? "bg-[#F4F4F4] border-b-2 border-[#E0E0E0]"
                                            : "border-b-2 border-[#E0E0E0]"
                                        }
                                        ${
                                          isLastRow && isFirst
                                            ? "rounded-none"
                                            : isLastRow && isLast
                                            ? "rounded-br-lg"
                                            : ""
                                        } ${isLastRow && "!border-b-0"} ${
                                        isLast ? "border-r-0" : "border-r"
                                      } border-[#E0E0E0]`}
                                      style={{
                                        width: `${otherColumnPercent}%`,
                                      }}
                                      transition={{
                                        layout: fmLayoutTransition,
                                      }}
                                    >
                                      {activeId !== null &&
                                        dropIndicatorIndex === cellIndex && (
                                          <motion.div
                                            layout
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 0.3 }}
                                            exit={{ opacity: 0 }}
                                            transition={{
                                              layout: fmLayoutTransition,
                                            }}
                                            className="absolute inset-0 bg-primary/10 pointer-events-none"
                                          />
                                        )}
                                      <Tooltip.Root delayDuration={150}>
                                        <Tooltip.Trigger asChild>
                                          <span className="text-sm font-medium block mx-auto whitespace-normal break-words">
                                            {cell.value &&
                                            typeof cell.value === "string" &&
                                            cell.value.includes("/") ? (
                                              <span className="inline-block align-top whitespace-normal break-words">
                                                <span className="text-primary-red">
                                                  {cell.value.split("/")[0]}
                                                </span>
                                                <span className="mx-0.5">
                                                  /
                                                </span>
                                                <span className="text-primary-green">
                                                  {cell.value.split("/")[1]}
                                                </span>
                                              </span>
                                            ) : (
                                              (cell.value as string) || "N/A"
                                            )}
                                          </span>
                                        </Tooltip.Trigger>
                                        <Tooltip.Portal>
                                          <Tooltip.Content
                                            side="top"
                                            align="center"
                                            sideOffset={6}
                                            avoidCollisions
                                            collisionPadding={8}
                                            className="z-[9999] rounded-md bg-gray-800/95 text-white text-xs px-2 py-1 shadow-lg backdrop-blur-sm max-w-[80vw]"
                                          >
                                            {typeof cell.value === "string"
                                              ? cell.value
                                              : String(cell.value ?? "N/A")}
                                            <Tooltip.Arrow className="fill-gray-800/95" />
                                          </Tooltip.Content>
                                        </Tooltip.Portal>
                                      </Tooltip.Root>
                                      <span
                                        className={cn(
                                          "text-[10px] mt-1 inline-block",
                                          cell.growth?.includes("+") &&
                                            "text-primary-green font-medium",
                                          cell.growth?.includes("-") &&
                                            "text-primary-red font-medium"
                                        )}
                                      >
                                        {cell.growth || ""}
                                      </span>
                                    </motion.td>
                                  </React.Fragment>
                                );
                              })}
                              {/* No row-level placeholder cell; overlay is used inside cells */}
                            </motion.tr>
                          );
                        })}

                        {/* Empty rows to maintain consistent height */}
                        {Array.from({ length: emptyRowsNeeded }, (_, index) => {
                          const rowIndex = currentRows + index;
                          const isLastRow = index === emptyRowsNeeded - 1;
                          return (
                            <tr key={`empty-${index}`}>
                              <td
                                className={`px-3 py-4 text-sm text-center border-0 w-40 max-w-48 rounded-none ${
                                  rowIndex % 2 !== 0
                                    ? "bg-[#F4F4F4] border-b-2 border-[#E0E0E0]"
                                    : "border-b-2 border-[#E0E0E0]"
                                } ${
                                  isLastRow ? "!border-b-0 rounded-bl-lg" : ""
                                } border-r border-[#E0E0E0]`}
                                style={{ height: "56px" }} // Match the height of data rows
                              >
                                &nbsp;
                              </td>
                              {columnOrder.map((colIdx, cellIndex) => {
                                const isFirst = cellIndex === 0;
                                const isLast =
                                  cellIndex === columnOrder.length - 1;
                                return (
                                  <td
                                    key={`empty-${colIdx}`}
                                    className={`px-3 py-4 text-sm rounded-none border-0 text-center
                                      ${
                                        rowIndex % 2 !== 0
                                          ? "bg-[#F4F4F4] border-b-2 border-[#E0E0E0]"
                                          : "border-b-2 border-[#E0E0E0]"
                                      }
                                      ${
                                        isLastRow && isFirst
                                          ? "rounded-none"
                                          : isLastRow && isLast
                                          ? "rounded-br-lg"
                                          : ""
                                      } ${isLastRow && "!border-b-0"} ${
                                      isLast ? "border-r-0" : "border-r"
                                    } border-[#E0E0E0]`}
                                    style={{
                                      width: 120,
                                      minWidth: 120,
                                      maxWidth: 180,
                                      height: "56px",
                                    }}
                                  >
                                    &nbsp;
                                  </td>
                                );
                              })}
                            </tr>
                          );
                        })}
                      </motion.tbody>
                    </table>
                  </div>
                </DndContext>
              </motion.div>
            </Tooltip.Provider>
          ) : (
            // Show empty state when no data is available
            <motion.div
              key={"empty"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="flex items-center justify-center py-16"
            >
              <div className="text-center">
                <p className="text-gray-500 text-lg">No data available</p>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DataTable;
