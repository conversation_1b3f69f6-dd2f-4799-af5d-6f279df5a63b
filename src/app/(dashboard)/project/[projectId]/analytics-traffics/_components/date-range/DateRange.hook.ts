/* ================================== AXIOS ================================= */
import AXIOS from "@/lib/axios";

/* =============================== REACT QUERY ============================== */
import { useQuery } from "@tanstack/react-query";

/* ================================== TYPES ================================= */
import type { DateRangeResponse } from "./DateRange.type";

/* ============================= GET DATE RANGE ============================= */
export const useDateRange = () => {
  return useQuery({
    queryKey: ["date-range-data"],
    queryFn: async (): Promise<DateRangeResponse> => {
      const { data } = await AXIOS.get("/api/dashboard/project/date-range");
      return data;
    },
  });
};
