"use client";
import React, { forwardRef, useRef, useState } from "react";
import { useClickOutside } from "@mantine/hooks";
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";

import DateRangePicker from "./date-range-picker/DateRangePicker";
import { AnalyticsConnectionButton } from "@/components/analytics/AnalyticsConnectionButton";

/* ================================== ICONS ================================= */
import { FiCalendar } from "react-icons/fi";
import ShareAndSettings from "@/ui/ShareAndSettings";

/* ================================= ZUSTAND ================================ */
import { useDateRangeStore } from "@/store/useDateRangeStore";

/* ========================================================================== */
type SharedAnalyticsHeaderProps = {
  title: string;
};

const SharedAnalyticsHeader = forwardRef<
  HTMLDivElement,
  SharedAnalyticsHeaderProps
>(({ title }, _ref) => {
  const [showDateRangePicker, setShowDateRangePicker] = useState(false);
  const toggleButtonRef = useRef<HTMLDivElement>(null);

  // Get selected date range from store
  const { selectedRange, comparisonRange, shouldShowComparison, isComparisonEnabled } =
    useDateRangeStore();

  const dateRangePickerRef = useClickOutside(() => {
    if (showDateRangePicker) {
      const isClickInsideToggle = toggleButtonRef.current?.contains(
        document.activeElement
      );

      if (!isClickInsideToggle) {
        setShowDateRangePicker(false);
      }
    }
  });

  const handleDateRangeClick = () => {
    setShowDateRangePicker((prev) => !prev);
  };

  // Helper function to format individual date range
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getPrimaryDateRange = () => {
    if (!selectedRange?.from || !selectedRange?.to) {
      return "Select date range";
    }
    return `${formatDate(selectedRange.from)} - ${formatDate(
      selectedRange.to
    )}`;
  };

  const getComparisonDateRange = () => {
    if (!comparisonRange?.from || !comparisonRange?.to) {
      return "Select comparison dates";
    }
    return `${formatDate(comparisonRange.from)} - ${formatDate(
      comparisonRange.to
    )}`;
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-full space-y-4  ">
      <DateRangePicker
        ShowDateRangePicker={showDateRangePicker}
        ref={dateRangePickerRef}
      />
      <Card className="flex flex-col xl:flex-row gap-2 w-full h-fit justify-between">
        <div className="flex flex-col justify-between">
          <div className="flex justify-between">
            <h3 className="font-extrabold text-xl text-secondary">{title}</h3>
          </div>
          <div className="flex gap-4 mt-3" ref={toggleButtonRef}>
            {/* Primary Date Range Container */}
            <motion.div 
              className="cursor-pointer" 
              onClick={handleDateRangeClick}
              whileTap={{ scale: 0.98 }}
              whileHover={{ opacity: 0.8 }}
            >
              <div className="text-sm font-medium text-secondary mb-1">
                Date Range
              </div>
              <div className="bg-gray-100 px-3 py-2 rounded-md min-w-[160px] flex items-center gap-2 relative">
                <div className="w-1 h-6 bg-primary rounded-sm flex-shrink-0"></div>
                <FiCalendar className="text-gray-500" />
                <span className="text-sm text-gray-800 font-medium">
                  {getPrimaryDateRange()}
                </span>
              </div>
            </motion.div>

            {/* Comparison Date Range Container - Only show when user has selected comparison dates */}
            {shouldShowComparison && (
              <motion.div 
                className="cursor-pointer" 
                onClick={handleDateRangeClick}
                whileTap={{ scale: 0.98 }}
                whileHover={{ opacity: 0.8 }}
              >
                <div className="text-sm font-medium text-secondary mb-1">
                  Compare To:
                </div>
                <div className="bg-gray-100 px-3 py-2 rounded-md min-w-[160px] flex items-center gap-2 relative">
                  <div className="w-1 h-6 bg-primary-yellow rounded-sm flex-shrink-0"></div>
                  <FiCalendar className="text-gray-500" />
                  <span className="text-sm text-gray-800 font-medium">
                    {getComparisonDateRange()}
                  </span>
                </div>
              </motion.div>
            )}
          </div>
        </div>
        <div className="flex flex-col justify-between items-end">
          <ShareAndSettings />
          <div className="w-full flex flex-col lg:flex-row justify-end gap-4 mt-2">
            <AnalyticsConnectionButton type="GA4" />
            <AnalyticsConnectionButton type="GSC" />
          </div>
        </div>
      </Card>
    </div>
  );
});

SharedAnalyticsHeader.displayName = "SharedAnalyticsHeader";

export default SharedAnalyticsHeader;