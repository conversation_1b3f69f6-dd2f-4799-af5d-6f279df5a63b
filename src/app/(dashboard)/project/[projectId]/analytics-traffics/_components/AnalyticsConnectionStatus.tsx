"use client";

import React from "react";
import { useAnalyticsConnection } from "@/contexts/AnalyticsConnectionContext";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";

interface AnalyticsConnectionStatusProps {
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

const AnalyticsConnectionStatus: React.FC<AnalyticsConnectionStatusProps> = ({
  showActions = false,
  compact = false,
  className = "",
}) => {
  const {
    isGoogleAnalyticsConnected,
    isGoogleSearchConsoleConnected,
    connectGA,
    connectGSC,
    gaConnecting,
    gscConnecting,
  } = useAnalyticsConnection();

  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Badge variant={isGoogleAnalyticsConnected ? "default" : "secondary"}>
          {isGoogleAnalyticsConnected ? (
            <CheckCircle className="w-3 h-3 mr-1" />
          ) : (
            <XCircle className="w-3 h-3 mr-1" />
          )}
          GA
        </Badge>
        <Badge variant={isGoogleSearchConsoleConnected ? "default" : "secondary"}>
          {isGoogleSearchConsoleConnected ? (
            <CheckCircle className="w-3 h-3 mr-1" />
          ) : (
            <XCircle className="w-3 h-3 mr-1" />
          )}
          GSC
        </Badge>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Google Analytics Status */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex items-center gap-3">
            {isGoogleAnalyticsConnected ? (
              <CheckCircle className="w-5 h-5 text-green-500" />
            ) : (
              <XCircle className="w-5 h-5 text-red-500" />
            )}
            <div>
              <h3 className="font-medium">Google Analytics</h3>
              <p className="text-sm text-muted-foreground">
                {isGoogleAnalyticsConnected ? "Connected" : "Not connected"}
              </p>
            </div>
          </div>
          {showActions && !isGoogleAnalyticsConnected && (
            <Button
              onClick={connectGA}
              disabled={gaConnecting}
              size="sm"
              variant="outline"
            >
              {gaConnecting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                "Connect"
              )}
            </Button>
          )}
        </div>

        {/* Google Search Console Status */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex items-center gap-3">
            {isGoogleSearchConsoleConnected ? (
              <CheckCircle className="w-5 h-5 text-green-500" />
            ) : (
              <XCircle className="w-5 h-5 text-red-500" />
            )}
            <div>
              <h3 className="font-medium">Search Console</h3>
              <p className="text-sm text-muted-foreground">
                {isGoogleSearchConsoleConnected ? "Connected" : "Not connected"}
              </p>
            </div>
          </div>
          {showActions && !isGoogleSearchConsoleConnected && (
            <Button
              onClick={connectGSC}
              disabled={gscConnecting}
              size="sm"
              variant="outline"
            >
              {gscConnecting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                "Connect"
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Connection Requirements Info */}
      {(!isGoogleAnalyticsConnected || !isGoogleSearchConsoleConnected) && (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> Some analytics features require Google Analytics 
            {!isGoogleAnalyticsConnected && !isGoogleSearchConsoleConnected 
              ? " and Search Console" 
              : !isGoogleAnalyticsConnected 
              ? "" 
              : " and Search Console"
            } to be connected to display data.
          </p>
        </div>
      )}
    </div>
  );
};

export default AnalyticsConnectionStatus;
