import React from "react";
import { useDateRangeStore } from "@/store/useDateRangeStore";

const DebugDateRange = () => {
  const { 
    selectedRange, 
    comparisonRange, 
    isComparisonEnabled, 
    shouldShowComparison,
    setComparisonEnabled,
    getFormattedDates
  } = useDateRangeStore();

  const formattedDates = getFormattedDates();

  return (
    <div className="fixed top-4 right-4 bg-white p-4 border rounded shadow-lg z-50 text-xs max-w-sm">
      <h3 className="font-bold mb-2">Debug Date Range Store</h3>
      
      <div className="space-y-1">
        <div>
          <strong>isComparisonEnabled:</strong> {isComparisonEnabled ? "✅ true" : "❌ false"}
        </div>
        
        <div>
          <strong>shouldShowComparison:</strong> {shouldShowComparison ? "✅ true" : "❌ false"}
        </div>
        
        <div>
          <strong>selectedRange:</strong> 
          {selectedRange?.from && selectedRange?.to 
            ? `${selectedRange.from.toDateString()} - ${selectedRange.to.toDateString()}`
            : "❌ None"
          }
        </div>
        
        <div>
          <strong>comparisonRange:</strong> 
          {comparisonRange?.from && comparisonRange?.to 
            ? `${comparisonRange.from.toDateString()} - ${comparisonRange.to.toDateString()}`
            : "❌ None"
          }
        </div>

        <div>
          <strong>Formatted Dates:</strong>
          <div className="ml-2">
            <div>Start: {formattedDates.startDate || "None"}</div>
            <div>End: {formattedDates.endDate || "None"}</div>
            <div>Comp Start: {formattedDates.comparisonStartDate || "None"}</div>
            <div>Comp End: {formattedDates.comparisonEndDate || "None"}</div>
          </div>
        </div>
      </div>

      <button 
        onClick={() => setComparisonEnabled(!isComparisonEnabled)}
        className="mt-2 px-2 py-1 bg-blue-500 text-white rounded text-xs"
      >
        Toggle Comparison
      </button>
    </div>
  );
};

export default DebugDateRange;