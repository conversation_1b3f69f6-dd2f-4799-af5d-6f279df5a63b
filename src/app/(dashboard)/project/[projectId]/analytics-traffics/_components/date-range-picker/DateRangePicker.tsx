import React, { useState, useMemo, forwardRef, useEffect } from "react";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";

/* =============================== STYLES =============================== */
import "./DateRangePicker.css";

/* ============================ REACT DAY PICKER ============================ */
import { DateRange } from "react-day-picker";

/* ================================== ICONS ================================= */
import { MdCompareArrows } from "react-icons/md";

/* ============================= EXTRA CONSTANTS ============================ */
import { getPastTenYears, months } from "./Constants";

/* =============================== COMPONENTS =============================== */
import RangePickerCalendar from "@/components/ui/range-picker-calendar/RangePickerCalendar";
import Card from "@/components/ui/card";
import { AnimatePresence } from "framer-motion";

/* ================================= ZUSTAND ================================ */
import { useDateRangeStore } from "@/store/useDateRangeStore";

/* ================================== MAIN ================================== */
const DateRangePicker = forwardRef<
  HTMLDivElement,
  { ShowDateRangePicker: boolean }
>(({ ShowDateRangePicker }, ref) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */

  // Zustand store
  const {
    selectedRange,
    setSelectedRange,
    comparisonRange,
    setComparisonRange,
    isComparisonEnabled,
    setComparisonEnabled,
  } = useDateRangeStore();

  const monthsDropdownData = useMemo(
    () => ({
      button: months[0],
      options: months,
    }),
    [months]
  );

  const yearsDropdownData = useMemo(
    () => ({
      button: getPastTenYears()[0],
      options: getPastTenYears(),
    }),
    [getPastTenYears]
  );

  const [month, setMonth] = useState(new Date());
  const [monthNext, setMonthNext] = useState(new Date());

  // Keep calendar months in sync with selected ranges
  useEffect(() => {
    // Primary calendar anchors to the selected range end month (or today)
    const end = selectedRange?.to ?? new Date();
    const primaryMonth = new Date(end.getFullYear(), end.getMonth(), 1);
    setMonth(primaryMonth);

    // Comparison calendar: if enabled and has range, anchor to its end month
    if (isComparisonEnabled && comparisonRange?.to) {
      const compEnd = comparisonRange.to;
      const compMonth = new Date(compEnd.getFullYear(), compEnd.getMonth(), 1);
      setMonthNext(compMonth);
    } else {
      // Otherwise, show the month immediately after the primary month
      const nextMonth = new Date(primaryMonth.getFullYear(), primaryMonth.getMonth() + 1, 1);
      setMonthNext(nextMonth);
    }
  }, [selectedRange, comparisonRange, isComparisonEnabled]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <AnimatePresence>
      {ShowDateRangePicker && (
        <Card
          ref={ref}
          key={ShowDateRangePicker ? "show" : "hide"}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.1 }}
          className={cn(
            "w-fit flex p-0 overflow-hidden border-2 shadow-xl absolute z-40 top-[290px] xl:top-[320px] left-[50vw] -translate-x-1/2 xl:-translate-x-0 xl:left-auto"
          )}
        >
          <div className="flex p-6 pl-4 items-start gap-2">
            <RangePickerCalendar
              selectedColor="bg-primary"
              yearDropdown={yearsDropdownData}
              monthDropdown={monthsDropdownData}
              month={month}
              setMonth={setMonth}
              selected={selectedRange}
              setSelected={setSelectedRange}
            />
            <div className="h-full flex items-center justify-center pt-[10%]">
              <button
                role="button"
                className={cn(
                  "rounded-lg p-1.5",
                  !isComparisonEnabled
                    ? "inner-shadow-disabled"
                    : "inner-shadow-enabled"
                )}
              >
                <MdCompareArrows
                  className={cn(
                    "text-xl text-secondary cursor-pointer transition-all duration-200"
                  )}
                  onClick={() => {
                    if (isComparisonEnabled) {
                      // If disabling comparison, clear the comparison range
                      setComparisonRange(undefined);
                      setComparisonEnabled(false);
                    } else {
                      // If enabling comparison, keep existing range or set default
                      setComparisonEnabled(true);
                      if (!comparisonRange?.from || !comparisonRange?.to) {
                        // Set default comparison range if none exists
                        const today = new Date();
                        const sixtyDaysAgo = new Date();
                        sixtyDaysAgo.setDate(today.getDate() - 60);
                        const thirtyDaysAgo = new Date();
                        thirtyDaysAgo.setDate(today.getDate() - 30);
                        setComparisonRange({
                          from: sixtyDaysAgo,
                          to: thirtyDaysAgo,
                        });
                      }
                    }
                  }}
                />
              </button>
            </div>
            <RangePickerCalendar
              disabled={!isComparisonEnabled}
              selectedColor="bg-primary-yellow"
              yearDropdown={yearsDropdownData}
              monthDropdown={monthsDropdownData}
              month={monthNext}
              setMonth={setMonthNext}
              selected={comparisonRange}
              setSelected={setComparisonRange}
            />
          </div>
        </Card>
      )}
    </AnimatePresence>
  );
});

DateRangePicker.displayName = "DateRangePicker";

export default DateRangePicker;