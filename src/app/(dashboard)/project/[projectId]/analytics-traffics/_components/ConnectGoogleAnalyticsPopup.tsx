"use client";
import React from "react";
import { IoClose } from "react-icons/io5";
import { BsShieldCheck } from "react-icons/bs";
import { Button } from "@/components/ui/button";
import GoogleAnalyticsPopupDropdown from "./GoogleAnalyticsPopupDropdown";
import googleAnalyticsLogo from "@/../public/images/create-project/google-analytics.svg";
import Image from "next/image";

/* ================================== TYPES ================================= */
type TProps = {
  connectGooglePopup: boolean;
  setConnectGooglePopup: React.Dispatch<React.SetStateAction<boolean>>;
};
/* ========================================================================== */
const ConnectGoogleAnalyticsPopup = ({
  connectGooglePopup,
  setConnectGooglePopup,
}: TProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const googleAccountOptions = [
    { id: 1, title: "option 1", icon: <IoClose /> },
    { id: 2, title: "option 2", icon: <IoClose /> },
    { id: 3, title: "option 3", icon: <IoClose /> },
  ];
  const dropDownData = [
    {
      id: 1,
      label: "Google account",
      deleteButton: true,
      icon: <BsShieldCheck />,
      options: googleAccountOptions,
      addNewButtonLabel: "add new account",
    },
    {
      id: 2,
      label: "google analytics account",
      deleteButton: false,
      options: googleAccountOptions,
      addNewButtonLabel: "",
    },
    {
      id: 3,
      label: "properties & apps",
      deleteButton: false,
      icon: <Image src={googleAnalyticsLogo} alt={"Google Analytics Logo"} />,
      options: googleAccountOptions,
      addNewButtonLabel: "",
    },
  ];
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return (
    <div
      className={`w-screen h-screen absolute z-40 top-0 left-0 flex justify-center items-center ${
        connectGooglePopup ? "flex" : "hidden"
      }`}
    >
      <div className="bg-white rounded-2xl p-6 hidden lg:flex lg:flex-col gap-6 absolute z-10 shadow-2xl w-[40vw]">
        <div className="flex justify-between items-center">
          <span>Connect Google Analytics</span>
          <IoClose
            onClick={() => setConnectGooglePopup((prev) => !prev)}
            className="cursor-pointer"
          />
        </div>
        {dropDownData.map((data) => (
          <GoogleAnalyticsPopupDropdown
            key={data.id}
            deleteButton={data.deleteButton}
            label={data.label}
            icon={data.icon}
            options={data.options}
            addNewButtonLabel={data.addNewButtonLabel}
          />
        ))}
        <div className="w-full flex justify-end gap-4">
          <Button
            onClick={() => setConnectGooglePopup((prev) => !prev)}
            variant={"outline"}
            className="border-2 border-primary-gray"
          >
            Cancel
          </Button>
          <Button variant={"default"}>Connect</Button>
        </div>
      </div>
      <div
        className="backdrop-blur-xs w-full h-full"
        onClick={() => setConnectGooglePopup((prev) => !prev)}
      />
    </div>
  );
};

export default ConnectGoogleAnalyticsPopup;
