import Card from "@/components/ui/card";
import { cn } from "@/utils/cn";
import React from "react";
import Skeleton from "react-loading-skeleton";

const SmallChartSkeleton = ({ className }: { className?: string }) => {
  return (
    <Card
      className={cn(
        "w-full col-span-1 lg:col-span-1 border h-[170px] animate-pulse bg-gray-50",
        className,
      )}
    >
      <Skeleton height={10} width={80} />
      <Skeleton height={20} width={50} />
      <svg
        className="recharts-surface"
        width="167"
        height="88"
        viewBox="0 0 167 88"
        style={{ width: "100%", height: "70%" }}
      >
        <defs>
          <clipPath id="recharts126-clip">
            <rect x="5" y="5" height="78" width="157"></rect>
          </clipPath>
        </defs>
        <g className="recharts-layer recharts-line">
          <path
            stroke="#bbb"
            width="157"
            height="78"
            strokeWidth="1"
            fill="none"
            className="recharts-curve recharts-line-curve"
            strokeDasharray="163.7733917236328px 0px"
            d="M5,31C18.083,37.5,31.167,44,44.25,44C57.333,44,70.417,27.1,83.5,27.1C96.583,27.1,109.667,31,122.75,31C135.833,31,148.917,31,162,31"
          ></path>
          <g className="recharts-layer"></g>
          <g className="recharts-layer recharts-line-dots">
            <circle
              r="3"
              stroke="#bbb"
              width="157"
              height="78"
              strokeWidth="1"
              fill="#fff"
              cx="5"
              cy="31.000000000000004"
              className="recharts-dot recharts-line-dot"
            ></circle>
            <circle
              r="3"
              stroke="#bbb"
              width="157"
              height="78"
              strokeWidth="1"
              fill="#fff"
              cx="44.25"
              cy="44"
              className="recharts-dot recharts-line-dot"
            ></circle>
            <circle
              r="3"
              stroke="#bbb"
              width="157"
              height="78"
              strokeWidth="1"
              fill="#fff"
              cx="83.5"
              cy="27.099999999999998"
              className="recharts-dot recharts-line-dot"
            ></circle>
            <circle
              r="3"
              stroke="#bbb"
              width="157"
              height="78"
              strokeWidth="1"
              fill="#fff"
              cx="122.75"
              cy="31.000000000000004"
              className="recharts-dot recharts-line-dot"
            ></circle>
            <circle
              r="3"
              stroke="#bbb"
              width="157"
              height="78"
              strokeWidth="1"
              fill="#fff"
              cx="162"
              cy="31.000000000000004"
              className="recharts-dot recharts-line-dot"
            ></circle>
          </g>
        </g>
      </svg>
    </Card>
  );
};

export default SmallChartSkeleton;
