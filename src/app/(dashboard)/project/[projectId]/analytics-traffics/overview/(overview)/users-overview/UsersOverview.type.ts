import { ProgressbarData } from "../../../types/AnalyticsTraffics.types";

// GA4 API Response Types for User Countries Data
export interface CountryItem {
  name: string; // Country name (e.g., "United States")
  users: number; // Number of users from this country
  percentage: number; // Percentage of total users
}

// GA4 API Response Types for User Cities Data
export interface CityItem {
  name: string; // City name (e.g., "New York")
  users: number; // Number of users from this city
  percentage: number; // Percentage of total users
}

// GA4 API Response Types for User Gender Data
export interface GenderItem {
  name: string; // Gender name (e.g., "Male", "Female")
  users: number; // Number of users of this gender
  percentage: number; // Percentage of total users
}

// GA4 API Response Types for User Device Data
export interface DeviceItem {
  name: string; // Device name (e.g., "Desktop", "Mobile", "Tablet")
  users: number; // Number of users using this device
  percentage: number; // Percentage of total users
}

// GA4 API Response Types for User Age Data
export interface AgeItem {
  name: string; // Age range (e.g., "18-24", "25-34", "35-44")
  users: number; // Number of users in this age range
  percentage: number; // Percentage of total users
}

export interface CountryDataTotals {
  total_users: number; // Total users across all countries
}

export interface CountryDataPeriod {
  start_date: string; // Start date in YYYY-MM-DD format
  end_date: string; // End date in YYYY-MM-DD format
}

export interface CountryData {
  items: CountryItem[]; // Array of country data
  totals: CountryDataTotals; // Total metrics
  period: CountryDataPeriod; // Date range for the data
}

export interface CountriesAPIResponse {
  status: string; // Status (e.g., "success")
  project_id: string; // Project identifier
  metric: string; // Metric type (e.g., "countries")
  data: CountryData; // Country data
  last_sync: string; // Last sync timestamp
}

// Cities API Response Types
export interface CityData {
  items: CityItem[]; // Array of city data
  totals: CountryDataTotals; // Total metrics (reusing same structure)
  period: CountryDataPeriod; // Date range for the data
}

export interface CitiesAPIResponse {
  status: string; // Status (e.g., "success")
  project_id: string; // Project identifier
  metric: string; // Metric type (e.g., "cities")
  data: CityData; // City data
  last_sync: string; // Last sync timestamp
}

// Gender API Response Types
export interface GenderData {
  items: GenderItem[]; // Array of gender data
  totals: CountryDataTotals; // Total metrics (reusing same structure)
  period: CountryDataPeriod; // Date range for the data
}

export interface GenderAPIResponse {
  status: string; // Status (e.g., "success")
  project_id: string; // Project identifier
  metric: string; // Metric type (e.g., "gender")
  data: GenderData; // Gender data
  last_sync: string; // Last sync timestamp
}

// Device API Response Types
export interface DeviceData {
  items: DeviceItem[]; // Array of device data
  totals: CountryDataTotals; // Total metrics (reusing same structure)
  period: CountryDataPeriod; // Date range for the data
}

export interface DeviceAPIResponse {
  status: string; // Status (e.g., "success")
  project_id: string; // Project identifier
  metric: string; // Metric type (e.g., "device")
  data: DeviceData; // Device data
  last_sync: string; // Last sync timestamp
}

// Age API Response Types
export interface AgeData {
  items: AgeItem[]; // Array of age data
  totals: CountryDataTotals; // Total metrics (reusing same structure)
  period: CountryDataPeriod; // Date range for the data
}

export interface AgeAPIResponse {
  status: string; // Status (e.g., "success")
  project_id: string; // Project identifier
  metric: string; // Metric type (e.g., "age")
  data: AgeData; // Age data
  last_sync: string; // Last sync timestamp
}

// Transformed Data Types for Components
export interface CountryMapData {
  leftMap: Record<string, string>;
  rightMap: Record<string, string>;
}

// Enhanced type with comparison support
export type UsersOverviewData = {
  leftMap: Record<string, string>;
  rightMap: Record<string, string>;
  progressbarData: ProgressbarData[];
  comparisonProgressbarData?: ProgressbarData[];
};
