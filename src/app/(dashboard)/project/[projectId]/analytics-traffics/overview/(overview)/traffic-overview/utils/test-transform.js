// Quick test to verify the transform logic
// This file can be deleted after verification

const testTransformLogic = () => {
  // Mock data for testing
  const currentMetric = {
    total_value: 13500,
    traffic_sources: {
      organic: { value: 13500, percentage: 100 },
    },
  };

  const previousMetric = {
    total_value: 12400,
    traffic_sources: {
      organic: { value: 12400, percentage: 100 },
    },
  };

  console.log("=== GROWTH SCENARIO TEST ===");
  console.log("Current (Purple): 13.5k");
  console.log("Previous (Yellow): 12.4k");
  console.log("Expected visualization:");
  console.log("- Yellow bar: 12.4k (base layer)");
  console.log("- Purple bar: 1.1k (difference layer)");
  console.log("- Tooltip should show: Previous: 12.4k, Current: 13.5k");

  // Reverse scenario
  const currentMetricDecline = {
    total_value: 1400,
    traffic_sources: {
      organic: { value: 1400, percentage: 100 },
    },
  };

  const previousMetricDecline = {
    total_value: 2800,
    traffic_sources: {
      organic: { value: 2800, percentage: 100 },
    },
  };

  console.log("\n=== DECLINE SCENARIO TEST ===");
  console.log("Current (Purple): 1.4k");
  console.log("Previous (Yellow): 2.8k");
  console.log("Expected visualization:");
  console.log("- Purple bar: 1.4k (base layer)");
  console.log("- Yellow bar: 1.4k (difference layer)");
  console.log("- Tooltip should show: Current: 1.4k, Previous: 2.8k");
};

// Run test
testTransformLogic();
