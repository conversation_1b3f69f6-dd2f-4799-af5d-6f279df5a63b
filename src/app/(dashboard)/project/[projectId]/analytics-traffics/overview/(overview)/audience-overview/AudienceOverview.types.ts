export type ChartDataPoint = {
  name: string;
  primaryDate?: string; // Original primary date from API
  value: number;
  comparisonValue?: number; // For dual period comparison
  comparisonDate?: string; // Original comparison date from API
};

export type ChartResponse = {
  id: number;
  title: string;
  bigNumber: string;
  smallNumber: string;
  data: ChartDataPoint[];
  hasComparison?: boolean; // Indicates if comparison data is available
};

/* ======================= AUDIENCE OVERVIEW API RESPONSE ======================= */
export interface DailyMetric {
  date: string;
  total_users: number;
  new_users: number;
  returning_users: number;
  views: number;
  sessions: number;
  engaged_sessions: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  active_users: number;
}

export interface Totals {
  total_users: number;
  new_users: number;
  returning_users: number;
  views: number;
  sessions: number;
  engaged_sessions: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  active_users: number;
  active_users_rate: number;
  returning_users_rate: number;
}

export interface Period {
  start_date: string;
  end_date: string;
  days_count: number;
}

export interface AudienceOverviewApiResponse {
  status: string;
  project_id: string;
  data: {
    daily_metrics: DailyMetric[];
    totals: Totals;
    period: Period;
  };
  last_sync: string;
}

/* ======================= LEGACY TYPES ======================= */
type Colors = {
  name: string;
  color: string;
};

type LineChartData = {
  name: string;
  totalUsers: number;
};

type TotalUsers = {
  amount: number;
  growth: string;
};

type CardsData = Record<string, TotalUsers>;

export type AudienceOverviewPopupResponse = {
  cardsData: CardsData;
  colors: Colors[];
  lineChartData: LineChartData[];
};
