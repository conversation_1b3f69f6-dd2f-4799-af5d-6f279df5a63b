import type {
  AudienceOverviewApiResponse,
  DailyMetric,
} from "./AudienceOverview.types";

/**
 * Formats a number to a readable string with appropriate suffixes
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

/**
 * Formats percentage to display with % symbol
 */
export const formatPercentage = (rate: number): string => {
  return (rate * 100).toFixed(1) + "%";
};

/**
 * Formats time in seconds to readable format
 */
export const formatTime = (seconds: number): string => {
  if (seconds >= 60) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${Math.floor(seconds)}s`;
};

/**
 * Formats date from YYYYMMDD to readable format
 * Uses month abbreviation and day for better readability
 */
export const formatDate = (dateStr: string): string => {
  const year = dateStr.substring(0, 4);
  const month = dateStr.substring(4, 6);
  const day = dateStr.substring(6, 8);
  const date = new Date(`${year}-${month}-${day}`);

  // Use format: Jul 16
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Calculates growth percentage between current and previous period
 */
export const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) return "+0%";
  const growth = ((current - previous) / previous) * 100;
  const sign = growth >= 0 ? "+" : "";
  return `${sign}${growth.toFixed(1)}%`;
};

/**
 * Creates chart data points with optional comparison values
 */
const createChartDataPoints = (
  primaryMetrics: DailyMetric[],
  comparisonMetrics: DailyMetric[] | undefined,
  valueKey: keyof DailyMetric
) => {
  return primaryMetrics.map((metric, index) => {
    const comparisonMetric = comparisonMetrics?.[index];
    return {
      name: formatDate(metric.date),
      primaryDate: metric.date, // Store original primary date
      value: metric[valueKey] as number,
      ...(comparisonMetric && {
        comparisonValue: comparisonMetric[valueKey] as number,
        comparisonDate: comparisonMetric.date, // Store original comparison date
      }),
    };
  });
};

/**
 * Transforms the audience overview API response into structured data for individual chart components
 * Supports dual period comparison when comparisonResponse is provided
 */
export const transformAudienceOverviewData = (
  primaryResponse: AudienceOverviewApiResponse,
  comparisonResponse?: AudienceOverviewApiResponse | null
) => {
  const { daily_metrics: primaryMetrics, totals: primaryTotals } =
    primaryResponse.data;
  const comparisonTotals = comparisonResponse?.data.totals;
  const comparisonMetrics = comparisonResponse?.data.daily_metrics;
  const hasComparison = !!comparisonResponse;

  // Create individual chart data objects
  return {
    totalUsers: {
      title: "Total Users",
      bigNumber: formatNumber(primaryTotals.total_users),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(
            primaryTotals.total_users,
            comparisonTotals.total_users
          )
        : undefined,
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "total_users"
      ),
      hasComparison,
    },
    newUsers: {
      title: "New Users",
      bigNumber: formatNumber(primaryTotals.new_users),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(primaryTotals.new_users, comparisonTotals.new_users)
        : undefined,
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "new_users"
      ),
      hasComparison,
    },
    returningUsers: {
      title: "Returning Users",
      bigNumber: formatNumber(primaryTotals.returning_users),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(
            primaryTotals.returning_users,
            comparisonTotals.returning_users
          )
        : undefined,
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "returning_users"
      ),
      hasComparison,
    },
    pageViews: {
      title: "Page Views",
      bigNumber: formatNumber(primaryTotals.views),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(primaryTotals.views, comparisonTotals.views)
        : undefined,
      data: createChartDataPoints(primaryMetrics, comparisonMetrics, "views"),
      hasComparison,
    },
    sessions: {
      title: "Sessions",
      bigNumber: formatNumber(primaryTotals.sessions),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(primaryTotals.sessions, comparisonTotals.sessions)
        : undefined,
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "sessions"
      ),
      hasComparison,
    },
    engagedSessions: {
      title: "Engaged Sessions",
      bigNumber: formatNumber(primaryTotals.engaged_sessions),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(
            primaryTotals.engaged_sessions,
            comparisonTotals.engaged_sessions
          )
        : undefined,
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "engaged_sessions"
      ),
      hasComparison,
    },
    avgEngagementTime: {
      title: "Avg. Engagement Time",
      bigNumber: formatTime(primaryTotals.avg_engagement_time),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(
            primaryTotals.avg_engagement_time,
            comparisonTotals.avg_engagement_time
          )
        : undefined,
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "avg_engagement_time"
      ),
      hasComparison,
    },
    engagementRate: {
      title: "Engagement Rate",
      bigNumber: formatPercentage(primaryTotals.engagement_rate),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(
            primaryTotals.engagement_rate,
            comparisonTotals.engagement_rate
          )
        : undefined,
      data: primaryMetrics.map((metric, index) => {
        const comparisonMetric = comparisonMetrics?.[index];
        return {
          name: formatDate(metric.date),
          primaryDate: metric.date, // Store original primary date
          value: metric.engagement_rate * 100, // Convert to percentage for chart
          ...(comparisonMetric && {
            comparisonValue: comparisonMetric.engagement_rate * 100,
            comparisonDate: comparisonMetric.date, // Store original comparison date
          }),
        };
      }),
      hasComparison,
    },
    eventCount: {
      title: "Event Count",
      bigNumber: formatNumber(primaryTotals.event_count),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(
            primaryTotals.event_count,
            comparisonTotals.event_count
          )
        : undefined,
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "event_count"
      ),
      hasComparison,
    },
    conversions: {
      title: "Conversions",
      bigNumber: formatNumber(primaryTotals.conversions),
      smallNumber: hasComparison && comparisonTotals
        ? calculateGrowth(
            primaryTotals.conversions,
            comparisonTotals.conversions
          )
        : undefined,
      data: createChartDataPoints(
        primaryMetrics,
        comparisonMetrics,
        "conversions"
      ),
      hasComparison,
    },
  };
};
