import { useMemo } from "react";
import type { UsersOverviewData } from "./UsersOverview.type";
import useCountriesData from "./hooks/useCountriesData";
import useCitiesData, { CitiesDataResult } from "./hooks/useCitiesData";
import useGenderData, { GenderDataResult } from "./hooks/useGenderData";
import useDeviceData, { DeviceDataResult } from "./hooks/useDeviceData";
import useAgeData, { AgeDataResult } from "./hooks/useAgeData";
import { ProgressbarData } from "../../types/AnalyticsTraffics.types";
import { useDateRangeStore } from "@/store/useDateRangeStore";

// Helper function to match comparison data by title
const matchComparisonData = (
  currentData: ProgressbarData[],
  comparisonData: ProgressbarData[] | undefined
): ProgressbarData[] => {
  if (!comparisonData) return [];

  return currentData.map((currentItem) => {
    const matchingComparison = comparisonData.find(
      (compItem) => compItem.title === currentItem.title
    );
    return {
      title: currentItem.title,
      percentage: matchingComparison?.percentage || 0,
    };
  });
};

/**
 * Custom hook for fetching user overview data from GA4 API
 * @param userOverviewQuery - The selected tab (Countries, Cities, Gender, Device, Age)
 * @returns Query result with map data and progress bar data
 */
const useUsersOverview = (userOverviewQuery: string) => {
  const { isComparisonEnabled } = useDateRangeStore();
  const isSelectedTabCities = userOverviewQuery.toLowerCase() === "cities";
  const isSelectedTabGender = userOverviewQuery.toLowerCase() === "gender";
  const isSelectedTabDevice = userOverviewQuery.toLowerCase() === "device";
  const isSelectedTabAge = userOverviewQuery.toLowerCase() === "age";

  // Always fetch countries data for maps (independent of tab selection)
  const {
    data: countriesMapData,
    isLoading: isCountriesLoading,
    error: countriesError,
  } = useCountriesData();

  // Only fetch cities data when Cities tab is selected
  const {
    data: citiesProgressData,
    isLoading: isCitiesLoading,
    error: citiesError,
  } = useCitiesData(isSelectedTabCities);

  // Only fetch gender data when Gender tab is selected
  const {
    data: genderProgressData,
    isLoading: isGenderLoading,
    error: genderError,
  } = useGenderData(isSelectedTabGender);

  // Only fetch device data when Device tab is selected
  const {
    data: deviceProgressData,
    isLoading: isDeviceLoading,
    error: deviceError,
  } = useDeviceData(isSelectedTabDevice);

  // Only fetch age data when Age tab is selected
  const {
    data: ageProgressData,
    isLoading: isAgeLoading,
    error: ageError,
  } = useAgeData(isSelectedTabAge);

  // Determine the appropriate data based on selected tab
  const result = useMemo((): {
    data: UsersOverviewData | undefined;
    isLoading: boolean;
    error: Error | null;
  } => {
    const isSelectedTabCountries =
      userOverviewQuery.toLowerCase() === "countries";

    // Use countries data for maps - only set rightMap when comparison is enabled
    const leftMap = countriesMapData?.leftMap || {};
    const rightMap = isComparisonEnabled ? (countriesMapData?.rightMap || {}) : {};

    // Determine progress bar data based on selected tab
    let progressbarData: ProgressbarData[] = [];
    let comparisonProgressbarData: ProgressbarData[] | undefined;

    if (isSelectedTabCities) {
      // Use cities API data for Cities tab
      progressbarData =
        (citiesProgressData as CitiesDataResult)?.progressbarData || [];
      const rawComparisonData = (citiesProgressData as CitiesDataResult)
        ?.comparisonProgressbarData;
      comparisonProgressbarData = isComparisonEnabled ? matchComparisonData(
        progressbarData,
        rawComparisonData
      ) : undefined;
    } else if (isSelectedTabGender) {
      // Use gender API data for Gender tab
      progressbarData =
        (genderProgressData as GenderDataResult)?.progressbarData || [];
      const rawComparisonData = (genderProgressData as GenderDataResult)
        ?.comparisonProgressbarData;
      comparisonProgressbarData = isComparisonEnabled ? matchComparisonData(
        progressbarData,
        rawComparisonData
      ) : undefined;
    } else if (isSelectedTabDevice) {
      // Use device API data for Device tab
      progressbarData =
        (deviceProgressData as DeviceDataResult)?.progressbarData || [];
      const rawComparisonData = (deviceProgressData as DeviceDataResult)
        ?.comparisonProgressbarData;
      comparisonProgressbarData = isComparisonEnabled ? matchComparisonData(
        progressbarData,
        rawComparisonData
      ) : undefined;
    } else if (isSelectedTabAge) {
      // Use age API data for Age tab
      progressbarData =
        (ageProgressData as AgeDataResult)?.progressbarData || [];
      const rawComparisonData = (ageProgressData as AgeDataResult)
        ?.comparisonProgressbarData;
      comparisonProgressbarData = isComparisonEnabled ? matchComparisonData(
        progressbarData,
        rawComparisonData
      ) : undefined;
    } else if (isSelectedTabCountries && countriesMapData) {
      // Use countries API data for Countries tab progress bars
      progressbarData = countriesMapData.progressbarData || [];
      const rawComparisonData = countriesMapData.comparisonProgressbarData;
      comparisonProgressbarData = isComparisonEnabled ? matchComparisonData(
        progressbarData,
        rawComparisonData
      ) : undefined;
    }
    // Removed the fallback mock data generation - if no data, return empty arrays

    // Determine overall loading state
    const isLoading =
      isCountriesLoading ||
      (isSelectedTabCities && isCitiesLoading) ||
      (isSelectedTabGender && isGenderLoading) ||
      (isSelectedTabDevice && isDeviceLoading) ||
      (isSelectedTabAge && isAgeLoading);

    // Determine overall error state
    const error =
      countriesError ||
      (isSelectedTabCities ? citiesError : null) ||
      (isSelectedTabGender ? genderError : null) ||
      (isSelectedTabDevice ? deviceError : null) ||
      (isSelectedTabAge ? ageError : null);

    // Return combined data
    const data: UsersOverviewData | undefined = countriesMapData
      ? {
          leftMap,
          rightMap,
          progressbarData,
          comparisonProgressbarData,
        }
      : undefined;

    return {
      data,
      isLoading,
      error,
    };
  }, [
    userOverviewQuery,
    countriesMapData,
    citiesProgressData,
    genderProgressData,
    deviceProgressData,
    ageProgressData,
    isCountriesLoading,
    isCitiesLoading,
    isGenderLoading,
    isDeviceLoading,
    isAgeLoading,
    countriesError,
    citiesError,
    genderError,
    deviceError,
    ageError,
    isComparisonEnabled, // Added this dependency
  ]);

  return result;
};

export default useUsersOverview;