import httpService from "@/services/httpService";
import { useQuery } from "@tanstack/react-query";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { ProgressbarData } from "../../../../types/AnalyticsTraffics.types";
import type { AgeAPIResponse } from "../UsersOverview.type";

// Extended type to include comparison data
export interface AgeDataResult {
  progressbarData: ProgressbarData[];
  comparisonProgressbarData?: ProgressbarData[];
}

/**
 * Check if Age API response is valid
 * @param response - The Age API response to validate
 * @returns Boolean indicating if response is valid
 */
function isValidAgeResponse(response: unknown): response is AgeAPIResponse {
  return (
    response &&
    typeof response === "object" &&
    response.status === "success" &&
    response.data &&
    Array.isArray(response.data.items) &&
    response.data.totals &&
    typeof response.data.totals.total_users === "number"
  );
}

/**
 * Transform Age API response data to progress bar format
 * @param apiResponse - The Age API response containing age data
 * @returns Array of progress bar data
 */
function transformAgeToProgressBarData(
  apiResponse: AgeAPIResponse
): ProgressbarData[] {
  const { items } = apiResponse.data;

  // Sort by users count (descending) and take all items (age ranges)
  const sortedAges = [...items].sort((a, b) => b.users - a.users);

  return sortedAges.map((item) => ({
    title: item.name,
    percentage: Math.round(item.percentage),
    value: item.users,
  }));
}

/**
 * Custom hook for fetching age data from GA4 API (for progress bars)
 * This hook is only called when the Age tab is selected
 * @param enabled - Whether to enable the query (should be true only for Age tab)
 * @returns Query result with age progress bar data
 */
const useAgeData = (enabled: boolean = false) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "user-age-data",
      projectId,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<AgeDataResult> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Build API URL with GA4 endpoint format for user age
      const apiUrl = `/api/project/GA4/user/overview/age/${projectId}/`;

      try {
        // Fetch current period data
        const currentParams: Record<string, string> = {};
        if (startDate && endDate) {
          currentParams.start_date = startDate;
          currentParams.end_date = endDate;
        }

        const response = await httpService.get(apiUrl, {
          params:
            Object.keys(currentParams).length > 0 ? currentParams : undefined,
          useAuth: true,
        });

        // Validate response
        if (!isValidAgeResponse(response.data)) {
          throw new Error("Invalid age API response format");
        }

        // Transform to progress bar data
        const progressbarData = transformAgeToProgressBarData(response.data);

        // Fetch comparison data if comparison is enabled
        let comparisonProgressbarData: ProgressbarData[] | undefined;
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          try {
            const comparisonParams: Record<string, string> = {
              start_date: comparisonStartDate,
              end_date: comparisonEndDate,
            };

            const comparisonResponse = await httpService.get(apiUrl, {
              params: comparisonParams,
              useAuth: true,
            });

            if (isValidAgeResponse(comparisonResponse.data)) {
              comparisonProgressbarData = transformAgeToProgressBarData(
                comparisonResponse.data
              );
            }
          } catch (error) {
            console.warn("Failed to fetch comparison age data:", error);
          }
        }

        return {
          progressbarData,
          comparisonProgressbarData,
        };
      } catch (error) {
        console.error("Age API call failed:", error);

        // Re-throw the error to let React Query handle it
        throw new Error(
          error instanceof Error
            ? `Failed to fetch user age data: ${error.message}`
            : "Failed to fetch user age data"
        );
      }
    },
    enabled: enabled && isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useAgeData;
