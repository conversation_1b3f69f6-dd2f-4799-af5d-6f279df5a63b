import React from "react";

/* ============================== FRAMER MOTION ============================= */
import { cn } from "@/lib/utils";
import Skeleton from "react-loading-skeleton";

interface ProgressBarProps {
  percentage: number;
  className?: string;
  title: string;
  color?: string;
  isLoading: boolean | false;
  // Comparison props
  comparisonPercentage?: number;
  isComparisonEnabled?: boolean;
  // New: numeric values for current and comparison
  value?: number;
  comparisonValue?: number;
}

const ProgressBar = ({
  percentage,
  className,
  title,
  color,
  isLoading,
  comparisonPercentage,
  isComparisonEnabled = false,
  value,
  comparisonValue,
}: ProgressBarProps) => {
  // Calculate comparison display values (for percentage bars)
  const pctCurrent = percentage;
  const pctComparison = comparisonPercentage ?? 0;
  const difference = pctCurrent - pctComparison;
  const isPositive = difference > 0;
  const isNegative = difference < 0;

  return (
    <div className={cn(className, "space-y-1.5 py-0.5")}>
      <div className="flex justify-between items-center text-secondary text-xs font-medium gap-2">
        {isLoading ? (
          <Skeleton width={100} height={12} />
        ) : (
          <>
            <span className="truncate">{title}</span>
            {/* Right-aligned counts with optional vs */}
            <div className="ml-auto flex items-center gap-1.5">
              {/* Show counts if provided */}
              {typeof value !== "undefined" && (
                <span className="font-semibold text-[#111827] min-w-[64px] text-right tabular-nums text-[11px]">
                  {Number(value).toLocaleString()}
                </span>
              )}
              {typeof comparisonValue !== "undefined" && isComparisonEnabled && (
                <>
                  <span className="text-[10px] text-gray-500">vs</span>
                  <span className="font-semibold text-[#6B7280] min-w-[64px] text-right tabular-nums text-[11px]">
                    {Number(comparisonValue).toLocaleString()}
                  </span>
                </>
              )}
            </div>
          </>
        )}
        {isLoading ? (
          <Skeleton width={30} height={12} />
        ) : (
          <div className="flex items-center gap-2">
            {/* <span className="font-semibold text-primary">{currentValue}%</span> */}
            {isComparisonEnabled &&
              comparisonPercentage !== undefined &&
              difference !== 0 && (
                <span
                  className={`font-semibold text-xs ${
                    isPositive ? "text-primary-green" : "text-primary-red"
                  }`}
                >
                  {isPositive ? "+" : ""}
                  {difference.toFixed(2)}%
                </span>
              )}
            {isComparisonEnabled &&
              comparisonPercentage !== undefined &&
              difference === 0 && (
                <span className="text-secondary/80 text-xs">No Change</span>
              )}
          </div>
        )}
      </div>
      <div className="bg-gray-100 h-3 w-full overflow-hidden rounded-full">
        {isLoading ? (
          <Skeleton
            width={`${percentage}%`}
            height={12}
            className="rounded-full"
            style={{ borderRadius: "200px" }}
          />
        ) : isComparisonEnabled && comparisonPercentage !== undefined ? (
          // Comparison mode: show current value in its color, then comparison difference
          <div className="h-full w-full relative">
            {pctCurrent === pctComparison ? (
              // Equal values: show only purple bar
              <div
                className="h-full absolute left-0 bg-primary rounded-full"
                style={{
                  width: `${pctCurrent}%`,
                }}
              />
            ) : pctComparison > pctCurrent ? (
              // Comparison is higher: show current first, then difference
              <>
                {/* Current value segment - only rounded on left, flat on right where it connects */}
                <div
                  className="h-full absolute left-0 bg-primary"
                  style={{
                    width: `${pctCurrent}%`,
                    borderTopLeftRadius: "9999px",
                    borderBottomLeftRadius: "9999px",
                  }}
                />
                {/* Comparison difference segment - only rounded on right, flat on left where it connects */}
                <div
                  className="h-full absolute bg-primary-yellow"
                  style={{
                    left: `${pctCurrent}%`,
                    width: `${pctComparison - pctCurrent}%`,
                    borderTopRightRadius: "9999px",
                    borderBottomRightRadius: "9999px",
                  }}
                />
              </>
            ) : (
              // Current is higher: show comparison first, then current difference
              <>
                {/* Comparison value segment - only rounded on left, flat on right where it connects */}
                <div
                  className="h-full absolute left-0 bg-primary-yellow"
                  style={{
                    width: `${pctComparison}%`,
                    borderTopLeftRadius: "9999px",
                    borderBottomLeftRadius: "9999px",
                  }}
                />
                {/* Current difference segment - only rounded on right, flat on left where it connects */}
                <div
                  className="h-full absolute bg-primary"
                  style={{
                    left: `${pctComparison}%`,
                    width: `${pctCurrent - pctComparison}%`,
                    borderTopRightRadius: "9999px",
                    borderBottomRightRadius: "9999px",
                  }}
                />
              </>
            )}
          </div>
        ) : (
          // Single value mode
          <div
            className={`h-full rounded-full`}
            style={{
              width: `${percentage}%`,
              backgroundColor: color || "#914AC4",
            }}
          />
        )}
      </div>
    </div>
  );
};

export default ProgressBar;
