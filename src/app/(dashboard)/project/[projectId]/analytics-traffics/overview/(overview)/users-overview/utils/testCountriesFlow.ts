// Test utility to verify Countries tab data flow
import type { CountriesAPIResponse } from "../UsersOverview.type";
import { transformToProgressBarData } from "./dataTransform";

// Mock API response for testing
const mockCountriesAPIResponse: CountriesAPIResponse = {
  status: "success",
  project_id: "test-project-id",
  metric: "countries",
  data: {
    items: [
      {
        name: "Iran",
        users: 7451,
        percentage: 56.03519590885162,
      },
      {
        name: "Germany", 
        users: 2596,
        percentage: 19.52320072196736,
      },
      {
        name: "Finland",
        users: 806,
        percentage: 6.061517635556893,
      },
      {
        name: "United States",
        users: 652,
        percentage: 4.90336166052493,
      },
      {
        name: "Canada",
        users: 470,
        percentage: 3.5346318718507934,
      },
    ],
    period: {
      end_date: "2025-07-30",
      start_date: "2025-06-30",
    },
    totals: {
      total_users: 13297,
    },
  },
  last_sync: "2025-07-30T14:43:58.283346Z",
};

/**
 * Test function to verify Countries tab progress bar data transformation
 */
export function testCountriesProgressBarData() {
  console.log("🧪 Testing Countries tab progress bar data transformation...");
  
  try {
    const progressBarData = transformToProgressBarData(mockCountriesAPIResponse);
    
    console.log("✅ Progress bar data generated:", progressBarData);
    
    // Verify the data structure
    const expectedFirstItem = {
      title: "Iran",
      percentage: 56, // rounded from 56.03519590885162
    };
    
    if (progressBarData.length > 0 && 
        progressBarData[0].title === expectedFirstItem.title &&
        progressBarData[0].percentage === expectedFirstItem.percentage) {
      console.log("✅ Countries tab progress bar data is correctly formatted");
      return true;
    } else {
      console.error("❌ Countries tab progress bar data format is incorrect");
      console.error("Expected:", expectedFirstItem);
      console.error("Actual:", progressBarData[0]);
      return false;
    }
  } catch (error) {
    console.error("❌ Error testing Countries tab progress bar data:", error);
    return false;
  }
}

// Run the test if this file is executed directly
if (typeof window !== 'undefined') {
  // Only run in browser environment
  testCountriesProgressBarData();
}
