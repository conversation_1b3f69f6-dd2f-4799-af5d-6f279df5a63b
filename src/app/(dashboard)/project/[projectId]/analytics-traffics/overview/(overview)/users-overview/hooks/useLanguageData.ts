import httpService from "@/services/httpService";
import { useQuery } from "@tanstack/react-query";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { ProgressbarData } from "../../../../types/AnalyticsTraffics.types";

// Types for the detailed language API response
interface LanguageData {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

interface LanguageAPIResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: LanguageData[];
    daily_metrics: any[];
  };
}

// Extended type to include comparison data
export interface LanguageDataResult {
  progressbarData: ProgressbarData[];
  comparisonProgressbarData?: ProgressbarData[];
}

/**
 * Check if Language API response is valid
 * @param response - The Language API response to validate
 * @returns Boolean indicating if response is valid
 */
function isValidLanguageResponse(response: any): response is LanguageAPIResponse {
  return (
    response &&
    typeof response === "object" &&
    response.status === "success" &&
    response.data &&
    Array.isArray(response.data.dimension_breakdown)
  );
}

/**
 * Transform Language API response data to progress bar format
 * @param apiResponse - The Language API response containing language data
 * @returns Array of progress bar data
 */
function transformLanguageToProgressBarData(
  apiResponse: LanguageAPIResponse
): ProgressbarData[] {
  const { dimension_breakdown } = apiResponse.data;

  // Sort by percentage (descending) and take top 10 languages
  const sortedLanguages = [...dimension_breakdown]
    .sort((a, b) => b.percentage - a.percentage)
    .slice(0, 10);

  return sortedLanguages.map((item) => ({
    title: item.name,
    percentage: Math.round(item.percentage),
    value: item.total_users,
  }));
}

/**
 * Custom hook for fetching language data from GA4 API (for progress bars)
 * This hook is only called when the Language tab is selected
 * @param enabled - Whether to enable the query (should be true only for Language tab)
 * @returns Query result with language progress bar data
 */
const useLanguageData = (enabled: boolean = false) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "user-language-data",
      projectId,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<LanguageDataResult> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Use the detailed language API endpoint since there's no overview endpoint
      const apiUrl = `/api/project/GA4/user/demographic/detailed-language/${projectId}`;

      try {
        // Fetch current period data
        const currentParams: Record<string, string> = {};
        if (startDate && endDate) {
          currentParams.start_date = startDate;
          currentParams.end_date = endDate;
        }

        const response = await httpService.get(apiUrl, {
          params:
            Object.keys(currentParams).length > 0 ? currentParams : undefined,
          useAuth: true,
        });

        // Validate response
        if (!isValidLanguageResponse(response.data)) {
          throw new Error("Invalid language API response format");
        }

        // Transform to progress bar data
        const progressbarData = transformLanguageToProgressBarData(response.data);

        // Fetch comparison data if comparison is enabled
        let comparisonProgressbarData: ProgressbarData[] | undefined;
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          try {
            const comparisonParams: Record<string, string> = {
              start_date: comparisonStartDate,
              end_date: comparisonEndDate,
            };

            const comparisonResponse = await httpService.get(apiUrl, {
              params: comparisonParams,
              useAuth: true,
            });

            if (isValidLanguageResponse(comparisonResponse.data)) {
              comparisonProgressbarData = transformLanguageToProgressBarData(
                comparisonResponse.data
              );
            }
          } catch (error) {
            console.warn("Failed to fetch comparison language data:", error);
          }
        }

        return {
          progressbarData,
          comparisonProgressbarData,
        };
      } catch (error) {
        console.error("Language API call failed:", error);

        // Re-throw the error to let React Query handle it
        throw new Error(
          error instanceof Error
            ? `Failed to fetch user language data: ${error.message}`
            : "Failed to fetch user language data"
        );
      }
    },
    enabled: enabled && isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useLanguageData;