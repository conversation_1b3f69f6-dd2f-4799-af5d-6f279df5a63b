"use client";
import React, { memo } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import abbreviateNumber from "@/utils/abbreviateNumber";
import { formatTooltipDate } from "@/utils/formatTooltipDate";

type PayloadEntry = {
  dataKey: string;
  value: number | string;
  color: string;
};

type TooltipContentProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cardsData: any;
  payload?: PayloadEntry[];
  label?: string;
  tooltipItemsClassName?: string;
};

const GscTooltip = memo(
  ({ payload, label, tooltipItemsClassName: _tooltipItemsClassName }: TooltipContentProps) => {
    if (!payload || payload.length === 0) return null;

    // Separate current (solid) vs comparison (dotted_ or comparison_) entries
    const primaryData = payload.filter(
      (entry) =>
        !entry.dataKey.includes("dotted_") &&
        !entry.dataKey.startsWith("comparison_")
    );
    const dottedComparison = payload.filter((entry) =>
      entry.dataKey.includes("dotted_")
    );
    const solidComparison = payload.filter((entry) =>
      entry.dataKey.startsWith("comparison_")
    );

    // Dates: read raw dates from the data point to ensure correct ranges
    const dataPoint = (
      payload as unknown as Array<{
        payload?: { primaryDate?: string; comparisonDate?: string };
      }>
    )[0]?.payload as { primaryDate?: string; comparisonDate?: string } | undefined;
    const formattedCurrentDate = formatTooltipDate(
      (dataPoint?.primaryDate as string) || (label as string) || ""
    );
    const formattedComparisonDate = dataPoint?.comparisonDate
      ? formatTooltipDate(dataPoint.comparisonDate)
      : formatTooltipDate((label as string) || "");

    return (
      <Card
        className="rounded-lg p-4 pr-6 text-sm grid gap-3 border border-[#E0E0E0]"
        style={{ boxShadow: "0px 4px 8px 0px #3440541A" }}
      >
        <div className="space-y-1.5" style={{ fontVariantNumeric: "tabular-nums" }}>
          {primaryData.map((primaryEntry: PayloadEntry, index: number) => {
            const baseKey = primaryEntry.dataKey;
            const metricLabel = baseKey.replace(/_/g, " ");
            const comparisonEntry =
              dottedComparison.find(
                (comp) => comp.dataKey === `dotted_${baseKey}`
              ) ||
              solidComparison.find(
                (comp) => comp.dataKey === `comparison_${baseKey}`
              );

            const toNumber = (v: unknown): number | null => {
              if (v === null || v === undefined) return null;
              if (typeof v === "number") return v;
              const parsed = Number(v as string);
              return isNaN(parsed) ? null : parsed;
            };

            const primaryNumeric = toNumber(primaryEntry.value);
            const comparisonNumeric = toNumber(comparisonEntry?.value);
            const hasComparison = comparisonEntry && comparisonNumeric !== null;
            const valueDelta =
              hasComparison && primaryNumeric !== null
                ? primaryNumeric - (comparisonNumeric as number)
                : null;
            const isLowerBetter = baseKey === "Avg_Position";

            return (
              <div
                key={index}
                className="grid items-center gap-1"
                style={{
                  gridTemplateColumns: "110px 120px 120px 50px",
                }}
              >
                {/* Metric name */}
                <span className="text-xs text-gray-900 w-[110px] max-w-[110px] truncate">
                  {metricLabel}
                </span>

                {/* Current period */}
                <div
                  className="grid items-center gap-1"
                  style={{ gridTemplateColumns: "6px 56px min-content" }}
                >
                  <span
                    className="inline-block w-2 h-2 rounded-full shrink-0"
                    style={{ backgroundColor: primaryEntry.color }}
                  />
                  <span className="text-xs text-gray-700 truncate ml-1">
                    {formattedCurrentDate}
                  </span>
                  <span className="font-bold text-xs text-right">
                    {typeof primaryEntry.value === "string"
                      ? (primaryEntry.value as string)
                      : abbreviateNumber(primaryEntry.value as number)}
                  </span>
                </div>

                {/* Comparison period with empty circle */}
                <div
                  className="grid items-center gap-1"
                  style={{ gridTemplateColumns: "6px 56px min-content" }}
                >
                  {hasComparison ? (
                    <>
                      <span
                        className="inline-block w-2 h-2 rounded-full border-2 shrink-0"
                        style={{
                          borderColor: (comparisonEntry as PayloadEntry).color,
                          backgroundColor: "transparent",
                        }}
                      />
                      <span className="text-xs text-gray-700 truncate ml-1">
                        {formattedComparisonDate}
                      </span>
                      <span className="font-bold text-xs text-right">
                        {typeof (comparisonEntry as PayloadEntry).value ===
                        "string"
                          ? ((comparisonEntry as PayloadEntry).value as string)
                          : abbreviateNumber(
                              (comparisonEntry as PayloadEntry).value as number
                            )}
                      </span>
                    </>
                  ) : (
                    <span className="text-[10px] text-gray-400">—</span>
                  )}
                </div>

                {/* Delta value (current - comparison) */}
                <div className="flex items-center gap-1 w-[50px] max-w-[50px]">
                  {hasComparison && valueDelta !== null ? (
                    <>
                      <span className="text-[10px] text-gray-500">change</span>
                      <span
                        className={`text-[10px] font-semibold ml-auto text-right ${
                          valueDelta === 0
                            ? "text-secondary"
                            : valueDelta > 0
                            ? isLowerBetter
                              ? "text-red-600"
                              : "text-green-600"
                            : isLowerBetter
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {valueDelta > 0 ? "+" : valueDelta < 0 ? "-" : ""}
                        {abbreviateNumber(Math.abs(valueDelta))}
                      </span>
                    </>
                  ) : (
                    <span className="text-[10px] text-gray-400">—</span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </Card>
    );
  }
);

GscTooltip.displayName = "GscTooltip";

export default GscTooltip;
