import React, { useState, useRef } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import Skeleton from "react-loading-skeleton";
import Card from "@/components/ui/card";
import abbreviateNumber from "@/utils/abbreviateNumber";

interface TooltipData {
  title: string;
  percentage: number;
  value?: number;
  color?: string;
}

interface ProgressBarWithTooltipProps {
  percentage: number;
  className?: string;
  title: string;
  color?: string;
  isLoading: boolean | false;
  tooltipData?: TooltipData[];
  showTooltip?: boolean;
  // Comparison data props
  comparisonPercentage?: number;
  comparisonColor?: string;
  showComparison?: boolean;
}

const ProgressBarWithTooltip = ({
  percentage,
  className,
  title,
  color,
  isLoading,
  tooltipData,
  showTooltip = true,
  comparisonPercentage,
  comparisonColor = "#FFCD29",
  showComparison = false,
}: ProgressBarWithTooltipProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const offset = 12;
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [showTooltipState, setShowTooltipState] = useState(false);

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!showTooltip) return;

    const bounds = containerRef.current?.getBoundingClientRect();
    if (!bounds) return;

    setMousePos({
      x: e.clientX - bounds.left,
      y: e.clientY - bounds.top,
    });
  };

  const getTooltipPosition = () => {
    const containerBounds = containerRef.current?.getBoundingClientRect();
    const tooltipBounds = tooltipRef.current?.getBoundingClientRect();

    if (!containerBounds || !tooltipBounds) return { top: 0, left: 0 };

    const fitsBelow =
      mousePos.y + tooltipBounds.height + offset < containerBounds.height;

    const top = fitsBelow
      ? mousePos.y + offset
      : mousePos.y - tooltipBounds.height - offset;

    const left = Math.min(
      mousePos.x + offset,
      containerBounds.width - tooltipBounds.width - offset
    );

    return { top, left };
  };

  const tooltipPos = getTooltipPosition();

  // Calculate stacked bar logic for comparison
  const primaryColor = color || "#914AC4";
  const hasComparison = showComparison && comparisonPercentage !== undefined;

  // Determine which value is lower and should be shown first
  const primaryValue = percentage;
  const comparisonValue = comparisonPercentage || 0;
  const lowerValue = Math.min(primaryValue, comparisonValue);
  const higherValue = Math.max(primaryValue, comparisonValue);

  // Determine colors based on which is lower
  const lowerColor =
    primaryValue <= comparisonValue ? primaryColor : comparisonColor;
  const higherColor =
    primaryValue > comparisonValue ? primaryColor : comparisonColor;

  return (
    <div
      ref={containerRef}
      className={cn(className, "space-y-1.5 py-0.5 relative")}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => showTooltip && setShowTooltipState(true)}
      onMouseLeave={() => setShowTooltipState(false)}
    >
      <div className="flex justify-between text-secondary text-xs font-medium">
        {isLoading ? (
          <Skeleton width={100} height={12} />
        ) : (
          <>
            <span className="truncate">{title}</span>
          </>
        )}
        {isLoading ? (
          <Skeleton width={30} height={12} />
        ) : (
          <div className="flex items-center gap-2">
            <span className="font-semibold text-primary">{percentage}%</span>
            {hasComparison && (
              <span className="font-semibold text-amber-600 text-xs">
                vs {comparisonPercentage}%
              </span>
            )}
          </div>
        )}
      </div>
      <div className="bg-gray-100 h-3 w-full overflow-hidden rounded-full relative">
        {isLoading ? (
          <Skeleton
            width={`${percentage}%`}
            height={12}
            className="rounded-full"
            style={{ borderRadius: "200px" }}
          />
        ) : hasComparison ? (
          <>
            {/* Lower value bar (shown first) */}
            <div
              className="h-full rounded-full absolute left-0 top-0"
              style={{
                width: `${lowerValue}%`,
                backgroundColor: lowerColor,
                zIndex: 1,
              }}
            />
            {/* Higher value bar (shown on top, but only the difference) */}
            <div
              className="h-full rounded-full absolute left-0 top-0"
              style={{
                width: `${higherValue}%`,
                backgroundColor: higherColor,
                zIndex: 0,
              }}
            />
          </>
        ) : (
          <div
            className="h-full rounded-full"
            style={{
              width: `${percentage}%`,
              backgroundColor: primaryColor,
            }}
          />
        )}
      </div>

      {/* Tooltip */}
      {showTooltip &&
        showTooltipState &&
        tooltipData &&
        tooltipData.length > 0 && (
          <Card
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="space-y-2 border shadow-lg w-fit absolute pointer-events-none z-10"
            ref={tooltipRef}
            style={{
              top: tooltipPos.top,
              left: tooltipPos.left,
            }}
          >
            {/* Tooltip title */}
            <div className="text-xs font-bold mb-2">{title} Breakdown</div>

            {/* Tooltip content showing all referral data */}
            <div className="space-y-1.5">
              {tooltipData.map((item, index) => (
                <div key={index} className="flex items-center gap-2.5">
                  <div
                    className="rounded-full w-3 h-3"
                    style={{
                      backgroundColor:
                        item.color || (index === 0 ? "#914AC4" : "#FFCD29"),
                    }}
                  />
                  <div className="flex flex-col">
                    <span className="text-xs font-extrabold">
                      {item.value
                        ? abbreviateNumber(item.value)
                        : `${item.percentage}%`}
                    </span>
                    <span className="text-xs text-gray-500">{item.title}</span>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}
    </div>
  );
};

export default ProgressBarWithTooltip;
