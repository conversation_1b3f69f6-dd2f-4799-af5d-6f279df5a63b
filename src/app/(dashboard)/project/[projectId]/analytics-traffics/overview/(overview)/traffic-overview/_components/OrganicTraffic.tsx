import HorizontalBar from "@/components/ui/horizontal-bar/HorizontalBar";
import abbreviateNumber from "@/utils/abbreviateNumber";
import React from "react";

/* ================================== TYPES ================================= */
import type { BarsData } from "../../../../types/HorizontalBars.types";

/* ========================================================================== */
const OrganicTraffic = ({ barsData }: { barsData: BarsData }) => {
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  if (!barsData || !barsData.maxValue) return <div>No data</div>;

  return (
    <div>
      <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2">
        {barsData.bars.map(({ barData, label }, index) => (
          <HorizontalBar
            key={index}
            label={label}
            bars={barData}
            totalValue={barsData.maxValue}
          />
        ))}
      </div>
      <div className="flex justify-between mx-8 text-secondary/80">
        {Array.from({ length: 6 }).map((_, index) => {
          // Create dynamic scale based on maxValue with better distribution
          const value = Math.floor((barsData.maxValue / 5) * index);
          return <span key={index}>{abbreviateNumber(value)}</span>;
        })}
      </div>
    </div>
  );
};

export default OrganicTraffic;
