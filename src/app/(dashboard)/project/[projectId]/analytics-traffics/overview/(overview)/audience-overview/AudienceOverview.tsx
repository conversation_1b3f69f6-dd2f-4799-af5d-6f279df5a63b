import React from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import NoData from "../../../analytic-insight/_components/NoData";
import ErrorDisplay from "../../../analytic-insight/_components/ErrorDisplay";
import DateRange from "../../../_components/date-range/DateRange";
import SmallChartSkeleton from "../../../_components/small-chart-skeleton/SmallChartSkeleton";

/* ========================== INDIVIDUAL CHART COMPONENTS ========================== */
import TotalUsersChart from "./_components/TotalUsersChart";
import NewUsersChart from "./_components/NewUsersChart";
import ReturningUsersChart from "./_components/ReturningUsersChart";
import PageViewsChart from "./_components/PageViewsChart";
import SessionsChart from "./_components/SessionsChart";
import EngagedSessionsChart from "./_components/EngagedSessionsChart";
import AvgEngagementTimeChart from "./_components/AvgEngagementTimeChart";
import EngagementRateChart from "./_components/EngagementRateChart";
import EventCountChart from "./_components/EventCountChart";
import ConversionsChart from "./_components/ConversionsChart";

/* ================================ API CALLS =============================== */
import { useAudienceOverview } from "./AudienceOverview.hook";
import { Button } from "@/components/ui/button";
import { AnimatePresence } from "framer-motion";
import Link from "next/link";
import { useProjectId } from "@/hooks/useProjectId";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
const AudienceOverview = () => {
  const { projectId } = useProjectId();
  const { projectName } = useProjectContext();
  const {
    data: audienceData,
    isLoading: ChartsIsLoading,
    isPending: ChartsIsPending,
    isError: ChartsIsError,
    error: ChartsError,
    refetch: ChartsRefetch,
  } = useAudienceOverview();

  const renderSkeletonCards = () => {
    return Array.from({ length: 10 }).map((_, index) => (
      <SmallChartSkeleton
        key={index}
        className={index > 7 ? "lg:col-span-2" : undefined}
      />
    ));
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  if (ChartsIsError) {
    return (
      <ErrorDisplay
        error={ChartsError}
        onRetry={() => ChartsRefetch()}
        title="Audience Overview"
        className="min-h-[400px]"
      />
    );
  }

  if (!audienceData && !ChartsIsLoading && !ChartsIsPending) {
    return <NoData title={`${projectName} Audience Overview`} />;
  }

  return (
    <Card className="space-y-6">
      <div className="space-y-2">
        <Title>{projectName} Audience Overview</Title>
        <DateRange />
      </div>
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-4">
        {ChartsIsLoading || ChartsIsPending ? (
          renderSkeletonCards()
        ) : (
          <AnimatePresence>
            {audienceData && (
              <>
                <TotalUsersChart {...audienceData.totalUsers} />
                <NewUsersChart {...audienceData.newUsers} />
                <ReturningUsersChart {...audienceData.returningUsers} />
                <PageViewsChart {...audienceData.pageViews} />
                <SessionsChart {...audienceData.sessions} />
                <EngagedSessionsChart {...audienceData.engagedSessions} />
                <AvgEngagementTimeChart {...audienceData.avgEngagementTime} />
                <EngagementRateChart {...audienceData.engagementRate} />
                <EventCountChart {...audienceData.eventCount} />
                <ConversionsChart {...audienceData.conversions} />
              </>
            )}
          </AnimatePresence>
        )}
      </div>
      <div className="w-full flex justify-end pt-9 pb-5">
        <Link
          href={`/project/${projectId}/analytics-traffics/analytic-insight?tab=audience`}
        >
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default AudienceOverview;
