import {
  GSCAPIResponse,
  GSCOverviewType,
  CardsData,
} from "./GSCOverview.types";

/**
 * Formats date from API format (YYYYMMDD) to display format (MMM DD)
 */
const formatDateForChart = (dateString: string): string => {
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);

  const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Calculates growth percentage between two values
 */
const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) return current > 0 ? "+100%" : "0%";
  const growth = ((current - previous) / previous) * 100;
  return growth >= 0 ? `+${growth.toFixed(1)}%` : `${growth.toFixed(1)}%`;
};

/**
 * Converts string values to numbers for calculations
 */
const parseMetricValue = (value: string): number => {
  // Handle values like "1498k" or plain numbers
  if (value.includes("k")) {
    return parseFloat(value.replace("k", "")) * 1000;
  }
  return parseFloat(value) || 0;
};

/**
 * Processes GSC API responses into chart-compatible format
 */
export const processGSCData = (
  primaryData: GSCAPIResponse,
  comparisonData?: GSCAPIResponse
): GSCOverviewType => {
  const metrics = [
    "Clicks",
    "CTR",
    "Search_Visibility",
    "Impression",
    "Avg_Position",
  ];

  // Process primary data
  const primaryMetrics = primaryData.data["web-pages"].daily_metrics;
  const primaryTotals = primaryData.data["web-pages"].totals;

  // Process comparison data if available
  const comparisonMetrics =
    comparisonData?.data["web-pages"].daily_metrics || [];
  const comparisonTotals = comparisonData?.data["web-pages"].totals;

  // Build a lookup map for comparison by exact date (YYYYMMDD)
  const comparisonByDate: Record<string, (typeof comparisonMetrics)[number]> =
    {};
  for (const m of comparisonMetrics) {
    comparisonByDate[m.date] = m;
  }

  // Helper to format Date to YYYYMMDD
  const toYYYYMMDD = (d: Date): string => {
    const y = d.getFullYear();
    const m = String(d.getMonth() + 1).padStart(2, "0");
    const day = String(d.getDate()).padStart(2, "0");
    return `${y}${m}${day}`;
  };

  // Parse comparison start date from period (YYYY-MM-DD)
  const comparisonStartDateObj = comparisonData?.period?.start_date
    ? new Date(comparisonData.period.start_date)
    : undefined;

  // Create line chart data
  const lineChartData: Record<string, string | number>[] = [];

  // Process primary data points
  primaryMetrics.forEach((metric, index) => {
    const chartPoint: Record<string, string | number> = {
      name: formatDateForChart(metric.date),
      // Keep raw dates for accurate tooltip display
      primaryDate: metric.date,
    };

    // Add primary metrics (normal lines)
    metrics.forEach((metricName) => {
      const rawValue = metric[metricName as keyof typeof metric];
      const value = parseMetricValue(rawValue);
      chartPoint[metricName] = value;

      if (metricName === "Search_Visibility") {
        console.log(`Search_Visibility processing:`, {
          date: metric.date,
          rawValue,
          parsedValue: value,
          isValid: !isNaN(value) && isFinite(value),
        });
      }
    });

    // Add comparison data if available - align by date offset from comparison period start
    if (comparisonMetrics.length > 0 && comparisonStartDateObj) {
      const compDateObj = new Date(comparisonStartDateObj);
      compDateObj.setDate(compDateObj.getDate() + index);
      const compDateKey = toYYYYMMDD(compDateObj);

      // Attach comparison raw date for tooltip even if value is missing (ensures correct date shown)
      (chartPoint as any).comparisonDate = compDateKey;

      const comparisonMetricByDate = comparisonByDate[compDateKey];
      metrics.forEach((metricName) => {
        const val = comparisonMetricByDate
          ? parseMetricValue(
              comparisonMetricByDate[
                metricName as keyof typeof comparisonMetricByDate
              ]
            )
          : null;
        // Use null to avoid drawing points for missing dates
        (chartPoint as any)[`dotted_${metricName}`] = val as any;
      });
    }

    lineChartData.push(chartPoint);
  });

  // Create cards data with growth calculations
  const cardsData: Record<string, CardsData> = {};

  metrics.forEach((metricName) => {
    const primaryValue = parseMetricValue(
      primaryTotals[metricName as keyof typeof primaryTotals]
    );
    const comparisonValue = comparisonTotals
      ? parseMetricValue(
          comparisonTotals[metricName as keyof typeof comparisonTotals]
        )
      : 0;

    cardsData[metricName] = {
      amount: primaryValue,
      growth: comparisonTotals
        ? calculateGrowth(primaryValue, comparisonValue)
        : "0%",
    };

    // Add dotted line cards data if comparison exists
    if (comparisonTotals) {
      cardsData[`dotted_${metricName}`] = {
        amount: comparisonValue,
        growth: "0%", // Comparison data doesn't show growth
      };
    }
  });

  // Debug: Check Search_Visibility data integrity
  const searchVisibilityValues = lineChartData.map(
    (point) => point.Search_Visibility
  );
  console.log("Search_Visibility values in final data:", {
    values: searchVisibilityValues,
    validValues: searchVisibilityValues.filter(
      (v) => typeof v === "number" && !isNaN(v) && isFinite(v)
    ),
    invalidValues: searchVisibilityValues.filter(
      (v) => typeof v !== "number" || isNaN(v) || !isFinite(v)
    ),
  });

  return {
    lineChartData,
    cardsData,
  };
};
