import httpService from "@/services/httpService";
import { useQuery } from "@tanstack/react-query";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { ProgressbarData } from "../../../../types/AnalyticsTraffics.types";
import type { CitiesAPIResponse } from "../UsersOverview.type";

// Extended type to include comparison data
export interface CitiesDataResult {
  progressbarData: ProgressbarData[];
  comparisonProgressbarData?: ProgressbarData[];
}

/**
 * Check if Cities API response is valid
 * @param response - The Cities API response to validate
 * @returns Boolean indicating if response is valid
 */
function isValidCitiesResponse(response: any): response is CitiesAPIResponse {
  return (
    response &&
    typeof response === "object" &&
    response.status === "success" &&
    response.data &&
    Array.isArray(response.data.items) &&
    response.data.totals &&
    typeof response.data.totals.total_users === "number"
  );
}

/**
 * Transform Cities API response data to progress bar format
 * @param apiResponse - The Cities API response containing cities data
 * @returns Array of progress bar data
 */
function transformCitiesToProgressBarData(
  apiResponse: CitiesAPIResponse
): ProgressbarData[] {
  const { items } = apiResponse.data;

  // Sort by users count (descending) and take top 10
  const topCities = [...items].sort((a, b) => b.users - a.users).slice(0, 10);

  return topCities.map((item) => ({
    title: item.name,
    percentage: Math.round(item.percentage),
    value: item.users,
  }));
}

/**
 * Custom hook for fetching cities data from GA4 API (for progress bars)
 * This hook is only called when the Cities tab is selected
 * @param enabled - Whether to enable the query (should be true only for Cities tab)
 * @returns Query result with cities progress bar data and comparison data
 */
const useCitiesData = (enabled: boolean = false) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "user-cities-data",
      projectId,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<CitiesDataResult> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Build API URL with GA4 endpoint format for user cities
      const apiUrl = `/api/project/GA4/user/overview/cities/${projectId}/`;

      try {
        // Fetch current period data
        const currentParams: Record<string, string> = {};
        if (startDate && endDate) {
          currentParams.start_date = startDate;
          currentParams.end_date = endDate;
        }

        const response = await httpService.get(apiUrl, {
          params:
            Object.keys(currentParams).length > 0 ? currentParams : undefined,
          useAuth: true,
        });

        // Validate response
        if (!isValidCitiesResponse(response.data)) {
          throw new Error("Invalid cities API response format");
        }

        // Transform to progress bar data
        const progressbarData = transformCitiesToProgressBarData(response.data);

        // Fetch comparison data if comparison is enabled
        let comparisonProgressbarData: ProgressbarData[] | undefined;
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          try {
            const comparisonParams: Record<string, string> = {
              start_date: comparisonStartDate,
              end_date: comparisonEndDate,
            };

            const comparisonResponse = await httpService.get(apiUrl, {
              params: comparisonParams,
              useAuth: true,
            });

            if (isValidCitiesResponse(comparisonResponse.data)) {
              comparisonProgressbarData = transformCitiesToProgressBarData(
                comparisonResponse.data
              );
            }
          } catch (error) {
            console.warn("Failed to fetch comparison cities data:", error);
          }
        }

        return {
          progressbarData,
          comparisonProgressbarData,
        };
      } catch (error) {
        console.error("Cities API call failed:", error);

        // Re-throw the error to let React Query handle it
        throw new Error(
          error instanceof Error
            ? `Failed to fetch user cities data: ${error.message}`
            : "Failed to fetch user cities data"
        );
      }
    },
    enabled: enabled && isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useCitiesData;
