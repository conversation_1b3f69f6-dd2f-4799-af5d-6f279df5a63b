import { useQuery } from "@tanstack/react-query";
import type {
  TrafficOverviewResponse,
  ComparisonTrafficOverviewResponse,
  ComparisonTrafficOverviewData,
} from "../../../types/HorizontalBars.types";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import http from "@/services/httpService";
import { getEmptyTrafficSources } from "./utils/transformTrafficData";

const useTrafficOverview = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: [
      "traffic-overview-data",
      projectId,
      getFormattedDates().startDate,
      getFormattedDates().endDate,
      getFormattedDates().comparisonStartDate,
      getFormattedDates().comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<ComparisonTrafficOverviewResponse> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const currentDataPromise = http.get(
        `/api/project/GA4/traffic/overview/${projectId}/`,
        {
          params: {
            start_date: startDate,
            end_date: endDate,
          },
          useAuth: true,
        }
      );

      // Fetch comparison period data if comparison is enabled and dates are available
      let previousDataPromise: Promise<{
        data: TrafficOverviewResponse;
      }> | null = null;

      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        previousDataPromise = http.get(
          `/api/project/GA4/traffic/overview/${projectId}/`,
          {
            params: {
              start_date: comparisonStartDate,
              end_date: comparisonEndDate,
            },
            useAuth: true,
          }
        );
      }

      try {
        // Execute API calls
        const results = await Promise.allSettled([
          currentDataPromise,
          previousDataPromise,
        ]);

        // Handle current period result
        const currentResult = results[0];
        if (currentResult.status === "rejected") {
          throw new Error(
            `Failed to fetch current period data: ${currentResult.reason}`
          );
        }
        const currentData: TrafficOverviewResponse = currentResult.value.data;

        // Handle comparison period result
        let previousData: TrafficOverviewResponse | null = null;
        if (previousDataPromise) {
          const previousResult = results[1];
          if (previousResult.status === "fulfilled") {
            previousData = previousResult.value.data;
          } else {
            console.warn(
              "Failed to fetch comparison period data:",
              previousResult.reason
            );
          }
        }

        // Create comparison response structure
        const comparisonData: ComparisonTrafficOverviewData = {
          current: currentData.data,
          previous: previousData?.data || {
            period: {
              start_date: comparisonStartDate || "",
              end_date: comparisonEndDate || "",
              days_count: 0,
            },
            metrics: {
              total_users: {
                total_value: 0,
                traffic_sources: getEmptyTrafficSources(),
              },
              new_users: {
                total_value: 0,
                traffic_sources: getEmptyTrafficSources(),
              },
              sessions: {
                total_value: 0,
                traffic_sources: getEmptyTrafficSources(),
              },
              active_users: {
                total_value: 0,
                traffic_sources: getEmptyTrafficSources(),
              },
              page_views: {
                total_value: 0,
                traffic_sources: getEmptyTrafficSources(),
              },
              event_count: {
                total_value: 0,
                traffic_sources: getEmptyTrafficSources(),
              },
              conversions: {
                total_value: 0,
                traffic_sources: getEmptyTrafficSources(),
              },
            },
          },
        };

        const response: ComparisonTrafficOverviewResponse = {
          status: currentData.status,
          project_id: currentData.project_id,
          data: comparisonData,
          last_sync: currentData.last_sync,
        };

        return response;
      } catch (error) {
        console.error("Error fetching traffic overview data:", error);
        throw error;
      }
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export default useTrafficOverview;
