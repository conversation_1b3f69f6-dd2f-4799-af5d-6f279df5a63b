import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { GSCOverviewType, GSCAPIResponse } from "./GSCOverview.types";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { processGSCData } from "./GSCOverview.utils";

/**
 * Validates if the API response has the expected GSC structure
 */
const isValidGSCResponse = (data: any): data is GSCAPIResponse => {
  return (
    data &&
    typeof data === "object" &&
    data.status &&
    data.project_id &&
    data.period &&
    data.data &&
    data.data["web-pages"] &&
    data.data["web-pages"].totals &&
    Array.isArray(data.data["web-pages"].daily_metrics)
  );
};

export const useGSCOverview = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, shouldShowComparison } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "gsc-overview-data",
      projectId,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      shouldShowComparison,
    ],
    queryFn: async (): Promise<GSCOverviewType> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Build API URL with GSC endpoint format
      const apiUrl = `/api/project/GSC/overview/web-pages/${projectId}`;

      try {
        // Fetch current period data (primary data)
        const currentParams: Record<string, string> = {};
        if (startDate && endDate) {
          currentParams.start_date = startDate;
          currentParams.end_date = endDate;
        }

        console.log("🔄 Fetching GSC primary data:", {
          url: apiUrl,
          params: currentParams,
        });

        const primaryResponse = await httpService.get(apiUrl, {
          params:
            Object.keys(currentParams).length > 0 ? currentParams : undefined,
          useAuth: true,
        });

        // Validate primary response
        if (!isValidGSCResponse(primaryResponse.data)) {
          throw new Error("Invalid primary period API response format");
        }

        console.log("✅ Primary GSC data fetched successfully");

        let comparisonResponse: { data: GSCAPIResponse } | null = null;

        // If comparison should be shown and we have comparison dates, fetch comparison data
        if (shouldShowComparison && comparisonStartDate && comparisonEndDate) {
          const comparisonParams: Record<string, string> = {
            start_date: comparisonStartDate,
            end_date: comparisonEndDate,
          };

          console.log("🔄 Fetching GSC comparison data:", {
            url: apiUrl,
            params: comparisonParams,
          });

          try {
            const comparisonResponseData = await httpService.get(apiUrl, {
              params: comparisonParams,
              useAuth: true,
            });

            // Validate comparison response
            if (isValidGSCResponse(comparisonResponseData.data)) {
              comparisonResponse = comparisonResponseData;
              console.log("✅ Comparison GSC data fetched successfully");
            } else {
              console.warn(
                "Invalid comparison period API response, using primary data only"
              );
            }
          } catch (comparisonError) {
            console.warn(
              "Failed to fetch comparison data, using primary data only:",
              comparisonError
            );
          }
        }

        // Process and merge the data
        const result = processGSCData(
          primaryResponse.data,
          comparisonResponse?.data
        );

        console.log("✅ Processed GSC data:", {
          lineChartDataLength: result.lineChartData.length,
          cardsDataKeys: Object.keys(result.cardsData),
          hasComparisonData: !!comparisonResponse,
          firstDataPoint: result.lineChartData[0],
          sampleDottedKeys: Object.keys(result.lineChartData[0] || {}).filter(
            (k) => k.includes("dotted_")
          ),
          searchVisibilityValues: result.lineChartData
            .map((point) => ({
              name: point.name,
              Search_Visibility: point.Search_Visibility,
            }))
            .slice(0, 5), // First 5 points
        });

        return result;
      } catch (error) {
        console.error("GSC API call failed:", error);

        // Re-throw the error to let React Query handle it
        throw new Error(
          error instanceof Error
            ? `Failed to fetch GSC overview data: ${error.message}`
            : "Failed to fetch GSC overview data"
        );
      }
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};
