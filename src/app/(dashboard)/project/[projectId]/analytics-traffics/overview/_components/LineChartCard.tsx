import React from "react";

/* ================================ RECHARTS ================================ */
import { LineChart, Line, ResponsiveContainer, Tooltip, YAxis } from "recharts";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import abbreviateNumber from "@/utils/abbreviateNumber";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";
import { formatTooltipDate } from "@/utils/formatTooltipDate";

/* ================================== TYPES ================================= */
import type { LineChartData } from "@/types/LineChartCard.type";
import Skeleton from "react-loading-skeleton";
import { CurveType } from "recharts/types/shape/Curve";

/* ========================================================================== */
/**
 * Custom tooltip for mini charts - matches ChartPopup styling
 */
const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload || !payload.length) return null;

  // Get the data point from payload to access original dates
  const dataPoint = payload[0]?.payload;

  // Format dates from the actual API response
  const formattedCurrentDate = formatTooltipDate(
    dataPoint?.primaryDate || label
  );
  const formattedComparisonDate = dataPoint?.comparisonDate
    ? formatTooltipDate(dataPoint.comparisonDate)
    : null;

  // Separate current period and comparison period data
  const currentPeriodData = payload.find(
    (entry: any) => entry.dataKey === "value"
  );
  const comparisonPeriodData = payload.find(
    (entry: any) => entry.dataKey === "comparisonValue"
  );

  return (
    <Card
      className="rounded-lg p-3 text-sm border border-[#E0E0E0]"
      style={{ boxShadow: "0px 4px 8px 0px #3440541A" }}
    >
      <div className="space-y-2">
        {/* Current Period */}
        {currentPeriodData && (
          <div className="flex items-center gap-2">
            <span
              className="inline-block w-2 h-2 rounded-full"
              style={{ backgroundColor: currentPeriodData.color }}
            />
            <span className="text-xs text-gray-700">
              {formattedCurrentDate}
            </span>
            <span className="font-bold text-xs ml-auto">
              {typeof currentPeriodData.value === "number"
                ? abbreviateNumber(currentPeriodData.value)
                : currentPeriodData.value}
            </span>
          </div>
        )}

        {/* Comparison Period */}
        {comparisonPeriodData && formattedComparisonDate && (
          <div className="flex items-center gap-2">
            <span
              className="inline-block w-2 h-2 rounded-full"
              style={{
                backgroundColor: "transparent",
                borderColor: comparisonPeriodData.color,
                borderWidth: 2,
              }}
            />
            <span className="text-xs text-gray-700">
              {formattedComparisonDate}
            </span>
            <span className="font-bold text-xs ml-auto">
              {typeof comparisonPeriodData.value === "number"
                ? abbreviateNumber(comparisonPeriodData.value)
                : comparisonPeriodData.value}
            </span>
          </div>
        )}
      </div>
    </Card>
  );
};

/* ========================================================================== */
/**

* A sleek little card component that displays a line chart with a big number and a growth indicator.
* 
* Perfect for dashboard flexing. 💪
* 
* @component
* @param props - Props for LineChartCard
* @param props.title - Title text shown at the top of the card
* @param [props.className] - Optional Tailwind class overrides for the card
* @param [props.bigNumber] - The big juicy number displayed prominently
* @param [props.smallNumber] - The smaller number showing growth like "+15%" or "-3%"
* @param props.data - Array of data points for the line chart
* @param [props.onClick] - Optional click handler for the whole card
* @param [props.chartType] - Optional chart type (line, bar, etc.)
* @returns A stylized line chart card component
*/
const LineChartCard = ({
  title,
  className,
  bigNumber,
  smallNumber,
  data,
  onClick,
  isLoading,
  chartType = "monotone",
}: LineChartData) => {
  // Do not show zero-change percentages like "+0%" or "0%"
  const shouldShowSmallNumber =
    typeof smallNumber === "string" && !/^\+?0(?:\.0+)?%$/.test(smallNumber.trim());
  return (
    <Card className={cn("border", className)} onClick={onClick}>
      {isLoading ? (
        <Skeleton height={120} />
      ) : (
        <>
          <p className="text-xs text-gray-700">{title}</p>
          <div
            style={{ width: "100%", height: 120 }}
            className={"flex flex-col items-start gap-1"}
          >
            <div className="space-x-1">
              {bigNumber && (
                <span className=" text-xl font-bold text-secondary">
                  {bigNumber}
                </span>
              )}
              {shouldShowSmallNumber && (
                <span
                  className={` text-sm ${
                    smallNumber.includes("+")
                      ? "text-green-600"
                      : smallNumber.includes("-")
                      ? "text-red-600"
                      : "text-secondary"
                  }`}
                >
                  {smallNumber}
                </span>
              )}
            </div>
            <ResponsiveContainer width="100%">
              <LineChart data={data}>
                <YAxis type="number" domain={["dataMin", "dataMax"]} hide />
                {/* Primary period line (purple) */}
                <Line
                  type={chartType as CurveType}
                  dataKey="value"
                  stroke="#914AC4"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4, fill: "#914AC4" }}
                  name="Current Period"
                />

                {/* Comparison period line (yellow) - only if comparison data exists */}
                {data.some((item) => item.comparisonValue !== undefined) && (
                  <Line
                    type={chartType as CurveType}
                    dataKey="comparisonValue"
                    stroke="#FFCD29"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#FFCD29" }}
                    name="Previous Period"
                  />
                )}

                {/* Custom tooltip matching ChartPopup style */}
                {/* <Tooltip content={<CustomTooltip />} /> */}
              </LineChart>
            </ResponsiveContainer>
          </div>
        </>
      )}
    </Card>
  );
};

export default LineChartCard;
