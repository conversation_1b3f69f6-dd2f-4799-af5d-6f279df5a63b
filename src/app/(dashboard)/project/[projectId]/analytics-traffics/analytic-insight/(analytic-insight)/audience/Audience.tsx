"use client";
import React, { useState, useEffect } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../_components/date-range/DateRange";
import ChartPopup from "../../../overview/(overview)/audience-overview/_components/ChartPopup";
import useAudience from "./Audience.hook";
import NoData from "../../_components/NoData";
import ErrorDisplay from "../../_components/ErrorDisplay";
import AudienceTabNavigation from "./components/AudienceTabNavigation";
import AudienceMainChart from "./components/AudienceMainChart";
import AudienceSmallCharts from "./components/AudienceSmallCharts";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
const Audience = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState("");
  const { projectName } = useProjectContext();
  const { data, isLoading, isPending, isError, error, refetch } =
    useAudience(activeTab);

  // Debug log to verify component stays mounted and data is preserved
  useEffect(() => {
    return () => {};
  }, [data, isLoading, isPending, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Handle error state first - but only show error display for critical errors
  if (isError) {
    const errorCode = (error as any)?.code;
    const errorStatus =
      (error as any)?.response?.status || (error as any)?.status;

    // Don't show error display for 404 (not found) - just show no data instead
    if (errorCode === "not_found" || errorStatus === 404) {
      return <NoData title="Audience" />;
    }

    // Show error display for other errors (500, network issues, auth, etc.)
    return (
      <ErrorDisplay
        error={error}
        onRetry={() => refetch()}
        title="Audience Data"
        className="min-h-[400px]"
      />
    );
  }

  // Handle no data state (when not loading and no error)
  if (!data && !isLoading && !isPending) {
    return <NoData title="Audience" />;
  }

  // Main render - loading or data available
  return (
    <>
      <Card className="space-y-2">
        <div>
          <Title>{projectName} Audience</Title>
        </div>
        <DateRange />

        <AudienceTabNavigation
          data={data}
          isLoading={isLoading || isPending}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />

        <AudienceMainChart data={data} isLoading={isLoading || isPending} />

        <AudienceSmallCharts data={data} isLoading={isLoading || isPending} />
      </Card>
      <ChartPopup />
    </>
  );
};

export default Audience;
