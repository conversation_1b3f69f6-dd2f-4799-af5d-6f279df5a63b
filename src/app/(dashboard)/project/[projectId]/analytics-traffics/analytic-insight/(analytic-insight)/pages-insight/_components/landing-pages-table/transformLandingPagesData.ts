import { LandingPagesResponse, LandingPageMetrics } from "./landingPagesService";

const ITEMS_PER_PAGE = 10;

export interface TransformedLandingPagesData {
  tableData: {
    tableHeadings: string[];
    tableBody: Array<{
      id: string;
      data: Array<{
        value: string | number;
        comparison?: {
          value: string | number;
          change: number;
          changeType: "increase" | "decrease" | "neutral";
        };
      }>;
    }>;
  };
  pagination: {
    totalPages: number;
    currentPage: number;
    totalItems: number;
  };
}

const formatNumber = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + "M";
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + "K";
  }
  return value.toString();
};

const formatTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}s`;
  }
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

const calculateChange = (
  current: number,
  previous: number
): { change: number; changeType: "increase" | "decrease" | "neutral" } => {
  if (previous === 0) {
    return { change: current > 0 ? 100 : 0, changeType: current > 0 ? "increase" : "neutral" };
  }
  
  const change = ((current - previous) / previous) * 100;
  const changeType = change > 0 ? "increase" : change < 0 ? "decrease" : "neutral";
  
  return { change: Math.abs(change), changeType };
};

export const transformLandingPagesData = (
  primaryData: LandingPagesResponse["data"],
  page: number,
  comparisonData?: LandingPagesResponse["data"] | null
): TransformedLandingPagesData => {
  const landingPages = primaryData["landing-pages"] || {};
  const comparisonLandingPages = comparisonData?.["landing-pages"] || {};

  // Convert to array and sort by sessions (descending)
  const landingPagesArray = Object.entries(landingPages).map(([page, metrics]) => ({
    page,
    metrics,
  }));

  landingPagesArray.sort((a, b) => b.metrics.SESSIONS - a.metrics.SESSIONS);

  // Pagination
  const totalItems = landingPagesArray.length;
  const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
  const startIndex = (page - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedData = landingPagesArray.slice(startIndex, endIndex);

  // Table headings
  const tableHeadings = [
    "Landing Page",
    "Sessions",
    "Engaged Sessions",
    "New Users",
    "Total Users",
    "Views",
    "Engaged Time",
    "Event Count",
    "Conversions",
    "Engaged Rate",
  ];

  // Transform data for table
  const tableBody = paginatedData.map((item, index) => {
    const { page: pagePath, metrics } = item;
    const comparisonMetrics = comparisonLandingPages[pagePath];

    const createCellData = (
      value: number,
      formatter: (val: number) => string = (val) => val.toString(),
      comparisonValue?: number
    ) => {
      const cellData: any = {
        value: formatter(value),
      };

      if (comparisonValue !== undefined && comparisonData) {
        const { change, changeType } = calculateChange(value, comparisonValue);
        cellData.comparison = {
          value: formatter(comparisonValue),
          change,
          changeType,
        };
      }

      return cellData;
    };

    return {
      id: `landing-page-${startIndex + index}`,
      data: [
        { value: pagePath === "(not set)" ? "(not set)" : pagePath },
        createCellData(metrics.SESSIONS, formatNumber, comparisonMetrics?.SESSIONS),
        createCellData(metrics["ENGAGED SESSIONS"], formatNumber, comparisonMetrics?.["ENGAGED SESSIONS"]),
        createCellData(metrics["NEW USERS"], formatNumber, comparisonMetrics?.["NEW USERS"]),
        createCellData(metrics["TOTAL USERS"], formatNumber, comparisonMetrics?.["TOTAL USERS"]),
        createCellData(metrics.VIEWS, formatNumber, comparisonMetrics?.VIEWS),
        createCellData(metrics["ENGAGED TIME"], formatTime, comparisonMetrics?.["ENGAGED TIME"]),
        createCellData(metrics["EVENT COUNT"], formatNumber, comparisonMetrics?.["EVENT COUNT"]),
        createCellData(metrics.CONVERSIONS, formatNumber, comparisonMetrics?.CONVERSIONS),
        createCellData(metrics["ENGAGED RATE"], formatPercentage, comparisonMetrics?.["ENGAGED RATE"]),
      ],
    };
  });

  return {
    tableData: {
      tableHeadings,
      tableBody,
    },
    pagination: {
      totalPages,
      currentPage: page,
      totalItems,
    },
  };
};