"use client";
import React from "react";

/* =============================== COMPONENTS =============================== */
import CardTab from "@/components/ui/card-tab/CardTab";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================== TYPES ================================= */
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";

/* ================================== HOOKS ================================= */
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";

/* ========================================================================== */
interface EventsCardTabsProps {
  cardTabs: CardTabType[];
  activeTab: string;
  onTabSelect: (tab: string) => void;
  isLoading: boolean;
}

/* ========================================================================== */
const EventsCardTabs: React.FC<EventsCardTabsProps> = ({
  cardTabs,
  activeTab,
  onTabSelect,
  isLoading,
}) => {
  const { themeColor } = useAppThemeColor();

  if (isLoading) {
    return (
      <motion.div
        className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
        key="loading"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        {Array.from({ length: 7 }).map((_, i) => (
          <CardTab key={i} isLoading />
        ))}
      </motion.div>
    );
  }

  if (cardTabs.length === 0) {
    return null;
  }

  return (
    <motion.div
      className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
      key="data"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {cardTabs.map(
        (
          {
            title,
            changeValue,
            value,
          }: { title: string; changeValue: string; value: string },
          index: number
        ) => (
          <CardTab
            key={index}
            title={title}
            value={value}
            changeValue={changeValue}
            className="border-2"
            style={
              activeTab === title
                ? { borderColor: themeColor }
                : { borderColor: "transparent" }
            }
            onSelect={() => onTabSelect(title)}
          />
        )
      )}
    </motion.div>
  );
};

export default EventsCardTabs;
