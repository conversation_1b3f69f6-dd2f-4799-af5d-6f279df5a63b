import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import { TableDataRequest } from "../../../../../_components/data-table/DataTable.types";

const useUserDemography = ({
  page,
  filterBy,
}: {
  page: number;
  filterBy: string;
}) =>
  useQuery({
    queryKey: ["user-demography-data", page, filterBy],
    queryFn: async (): Promise<TableDataRequest> => {
      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/analytic-insight/about-users/user-demography",
        {
          params: {
            page,
            filterBy,
          },
        },
      );
      return data;
    },
  });

export default useUserDemography;
