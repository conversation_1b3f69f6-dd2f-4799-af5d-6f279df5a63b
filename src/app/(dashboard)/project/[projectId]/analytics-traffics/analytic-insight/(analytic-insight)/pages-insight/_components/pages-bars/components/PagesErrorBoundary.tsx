import React from "react";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";

interface PagesErrorStateProps {
  error: any;
  title: string;
  description: string;
  onRetry: () => void;
}

const PagesErrorState: React.FC<PagesErrorStateProps> = ({
  error,
  title,
  description,
  onRetry,
}) => {
  return (
    <Card className="space-y-4">
      <div className="space-y-2">
        <Title>{title}</Title>
      </div>
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Something went wrong
          </h3>
          <p className="text-gray-600 mb-4">{description}</p>
          <button
            onClick={onRetry}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
        {process.env.NODE_ENV === "development" && (
          <details className="mt-4 p-4 bg-gray-100 rounded-md max-w-md">
            <summary className="cursor-pointer text-sm font-medium">
              Error Details (Development)
            </summary>
            <pre className="mt-2 text-xs text-gray-700 overflow-auto">
              {JSON.stringify(error, null, 2)}
            </pre>
          </details>
        )}
      </div>
    </Card>
  );
};

export default PagesErrorState;
