"use client";
import React, { useRef, useState, useMemo } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "../../../../../_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
import usePagesTable from "./PagesReportsTable.hooks";
import NoData from "../../../../_components/NoData";
import { TAB_TYPES } from "./constants/api.constants";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
const PagesReportsTable = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);
  const badges = ["All Events", "All Conversions"];
  const [filterBy, setFilterBy] = useState(badges[0]);
  const { projectName } = useProjectContext();

  // Determine tab type based on selected filter
  const activeTab =
    filterBy === "All Events" ? TAB_TYPES.EVENTS : TAB_TYPES.CONVERSIONS;

  const { data, isLoading, isError } = usePagesTable({
    page,
  });

  // Select the appropriate data based on the active filter
  const selectedData =
    filterBy === "All Events" ? data?.eventsData : data?.conversionsData;

  // Apply client-side pagination to the selected data
  const paginatedData = useMemo(() => {
    if (!selectedData?.tableData?.tableBody) {
      return {
        tableData: {
          tableHeadings: selectedData?.tableData?.tableHeadings || [],
          tableBody: [],
        },
        pagination: {
          totalPages: 1,
          initialPage: page,
        },
      };
    }

    const itemsPerPage = 10;
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedBody = selectedData.tableData.tableBody.slice(
      startIndex,
      endIndex
    );
    const totalPages = Math.ceil(
      selectedData.tableData.tableBody.length / itemsPerPage
    );

    return {
      tableData: {
        tableHeadings: selectedData.tableData.tableHeadings,
        tableBody: paginatedBody,
      },
      pagination: {
        totalPages: Math.max(totalPages, 1),
        initialPage: page,
      },
    };
  }, [selectedData, page]);

  const lastDataRef = useRef(paginatedData);

  if (paginatedData) {
    lastDataRef.current = paginatedData;
  }

  const stableData = paginatedData ?? lastDataRef.current;

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Create fallback table data if none exists
  const fallbackTableData = {
    tableHeadings:
      activeTab === TAB_TYPES.EVENTS
        ? [
            "Event Name",
            "Conversions",
            "Sessions",
            "Engaged Sessions",
            "New Users",
            "Total Users",
            "Views",
            "Event Count",
            "Total Revenue",
          ]
        : [
            "Conversion Name",
            "Conversions",
            "Sessions",
            "Engaged Sessions",
            "New Users",
            "Total Users",
            "Views",
            "Event Count",
            "Total Revenue",
          ],
    tableBody: [],
  };

  // Ensure we always have valid table data
  const tableDataToRender = stableData?.tableData || fallbackTableData;

  // Always show the table structure, never show "No Data"
  return (
    <Card className="w-full space-y-10 min-h-[520px] flex flex-col justify-between">
      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <DataTable
          title={`${projectName} ${activeTab} Demographic Reports`}
          tableData={tableDataToRender}
          isLoading={isLoading}
          badges={badges}
          selectedItem={filterBy}
          setSelectedItem={setFilterBy}
        />
      </motion.div>
      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={stableData?.pagination.initialPage || 1}
        onPageChange={setPage}
      />
    </Card>
  );
};
export default PagesReportsTable;
