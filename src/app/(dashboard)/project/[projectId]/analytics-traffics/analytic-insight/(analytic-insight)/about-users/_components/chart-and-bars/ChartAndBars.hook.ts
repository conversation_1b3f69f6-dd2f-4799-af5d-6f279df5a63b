import { useMemo } from "react";
import useAgeData from "../../../../../overview/(overview)/users-overview/hooks/useAgeData";
import useCountriesData from "../../../../../overview/(overview)/users-overview/hooks/useCountriesData";
import useCitiesData from "../../../../../overview/(overview)/users-overview/hooks/useCitiesData";
import useLanguageData from "../../../../../overview/(overview)/users-overview/hooks/useLanguageData";
import useGenderData from "../../../../../overview/(overview)/users-overview/hooks/useGenderData";

/**
 * Custom hook for ChartAndBars component to fetch data based on selected filter
 * @param activeTabFilter - The currently selected filter (Age, Countries, Cities, Language, Gender)
 * @returns Query result with progress bar data for the selected filter
 */
const useChartAndBarsData = (activeTabFilter: string) => {
  const isAgeFilterSelected = activeTabFilter.toLowerCase() === "age";
  const isCountriesFilterSelected =
    activeTabFilter.toLowerCase() === "countries";
  const isCitiesFilterSelected = activeTabFilter.toLowerCase() === "cities";
  const isLanguageFilterSelected = activeTabFilter.toLowerCase() === "language";
  const isGenderFilterSelected = activeTabFilter.toLowerCase() === "gender";

  // Only fetch age data when Age filter is selected
  const {
    data: ageProgressData,
    isLoading: isAgeLoading,
    error: ageError,
  } = useAgeData(isAgeFilterSelected);

  // Always fetch countries data (it's used for maps and Countries tab)
  const {
    data: countriesData,
    isLoading: isCountriesLoading,
    error: countriesError,
  } = useCountriesData();

  // Only fetch cities data when Cities filter is selected
  const {
    data: citiesProgressData,
    isLoading: isCitiesLoading,
    error: citiesError,
  } = useCitiesData(isCitiesFilterSelected);

  // Only fetch language data when Language filter is selected
  const {
    data: languageProgressData,
    isLoading: isLanguageLoading,
    error: languageError,
  } = useLanguageData(isLanguageFilterSelected);

  // Only fetch gender data when Gender filter is selected
  const {
    data: genderProgressData,
    isLoading: isGenderLoading,
    error: genderError,
  } = useGenderData(isGenderFilterSelected);

  // Return the appropriate data based on selected filter
  const result = useMemo(() => {
    if (isAgeFilterSelected) {
      // Transform age data to include comparison percentages
      const transformedProgressBarData =
        ageProgressData?.progressbarData?.map((item, index) => {
          const comparisonItem =
            ageProgressData?.comparisonProgressbarData?.[index];
          return {
            ...item,
            comparisonPercentage: comparisonItem?.percentage,
            comparisonValue: comparisonItem?.value,
          };
        }) || [];

      return {
        progressBarData: transformedProgressBarData,
        isLoading: isAgeLoading,
        error: ageError,
        hasData: transformedProgressBarData.length > 0,
      };
    }

    if (isCountriesFilterSelected) {
      // Transform countries data to include comparison percentages
      const transformedProgressBarData =
        countriesData?.progressbarData?.map((item, index) => {
          const comparisonItem =
            countriesData?.comparisonProgressbarData?.[index];
          return {
            ...item,
            comparisonPercentage: comparisonItem?.percentage,
            comparisonValue: comparisonItem?.value,
          };
        }) || [];

      return {
        progressBarData: transformedProgressBarData,
        isLoading: isCountriesLoading,
        error: countriesError,
        hasData: transformedProgressBarData.length > 0,
      };
    }

    if (isCitiesFilterSelected) {
      // Transform cities data to include comparison percentages
      const transformedProgressBarData =
        citiesProgressData?.progressbarData?.map((item, index) => {
          const comparisonItem =
            citiesProgressData?.comparisonProgressbarData?.[index];
          return {
            ...item,
            comparisonPercentage: comparisonItem?.percentage,
            comparisonValue: comparisonItem?.value,
          };
        }) || [];

      return {
        progressBarData: transformedProgressBarData,
        isLoading: isCitiesLoading,
        error: citiesError,
        hasData: transformedProgressBarData.length > 0,
      };
    }

    if (isLanguageFilterSelected) {
      // Transform language data to include comparison percentages
      const transformedProgressBarData =
        languageProgressData?.progressbarData?.map((item, index) => {
          const comparisonItem =
            languageProgressData?.comparisonProgressbarData?.[index];
          return {
            ...item,
            comparisonPercentage: comparisonItem?.percentage,
            comparisonValue: comparisonItem?.value,
          };
        }) || [];

      return {
        progressBarData: transformedProgressBarData,
        isLoading: isLanguageLoading,
        error: languageError,
        hasData: transformedProgressBarData.length > 0,
      };
    }

    if (isGenderFilterSelected) {
      // Transform gender data to include comparison percentages
      const transformedProgressBarData =
        genderProgressData?.progressbarData?.map((item, index) => {
          const comparisonItem =
            genderProgressData?.comparisonProgressbarData?.[index];
          return {
            ...item,
            comparisonPercentage: comparisonItem?.percentage,
            comparisonValue: comparisonItem?.value,
          };
        }) || [];

      return {
        progressBarData: transformedProgressBarData,
        isLoading: isGenderLoading,
        error: genderError,
        hasData: transformedProgressBarData.length > 0,
      };
    }

    // Fallback for unknown filters
    return {
      progressBarData: [],
      isLoading: false,
      error: null,
      hasData: false,
    };
  }, [
    activeTabFilter,
    ageProgressData,
    isAgeLoading,
    ageError,
    isAgeFilterSelected,
    countriesData,
    isCountriesLoading,
    countriesError,
    isCountriesFilterSelected,
    citiesProgressData,
    isCitiesLoading,
    citiesError,
    isCitiesFilterSelected,
    languageProgressData,
    isLanguageLoading,
    languageError,
    isLanguageFilterSelected,
    genderProgressData,
    isGenderLoading,
    genderError,
    isGenderFilterSelected,
  ]);

  return result;
};

export default useChartAndBarsData;
