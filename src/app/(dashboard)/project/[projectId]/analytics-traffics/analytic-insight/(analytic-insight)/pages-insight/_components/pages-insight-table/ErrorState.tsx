"use client";
import React from "react";
import { AlertCircle, RefreshCw } from "lucide-react";

interface ErrorStateProps {
  message: string;
  onRetry?: () => void;
  type?: "primary" | "comparison";
}

const ErrorState: React.FC<ErrorStateProps> = ({ 
  message, 
  onRetry, 
  type = "primary" 
}) => {
  const isComparisonError = type === "comparison";
  
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className={`rounded-full p-3 mb-4 ${
        isComparisonError 
          ? "bg-yellow-100 text-yellow-600" 
          : "bg-red-100 text-red-600"
      }`}>
        <AlertCircle className="h-8 w-8" />
      </div>
      
      <h3 className={`text-lg font-semibold mb-2 ${
        isComparisonError ? "text-yellow-800" : "text-red-800"
      }`}>
        {isComparisonError ? "Comparison Data Unavailable" : "Failed to Load Data"}
      </h3>
      
      <p className="text-gray-600 mb-4 max-w-md">
        {isComparisonError 
          ? "Unable to load comparison data. The main data is still available."
          : message || "There was an error loading the page data. Please try again."
        }
      </p>
      
      {onRetry && !isComparisonError && (
        <button
          onClick={onRetry}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </button>
      )}
    </div>
  );
};

export default ErrorState;