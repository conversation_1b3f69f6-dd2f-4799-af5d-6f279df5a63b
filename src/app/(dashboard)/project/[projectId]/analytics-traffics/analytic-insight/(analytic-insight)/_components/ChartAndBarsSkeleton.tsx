import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import React from "react";
import Skeleton from "react-loading-skeleton";

const ChartAndBarsSkeleton = () => {
  return (
    <Card className="h-[500px] flex flex-col gap-4">
      <div>
        <Title>About Users</Title>
      </div>
      <DateRange />
      <div className="flex h-[30%] gap-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="h-[72px] w-full">
            <Skeleton height={"100%"} width={"100%"} />
          </div>
        ))}
      </div>
      <div className="h-full flex flex-row justify-between gap-4">
        <div className="w-2/3">
          <Skeleton height={"100%"} />
        </div>
        <div className="w-1/3">
          <Skeleton height={"100%"} />
        </div>
      </div>
    </Card>
  );
};

export default ChartAndBarsSkeleton;
