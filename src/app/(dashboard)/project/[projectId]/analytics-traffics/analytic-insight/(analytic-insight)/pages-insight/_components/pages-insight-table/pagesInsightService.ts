import http from "@/services/httpService";

export interface PageMetrics {
  SESSIONS: number;
  "ENGAGED SESSIONS": number;
  "NEW USERS": number;
  "TOTAL USERS": number;
  VIEWS: number;
  "ENGAGED TIME": number;
  "ENGAGED RATE": number;
  "EVENT COUNT": number;
  CONVERSIONS: number;
}

export interface PagesResponse {
  status: string;
  project_id: string;
  period: {
    start_date: string;
    end_date: string;
    days_count: number;
  };
  data: Record<string, PageMetrics> | {
    [key: string]: Record<string, PageMetrics>;
  };
}

export type PageType = "all-pages" | "high-traffic" | "landing-pages";

export const pagesInsightService = {
  // Get pages data based on type
  getPages: async (
    projectId: string,
    startDate: string,
    endDate: string,
    pageType: PageType
  ): Promise<{ data: PagesResponse["data"]; period: PagesResponse["period"] }> => {
    try {
      const endpoint = `/api/project/GA4/page/demographic/${pageType}/${projectId}`;
      
      console.log("🌐 Pages Insight API Call:", {
        url: endpoint,
        params: { start_date: startDate, end_date: endDate },
        projectId,
        startDate,
        endDate,
        pageType,
      });

      const response = await http.get(endpoint, {
        params: {
          start_date: startDate,
          end_date: endDate,
        },
        useAuth: true,
      });

      console.log("🌐 Pages Insight API Response:", {
        status: response.status,
        hasData: !!response.data,
        pageType,
        dataKeys: response.data ? Object.keys(response.data) : [],
        responseStructure: {
          hasStatus: !!response.data?.status,
          hasDataProperty: !!response.data?.data,
          statusValue: response.data?.status,
          actualDataKeys: response.data?.data
            ? Object.keys(response.data.data)
            : [],
        },
      });

      // Validate response structure
      if (!response?.data) {
        throw new Error("Invalid API response: missing data");
      }

      if (response.data.status !== "success") {
        throw new Error(`API returned error status: ${response.data.status}`);
      }

      // The API returns { status: "success", data: {...}, period: {...} }
      const data = response.data.data || {};
      const period = response.data.period || {
        start_date: startDate,
        end_date: endDate,
        days_count: 0,
      };

      return { data, period };
    } catch (error) {
      console.error(`Pages insight API error for ${pageType}:`, error);

      // Re-throw with more context for better error handling
      if (error instanceof Error) {
        throw new Error(`Failed to fetch ${pageType} data: ${error.message}`);
      }
      
      throw new Error(`Failed to fetch ${pageType} data: Unknown error`);
    }
  },
};