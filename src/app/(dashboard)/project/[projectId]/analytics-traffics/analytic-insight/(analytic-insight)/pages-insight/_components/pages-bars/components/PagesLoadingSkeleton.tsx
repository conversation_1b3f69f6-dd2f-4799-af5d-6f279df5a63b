import React from "react";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../../../../_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import HorizontalBar from "@/components/ui/horizontal-bar/HorizontalBar";
import { useProjectContext } from "@/contexts/ProjectContext";

const PagesLoadingSkeleton: React.FC = () => {
  const { projectName } = useProjectContext();
  return (
    <Card className="space-y-4">
      <div className="space-y-2">
        <div className="flex w-full justify-between items-center">
          <Title>{projectName} Pages</Title>
          <div className="w-32 h-8 bg-gray-200 rounded animate-pulse" />
        </div>
        <DateRange />
      </div>

      <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap">
        {Array.from({ length: 7 }).map((_, i) => (
          <CardTab key={i} isLoading />
        ))}
      </div>

      <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2 min-h-[435px]">
        {Array.from({ length: 7 }).map((_, i) => (
          <HorizontalBar isLoading key={i} />
        ))}
      </div>
    </Card>
  );
};

export default PagesLoadingSkeleton;
