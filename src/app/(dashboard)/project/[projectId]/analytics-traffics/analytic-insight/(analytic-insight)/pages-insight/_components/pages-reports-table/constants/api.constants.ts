// API endpoints
export const API_ENDPOINTS = {
  EVENTS_DEMOGRAPHIC: (projectId: string) =>
    `/api/project/GA4/eventsandconversions/demographic/events/${projectId}/`,
  CONVERSIONS_DEMOGRAPHIC: (projectId: string) =>
    `/api/project/GA4/eventsandconversions/demographic/conversions/${projectId}/`,
} as const;

// Tab types
export const TAB_TYPES = {
  EVENTS: "Events",
  CONVERSIONS: "Conversions",
} as const;

// Table configuration
export const TABLE_CONFIG = {
  ITEMS_PER_PAGE: 10,
  DEFAULT_PAGE: 1,
} as const;

// Table headings mapping
export const TABLE_HEADINGS = {
  EVENTS: [
    "Event Name",
    "Conversions",
    "Sessions",
    "Engaged Sessions",
    "New Users",
    "Total Users",
    "Views",
    "Event Count",
    "Total Revenue",
  ],
  CONVERSIONS: [
    "Conversion Name",
    "Conversions",
    "Sessions",
    "Engaged Sessions",
    "New Users",
    "Total Users",
    "Views",
    "Event Count",
    "Total Revenue",
  ],
} as const;

// Metric keys mapping to API response
export const METRIC_KEYS = [
  "CONVERSION",
  "SESSIONS",
  "ENGAGED",
  "NEW USERS",
  "TOTAL USERS",
  "VIEWS",
  "EVENT COUNT",
  "TOTAL REVENUE",
] as const;

// Query configuration
export const QUERY_CONFIG = {
  STALE_TIME: 5 * 60 * 1000, // 5 minutes
  GC_TIME: 10 * 60 * 1000, // 10 minutes
} as const;
