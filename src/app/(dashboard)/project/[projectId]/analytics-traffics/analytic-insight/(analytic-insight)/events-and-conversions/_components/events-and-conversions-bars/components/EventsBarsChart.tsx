"use client";
import React from "react";

/* =============================== COMPONENTS =============================== */
import HorizontalBar from "@/components/ui/horizontal-bar/HorizontalBar";

/* ================================== UTILS ================================= */
import abbreviateNumber from "@/utils/abbreviateNumber";

/* ================================== TYPES ================================= */
import type { BarsData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/HorizontalBars.types";

/* ========================================================================== */
interface EventsBarsChartProps {
  barsData: BarsData | null;
  isLoading: boolean;
  activeTab?: string;
}

/* ========================================================================== */
const EventsBarsChart: React.FC<EventsBarsChartProps> = ({
  barsData,
  isLoading,
  activeTab,
}) => {
  const percentageLabelByTab: Record<string, string> = {
    "All Users": "of all users",
    "Active Users": "of active users",
    "New Users": "of new users",
    "Returning Users": "of returning users",
    Sessions: "of sessions",
    "Engaged Sessions": "of engaged sessions",
    Views: "of views",
  };

  const computedPercentageLabel = activeTab
    ? percentageLabelByTab[activeTab] ?? ""
    : "";
  if (isLoading) {
    return (
      <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2 min-h-[435px]">
        {Array.from({ length: 7 }).map((_, i) => (
          <HorizontalBar isLoading key={i} />
        ))}
      </div>
    );
  }

  if (!barsData || !barsData.bars || barsData.bars.length === 0) {
    return (
      <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2 min-h-[435px] flex items-center justify-center">
        <div className="text-secondary text-center">
          <p className="text-lg font-medium">No data available</p>
          <p className="text-sm">
            Try selecting a different filter or date range
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2 min-h-[435px]">
        {barsData.bars.map(({ barData, label, apiPercentage }, index) => (
          <HorizontalBar
            percentageLabel={computedPercentageLabel}
            key={index}
            label={label}
            bars={barData}
            totalValue={barsData.maxValue}
            customPercentage={
              typeof apiPercentage === "number" ? apiPercentage : undefined
            }
          />
        ))}
      </div>
      {/* Scale indicators like in traffic overview */}
      <div className="flex justify-between mx-8 text-secondary/80">
        {Array.from({ length: 7 }).map((_, index) => (
          <span key={index}>
            {abbreviateNumber(Math.floor((barsData.maxValue / 6) * index))}
          </span>
        ))}
      </div>
    </div>
  );
};

export default EventsBarsChart;
