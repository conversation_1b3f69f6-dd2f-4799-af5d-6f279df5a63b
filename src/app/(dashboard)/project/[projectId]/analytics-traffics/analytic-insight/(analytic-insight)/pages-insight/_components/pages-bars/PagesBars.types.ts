import { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import { BarsData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/HorizontalBars.types";

// Common types for page data
export interface PageData {
  value: number;
  percentage: number;
}

export interface PagesMap {
  [key: string]: PageData;
}

// All Pages API Response Types
export interface AllPagesData {
  views: number;
  views_pages: PagesMap;
  sessions: number;
  sessions_pages: PagesMap;
  new_users: number;
  new_users_pages: PagesMap;
  total_users: number;
  total_users_pages: PagesMap;
  returning_users: number;
  returning_users_pages: PagesMap;
  engaged_sessions: number;
  engaged_sessions_pages: PagesMap;
  avg_engagement_time: number;
  avg_engagement_time_pages: PagesMap;
}

export interface AllPagesApiResponse {
  status: "success";
  project_id: string;
  period: {
    start_date: string;
    end_date: string;
    days_count: number;
  };
  data: {
    "all-pages": {
      "all-pages": AllPagesData;
    };
  };
}

// High Traffic API Response Types (uses same structure as landing pages)
export interface HighTrafficData {
  views: number;
  views_pages: PagesMap;
  sessions: number;
  sessions_pages: PagesMap;
  new_users: number;
  new_users_pages: PagesMap;
  total_users: number;
  total_users_pages: PagesMap;
  returning_users: number;
  returning_users_pages: PagesMap;
  engaged_sessions: number;
  engaged_sessions_pages: PagesMap;
  avg_engagement_time: number;
  avg_engagement_time_pages: PagesMap;
  active_users: number;
  active_users_pages: PagesMap;
}

export interface HighTrafficApiResponse {
  status: "success";
  project_id: string;
  period: {
    start_date: string;
    end_date: string;
    days_count: number;
  };
  data: {
    "high-traffic": {
      "high-traffic": HighTrafficData;
    };
  };
}

// Landing Pages API Response Types (keeping for backward compatibility)
export interface LandingPagesApiResponse {
  status: string;
  project_id: string;
  period: {
    start_date: string;
    end_date: string;
    days_count: number;
  };
  data: {
    "landing-pages": {
      "landing-pages": {
        views: number;
        views_pages: Record<string, { value: number; percentage: number }>;
        sessions: number;
        sessions_pages: Record<string, { value: number; percentage: number }>;
        new_users: number;
        new_users_pages: Record<string, { value: number; percentage: number }>;
        total_users: number;
        total_users_pages: Record<
          string,
          { value: number; percentage: number }
        >;
        returning_users: number;
        returning_users_pages: Record<
          string,
          { value: number; percentage: number }
        >;
        engaged_sessions: number;
        engaged_sessions_pages: Record<
          string,
          { value: number; percentage: number }
        >;
        avg_engagement_time: number;
        avg_engagement_time_pages: Record<
          string,
          { value: number; percentage: number }
        >;
      };
    };
  };
}

// Transformed data types
export interface TransformedPagesData {
  cardTabs: CardTabType[];
  barsData: BarsData;
}

// Legacy type for backward compatibility
export type PagesBarsResponse = TransformedPagesData;
