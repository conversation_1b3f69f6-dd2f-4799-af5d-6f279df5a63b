import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import { TableDataRequest } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";
import { useProjectId } from "@/hooks/useProjectId";

const useTrafficTable = ({
  page,
  filterBy,
}: {
  page: number;
  filterBy: string;
}) => {
  const { projectId, isValidProjectId } = useProjectId();

  return useQuery({
    queryKey: ["user-traffic-table", projectId, page, filterBy],
    queryFn: async (): Promise<TableDataRequest> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/analytic-insight/traffics/traffics-table",
        {
          params: {
            projectId,
            page,
            filterBy,
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });
};

export default useTrafficTable;
