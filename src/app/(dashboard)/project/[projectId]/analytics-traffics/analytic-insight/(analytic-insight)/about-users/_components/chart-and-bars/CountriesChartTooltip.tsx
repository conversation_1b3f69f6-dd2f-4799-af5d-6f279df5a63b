import React, { memo } from "react";
import Card from "@/components/ui/card";
import abbreviateNumber from "@/utils/abbreviateNumber";
import type { DailyCountriesMetric } from "./useDailyCountriesData";

interface CountriesChartTooltipProps {
  payload?: unknown[];
  label?: string;
  dailyCountriesData?: {
    currentPeriod: DailyCountriesMetric[];
    comparisonPeriod: DailyCountriesMetric[] | null;
  };
  isComparisonEnabled?: boolean;
  convertChartDateToApiDate: (chartDate: string) => string | null;
}

/**
 * Custom tooltip for Countries chart that shows country breakdown
 */
const CountriesChartTooltip = memo(
  ({
    payload,
    label,
    dailyCountriesData,
    isComparisonEnabled,
    convertChartDateToApiDate,
  }: CountriesChartTooltipProps) => {
    if (!payload || !payload.length || !label || !dailyCountriesData)
      return null;

    // Convert chart date to API date format
    const apiDate = convertChartDateToApiDate(label);
    if (!apiDate) return null;

    // Find the daily metric for the hovered date
    const dayData = dailyCountriesData.currentPeriod?.find(
      (day) => day.date === apiDate
    );

    if (!dayData || !dayData.dimensions) return null;

    // Find comparison data for the same date if available
    const comparisonDayData = dailyCountriesData.comparisonPeriod?.find(
      (day) => day.date === apiDate
    );

    // Create a map of comparison data for easy lookup
    const comparisonMap = new Map();
    if (comparisonDayData?.dimensions) {
      comparisonDayData.dimensions.forEach((country) => {
        comparisonMap.set(country.name, country);
      });
    }

    // Calculate total users for percentage calculation
    const totalUsers = dayData.dimensions.reduce(
      (sum, country) => sum + country.total_users,
      0
    );

    const comparisonTotalUsers =
      comparisonDayData?.dimensions?.reduce(
        (sum, country) => sum + country.total_users,
        0
      ) || 0;

    // Get top 5 countries for the tooltip
    const topCountries = dayData.dimensions
      .sort((a, b) => b.total_users - a.total_users)
      .slice(0, 5);

    // Calculate percentage change
    const calculatePercentageChange = (
      current: number,
      previous: number
    ): number | null => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    // Determine color based on change direction
    const getPercentageColor = (change: number) => {
      if (change === 0) return "text-gray-600";
      return change > 0 ? "text-green-600" : "text-red-600";
    };

    const hasComparison =
      isComparisonEnabled && comparisonDayData && comparisonTotalUsers > 0;

    return (
      <Card
        className="rounded-lg p-4 text-sm grid gap-3 border border-[#E0E0E0] min-w-[280px]"
        style={{ boxShadow: "0px 4px 8px 0px #3440541A" }}
      >
        <div className="text-sm font-semibold text-gray-900">{label}</div>

        {/* Show main chart metrics first */}
        <div className="space-y-2 border-b border-gray-200 pb-2">
          <div className="text-xs font-semibold text-gray-700 mb-2">
            Daily Metrics
          </div>
          {payload.map((entry: unknown, index: number) => {
            const isDotted = entry.dataKey.includes("dotted_");
            if (isDotted) return null; // Skip dotted lines in this section

            return (
              <div
                key={index}
                className="flex items-center justify-between gap-4"
              >
                <div className="flex items-center gap-2">
                  <span
                    className="inline-block w-2 h-2 rounded-full"
                    style={{ backgroundColor: entry.color }}
                  />
                  <span className="text-xs text-gray-600 capitalize">
                    {entry.dataKey.replace(/_/g, " ")}
                  </span>
                </div>
                <span className="font-bold text-xs">
                  {typeof entry.value === "string"
                    ? entry.value
                    : abbreviateNumber(entry.value)}
                </span>
              </div>
            );
          })}
        </div>

        {/* Show country breakdown */}
        <div className="space-y-2">
          <div className="text-xs font-semibold text-gray-700 mb-2">
            Top Countries ({abbreviateNumber(totalUsers)} total users)
          </div>

          {topCountries.map((country, index) => {
            const comparisonCountry = comparisonMap.get(country.name);
            const currentPercentage =
              totalUsers > 0
                ? parseFloat(
                    ((country.total_users / totalUsers) * 100).toFixed(2)
                  )
                : 0;
            const comparisonPercentage =
              comparisonCountry && comparisonTotalUsers > 0
                ? parseFloat(
                    (
                      (comparisonCountry.total_users / comparisonTotalUsers) *
                      100
                    ).toFixed(2)
                  )
                : null;

            const percentageChange = comparisonCountry
              ? calculatePercentageChange(
                  country.total_users,
                  comparisonCountry.total_users
                )
              : null;

            return (
              <div key={country.name} className="space-y-1">
                {hasComparison && comparisonPercentage !== null ? (
                  // Comparison mode: show current and comparison side by side
                  <div className="grid grid-cols-2 gap-4">
                    {/* Current period */}
                    <div className="flex items-center justify-between gap-2">
                      <div className="flex items-center gap-2">
                        <span className="inline-block w-2 h-2 rounded-full bg-primary" />
                        <span className="text-xs text-gray-600 truncate">
                          {country.name}
                        </span>
                      </div>
                      <span className="font-bold text-xs">
                        {abbreviateNumber(country.total_users)}
                      </span>
                    </div>

                    {/* Comparison period */}
                    <div className="flex items-center justify-between gap-2">
                      <div className="flex items-center gap-2">
                        <span
                          className="inline-block w-2 h-2 rounded-full border border-primary-yellow"
                          style={{ backgroundColor: "transparent" }}
                        />
                      </div>
                      <span className="font-bold text-xs">
                        {abbreviateNumber(comparisonCountry?.total_users || 0)}
                      </span>
                    </div>
                  </div>
                ) : (
                  // Single period mode
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-2">
                      <span
                        className={`inline-block w-2 h-2 rounded-full ${
                          index === 0
                            ? "bg-primary"
                            : index === 1
                            ? "bg-primary-yellow"
                            : "bg-gray-400"
                        }`}
                      />
                      <span className="text-xs text-gray-600 truncate">
                        {country.name}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-bold text-xs">
                        {abbreviateNumber(country.total_users)}
                      </span>
                      <span className="text-xs text-gray-500">
                        ({currentPercentage.toFixed(2)}%)
                      </span>
                    </div>
                  </div>
                )}

                {/* Percentage change row for comparison mode */}
                {hasComparison && percentageChange !== null && (
                  <div className="flex justify-center">
                    <span
                      className={`text-xs font-semibold ${getPercentageColor(
                        percentageChange
                      )}`}
                    >
                      {percentageChange > 0 ? "+" : ""}
                      {percentageChange.toFixed(1)}%
                    </span>
                  </div>
                )}
              </div>
            );
          })}

          {dayData.dimensions.length > 5 && (
            <div className="text-xs text-gray-500 text-center pt-1">
              +{dayData.dimensions.length - 5} more countries
            </div>
          )}
        </div>
      </Card>
    );
  }
);

CountriesChartTooltip.displayName = "CountriesChartTooltip";

export default CountriesChartTooltip;
