// Common metrics structure for both Events and Conversions
export interface DemographicMetrics {
  CONVERSION: number;
  SESSIONS: number;
  ENGAGED: number;
  "NEW USERS": number;
  "TOTAL USERS": number;
  VIEWS: number;
  "EVENT COUNT": number;
  "TOTAL REVENUE": number;
}

// Events data structure
export interface EventsData {
  [eventName: string]: DemographicMetrics;
}

// Conversions data structure
export interface ConversionsData {
  [conversionName: string]: DemographicMetrics;
}

// Common API period structure
export interface ApiPeriod {
  start_date: string;
  end_date: string;
  days_count: number;
}

// Events API response
export interface EventsDemographicApiResponse {
  status: string;
  project_id: string;
  period: ApiPeriod;
  data: {
    events: EventsData;
  };
}

// Conversions API response
export interface ConversionsDemographicApiResponse {
  status: string;
  project_id: string;
  period: ApiPeriod;
  data: {
    conversions: ConversionsData;
  };
}

// Union type for both API responses
export type DemographicApiResponse =
  | EventsDemographicApiResponse
  | ConversionsDemographicApiResponse;

// Union type for both data types
export type DemographicData = EventsData | ConversionsData;

// Transformed data for table display
export interface TransformedTableData {
  tableData: {
    tableHeadings: string[];
    tableBody: Array<Array<{ value: string; growth?: string }>>;
  };
  pagination: {
    totalPages: number;
    initialPage?: number;
  };
}
