import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import type {
  EventsApiResponse,
  ConversionsApiResponse,
  TransformedAnalyticsData,
} from "./EventsAndConversions.types";
import { useProjectId } from "@/hooks/useProjectId";
import {
  transformEventsApiResponse,
  transformConversionsApiResponse,
} from "./utils/transformEventsData";
import {
  API_ENDPOINTS,
  FILTER_TYPES,
  DEFAULTS,
  QUERY_CONFIG,
} from "./constants/analytics.constants";

// Raw data type for storing API responses
type RawAnalyticsData = {
  eventsData?: EventsApiResponse;
  conversionsData?: ConversionsApiResponse;
  eventsComparisonData?: EventsApiResponse | null;
  conversionsComparisonData?: ConversionsApiResponse | null;
};

const useEventsAndConversionsBars = ({
  tab,
  filter,
}: {
  tab: string;
  filter: string;
}) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  const query = useQuery({
    queryKey: [
      "events-and-conversions-bars",
      projectId,
      getFormattedDates(),
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<RawAnalyticsData> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      const result: RawAnalyticsData = {};

      // Fetch Events data
      try {
        const eventsResponse = await httpService.get(
          API_ENDPOINTS.EVENTS(projectId),
          {
            params: {
              start_date: startDate,
              end_date: endDate,
            },
            useAuth: true,
          }
        );
        result.eventsData = eventsResponse.data as EventsApiResponse;

        // Fetch Events comparison data if comparison is enabled
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          try {
            const comparisonResponse = await httpService.get(
              API_ENDPOINTS.EVENTS(projectId),
              {
                params: {
                  start_date: comparisonStartDate,
                  end_date: comparisonEndDate,
                },
                useAuth: true,
              }
            );
            result.eventsComparisonData =
              comparisonResponse.data as EventsApiResponse;
          } catch (error) {
            console.warn("Failed to fetch Events comparison data:", error);
            result.eventsComparisonData = null;
          }
        }
      } catch (error) {
        console.warn("Failed to fetch Events data:", error);
      }

      // Fetch Conversions data
      try {
        const conversionsResponse = await httpService.get(
          API_ENDPOINTS.CONVERSIONS(projectId),
          {
            params: {
              start_date: startDate,
              end_date: endDate,
            },
            useAuth: true,
          }
        );
        result.conversionsData =
          conversionsResponse.data as ConversionsApiResponse;

        // Fetch Conversions comparison data if comparison is enabled
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          try {
            const comparisonResponse = await httpService.get(
              API_ENDPOINTS.CONVERSIONS(projectId),
              {
                params: {
                  start_date: comparisonStartDate,
                  end_date: comparisonEndDate,
                },
                useAuth: true,
              }
            );
            result.conversionsComparisonData =
              comparisonResponse.data as ConversionsApiResponse;
          } catch (error) {
            console.warn("Failed to fetch Conversions comparison data:", error);
            result.conversionsComparisonData = null;
          }
        }
      } catch (error) {
        console.warn("Failed to fetch Conversions data:", error);
      }

      return result;
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: QUERY_CONFIG.STALE_TIME,
    gcTime: QUERY_CONFIG.GC_TIME,
  });

  // Transform the raw data for the specific tab and filter
  const transformDataForTab = (): TransformedAnalyticsData | undefined => {
    if (!query.data) return undefined;

    const activeTab = tab || DEFAULTS.ACTIVE_TAB;
    const { isComparisonEnabled } = useDateRangeStore.getState();

    if (filter === FILTER_TYPES.EVENTS && query.data.eventsData) {
      return transformEventsApiResponse(
        query.data.eventsData,
        activeTab,
        isComparisonEnabled ? query.data.eventsComparisonData : undefined
      );
    } else if (
      filter === FILTER_TYPES.CONVERSIONS &&
      query.data.conversionsData
    ) {
      return transformConversionsApiResponse(
        query.data.conversionsData,
        activeTab,
        isComparisonEnabled ? query.data.conversionsComparisonData : undefined
      );
    }

    // Return empty data if no matching data found
    return {
      cardTabs: [],
      barsData: {
        bars: [],
        maxValue: 0,
      },
    };
  };

  return {
    ...query,
    data: transformDataForTab(),
  };
};

export default useEventsAndConversionsBars;
