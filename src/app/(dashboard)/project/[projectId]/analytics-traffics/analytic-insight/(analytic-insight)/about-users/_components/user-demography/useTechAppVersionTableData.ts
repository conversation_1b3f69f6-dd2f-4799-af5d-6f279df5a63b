import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";

// Types for tech app version table data
interface TechAppVersionTableItem {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

interface TechAppVersionTableData {
  tableData: {
    tableHeadings: string[];
    tableBody: Array<Array<{ value: string; growth?: string }>>;
  };
  pagination: {
    totalPages: number;
    currentPage: number;
    totalItems: number;
  };
}

interface TechAppVersionApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: TechAppVersionTableItem[];
    daily_metrics: Array<{
      date: string;
      dimensions: TechAppVersionTableItem[];
    }>;
  };
}

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// Helper function to format percentage
const formatPercentage = (rate: number): string => {
  return `${(rate * 100).toFixed(1)}%`;
};

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

/**
 * Hook to fetch tech app version table data
 */
const useTechAppVersionTableData = ({ page }: { page: number }) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: ["tech-app-version-table-data", projectId, page, getFormattedDates()],
    queryFn: async (): Promise<TechAppVersionTableData> => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } = await httpService.get<TechAppVersionApiResponse>(
        `/api/project/GA4/user/tech/app-version/${projectId}?start_date=${startDate}&end_date=${endDate}`,
        { useAuth: true }
      );

      let comparisonData: TechAppVersionApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get<TechAppVersionApiResponse>(
            `/api/project/GA4/user/tech/app-version/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison data:", error);
        }
      }

      // Create a map of comparison data for easy lookup
      const comparisonMap = new Map<string, TechAppVersionTableItem>();
      if (comparisonData?.data?.dimension_breakdown) {
        comparisonData.data.dimension_breakdown.forEach((appVersion) => {
          comparisonMap.set(appVersion.name, appVersion);
        });
      }

      // Transform API data to table format
      const allAppVersionData = currentData.data.dimension_breakdown;

      // Table headings
      const tableHeadings = [
        "APP VERSION",
        "TOTAL USERS",
        "NEW USERS",
        "SESSIONS",
        "ENGAGED SESSIONS",
        "VIEWS",
        "ENGAGEMENT RATE",
        "EVENT COUNT",
        "CONVERSIONS",
        "PERCENTAGE",
      ];

      // Transform data to table body format with comparison
      const allTableBody = allAppVersionData.map((appVersion) => {
        const comparisonAppVersion = comparisonMap.get(appVersion.name);

        return [
          { value: appVersion.name },
          {
            value: formatNumber(appVersion.total_users),
            growth: comparisonAppVersion
              ? calculatePercentageChange(
                  appVersion.total_users,
                  comparisonAppVersion.total_users
                )
              : undefined,
          },
          {
            value: formatNumber(appVersion.new_users),
            growth: comparisonAppVersion
              ? calculatePercentageChange(
                  appVersion.new_users,
                  comparisonAppVersion.new_users
                )
              : undefined,
          },
          {
            value: formatNumber(appVersion.sessions),
            growth: comparisonAppVersion
              ? calculatePercentageChange(
                  appVersion.sessions,
                  comparisonAppVersion.sessions
                )
              : undefined,
          },
          {
            value: formatNumber(appVersion.engaged_sessions),
            growth: comparisonAppVersion
              ? calculatePercentageChange(
                  appVersion.engaged_sessions,
                  comparisonAppVersion.engaged_sessions
                )
              : undefined,
          },
          {
            value: formatNumber(appVersion.views),
            growth: comparisonAppVersion
              ? calculatePercentageChange(
                  appVersion.views,
                  comparisonAppVersion.views
                )
              : undefined,
          },
          {
            value: formatPercentage(appVersion.engagement_rate),
            growth: comparisonAppVersion
              ? calculatePercentageChange(
                  appVersion.engagement_rate,
                  comparisonAppVersion.engagement_rate
                )
              : undefined,
          },
          {
            value: formatNumber(appVersion.event_count),
            growth: comparisonAppVersion
              ? calculatePercentageChange(
                  appVersion.event_count,
                  comparisonAppVersion.event_count
                )
              : undefined,
          },
          {
            value: formatNumber(appVersion.conversions),
            growth: comparisonAppVersion
              ? calculatePercentageChange(
                  appVersion.conversions,
                  comparisonAppVersion.conversions
                )
              : undefined,
          },
          {
            value: `${appVersion.percentage.toFixed(1)}%`,
            growth: comparisonAppVersion
              ? calculatePercentageChange(
                  appVersion.percentage,
                  comparisonAppVersion.percentage
                )
              : undefined,
          },
        ];
      });

      // Simple pagination - show 5 items per page
      const itemsPerPage = 5;
      const totalItems = allAppVersionData.length;
      const totalPages = Math.ceil(totalItems / itemsPerPage);
      const startIndex = (page - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedTableBody = allTableBody.slice(startIndex, endIndex);

      return {
        tableData: {
          tableHeadings,
          tableBody: paginatedTableBody,
        },
        pagination: {
          totalPages,
          currentPage: page,
          totalItems,
        },
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export default useTechAppVersionTableData;