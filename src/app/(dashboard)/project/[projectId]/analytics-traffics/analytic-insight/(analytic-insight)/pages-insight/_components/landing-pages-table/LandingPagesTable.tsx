"use client";
import React, { useRef, useState } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "../../../../../_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import useLandingPagesData from "./useLandingPagesData";

/* ========================================================================== */
const LandingPagesTable = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);

  const { data, isLoading, isError } = useLandingPagesData({
    page,
  });

  const lastDataRef = useRef(data);

  if (data) {
    lastDataRef.current = data;
  }

  const stableData = data ?? lastDataRef.current;

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return (
    <Card className="w-full space-y-4 min-h-[520px] flex flex-col justify-between">
      {/* Header with title only */}
      <div className="flex w-full justify-between items-center">
        <Title>Landing Pages Performance</Title>
      </div>

      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="flex-1"
      >
        <DataTable
          title=""
          tableData={data?.tableData || { tableHeadings: [], tableBody: [] }}
          isLoading={isLoading}
          currentPage={page}
        />
      </motion.div>
      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={page}
        onPageChange={setPage}
      />
    </Card>
  );
};

export default LandingPagesTable;