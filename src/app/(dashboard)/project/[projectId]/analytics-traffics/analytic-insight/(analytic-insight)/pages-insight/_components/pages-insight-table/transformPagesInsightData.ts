import { PagesResponse, PageMetrics, PageType } from "./pagesInsightService";

const ITEMS_PER_PAGE = 5;

export interface TransformedPagesInsightData {
  tableData: {
    tableHeadings: string[];
    tableBody: Array<Array<{
      value: string;
      growth?: string;
    }>>;
  };
  pagination: {
    totalPages: number;
    currentPage: number;
    totalItems: number;
  };
}

const formatNumber = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + "M";
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + "K";
  }
  return value.toString();
};

const formatTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}s`;
  }
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

const calculateChange = (
  current: number,
  previous: number
): { change: number; changeType: "increase" | "decrease" | "neutral" } => {
  if (previous === 0) {
    return { change: current > 0 ? 100 : 0, changeType: current > 0 ? "increase" : "neutral" };
  }
  
  const change = ((current - previous) / previous) * 100;
  const changeType = change > 0 ? "increase" : change < 0 ? "decrease" : "neutral";
  
  return { change: Math.abs(change), changeType };
};

const getDataKey = (pageType: PageType): string => {
  switch (pageType) {
    case "all-pages":
      return "all-pages";
    case "high-traffic":
      return "high-traffic";
    case "landing-pages":
      return "landing-pages";
    default:
      return "all-pages";
  }
};

export const transformPagesInsightData = (
  primaryData: PagesResponse["data"],
  pageType: PageType,
  page: number,
  comparisonData?: PagesResponse["data"] | null
): TransformedPagesInsightData => {
  console.log("🔄 transformPagesInsightData called:", {
    pageType,
    page,
    primaryDataKeys: Object.keys(primaryData || {}),
    hasComparisonData: !!comparisonData,
  });

  // Handle both nested and flat data structures
  let pages: Record<string, PageMetrics> = {};
  let comparisonPages: Record<string, PageMetrics> = {};

  // Check if data is nested under pageType key or flat
  const dataKey = getDataKey(pageType);
  console.log("🔍 Checking data structure:", {
    dataKey,
    hasNestedKey: !!(primaryData as any)?.[dataKey],
    primaryDataType: typeof primaryData,
    primaryDataKeys: Object.keys(primaryData || {}),
  });

  if ((primaryData as any)?.[dataKey]) {
    // Nested structure
    console.log("📁 Using nested structure");
    pages = (primaryData as any)[dataKey] || {};
    comparisonPages = (comparisonData as any)?.[dataKey] || {};
  } else {
    // Flat structure - use the data directly
    console.log("📄 Using flat structure");
    pages = primaryData as Record<string, PageMetrics>;
    comparisonPages = (comparisonData as Record<string, PageMetrics>) || {};
  }

  console.log("📊 Pages data:", {
    pagesCount: Object.keys(pages).length,
    firstPageKey: Object.keys(pages)[0],
    firstPageData: pages[Object.keys(pages)[0]],
  });

  // Convert to array and sort by sessions (descending)
  const pagesArray = Object.entries(pages).map(([pagePath, metrics]) => ({
    pagePath,
    metrics,
  }));

  pagesArray.sort((a, b) => b.metrics.SESSIONS - a.metrics.SESSIONS);

  // Pagination
  const totalItems = pagesArray.length;
  const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
  const startIndex = (page - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedData = pagesArray.slice(startIndex, endIndex);

  // Table headings
  const tableHeadings = [
    "Page",
    "Sessions",
    "Engaged Sessions",
    "New Users",
    "Total Users",
    "Views",
    "Engaged Time",
    "Event Count",
    "Conversions",
    "Engaged Rate",
  ];

  // Transform data for table - return 2D array format expected by DataTable
  const tableBody = paginatedData.map((item) => {
    const { pagePath, metrics } = item;
    const comparisonMetrics = comparisonPages[pagePath];

    const createCellData = (
      value: number,
      formatter: (val: number) => string = (val) => val.toString(),
      comparisonValue?: number
    ) => {
      let growth = "";
      
      if (comparisonValue !== undefined && comparisonData) {
        const { change, changeType } = calculateChange(value, comparisonValue);
        const sign = changeType === "increase" ? "+" : changeType === "decrease" ? "-" : "";
        growth = `${sign}${change.toFixed(1)}%`;
      }

      return {
        value: formatter(value),
        growth: growth || undefined,
      };
    };

    // Return array of cell data (not object with id and data)
    return [
      { value: pagePath === "(not set)" ? "(not set)" : pagePath },
      createCellData(metrics.SESSIONS, formatNumber, comparisonMetrics?.SESSIONS),
      createCellData(metrics["ENGAGED SESSIONS"], formatNumber, comparisonMetrics?.["ENGAGED SESSIONS"]),
      createCellData(metrics["NEW USERS"], formatNumber, comparisonMetrics?.["NEW USERS"]),
      createCellData(metrics["TOTAL USERS"], formatNumber, comparisonMetrics?.["TOTAL USERS"]),
      createCellData(metrics.VIEWS, formatNumber, comparisonMetrics?.VIEWS),
      createCellData(metrics["ENGAGED TIME"], formatTime, comparisonMetrics?.["ENGAGED TIME"]),
      createCellData(metrics["EVENT COUNT"], formatNumber, comparisonMetrics?.["EVENT COUNT"]),
      createCellData(metrics.CONVERSIONS, formatNumber, comparisonMetrics?.CONVERSIONS),
      createCellData(metrics["ENGAGED RATE"], formatPercentage, comparisonMetrics?.["ENGAGED RATE"]),
    ];
  });

  console.log("📋 Final table body:", {
    rowCount: tableBody.length,
    firstRow: tableBody[0],
    firstRowLength: tableBody[0]?.length,
  });

  return {
    tableData: {
      tableHeadings,
      tableBody,
    },
    pagination: {
      totalPages,
      currentPage: page,
      totalItems,
    },
  };
};