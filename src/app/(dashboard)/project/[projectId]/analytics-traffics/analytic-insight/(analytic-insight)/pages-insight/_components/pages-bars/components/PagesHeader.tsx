import React from "react";
import Title from "@/components/ui/Title";
import DateRange from "../../../../../../_components/date-range/DateRange";
import Dropdown from "@/components/ui/Dropdown";
import { useProjectContext } from "@/contexts/ProjectContext";

interface PagesHeaderProps {
  activeFilter: string;
  filters: string[];
  onFilterChange: (filter: string) => void;
}

const PagesHeader: React.FC<PagesHeaderProps> = ({
  activeFilter,
  filters,
  onFilterChange,
}) => {
  const { projectName } = useProjectContext();
  return (
    <div className="space-y-2">
      <div className="flex w-full justify-between items-center">
        <Title>{projectName} Pages</Title>
        <Dropdown>
          <Dropdown.Button>{activeFilter}</Dropdown.Button>
          <Dropdown.Options>
            {filters.map((filter, index) => (
              <Dropdown.Option
                key={index}
                onClick={() => onFilterChange(filter)}
              >
                {filter}
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
      </div>
      <DateRange />
    </div>
  );
};

export default PagesHeader;
