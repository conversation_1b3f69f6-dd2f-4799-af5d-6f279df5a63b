import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { trafficDemographicService } from "./trafficDemographicService";
import { transformTrafficTableData } from "./transformTrafficTableData";

interface UseTrafficTableDataProps {
  page: number;
  trafficSource: string;
}

const useTrafficTableData = ({
  page,
  trafficSource,
}: UseTrafficTableDataProps) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API calls
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  // Fetch primary data
  const {
    data: primaryData,
    isLoading: primaryLoading,
    isError: primaryError,
  } = useQuery({
    queryKey: ["traffic-demographic-primary", projectId, startDate, endDate],
    queryFn: async () => {
      if (!projectId || !startDate || !endDate) {
        throw new Error("Missing required parameters");
      }
      return trafficDemographicService.getTrafficDemographic(
        projectId,
        startDate,
        endDate
      );
    },
    enabled: isValidProjectId && !!projectId && !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    // Prevent query from throwing and breaking the component
    throwOnError: false,
  });

  // Fetch comparison data if comparison is enabled
  const {
    data: comparisonData,
    isLoading: comparisonLoading,
    isError: comparisonError,
  } = useQuery({
    queryKey: [
      "traffic-demographic-comparison",
      projectId,
      comparisonStartDate,
      comparisonEndDate,
    ],
    queryFn: async () => {
      if (!projectId || !comparisonStartDate || !comparisonEndDate) {
        throw new Error("Missing required parameters");
      }
      return trafficDemographicService.getTrafficDemographic(
        projectId,
        comparisonStartDate,
        comparisonEndDate
      );
    },
    enabled:
      isComparisonEnabled &&
      isValidProjectId &&
      !!projectId &&
      !!comparisonStartDate &&
      !!comparisonEndDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    // Prevent query from throwing and breaking the component
    throwOnError: false,
  });

  // Transform data client-side with error handling
  const transformedData = useMemo(() => {
    console.log("🔄 useTrafficTableData transform memo:", {
      hasPrimaryData: !!primaryData?.data,
      hasComparisonData: !!comparisonData?.data,
      trafficSource,
      page,
      primaryLoading,
      primaryError,
    });

    // Always return a valid structure to ensure tabs don't disappear
    const emptyTableStructure = {
      tableData: {
        tableHeadings: [
          "Source",
          "Sessions",
          "Engaged Sessions",
          "New Users",
          "Total Users",
          "Views",
          "Engaged Time",
          "Event Count",
          "Conversions",
          "Engaged Rate",
        ],
        tableBody: [],
      },
      pagination: {
        totalPages: 1,
        currentPage: 1,
        totalItems: 0,
      },
    };

    try {
      if (!primaryData?.data) {
        console.log("❌ No primary data available");
        return emptyTableStructure;
      }

      console.log("✅ Primary data available, transforming...");
      return transformTrafficTableData(
        primaryData.data,
        trafficSource,
        page,
        comparisonData?.data || null
      );
    } catch (error) {
      console.error("Error transforming traffic table data:", error);
      return emptyTableStructure;
    }
  }, [primaryData, comparisonData, trafficSource, page]);

  // Determine loading state - don't wait for comparison if primary fails
  const isLoading =
    primaryLoading ||
    (isComparisonEnabled && comparisonLoading && !primaryError);

  // Only show error if primary data fails - but still return data structure
  const isError = primaryError;

  // If queries are disabled (e.g., missing projectId), still return valid structure
  const finalData = transformedData || {
    tableData: {
      tableHeadings: [
        "Source",
        "Sessions",
        "Engaged Sessions",
        "New Users",
        "Total Users",
        "Views",
        "Engaged Time",
        "Event Count",
        "Conversions",
        "Engaged Rate",
      ],
      tableBody: [],
    },
    pagination: {
      totalPages: 1,
      currentPage: 1,
      totalItems: 0,
    },
  };

  return {
    data: finalData, // Always returns valid structure
    isLoading,
    isError,
  };
};

export default useTrafficTableData;
