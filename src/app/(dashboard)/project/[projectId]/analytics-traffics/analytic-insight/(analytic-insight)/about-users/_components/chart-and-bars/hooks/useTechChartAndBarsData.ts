import { useMemo } from "react";
import useTechDeviceData from "./useTechDeviceData";
import useTechOSData from "./useTechOSData";
import useTechAppVersionData from "./useTechAppVersionData";
import useTechBrowserData from "./useTechBrowserData";

/**
 * Custom hook for ChartAndBars component to fetch tech data based on selected filter
 * @param activeTabFilter - The currently selected tech filter (Device, OS, App version, Browser)
 * @returns Query result with progress bar data for the selected tech filter
 */
const useTechChartAndBarsData = (activeTabFilter: string) => {
  const isDeviceFilterSelected = activeTabFilter.toLowerCase() === "device";
  const isOSFilterSelected = activeTabFilter.toLowerCase() === "os";
  const isAppVersionFilterSelected = activeTabFilter.toLowerCase() === "app version";
  const isBrowserFilterSelected = activeTabFilter.toLowerCase() === "browser";

  // Only fetch device data when <PERSON><PERSON> filter is selected
  const {
    data: techDeviceProgressData,
    isLoading: isTechDeviceLoading,
    error: techDeviceError,
  } = useTechDeviceData(isDeviceFilterSelected);

  // Only fetch OS data when OS filter is selected
  const {
    data: techOSProgressData,
    isLoading: isTechOSLoading,
    error: techOSError,
  } = useTechOSData(isOSFilterSelected);

  // Only fetch app version data when App version filter is selected
  const {
    data: techAppVersionProgressData,
    isLoading: isTechAppVersionLoading,
    error: techAppVersionError,
  } = useTechAppVersionData(isAppVersionFilterSelected);

  // Only fetch browser data when Browser filter is selected
  const {
    data: techBrowserProgressData,
    isLoading: isTechBrowserLoading,
    error: techBrowserError,
  } = useTechBrowserData(isBrowserFilterSelected);

  // Return the appropriate data based on selected filter
  const result = useMemo(() => {
    if (isDeviceFilterSelected) {
      // Transform tech device data to include comparison percentages
      const transformedProgressBarData =
        techDeviceProgressData?.progressbarData?.map((item, index) => {
          const comparisonItem =
            techDeviceProgressData?.comparisonProgressbarData?.[index];
          return {
            ...item,
            comparisonPercentage: comparisonItem?.percentage,
          };
        }) || [];

      return {
        progressBarData: transformedProgressBarData,
        isLoading: isTechDeviceLoading,
        error: techDeviceError,
        hasData: transformedProgressBarData.length > 0,
      };
    }

    if (isOSFilterSelected) {
      // Transform tech OS data to include comparison percentages
      const transformedProgressBarData =
        techOSProgressData?.progressbarData?.map((item, index) => {
          const comparisonItem =
            techOSProgressData?.comparisonProgressbarData?.[index];
          return {
            ...item,
            comparisonPercentage: comparisonItem?.percentage,
          };
        }) || [];

      return {
        progressBarData: transformedProgressBarData,
        isLoading: isTechOSLoading,
        error: techOSError,
        hasData: transformedProgressBarData.length > 0,
      };
    }

    if (isAppVersionFilterSelected) {
      // Transform tech app version data to include comparison percentages
      const transformedProgressBarData =
        techAppVersionProgressData?.progressbarData?.map((item, index) => {
          const comparisonItem =
            techAppVersionProgressData?.comparisonProgressbarData?.[index];
          return {
            ...item,
            comparisonPercentage: comparisonItem?.percentage,
          };
        }) || [];

      return {
        progressBarData: transformedProgressBarData,
        isLoading: isTechAppVersionLoading,
        error: techAppVersionError,
        hasData: transformedProgressBarData.length > 0,
      };
    }

    if (isBrowserFilterSelected) {
      // Transform tech browser data to include comparison percentages
      const transformedProgressBarData =
        techBrowserProgressData?.progressbarData?.map((item, index) => {
          const comparisonItem =
            techBrowserProgressData?.comparisonProgressbarData?.[index];
          return {
            ...item,
            comparisonPercentage: comparisonItem?.percentage,
          };
        }) || [];

      return {
        progressBarData: transformedProgressBarData,
        isLoading: isTechBrowserLoading,
        error: techBrowserError,
        hasData: transformedProgressBarData.length > 0,
      };
    }

    // Fallback for unknown filters
    return {
      progressBarData: [],
      isLoading: false,
      error: null,
      hasData: false,
    };
  }, [
    activeTabFilter,
    techDeviceProgressData,
    isTechDeviceLoading,
    techDeviceError,
    isDeviceFilterSelected,
    techOSProgressData,
    isTechOSLoading,
    techOSError,
    isOSFilterSelected,
    techAppVersionProgressData,
    isTechAppVersionLoading,
    techAppVersionError,
    isAppVersionFilterSelected,
    techBrowserProgressData,
    isTechBrowserLoading,
    techBrowserError,
    isBrowserFilterSelected,
  ]);

  return result;
};

export default useTechChartAndBarsData;