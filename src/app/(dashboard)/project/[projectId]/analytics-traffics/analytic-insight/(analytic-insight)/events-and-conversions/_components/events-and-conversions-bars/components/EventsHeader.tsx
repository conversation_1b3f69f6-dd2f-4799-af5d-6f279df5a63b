"use client";
import React from "react";

/* =============================== COMPONENTS =============================== */
import Title from "@/components/ui/Title";
import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import Dropdown from "@/components/ui/Dropdown";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ================================== TYPES ================================= */
interface EventsHeaderProps {
  activeFilter: string;
  filters: string[];
  onFilterChange: (filter: string) => void;
}

/* ========================================================================== */
const EventsHeader: React.FC<EventsHeaderProps> = ({
  activeFilter,
  filters,
  onFilterChange,
}) => {
  const { projectName } = useProjectContext();
  return (
    <div className="space-y-2">
      <div className="flex w-full justify-between items-center">
        <Title>{projectName} Events & Conversions</Title>
        <Dropdown>
          <Dropdown.Button>{activeFilter}</Dropdown.Button>
          <Dropdown.Options>
            {filters.map((filter, index) => (
              <Dropdown.Option
                key={index}
                onClick={() => onFilterChange(filter)}
              >
                {filter}
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
      </div>
      <DateRange />
    </div>
  );
};

export default EventsHeader;
