"use client";
import React, { useRef, useState, useEffect } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "../../../../../_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import useTrafficTableData from "./useTrafficTableData";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
const TrafficTable = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);
  const { projectName } = useProjectContext();

  // Traffic source filters
  const trafficFilters = ["Total Traffic", "Organic", "Referral", "Social"];
  const [filterBy, setFilterBy] = useState(trafficFilters[0]);

  // Reset page to 1 when filter changes
  useEffect(() => {
    setPage(1);
  }, [filterBy]);

  const { data, isLoading, isError } = useTrafficTableData({
    page,
    trafficSource: filterBy,
  });

  const lastDataRef = useRef(data);

  if (data) {
    lastDataRef.current = data;
  }

  const stableData = data ?? lastDataRef.current;

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Always render the card structure to maintain tabs
  return (
    <Card className="w-full space-y-4 min-h-[520px] flex flex-col justify-between">
      {/* Header with title only */}
      <div className="flex w-full justify-between items-center">
        <Title>{projectName} Traffic Demographics</Title>
      </div>

      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="flex-1"
      >
        <DataTable
          title=""
          tableData={data?.tableData || { tableHeadings: [], tableBody: [] }}
          isLoading={isLoading}
          badges={trafficFilters}
          selectedItem={filterBy}
          setSelectedItem={setFilterBy}
          currentPage={page}
          firstColumnWidthPercent={15}
          pageLinkBaseUrl={filterBy === "Referral" ? "https://" : undefined}
          openLinksInNewTab={filterBy === "Referral"}
        />
      </motion.div>
      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={page}
        onPageChange={setPage}
      />
    </Card>
  );
};

export default TrafficTable;
