import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { landingPagesService } from "./landingPagesService";
import { transformLandingPagesData } from "./transformLandingPagesData";

interface UseLandingPagesDataProps {
  page: number;
}

const useLandingPagesData = ({ page }: UseLandingPagesDataProps) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API calls
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  // Fetch primary data
  const {
    data: primaryData,
    isLoading: primaryLoading,
    isError: primaryError,
  } = useQuery({
    queryKey: ["landing-pages-primary", projectId, startDate, endDate],
    queryFn: async () => {
      if (!projectId || !startDate || !endDate) {
        throw new Error("Missing required parameters");
      }
      return landingPagesService.getLandingPages(
        projectId,
        startDate,
        endDate
      );
    },
    enabled: isValidProjectId && !!projectId && !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    // Prevent query from throwing and breaking the component
    throwOnError: false,
  });

  // Fetch comparison data if comparison is enabled
  const {
    data: comparisonData,
    isLoading: comparisonLoading,
    isError: comparisonError,
  } = useQuery({
    queryKey: [
      "landing-pages-comparison",
      projectId,
      comparisonStartDate,
      comparisonEndDate,
    ],
    queryFn: async () => {
      if (!projectId || !comparisonStartDate || !comparisonEndDate) {
        throw new Error("Missing required parameters");
      }
      return landingPagesService.getLandingPages(
        projectId,
        comparisonStartDate,
        comparisonEndDate
      );
    },
    enabled:
      isComparisonEnabled &&
      isValidProjectId &&
      !!projectId &&
      !!comparisonStartDate &&
      !!comparisonEndDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    // Prevent query from throwing and breaking the component
    throwOnError: false,
  });

  // Transform data client-side with error handling
  const transformedData = useMemo(() => {
    console.log("🔄 useLandingPagesData transform memo:", {
      hasPrimaryData: !!primaryData?.data,
      hasComparisonData: !!comparisonData?.data,
      page,
      primaryLoading,
      primaryError,
    });

    // Always return a valid structure
    const emptyTableStructure = {
      tableData: {
        tableHeadings: [
          "Landing Page",
          "Sessions",
          "Engaged Sessions",
          "New Users",
          "Total Users",
          "Views",
          "Engaged Time",
          "Event Count",
          "Conversions",
          "Engaged Rate",
        ],
        tableBody: [],
      },
      pagination: {
        totalPages: 1,
        currentPage: 1,
        totalItems: 0,
      },
    };

    try {
      if (!primaryData?.data) {
        console.log("❌ No primary data available");
        return emptyTableStructure;
      }

      console.log("✅ Primary data available, transforming...");
      return transformLandingPagesData(
        primaryData.data,
        page,
        comparisonData?.data || null
      );
    } catch (error) {
      console.error("Error transforming landing pages data:", error);
      return emptyTableStructure;
    }
  }, [primaryData, comparisonData, page]);

  // Determine loading state - don't wait for comparison if primary fails
  const isLoading =
    primaryLoading ||
    (isComparisonEnabled && comparisonLoading && !primaryError);

  // Only show error if primary data fails - but still return data structure
  const isError = primaryError;

  // If queries are disabled (e.g., missing projectId), still return valid structure
  const finalData = transformedData || {
    tableData: {
      tableHeadings: [
        "Landing Page",
        "Sessions",
        "Engaged Sessions",
        "New Users",
        "Total Users",
        "Views",
        "Engaged Time",
        "Event Count",
        "Conversions",
        "Engaged Rate",
      ],
      tableBody: [],
    },
    pagination: {
      totalPages: 1,
      currentPage: 1,
      totalItems: 0,
    },
  };

  return {
    data: finalData, // Always returns valid structure
    isLoading,
    isError,
  };
};

export default useLandingPagesData;