import React from "react";
import HorizontalBar from "@/components/ui/horizontal-bar/HorizontalBar";
import type { BarsData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/HorizontalBars.types";
import abbreviateNumber from "@/utils/abbreviateNumber";

interface PagesBarsChartProps {
  barsData: BarsData | null;
  isLoading: boolean;
}

const PagesBarsChart: React.FC<PagesBarsChartProps> = ({
  barsData,
  isLoading,
}) => {
  if (isLoading) {
    return (
      <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2 min-h-[435px]">
        {Array.from({ length: 7 }).map((_, i) => (
          <HorizontalBar isLoading key={i} />
        ))}
      </div>
    );
  }

  if (!barsData || !barsData.bars || barsData.bars.length === 0) {
    return (
      <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2 min-h-[435px] flex items-center justify-center">
        <div className="text-secondary text-center">
          <p className="text-lg font-medium">No data available</p>
          <p className="text-sm">
            Try selecting a different filter or date range
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2 min-h-[435px]">
        {barsData.bars.map(({ barData, label, apiPercentage }, index) => (
          <HorizontalBar
            percentageLabel={index < 2 ? "of all pages" : ""}
            key={index}
            label={label}
            bars={barData}
            totalValue={barsData.maxValue}
            customPercentage={apiPercentage}
          />
        ))}
      </div>
      {/* Scale indicators like in EventsBarsChart */}
      <div className="flex justify-between mx-8 text-secondary/80">
        {Array.from({ length: 7 }).map((_, index) => (
          <span key={index}>
            {abbreviateNumber(Math.floor((barsData.maxValue / 6) * index))}
          </span>
        ))}
      </div>
    </div>
  );
};

export default PagesBarsChart;
