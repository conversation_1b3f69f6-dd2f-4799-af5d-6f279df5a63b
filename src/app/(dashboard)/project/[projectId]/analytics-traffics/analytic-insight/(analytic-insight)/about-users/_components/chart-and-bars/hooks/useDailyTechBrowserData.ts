import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";

// Types for the daily tech browser metrics API response
interface DailyTechBrowserData {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
}

interface DailyTechBrowserMetric {
  date: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  dimensions: DailyTechBrowserData[];
}

interface DailyTechBrowserApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: DailyTechBrowserData[];
    daily_metrics: DailyTechBrowserMetric[];
  };
}

/**
 * Hook to fetch daily tech browser metrics data with comparison support
 * This provides tech browser breakdown for each day in the selected date range
 */
const useDailyTechBrowserData = (enabled: boolean = true) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: [
      "daily-tech-browser-data",
      projectId,
      getFormattedDates(),
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<{
      currentPeriod: DailyTechBrowserMetric[];
      comparisonPeriod: DailyTechBrowserMetric[] | null;
    }> => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } =
        await httpService.get<DailyTechBrowserApiResponse>(
          `/api/project/GA4/user/tech/browser/${projectId}?start_date=${startDate}&end_date=${endDate}`,
          { useAuth: true }
        );

      let comparisonData: DailyTechBrowserApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get<DailyTechBrowserApiResponse>(
            `/api/project/GA4/user/tech/browser/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison tech browser data:", error);
        }
      }

      return {
        currentPeriod: currentData.data?.daily_metrics || [],
        comparisonPeriod: comparisonData?.data?.daily_metrics || null,
      };
    },
    enabled: enabled && isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export default useDailyTechBrowserData;
export type { DailyTechBrowserMetric, DailyTechBrowserData };