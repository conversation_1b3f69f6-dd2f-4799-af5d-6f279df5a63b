import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { useProjectId } from "@/hooks/useProjectId";
import type {
  EventsDemographicApiResponse,
  ConversionsDemographicApiResponse,
  TransformedTableData,
} from "./types/EventsDemographic.types";
import {
  transformEventsDemographicToTable,
  transformConversionsDemographicToTable,
} from "./utils/transformEventsDemographicData";
import {
  API_ENDPOINTS,
  QUERY_CONFIG,
  TAB_TYPES,
} from "./constants/api.constants";

const usePagesTable = ({
  page,
  filterBy,
  tabType = TAB_TYPES.EVENTS,
}: {
  page: number;
  filterBy?: string;
  tabType?: string;
}) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates } = useDateRangeStore();

  // Use a completely static query key that never changes
  // This ensures the data is fetched only once and cached forever
  return useQuery({
    queryKey: ["demographic-table-static", projectId],
    queryFn: async (): Promise<{
      eventsData: TransformedTableData;
      conversionsData: TransformedTableData;
    }> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      try {
        const { startDate: formattedStartDate, endDate: formattedEndDate } =
          getFormattedDates();

        // Fetch both Events and Conversions data in parallel
        const [eventsResponse, conversionsResponse] = await Promise.all([
          httpService.get(API_ENDPOINTS.EVENTS_DEMOGRAPHIC(projectId), {
            params: {
              start_date: formattedStartDate,
              end_date: formattedEndDate,
            },
            useAuth: true,
          }),
          httpService.get(API_ENDPOINTS.CONVERSIONS_DEMOGRAPHIC(projectId), {
            params: {
              start_date: formattedStartDate,
              end_date: formattedEndDate,
            },
            useAuth: true,
          }),
        ]);

        // Transform both datasets
        const eventsApiData =
          eventsResponse.data as EventsDemographicApiResponse;
        const conversionsApiData =
          conversionsResponse.data as ConversionsDemographicApiResponse;

        const eventsData = transformEventsDemographicToTable(
          eventsApiData,
          page,
          "All Events"
        );

        const conversionsData = transformConversionsDemographicToTable(
          conversionsApiData,
          page,
          "All Conversions"
        );

        return {
          eventsData,
          conversionsData,
        };
      } catch (error) {
        // If API fails, return empty table structure instead of throwing
        console.warn(`Failed to fetch demographic data:`, error);

        const emptyTableData = {
          tableData: {
            tableHeadings: [
              "Event Name",
              "Conversions",
              "Sessions",
              "Engaged Sessions",
              "New Users",
              "Total Users",
              "Views",
              "Event Count",
              "Total Revenue",
            ],
            tableBody: [],
          },
          pagination: {
            totalPages: 1,
            initialPage: page,
          },
        };

        return {
          eventsData: emptyTableData,
          conversionsData: emptyTableData,
        };
      }
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: Infinity, // Never consider data stale
    gcTime: Infinity, // Never garbage collect
    retry: false, // Don't retry on error, just show empty table
    throwOnError: false, // Don't throw errors, handle gracefully
  });
};

export default usePagesTable;
