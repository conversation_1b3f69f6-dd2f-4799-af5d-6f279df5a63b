"use client";
import React, { useEffect, useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import useEventsAndConversionsBars from "./EventsAndConversionsBars.hooks";
import EventsHeader from "./components/EventsHeader";
import EventsCardTabs from "./components/EventsCardTabs";
import EventsBarsChart from "./components/EventsBarsChart";
import EventsErrorState from "./components/EventsErrorBoundary";
import EventsLoadingSkeleton from "./components/EventsLoadingSkeleton";
import { FILTER_TYPES, DEFAULTS } from "./constants/analytics.constants";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
const EventsAndConversionsBars = () => {
  /* ========================================================================== */
  /*                                   STATE                                    */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState<string>(DEFAULTS.ACTIVE_TAB);
  const filters = [FILTER_TYPES.EVENTS, FILTER_TYPES.CONVERSIONS];
  const [activeFilter, setActiveFilter] = useState<string>(
    DEFAULTS.ACTIVE_FILTER
  );
  const { projectName } = useProjectContext();

  const { data, isError, error, isLoading } = useEventsAndConversionsBars({
    tab: activeTab,
    filter: activeFilter,
  });

  /* ========================================================================== */
  /*                                  EFFECTS                                   */
  /* ========================================================================== */
  useEffect(() => {
    if (data && data.cardTabs.length > 0 && !activeTab) {
      setActiveTab(data.cardTabs[0].title);
    }
  }, [data, activeTab]);

  /* ========================================================================== */
  /*                                 HANDLERS                                   */
  /* ========================================================================== */
  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter);
  };

  const handleTabSelect = (tab: string) => {
    setActiveTab(tab);
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Show error state
  if (isError) {
    console.error("fetching events and conversions data failed: ", error);
    return (
      <EventsErrorState
        error={error}
        title={`${projectName} Events & Conversions`}
        description="Unable to load events and conversions data"
        onRetry={() => window.location.reload()}
      />
    );
  }

  // Show initial loading state when no data is available yet
  if (isLoading && !data) {
    return <EventsLoadingSkeleton />;
  }

  return (
    <Card className="space-y-4">
      <EventsHeader
        activeFilter={activeFilter}
        filters={filters}
        onFilterChange={handleFilterChange}
      />

      <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap">
        <EventsCardTabs
          cardTabs={data?.cardTabs || []}
          activeTab={activeTab}
          onTabSelect={handleTabSelect}
          isLoading={isLoading}
        />
      </div>

      <EventsBarsChart
        barsData={data?.barsData || null}
        isLoading={isLoading}
        activeTab={activeTab}
      />
    </Card>
  );
};

export default EventsAndConversionsBars;
