import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";

// Types for tech OS table data
interface TechOSTableItem {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

interface TechOSTableData {
  tableData: {
    tableHeadings: string[];
    tableBody: Array<Array<{ value: string; growth?: string }>>;
  };
  pagination: {
    totalPages: number;
    currentPage: number;
    totalItems: number;
  };
}

interface TechOSApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: TechOSTableItem[];
    daily_metrics: Array<{
      date: string;
      dimensions: TechOSTableItem[];
    }>;
  };
}

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// Helper function to format percentage
const formatPercentage = (rate: number): string => {
  return `${(rate * 100).toFixed(1)}%`;
};

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

/**
 * Hook to fetch tech OS table data
 */
const useTechOSTableData = ({ page }: { page: number }) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: ["tech-os-table-data", projectId, page, getFormattedDates()],
    queryFn: async (): Promise<TechOSTableData> => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } = await httpService.get<TechOSApiResponse>(
        `/api/project/GA4/user/tech/os/${projectId}?start_date=${startDate}&end_date=${endDate}`,
        { useAuth: true }
      );

      let comparisonData: TechOSApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get<TechOSApiResponse>(
            `/api/project/GA4/user/tech/os/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison data:", error);
        }
      }

      // Create a map of comparison data for easy lookup
      const comparisonMap = new Map<string, TechOSTableItem>();
      if (comparisonData?.data?.dimension_breakdown) {
        comparisonData.data.dimension_breakdown.forEach((os) => {
          comparisonMap.set(os.name, os);
        });
      }

      // Transform API data to table format
      const allOSData = currentData.data.dimension_breakdown;

      // Table headings
      const tableHeadings = [
        "OPERATING SYSTEM",
        "TOTAL USERS",
        "NEW USERS",
        "SESSIONS",
        "ENGAGED SESSIONS",
        "VIEWS",
        "ENGAGEMENT RATE",
        "EVENT COUNT",
        "CONVERSIONS",
        "PERCENTAGE",
      ];

      // Transform data to table body format with comparison
      const allTableBody = allOSData.map((os) => {
        const comparisonOS = comparisonMap.get(os.name);

        return [
          { value: os.name },
          {
            value: formatNumber(os.total_users),
            growth: comparisonOS
              ? calculatePercentageChange(
                  os.total_users,
                  comparisonOS.total_users
                )
              : undefined,
          },
          {
            value: formatNumber(os.new_users),
            growth: comparisonOS
              ? calculatePercentageChange(
                  os.new_users,
                  comparisonOS.new_users
                )
              : undefined,
          },
          {
            value: formatNumber(os.sessions),
            growth: comparisonOS
              ? calculatePercentageChange(
                  os.sessions,
                  comparisonOS.sessions
                )
              : undefined,
          },
          {
            value: formatNumber(os.engaged_sessions),
            growth: comparisonOS
              ? calculatePercentageChange(
                  os.engaged_sessions,
                  comparisonOS.engaged_sessions
                )
              : undefined,
          },
          {
            value: formatNumber(os.views),
            growth: comparisonOS
              ? calculatePercentageChange(
                  os.views,
                  comparisonOS.views
                )
              : undefined,
          },
          {
            value: formatPercentage(os.engagement_rate),
            growth: comparisonOS
              ? calculatePercentageChange(
                  os.engagement_rate,
                  comparisonOS.engagement_rate
                )
              : undefined,
          },
          {
            value: formatNumber(os.event_count),
            growth: comparisonOS
              ? calculatePercentageChange(
                  os.event_count,
                  comparisonOS.event_count
                )
              : undefined,
          },
          {
            value: formatNumber(os.conversions),
            growth: comparisonOS
              ? calculatePercentageChange(
                  os.conversions,
                  comparisonOS.conversions
                )
              : undefined,
          },
          {
            value: `${os.percentage.toFixed(1)}%`,
            growth: comparisonOS
              ? calculatePercentageChange(
                  os.percentage,
                  comparisonOS.percentage
                )
              : undefined,
          },
        ];
      });

      // Simple pagination - show 5 items per page
      const itemsPerPage = 5;
      const totalItems = allOSData.length;
      const totalPages = Math.ceil(totalItems / itemsPerPage);
      const startIndex = (page - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedTableBody = allTableBody.slice(startIndex, endIndex);

      return {
        tableData: {
          tableHeadings,
          tableBody: paginatedTableBody,
        },
        pagination: {
          totalPages,
          currentPage: page,
          totalItems,
        },
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export default useTechOSTableData;