import type {
  EventsDemographicApiResponse,
  ConversionsDemographicApiResponse,
  DemographicApiResponse,
  TransformedTableData,
  DemographicData,
  DemographicMetrics,
} from "../types/EventsDemographic.types";
import {
  TABLE_HEADINGS,
  METRIC_KEYS,
  TABLE_CONFIG,
  TAB_TYPES,
} from "../constants/api.constants";

/**
 * Formats a number for display with appropriate suffixes and formatting
 */
const formatNumber = (value: number): string => {
  if (value >= 1_000_000) {
    return `${(value / 1_000_000).toFixed(1)}M`;
  }
  if (value >= 1_000) {
    return `${(value / 1_000).toFixed(1)}K`;
  }
  return value.toString();
};

/**
 * Formats revenue values with currency symbol
 */
const formatRevenue = (value: number): string => {
  if (value === 0) return "$0.00";
  if (value >= 1_000_000) {
    return `$${(value / 1_000_000).toFixed(2)}M`;
  }
  if (value >= 1_000) {
    return `$${(value / 1_000).toFixed(2)}K`;
  }
  return `$${value.toFixed(2)}`;
};

/**
 * Formats item name for display (converts snake_case to Title Case)
 */
const formatItemName = (itemName: string): string => {
  return itemName.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
};

/**
 * Transforms a single item's metrics to table row format
 */
const transformItemToTableRow = (
  itemName: string,
  metrics: DemographicMetrics
): Array<{ value: string; growth?: string }> => {
  const row = [
    { value: formatItemName(itemName) }, // Item Name (Event/Conversion)
  ];

  // Add each metric value in the correct order
  METRIC_KEYS.forEach((key) => {
    const value = metrics[key];
    let formattedValue: string;

    if (key === "TOTAL REVENUE") {
      formattedValue = formatRevenue(value);
    } else {
      formattedValue = formatNumber(value);
    }

    row.push({ value: formattedValue });
  });

  return row;
};

/**
 * Applies pagination to the data
 */
const paginateItems = (
  items: [string, DemographicMetrics][],
  page: number
): {
  paginatedItems: [string, DemographicMetrics][];
  totalPages: number;
} => {
  const startIndex = (page - 1) * TABLE_CONFIG.ITEMS_PER_PAGE;
  const endIndex = startIndex + TABLE_CONFIG.ITEMS_PER_PAGE;
  const paginatedItems = items.slice(startIndex, endIndex);
  const totalPages = Math.ceil(items.length / TABLE_CONFIG.ITEMS_PER_PAGE);

  return { paginatedItems, totalPages };
};

/**
 * Sorts items by a specific metric (default: EVENT COUNT descending)
 */
const sortItemsByMetric = (
  items: [string, DemographicMetrics][],
  sortBy: keyof DemographicMetrics = "EVENT COUNT"
): [string, DemographicMetrics][] => {
  return items.sort(([, a], [, b]) => b[sortBy] - a[sortBy]);
};

/**
 * Filters items based on the selected filter type
 */
const filterItemsByType = (
  items: [string, DemographicMetrics][],
  filterBy: string,
  tabType: string
): [string, DemographicMetrics][] => {
  switch (filterBy) {
    case "High Conversion":
      // Filter items with conversion > 50
      return items.filter(([, metrics]) => metrics.CONVERSION > 50);

    case "High Revenue":
      // Filter items with revenue > 1000
      return items.filter(([, metrics]) => metrics["TOTAL REVENUE"] > 1000);

    case "All Events":
    case "All Conversions":
    default:
      return items;
  }
};

/**
 * Generic transformation function for demographic data
 */
const transformDemographicDataToTable = (
  data: DemographicData,
  tabType: string,
  page: number = TABLE_CONFIG.DEFAULT_PAGE,
  filterBy?: string
): TransformedTableData => {
  // Convert data object to array of entries
  let dataEntries = Object.entries(data || {});

  // If no data, create table structure with headers but empty body
  if (dataEntries.length === 0) {
    return {
      tableData: {
        tableHeadings:
          tabType === TAB_TYPES.EVENTS
            ? [...TABLE_HEADINGS.EVENTS]
            : [...TABLE_HEADINGS.CONVERSIONS],
        tableBody: [], // Empty body - DataTable will handle this gracefully
      },
      pagination: {
        totalPages: 1,
        initialPage: page,
      },
    };
  }

  // Apply filtering based on the selected filter
  const filterValue =
    filterBy ||
    (tabType === TAB_TYPES.EVENTS ? "All Events" : "All Conversions");

  const filteredItems = filterItemsByType(dataEntries, filterValue, tabType);

  // Sort items by event count (highest first)
  const sortedItems = sortItemsByMetric(filteredItems);

  // Apply pagination
  const { paginatedItems, totalPages } = paginateItems(sortedItems, page);

  // Transform to table format
  const tableBody = paginatedItems.map(([itemName, metrics]) =>
    transformItemToTableRow(itemName, metrics)
  );

  return {
    tableData: {
      tableHeadings:
        tabType === TAB_TYPES.EVENTS
          ? [...TABLE_HEADINGS.EVENTS]
          : [...TABLE_HEADINGS.CONVERSIONS],
      tableBody,
    },
    pagination: {
      totalPages: Math.max(totalPages, 1), // Ensure at least 1 page
      initialPage: page,
    },
  };
};

/**
 * Transforms Events Demographic API response to table format
 */
export const transformEventsDemographicToTable = (
  apiResponse: EventsDemographicApiResponse,
  page: number = TABLE_CONFIG.DEFAULT_PAGE,
  filterBy?: string
): TransformedTableData => {
  return transformDemographicDataToTable(
    apiResponse.data.events,
    TAB_TYPES.EVENTS,
    page,
    filterBy
  );
};

/**
 * Transforms Conversions Demographic API response to table format
 */
export const transformConversionsDemographicToTable = (
  apiResponse: ConversionsDemographicApiResponse,
  page: number = TABLE_CONFIG.DEFAULT_PAGE,
  filterBy?: string
): TransformedTableData => {
  return transformDemographicDataToTable(
    apiResponse.data.conversions,
    TAB_TYPES.CONVERSIONS,
    page,
    filterBy
  );
};

/**
 * Utility function to get total events count
 */
export const getTotalItemsCount = (data: DemographicData): number => {
  return Object.keys(data).length;
};

/**
 * Utility function to get top events by a specific metric
 */
export const getTopItemsByMetric = (
  data: DemographicData,
  metric: keyof DemographicMetrics,
  limit: number = 5
): Array<{ itemName: string; value: number }> => {
  const dataEntries = Object.entries(data);
  const sortedItems = dataEntries.sort(([, a], [, b]) => b[metric] - a[metric]);

  return sortedItems.slice(0, limit).map(([itemName, metrics]) => ({
    itemName: formatItemName(itemName),
    value: metrics[metric],
  }));
};
