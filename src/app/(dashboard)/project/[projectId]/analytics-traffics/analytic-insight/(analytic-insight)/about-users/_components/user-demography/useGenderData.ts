import React from "react";
import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { TableDataRequest } from "../../../../../_components/data-table/DataTable.types";

// Types for the API response
interface GenderData {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

interface GenderApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: GenderData[];
    daily_metrics: any[];
  };
}

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// Helper function to format engagement time
const formatEngagementTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

// Helper function to format percentage
const formatPercentage = (rate: number): string => {
  return `${(rate * 100).toFixed(1)}%`;
};

// First, create a hook that fetches all the data
const useGenderDataRaw = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: ["gender-data", projectId, getFormattedDates()],
    queryFn: async () => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } = await httpService.get(
        `/api/project/GA4/user/demographic/detailed-gender/${projectId}?start_date=${startDate}&end_date=${endDate}`,
        { useAuth: true }
      );

      let comparisonData: GenderApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get(
            `/api/project/GA4/user/demographic/detailed-gender/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison data:", error);
        }
      }

      // Create a map of comparison data for easy lookup
      const comparisonMap = new Map<string, GenderData>();
      if (comparisonData?.data?.dimension_breakdown) {
        comparisonData.data.dimension_breakdown.forEach((gender) => {
          comparisonMap.set(gender.name, gender);
        });
      }

      // Transform data for the table
      const tableHeadings = [
        "GENDER",
        "TOTAL USERS",
        "NEW USERS",
        "SESSIONS",
        "ENGAGED SESSIONS",
        "VIEWS",
        "ENGAGEMENT RATE",
        "EVENT COUNT",
        "CONVERSIONS",
        "PERCENTAGE",
      ];

      // Transform all data (no pagination here)
      const allGenderData = currentData.data.dimension_breakdown;

      const tableBody = allGenderData.map((gender: GenderData) => {
        const comparisonGender = comparisonMap.get(gender.name);

        return [
          { value: gender.name },
          {
            value: formatNumber(gender.total_users),
            growth: comparisonGender
              ? calculatePercentageChange(
                  gender.total_users,
                  comparisonGender.total_users
                )
              : undefined,
          },
          {
            value: formatNumber(gender.new_users),
            growth: comparisonGender
              ? calculatePercentageChange(
                  gender.new_users,
                  comparisonGender.new_users
                )
              : undefined,
          },
          {
            value: formatNumber(gender.sessions),
            growth: comparisonGender
              ? calculatePercentageChange(
                  gender.sessions,
                  comparisonGender.sessions
                )
              : undefined,
          },
          {
            value: formatNumber(gender.engaged_sessions),
            growth: comparisonGender
              ? calculatePercentageChange(
                  gender.engaged_sessions,
                  comparisonGender.engaged_sessions
                )
              : undefined,
          },
          {
            value: formatNumber(gender.views),
            growth: comparisonGender
              ? calculatePercentageChange(gender.views, comparisonGender.views)
              : undefined,
          },
          {
            value: formatPercentage(gender.engagement_rate),
            growth: comparisonGender
              ? calculatePercentageChange(
                  gender.engagement_rate,
                  comparisonGender.engagement_rate
                )
              : undefined,
          },
          {
            value: formatNumber(gender.event_count),
            growth: comparisonGender
              ? calculatePercentageChange(
                  gender.event_count,
                  comparisonGender.event_count
                )
              : undefined,
          },
          {
            value: formatNumber(gender.conversions),
            growth: comparisonGender
              ? calculatePercentageChange(
                  gender.conversions,
                  comparisonGender.conversions
                )
              : undefined,
          },
          {
            value: `${gender.percentage.toFixed(1)}%`,
            growth: comparisonGender
              ? calculatePercentageChange(
                  gender.percentage,
                  comparisonGender.percentage
                )
              : undefined,
          },
        ];
      });

      // Calculate pagination
      const itemsPerPage = 5;
      const totalPages = Math.ceil(allGenderData.length / itemsPerPage);

      return {
        tableHeadings,
        allTableBody: tableBody,
        totalPages,
        allData: allGenderData,
        itemsPerPage,
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Main hook that handles pagination
const useGenderData = ({ page }: { page: number }) => {
  const rawDataQuery = useGenderDataRaw();

  // Handle pagination on the client side
  const paginatedData = React.useMemo(() => {
    if (!rawDataQuery.data) return null;

    const { allTableBody, tableHeadings, totalPages, itemsPerPage } =
      rawDataQuery.data;

    // Calculate pagination
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedTableBody = allTableBody.slice(startIndex, endIndex);

    return {
      tableData: {
        tableHeadings,
        tableBody: paginatedTableBody,
      },
      pagination: {
        totalPages,
        initialPage: page,
      },
    };
  }, [rawDataQuery.data, page]);

  return {
    ...rawDataQuery,
    data: paginatedData,
  };
};

export default useGenderData;
