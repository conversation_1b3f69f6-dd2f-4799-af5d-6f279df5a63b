"use client";
import React, { useEffect, useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import usePagesBars from "./PagesBars.hooks";
import PagesHeader from "./components/PagesHeader";
import PagesCardTabs from "./components/PagesCardTabs";
import PagesBarsChart from "./components/PagesBarsChart";
import PagesErrorState from "./components/PagesErrorBoundary";
import PagesLoadingSkeleton from "./components/PagesLoadingSkeleton";
import {
  FILTER_TYPES,
  DEFAULTS,
  METRIC_TABS,
} from "./constants/pages.constants";

/* ========================================================================== */
const PagesBars = () => {
  /* ========================================================================== */
  /*                                   STATE                                    */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState<string>(DEFAULTS.ACTIVE_TAB);
  const filters = [
    FILTER_TYPES.ALL_PAGES,
    FILTER_TYPES.LANDING_PAGES,
    FILTER_TYPES.HIGH_TRAFFIC,
  ];
  const [activeFilter, setActiveFilter] = useState<string>(
    DEFAULTS.ACTIVE_FILTER
  );

  const { data, isError, error, isLoading } = usePagesBars({
    tab: activeTab,
    filter: activeFilter,
  });

  /* ========================================================================== */
  /*                                  EFFECTS                                   */
  /* ========================================================================== */
  useEffect(() => {
    if (data && data.cardTabs.length > 0 && !activeTab) {
      setActiveTab(data.cardTabs[0].title);
    }
  }, [data, activeTab]);

  /* ========================================================================== */
  /*                                 HANDLERS                                   */
  /* ========================================================================== */
  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter);
  };

  const handleTabSelect = (tab: string) => {
    setActiveTab(tab);
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Show error state
  if (isError) {
    console.error("fetching pages data failed: ", error);
    return (
      <PagesErrorState
        error={error}
        title="Pages"
        description="Unable to load pages data"
        onRetry={() => window.location.reload()}
      />
    );
  }

  // Show initial loading state when no data is available yet
  if (isLoading && !data) {
    return <PagesLoadingSkeleton />;
  }

  return (
    <Card className="space-y-4">
      <PagesHeader
        activeFilter={activeFilter}
        filters={filters}
        onFilterChange={handleFilterChange}
      />

      <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap">
        <PagesCardTabs
          cardTabs={data?.cardTabs || []}
          activeTab={activeTab}
          onTabSelect={handleTabSelect}
          isLoading={isLoading}
        />
      </div>

      <PagesBarsChart barsData={data?.barsData || null} isLoading={isLoading} />
    </Card>
  );
};

export default PagesBars;
