import React from "react";
import { motion } from "framer-motion";
import CardTab from "@/components/ui/card-tab/CardTab";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";

interface PagesCardTabsProps {
  cardTabs: CardTabType[];
  activeTab: string;
  onTabSelect: (tab: string) => void;
  isLoading: boolean;
}

const PagesCardTabs: React.FC<PagesCardTabsProps> = ({
  cardTabs,
  activeTab,
  onTabSelect,
  isLoading,
}) => {
  const { themeColor } = useAppThemeColor();

  if (isLoading && cardTabs.length === 0) {
    return (
      <motion.div
        className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
        key="loading"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        {Array.from({ length: 7 }).map((_, i) => (
          <CardTab key={i} isLoading />
        ))}
      </motion.div>
    );
  }

  return (
    <motion.div
      className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
      key="data"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      {cardTabs.map(({ title, changeValue, value }, index) => (
        <CardTab
          key={index}
          title={title}
          value={value}
          changeValue={changeValue}
          className="border-2"
          style={
            activeTab === title
              ? { borderColor: themeColor }
              : { borderColor: "transparent" }
          }
          onSelect={() => onTabSelect(title)}
        />
      ))}
    </motion.div>
  );
};

export default PagesCardTabs;
