import type {
  EventsApiResponse,
  ConversionsApiResponse,
  TransformedAnalyticsData,
  AnalyticsData,
  MetricBreakdownData,
} from "../EventsAndConversions.types";
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import type {
  BarsData,
  BarsType,
} from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/HorizontalBars.types";
import {
  CHART_COLORS,
  METRIC_TABS,
  EVENTS_METRIC_MAPPING,
  CONVERSIONS_METRIC_MAPPING,
  FILTER_TYPES,
  DEFAULTS,
} from "../constants/analytics.constants";

// Define BarData type to match HorizontalBar component expectations
type BarData = {
  value: number;
  color: string;
  actualValue?: number;
  period?: "current" | "previous";
};

/**
 * Creates bar data for comparison visualization using stacking logic
 */
const createComparisonBarData = (
  currentValue: number,
  previousValue: number
): BarData[] => {
  const barData: BarData[] = [];

  if (currentValue > previousValue && previousValue > 0) {
    // Growth: show previous as base, current difference as additional
    barData.push({
      value: previousValue,
      color: CHART_COLORS.PREVIOUS_PERIOD,
      actualValue: previousValue,
      period: "previous",
    });
    barData.push({
      value: currentValue - previousValue,
      color: CHART_COLORS.CURRENT_PERIOD,
      actualValue: currentValue,
      period: "current",
    });
  } else if (currentValue < previousValue && currentValue > 0) {
    // Decline: show current as base, previous difference as additional
    barData.push({
      value: currentValue,
      color: CHART_COLORS.CURRENT_PERIOD,
      actualValue: currentValue,
      period: "current",
    });
    barData.push({
      value: previousValue - currentValue,
      color: CHART_COLORS.PREVIOUS_PERIOD,
      actualValue: previousValue,
      period: "previous",
    });
  } else if (currentValue > 0 && previousValue === 0) {
    // Only current value
    barData.push({
      value: currentValue,
      color: CHART_COLORS.CURRENT_PERIOD,
      actualValue: currentValue,
      period: "current",
    });
  } else if (previousValue > 0 && currentValue === 0) {
    // Only previous value
    barData.push({
      value: previousValue,
      color: CHART_COLORS.PREVIOUS_PERIOD,
      actualValue: previousValue,
      period: "previous",
    });
  } else if (currentValue === previousValue && currentValue > 0) {
    // Equal values: show current visually
    barData.push({
      value: currentValue,
      color: CHART_COLORS.CURRENT_PERIOD,
      actualValue: currentValue,
      period: "current",
    });
  }

  return barData;
};

/**
 * Formats a number for display with appropriate suffixes (K, M, etc.)
 */
const formatNumber = (value: number): string => {
  if (value >= 1_000_000) {
    return `${(value / 1_000_000).toFixed(1)}M`;
  }
  if (value >= 1_000) {
    return `${(value / 1_000).toFixed(1)}K`;
  }
  return value.toString();
};

/**
 * Calculates percentage change between current and comparison values
 */
const calculatePercentageChange = (
  current: number,
  comparison: number
): string => {
  if (comparison === 0) return "+0%";
  const change = ((current - comparison) / comparison) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

/**
 * Creates CardTab data from analytics data (events or conversions)
 */
const createCardTabs = (
  data: AnalyticsData,
  comparisonData?: AnalyticsData
): CardTabType[] => {
  return METRIC_TABS.filter((metric) => {
    const value = data[metric.key as keyof AnalyticsData] as number | undefined;
    return typeof value === "number" && !Number.isNaN(value);
  }).map((metric) => {
    const currentValue = data[metric.key as keyof AnalyticsData] as number;
    let changeValue = "";

    if (comparisonData) {
      const comparisonValue = comparisonData[
        metric.key as keyof AnalyticsData
      ] as number;
      if (typeof comparisonValue === "number") {
        changeValue = calculatePercentageChange(currentValue, comparisonValue);
      }
    }

    return {
      title: metric.title,
      value: formatNumber(currentValue),
      changeValue,
    };
  });
};

/**
 * Creates horizontal bar data for a specific metric breakdown
 */
const createBarsDataForMetric = (
  data: AnalyticsData,
  activeTab: string,
  filter: string,
  comparisonData?: AnalyticsData
): BarsData => {
  const metricMapping =
    filter === FILTER_TYPES.EVENTS
      ? EVENTS_METRIC_MAPPING
      : CONVERSIONS_METRIC_MAPPING;

  const metricKey =
    metricMapping[activeTab] ||
    (filter === FILTER_TYPES.EVENTS ? "all-events" : "all-conversions");

  const breakdown = data[
    metricKey as keyof AnalyticsData
  ] as MetricBreakdownData;
  const comparisonBreakdown = comparisonData?.[
    metricKey as keyof AnalyticsData
  ] as MetricBreakdownData;

  if (!breakdown) {
    return { bars: [], maxValue: 0 };
  }

  // Convert breakdown to array and sort by value
  const entries = Object.entries(breakdown);
  const sortedEntries = entries.sort(([, a], [, b]) => b.value - a.value);

  // Decide whether to use percentage-based widths (no comparison) or value-based (comparison)
  const hasComparisonValues = Boolean(
    comparisonBreakdown &&
      Object.values(comparisonBreakdown).some((e) => (e as any)?.value > 0)
  );

  // Calculate the maximum value for scaling
  const entryTotals = sortedEntries.map(([entryName, entryData]) => {
    let total = entryData.value;
    if (hasComparisonValues && comparisonBreakdown?.[entryName] && comparisonBreakdown[entryName].value > 0) {
      total += comparisonBreakdown[entryName].value;
    }
    return total;
  });

  const maxValue = hasComparisonValues
    ? Math.max(...entryTotals, DEFAULTS.MIN_MAX_VALUE)
    : 100; // Use 100 when relying on API percentages

  const bars: BarsType[] = sortedEntries
    .filter(([, entry]) => entry.value > 0)
    .map(([entryName, entryData]) => {
      const currentValue = entryData.value;
      const previousValue = comparisonBreakdown?.[entryName]?.value || 0;

      // If we don't have comparison values, use API percentage to drive the width
      const usePercentageWidth = !hasComparisonValues && typeof entryData.percentage === "number";

      const barData = usePercentageWidth
        ? [
            {
              value: entryData.percentage ?? 0,
              color: CHART_COLORS.CURRENT_PERIOD,
              actualValue: currentValue,
              period: "current" as const,
            },
          ]
        : comparisonBreakdown?.[entryName] && previousValue > 0
        ? createComparisonBarData(currentValue, previousValue)
        : [
            {
              value: currentValue,
              color: CHART_COLORS.CURRENT_PERIOD,
              actualValue: currentValue,
              period: "current" as const,
            },
          ];

      return {
        label: entryName
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase()),
        barData,
        apiPercentage: typeof entryData.percentage === "number"
          ? entryData.percentage
          : undefined,
      };
    });

  return {
    bars,
    maxValue,
  };
};

/**
 * Generic transformation function for analytics data
 */
const transformAnalyticsData = (
  data: AnalyticsData,
  comparisonData: AnalyticsData | undefined,
  activeTab: string,
  filter: string
): TransformedAnalyticsData => {
  // Only use comparison data if it's actually available
  const validComparisonData = comparisonData ? comparisonData : undefined;
  
  const cardTabs = createCardTabs(data, validComparisonData);
  const barsData = createBarsDataForMetric(
    data,
    activeTab,
    filter,
    validComparisonData
  );

  return { cardTabs, barsData };
};

/**
 * Transforms Events API response to UI format
 */
export const transformEventsApiResponse = (
  apiResponse: EventsApiResponse,
  activeTab: string,
  comparisonResponse?: EventsApiResponse | null
): TransformedAnalyticsData => {
  return transformAnalyticsData(
    apiResponse.data.events,
    comparisonResponse?.data?.events,
    activeTab,
    FILTER_TYPES.EVENTS
  );
};

/**
 * Transforms Conversions API response to UI format
 */
export const transformConversionsApiResponse = (
  apiResponse: ConversionsApiResponse,
  activeTab: string,
  comparisonResponse?: ConversionsApiResponse | null
): TransformedAnalyticsData => {
  return transformAnalyticsData(
    apiResponse.data.conversions,
    comparisonResponse?.data?.conversions,
    activeTab,
    FILTER_TYPES.CONVERSIONS
  );
};
