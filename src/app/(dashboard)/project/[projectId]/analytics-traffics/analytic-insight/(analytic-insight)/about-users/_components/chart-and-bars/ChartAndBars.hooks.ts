import { useQuery } from "@tanstack/react-query";
import AXIOS from "@/lib/axios";
import type { ChartsAndBars } from "../../../types/ChartAndBars.types";
import { useProjectId } from "@/hooks/useProjectId";

const useChartAndBars = ({
  tab,
  domesticFilter,
  tabFilter,
}: {
  tab: string;
  domesticFilter: string;
  tabFilter: string;
}) => {
  const { projectId, isValidProjectId } = useProjectId();

  const query = useQuery({
    queryKey: ["charts-and-bars", projectId],
    queryFn: async (): Promise<ChartsAndBars> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { data } = await AXIOS.get(
        "/api/dashboard/project/analytics-traffics/analytic-insight/about-users/chart-and-bars-section",
        {
          params: {
            projectId,
            // Fetch all data without filtering parameters
          },
        }
      );
      return data;
    },
    enabled: isValidProjectId && !!projectId,
  });

  // Filter the data on the client side based on the provided parameters
  const filteredData = query.data
    ? {
        ...query.data,
        // Apply client-side filtering here if needed
        // The exact filtering logic depends on the data structure
      }
    : undefined;

  return {
    ...query,
    data: filteredData,
  };
};

export default useChartAndBars;
