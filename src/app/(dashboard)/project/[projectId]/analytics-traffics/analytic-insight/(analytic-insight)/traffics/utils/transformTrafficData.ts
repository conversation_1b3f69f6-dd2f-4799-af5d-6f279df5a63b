import type {
  GA4TrafficResponse,
  TransformedTrafficData,
  DailyMetric,
  TrafficTotals,
  TrafficSourceData,
  ReferralTrafficSourceData,
} from "../types/TrafficData.types";
import abbreviateNumber from "@/utils/abbreviateNumber";

// Color configuration for different traffic sources
const TRAFFIC_SOURCE_COLORS = {
  organic: "#22C55E",
  paid: "#3B82F6",
  referral: "#F59E0B",
  social: "#8B5CF6",
  direct: "#EF4444",
  unassigned: "#6B7280",
  mail: "#EC4899",
};

// Metric display names
const METRIC_NAMES = {
  all_users: "All Users",
  active_users: "Active Users",
  new_users: "New Users",
  returning_users: "Returning Users",
  sessions: "Sessions",
  engaged_sessions: "Engaged Sessions",
  views: "Page Views",
};

// Color constants for comparison charts
const PRIMARY_COLOR = "#914AC4"; // Primary purple color
const COMPARISON_COLOR = "#F59E0B"; // Yellow/Orange for comparison data

// Normalize a date string to YYYYMMDD for reliable keying
const normalizeYmd = (dateStr: string): string => {
  if (!dateStr) return "";
  if (/^\d{8}$/.test(dateStr)) return dateStr;
  // Expected format YYYY-MM-DD
  const parts = dateStr.split("-");
  if (parts.length === 3) {
    const [y, m, d] = parts;
    return `${y}${m}${d}`;
  }
  return dateStr;
};

// Add N days to a YYYYMMDD string and return YYYYMMDD in UTC
const addDaysToYyyymmdd = (yyyymmdd: string, days: number): string => {
  if (!/^\d{8}$/.test(yyyymmdd)) return yyyymmdd;
  const year = Number(yyyymmdd.slice(0, 4));
  const month = Number(yyyymmdd.slice(4, 6));
  const day = Number(yyyymmdd.slice(6, 8));
  const date = new Date(Date.UTC(year, month - 1, day));
  date.setUTCDate(date.getUTCDate() + days);
  const y = date.getUTCFullYear();
  const m = String(date.getUTCMonth() + 1).padStart(2, "0");
  const d = String(date.getUTCDate()).padStart(2, "0");
  return `${y}${m}${d}`;
};

/**
 * Transform GA4 traffic API response to chart component format with comparison support
 */
export const transformTrafficData = (
  apiResponse: GA4TrafficResponse,
  selectedMetric: string = "all_users",
  hoveredDate?: string,
  comparisonResponse?: GA4TrafficResponse | null
): TransformedTrafficData => {
  const { data } = apiResponse;
  const { totals, daily_metrics } = data;

  // Get comparison data if available
  const comparisonData = comparisonResponse?.data;
  const comparisonTotals = comparisonData?.totals;
  const comparisonDailyMetrics = comparisonData?.daily_metrics;

  // Create card tabs for different metrics with comparison support
  const cardTabs = Object.entries(METRIC_NAMES).map(([key, displayName]) => {
    const currentValue = totals[key as keyof typeof totals] as number;
    const comparisonValue = comparisonTotals?.[
      key as keyof typeof comparisonTotals
    ] as number;

    // Calculate growth percentage if comparison data is available and comparison is enabled
    let changeValue = "";
    if (comparisonResponse && comparisonValue && comparisonValue > 0) {
      const growth = ((currentValue - comparisonValue) / comparisonValue) * 100;
      const sign = growth >= 0 ? "+" : "";
      changeValue = `${sign}${growth.toFixed(1)}%`;
    }

    return {
      title: displayName,
      value: abbreviateNumber(currentValue),
      changeValue,
    };
  });

  // Transform daily metrics to line chart format with comparison support
  // Attach primaryDate (YYYYMMDD) and computed comparisonDate (YYYYMMDD)
  const lineChartData = (() => {
    // Build comparison lookup keyed by YYYYMMDD
    const comparisonLookup = new Map<string, DailyMetric>();
    (comparisonDailyMetrics || []).forEach((m) => {
      comparisonLookup.set(normalizeYmd(m.date), m);
    });

    // Determine comparison start in YYYYMMDD for index-based progression
    const comparisonStartYmd = comparisonData
      ? normalizeYmd(comparisonData.period.start_date)
      : null;

    return daily_metrics.map((metric: DailyMetric, index: number) => {
      const name = formatDate(metric.date);
      const primaryYmd = normalizeYmd(metric.date);
      const comparisonYmd = comparisonStartYmd
        ? addDaysToYyyymmdd(comparisonStartYmd, index)
        : undefined;
      const compMetric = comparisonYmd
        ? comparisonLookup.get(comparisonYmd)
        : undefined;

      const chartData: Record<string, string | number | null> = {
        name,
        primaryDate: primaryYmd,
        ...(comparisonYmd ? { comparisonDate: comparisonYmd } : {}),
      };

      // Primary series value
      if (selectedMetric in metric) {
        chartData[selectedMetric] = metric[
          selectedMetric as keyof DailyMetric
        ] as number;
      }

      // Comparison series value - always include key to ensure consistent keys across points
      chartData[`comparison_${selectedMetric}`] =
        compMetric && selectedMetric in compMetric
          ? (compMetric[selectedMetric as keyof DailyMetric] as number) ?? 0
          : null;

      return chartData as Record<string, string | number>;
    });
  })();

  // Create color configuration for primary and comparison data
  const colors = [
    {
      name: selectedMetric,
      color: PRIMARY_COLOR, // Purple for primary data
    },
  ];

  // Selected lines for primary data
  const selectedLines = [selectedMetric];

  // Add comparison colors and lines if comparison data exists
  if (comparisonDailyMetrics) {
    colors.push({
      name: `comparison_${selectedMetric}`,
      color: COMPARISON_COLOR, // Yellow for comparison data
    });
    selectedLines.push(`comparison_${selectedMetric}`);
  }

  // Cards data for tooltip with comparison support
  const cardsData: Record<string, { amount: number; growth: string }> = {
    [selectedMetric]: {
      amount: totals[selectedMetric as keyof typeof totals] as number,
      growth:
        comparisonResponse && comparisonTotals
          ? (() => {
              const currentValue = totals[
                selectedMetric as keyof typeof totals
              ] as number;
              const comparisonValue = comparisonTotals[
                selectedMetric as keyof typeof comparisonTotals
              ] as number;
              if (comparisonValue && comparisonValue > 0) {
                const growth =
                  ((currentValue - comparisonValue) / comparisonValue) * 100;
                const sign = growth >= 0 ? "+" : "";
                return `${sign}${growth.toFixed(1)}%`;
              }
              return "";
            })()
          : "",
    },
  };

  // Add comparison data to cards if available
  if (comparisonResponse && comparisonTotals) {
    cardsData[`comparison_${selectedMetric}`] = {
      amount: comparisonTotals[
        selectedMetric as keyof typeof comparisonTotals
      ] as number,
      growth: "", // Comparison data doesn't show growth relative to itself
    };
  }

  // Progress bar data - traffic sources breakdown
  const progressbarData = getProgressBarData(totals);

  // Get hovered data if hoveredDate is provided
  const hoveredDayData = hoveredDate
    ? daily_metrics.find((m) => formatDate(m.date) === hoveredDate)
    : null;

  // For comparison data, match by index instead of date for better alignment
  const hoveredDayIndex = hoveredDate
    ? daily_metrics.findIndex((m) => formatDate(m.date) === hoveredDate)
    : -1;

  const hoveredComparisonData =
    hoveredDayIndex >= 0 &&
    comparisonDailyMetrics &&
    comparisonDailyMetrics[hoveredDayIndex]
      ? comparisonDailyMetrics[hoveredDayIndex]
      : null;

  // Add comparison data to progress bars
  const progressbarDataWithComparison = comparisonTotals
    ? mergeProgressBarDataWithComparison(
        progressbarData,
        getProgressBarData(comparisonTotals)
      )
    : progressbarData;

  return {
    cardTabs,
    lineChartData,
    colors,
    selectedLines,
    cardsData,
    progressbarData: progressbarDataWithComparison,
    ...(hoveredDate &&
      hoveredDayData && {
        hoveredData: {
          date: hoveredDate,
          progressbarData: hoveredComparisonData
            ? mergeProgressBarDataWithComparison(
                getProgressBarData(hoveredDayData),
                getProgressBarData(hoveredComparisonData)
              )
            : getProgressBarData(hoveredDayData),
          ...(hoveredComparisonData && {
            comparisonProgressbarData: getProgressBarData(
              hoveredComparisonData
            ),
          }),
        },
      }),
  };
};

// Color configuration for search engines (organic traffic)
const SEARCH_ENGINE_COLORS = {
  google: "#4285F4",
  yahoo: "#7B0099",
  yandex: "#FF0000",
  bing: "#00BCF2",
};

// Color configuration for social platforms (social traffic)
const SOCIAL_PLATFORM_COLORS = {
  instagram: "#E4405F",
  facebook: "#1877F2",
  youtube: "#FF0000",
  linkedin: "#0A66C2",
};

// Color configuration for paid traffic sources
const PAID_TRAFFIC_COLORS = {
  google_ads: "#4285F4",
  facebook_ads: "#1877F2",
  microsoft_ads: "#00BCF2",
  linkedin_ads: "#0A66C2",
  display_ads: "#FF6B35",
  youtube_ads: "#FF0000",
  other_paid: "#6B7280",
};

/**
 * Get progress bar data for traffic sources
 */
const getProgressBarData = (data: TrafficTotals | DailyMetric) => {
  // Check for paid traffic sources
  const paidTrafficKeys = [
    "google_ads",
    "facebook_ads",
    "microsoft_ads",
    "linkedin_ads",
    "display_ads",
    "youtube_ads",
    "other_paid",
  ];
  const hasPaidTrafficData = paidTrafficKeys.some(
    (key) => (data as any)[key] !== undefined
  );

  // If we have paid traffic data, show paid traffic breakdown
  if (hasPaidTrafficData) {
    return paidTrafficKeys
      .map((key) => {
        const paidTrafficData = (data as any)[key] as any;
        // Convert snake_case to readable format
        const title = key
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");

        return {
          title,
          percentage: paidTrafficData?.percentage || 0,
          value: paidTrafficData?.value || 0,
          color: PAID_TRAFFIC_COLORS[key as keyof typeof PAID_TRAFFIC_COLORS],
        };
      })
      .sort((a, b) => b.percentage - a.percentage);
  }

  // Check for search engine data (organic traffic)
  const searchEngineKeys = ["google", "yahoo", "yandex", "bing"];
  const hasSearchEngineData = searchEngineKeys.some(
    (key) => (data as any)[key] && (data as any)[key].value > 0
  );

  // If we have search engine data, show search engine breakdown
  if (hasSearchEngineData) {
    return searchEngineKeys
      .map((key) => {
        const searchEngineData = (data as any)[key] as any;
        return {
          title: key.charAt(0).toUpperCase() + key.slice(1), // Capitalize first letter
          percentage: searchEngineData?.percentage || 0,
          value: searchEngineData?.value || 0,
          color: SEARCH_ENGINE_COLORS[key as keyof typeof SEARCH_ENGINE_COLORS],
        };
      })
      .filter((item) => item.percentage > 0) // Only show search engines with traffic
      .sort((a, b) => b.percentage - a.percentage);
  }

  // Check for social platform data (social traffic)
  const socialPlatformKeys = ["instagram", "facebook", "youtube", "linkedin"];
  const hasSocialPlatformData = socialPlatformKeys.some(
    (key) => (data as any)[key] && (data as any)[key].value > 0
  );

  if (hasSocialPlatformData) {
    return socialPlatformKeys
      .map((key) => {
        const platformData = (data as any)[key] as any;
        return {
          title: key.charAt(0).toUpperCase() + key.slice(1),
          percentage: platformData?.percentage || 0,
          value: platformData?.value || 0,
          color:
            SOCIAL_PLATFORM_COLORS[key as keyof typeof SOCIAL_PLATFORM_COLORS],
        };
      })
      .filter((item) => item.percentage > 0)
      .sort((a, b) => b.percentage - a.percentage);
  }

  // Check for individual referral sources (referral_1, referral_2, etc.)
  const referralKeys = Object.keys(data).filter(
    (key) => key.startsWith("referral_") && key !== "referral"
  );

  // If we have individual referral sources, show only those
  if (referralKeys.length > 0) {
    // Handle referral traffic with referral_1, referral_2, etc.
    // Use different colors for each referral source
    const referralColors = [
      "#3B82F6", // Blue
      "#10B981", // Green
      "#F59E0B", // Orange
      "#EF4444", // Red
      "#8B5CF6", // Purple
      "#EC4899", // Pink
      "#6B7280", // Gray
    ];

    return referralKeys
      .map((key, index) => {
        const referralData = (data as any)[key] as any;
        // Use the URL as title if available, otherwise fallback to generic name
        const title =
          referralData?.url || `Referral ${key.replace("referral_", "")}`;

        return {
          title,
          percentage: referralData?.percentage || 0,
          value: referralData?.value || 0,
          color: referralColors[index % referralColors.length], // Cycle through colors
        };
      })
      .filter((item) => item.percentage > 0) // Only show referrals with traffic > 0
      .sort((a, b) => b.percentage - a.percentage);
  }

  // Fallback: Default traffic sources for other traffic types (when no individual referrals or search engines)
  const trafficSources = [
    {
      key: "organic",
      title: "Organic Traffic",
      color: TRAFFIC_SOURCE_COLORS.organic,
    },
    { key: "paid", title: "Paid Traffic", color: TRAFFIC_SOURCE_COLORS.paid },
    {
      key: "referral",
      title: "Referral Traffic",
      color: TRAFFIC_SOURCE_COLORS.referral,
    },
    {
      key: "social",
      title: "Social Traffic",
      color: TRAFFIC_SOURCE_COLORS.social,
    },
    {
      key: "direct",
      title: "Direct Traffic",
      color: TRAFFIC_SOURCE_COLORS.direct,
    },
    {
      key: "unassigned",
      title: "Unassigned Traffic",
      color: TRAFFIC_SOURCE_COLORS.unassigned,
    },
    { key: "mail", title: "Email Traffic", color: TRAFFIC_SOURCE_COLORS.mail },
  ];

  return trafficSources
    .map(({ key, title, color }) => ({
      title,
      percentage: (data as any)[key]?.percentage || 0,
      value: (data as unknown)[key]?.value || 0,
      color,
    }))
    .filter((item) => item.percentage > 0)
    .sort((a, b) => b.percentage - a.percentage);
};

/**
 * Format date from YYYYMMDD to readable format
 */
const formatDate = (dateString: string): string => {
  if (dateString.length !== 8) return dateString;

  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);

  const date = new Date(`${year}-${month}-${day}`);
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Get traffic source endpoint based on filter
 */
export const getTrafficSourceEndpoint = (filter: string): string => {
  const filterMap: Record<string, string> = {
    "Total Traffics ": "total",
    "Paid Traffics": "paid",
    "Referral Traffics": "referral",
    "Social Traffics": "social",
    "Organic Traffics": "organic",
  };

  return filterMap[filter] || "total";
};

/**
 * Get metric key from display name
 */
export const getMetricKeyFromDisplayName = (displayName: string): string => {
  const reverseMap: Record<string, string> = {};
  Object.entries(METRIC_NAMES).forEach(([key, name]) => {
    reverseMap[name] = key;
  });

  return reverseMap[displayName] || "all_users";
};

/**
 * Merge progress bar data with comparison data
 */
const mergeProgressBarDataWithComparison = (
  primaryData: Array<{
    title: string;
    percentage: number;
    value?: number;
    color?: string;
  }>,
  comparisonData: Array<{
    title: string;
    percentage: number;
    value?: number;
    color?: string;
  }>
) => {
  return primaryData.map((primaryItem) => {
    // Find matching comparison item by title
    const comparisonItem = comparisonData.find(
      (comp) => comp.title === primaryItem.title
    );

    return {
      ...primaryItem,
      comparisonPercentage: comparisonItem?.percentage,
      comparisonValue: comparisonItem?.value,
      showComparison: !!comparisonItem,
    };
  });
};
