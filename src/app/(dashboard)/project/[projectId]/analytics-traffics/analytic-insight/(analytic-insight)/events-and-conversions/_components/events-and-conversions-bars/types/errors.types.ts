// Error types for better error handling
export class AnalyticsApiError extends Error {
  constructor(
    message: string,
    public readonly statusCode?: number,
    public readonly endpoint?: string
  ) {
    super(message);
    this.name = "AnalyticsApiError";
  }
}

export class ProjectIdError extends Error {
  constructor(message: string = "Project ID is required") {
    super(message);
    this.name = "ProjectIdError";
  }
}

export class DateRangeError extends Error {
  constructor(message: string = "Date range is required") {
    super(message);
    this.name = "DateRangeError";
  }
}

export class TransformationError extends Error {
  constructor(message: string, public readonly data?: unknown) {
    super(message);
    this.name = "TransformationError";
  }
}

// Error type union for better type safety
export type AnalyticsError = 
  | AnalyticsApiError 
  | ProjectIdError 
  | DateRangeError 
  | TransformationError;

// Error handler utility
export const handleAnalyticsError = (error: unknown): AnalyticsError => {
  if (error instanceof AnalyticsApiError || 
      error instanceof ProjectIdError || 
      error instanceof DateRangeError || 
      error instanceof TransformationError) {
    return error;
  }

  if (error instanceof Error) {
    return new AnalyticsApiError(error.message);
  }

  return new AnalyticsApiError("An unknown error occurred");
};
