import React, { useState } from "react";

/* =============================== COMPONENTS =============================== */
import ChartAndBars from "./_components/chart-and-bars/ChartAndBars";
import TableSection from "./_components/user-demography/UserDemography";

const AboutUsers = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const domesticInfoFilters = ["Demographic info ", "Tech info "] as const;
  const [activeDomesticFilter, setActiveDomesticFilter] = useState<string>(
    domesticInfoFilters[0]
  );

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="space-y-6">
      <ChartAndBars
        activeDomesticFilter={activeDomesticFilter}
        onChangeDomesticFilter={setActiveDomesticFilter}
      />
      <TableSection activeDomesticFilter={activeDomesticFilter} />
    </div>
  );
};

export default AboutUsers;
