"use client";
import React, { useRef, useState, useEffect } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "../../../../../_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
// DateRange not needed here
// import DateRange from "../../../../../_components/date-range/DateRange";
// import useUserDemography from "./UserDemography.hooks";
import useCountriesData from "./useCountriesData";
import useCitiesData from "./useCitiesData";
import useGenderData from "./useGenderData";
import useLanguageData from "./useLanguageData";
import useAgeData from "./useAgeData";
import useTechDeviceTableData from "./useTechDeviceTableData";
import useTechOSTableData from "./useTechOSTableData";
import useTechAppVersionTableData from "./useTechAppVersionTableData";
import useTechBrowserTableData from "./useTechBrowserTableData";
import NoData from "../../../../_components/NoData";
import Title from "@/components/ui/Title";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
type TableSectionProps = {
  activeDomesticFilter: string; // "Demographic info " | "Tech info "
};

const TableSection = ({ activeDomesticFilter }: TableSectionProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);
  const { projectName } = useProjectContext();

  // Local state for table section filter management
  const demographicFilters = [
    "Countries",
    "Cities",
    "Gender",
    "Language",
    "Age",
  ];
  const techFilters = ["Device", "OS", "App version", "Browser"];
  const [filterBy, setFilterBy] = useState(demographicFilters[0]);

  // Determine if we're in tech mode
  const isTechMode = activeDomesticFilter.trim() === "Tech info";
  const currentFilters = isTechMode ? techFilters : demographicFilters;

  // Reset page and filter when switching modes
  useEffect(() => {
    setPage(1);
    if (isTechMode) {
      setFilterBy(techFilters[0]);
    } else {
      setFilterBy(demographicFilters[0]);
    }
  }, [isTechMode]);

  // Reset page to 1 when filter changes
  useEffect(() => {
    setPage(1);
  }, [filterBy]);

  // Demographic data hooks
  const {
    data: countriesData,
    isLoading: countriesLoading,
    isError: countriesError,
  } = useCountriesData({
    page,
  });

  const {
    data: citiesData,
    isLoading: citiesLoading,
    isError: citiesError,
  } = useCitiesData({
    page,
  });

  const {
    data: genderData,
    isLoading: genderLoading,
    isError: genderError,
  } = useGenderData({
    page,
  });

  const {
    data: languageData,
    isLoading: languageLoading,
    isError: languageError,
  } = useLanguageData({
    page,
  });

  const {
    data: ageData,
    isLoading: ageLoading,
    isError: ageError,
  } = useAgeData({
    page,
  });

  // Tech data hooks
  const {
    data: techDeviceData,
    isLoading: techDeviceLoading,
    isError: techDeviceError,
  } = useTechDeviceTableData({
    page,
  });

  const {
    data: techOSData,
    isLoading: techOSLoading,
    isError: techOSError,
  } = useTechOSTableData({
    page,
  });

  const {
    data: techAppVersionData,
    isLoading: techAppVersionLoading,
    isError: techAppVersionError,
  } = useTechAppVersionTableData({
    page,
  });

  const {
    data: techBrowserData,
    isLoading: techBrowserLoading,
    isError: techBrowserError,
  } = useTechBrowserTableData({
    page,
  });

  // Determine which data to use based on the filter and mode
  const getDataByFilter = () => {
    if (isTechMode) {
      switch (filterBy) {
        case "Device":
          return {
            data: techDeviceData,
            isLoading: techDeviceLoading,
            isError: techDeviceError,
          };
        case "OS":
          return {
            data: techOSData,
            isLoading: techOSLoading,
            isError: techOSError,
          };
        case "App version":
          return {
            data: techAppVersionData,
            isLoading: techAppVersionLoading,
            isError: techAppVersionError,
          };
        case "Browser":
          return {
            data: techBrowserData,
            isLoading: techBrowserLoading,
            isError: techBrowserError,
          };
        default:
          return {
            data: techDeviceData,
            isLoading: techDeviceLoading,
            isError: techDeviceError,
          };
      }
    } else {
      switch (filterBy) {
        case "Countries":
          return {
            data: countriesData,
            isLoading: countriesLoading,
            isError: countriesError,
          };
        case "Cities":
          return {
            data: citiesData,
            isLoading: citiesLoading,
            isError: citiesError,
          };
        case "Gender":
          return {
            data: genderData,
            isLoading: genderLoading,
            isError: genderError,
          };
        case "Language":
          return {
            data: languageData,
            isLoading: languageLoading,
            isError: languageError,
          };
        case "Age":
          return {
            data: ageData,
            isLoading: ageLoading,
            isError: ageError,
          };
        default:
          return {
            data: countriesData,
            isLoading: countriesLoading,
            isError: countriesError,
          };
      }
    }
  };

  const { data, isLoading, isError } = getDataByFilter();

  const lastDataRef = useRef(data);

  if (data) {
    lastDataRef.current = data;
  }

  const stableData = data ?? lastDataRef.current;

  // Get the appropriate title based on mode
  const getTableTitle = () => {
    if (isTechMode) {
      return "All Reports-Tech Info";
    } else {
      return "All Reports-User's Demography";
    }
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return isError ? (
    <motion.div
      key="error"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <NoData title={projectName + " " + getTableTitle()} />
    </motion.div>
  ) : data?.tableData || isLoading ? (
    <Card className="w-full space-y-4 min-h-[520px] flex flex-col justify-between">
      <div className="space-y-2">
        {/* Header without dropdown (controlled by ChartAndBars) */}
        <div className="flex w-full justify-between items-center">
          <Title>
            {projectName} {getTableTitle()}
          </Title>
        </div>
      </div>

      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="flex-1"
      >
        <DataTable
          title=""
          tableData={data?.tableData}
          isLoading={isLoading}
          badges={currentFilters}
          selectedItem={filterBy}
          setSelectedItem={setFilterBy}
          currentPage={page}
          firstColumnWidthPercent={15}
        />
      </motion.div>
      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={page}
        onPageChange={setPage}
      />
    </Card>
  ) : (
    <motion.div
      key="nodata"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <NoData title={getTableTitle()} />
    </motion.div>
  );
};

export default TableSection;
