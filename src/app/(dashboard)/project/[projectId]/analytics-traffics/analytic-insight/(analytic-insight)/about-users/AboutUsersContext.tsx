"use client";
import React, { createContext, useContext, useState, ReactNode } from "react";

// Types for the context
interface AboutUsersContextType {
  // Mode selection (Demographic info vs Tech info)
  activeDomesticFilter: string;
  setActiveDomesticFilter: (filter: string) => void;
  
  // Filter selection within each mode
  activeTabFilter: string;
  setActiveTabFilter: (filter: string) => void;
  
  // Available filters for current mode
  currentFilters: string[];
  
  // Helper to determine if we're in tech mode
  isTechMode: boolean;
}

// Create the context
const AboutUsersContext = createContext<AboutUsersContextType | undefined>(undefined);

// Provider component
interface AboutUsersProviderProps {
  children: ReactNode;
}

export const AboutUsersProvider: React.FC<AboutUsersProviderProps> = ({ children }) => {
  const demographicFilters = ["Age", "Countries", "Cities", "Language", "Gender"];
  const techFilters = ["Device", "OS", "App version", "Browser"];
  const domesticInfoFilters = ["Demographic info ", "Tech info "];
  
  const [activeDomesticFilter, setActiveDomesticFilter] = useState(domesticInfoFilters[0]);
  const [activeTabFilter, setActiveTabFilter] = useState(demographicFilters[0]);
  
  // Determine if we're in tech mode
  const isTechMode = activeDomesticFilter.trim() === "Tech info";
  const currentFilters = isTechMode ? techFilters : demographicFilters;
  
  // When switching modes, reset the filter to the first option of the new mode
  const handleModeChange = (filter: string) => {
    setActiveDomesticFilter(filter);
    const newIsTechMode = filter.trim() === "Tech info";
    const newFilters = newIsTechMode ? techFilters : demographicFilters;
    setActiveTabFilter(newFilters[0]);
  };

  const value: AboutUsersContextType = {
    activeDomesticFilter,
    setActiveDomesticFilter: handleModeChange,
    activeTabFilter,
    setActiveTabFilter,
    currentFilters,
    isTechMode,
  };

  return (
    <AboutUsersContext.Provider value={value}>
      {children}
    </AboutUsersContext.Provider>
  );
};

// Custom hook to use the context
export const useAboutUsers = (): AboutUsersContextType => {
  const context = useContext(AboutUsersContext);
  if (context === undefined) {
    throw new Error("useAboutUsers must be used within an AboutUsersProvider");
  }
  return context;
};