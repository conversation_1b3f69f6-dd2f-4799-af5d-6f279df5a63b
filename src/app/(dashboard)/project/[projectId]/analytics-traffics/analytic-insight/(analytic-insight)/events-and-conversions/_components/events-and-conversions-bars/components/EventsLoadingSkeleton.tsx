"use client";
import React from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import HorizontalBar from "@/components/ui/horizontal-bar/HorizontalBar";
import Dropdown from "@/components/ui/Dropdown";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ========================================================================== */
const EventsLoadingSkeleton: React.FC = () => {
  return (
    <Card className="space-y-4">
      {/* Header Section */}
      <div className="space-y-2">
        <div className="flex w-full justify-between items-center">
          <Title>Events & Conversions</Title>
          <div className="w-24 h-8 bg-secondary/20 rounded animate-pulse" />
        </div>
        <DateRange />
      </div>

      {/* Card Tabs Loading */}
      <motion.div
        className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        {Array.from({ length: 7 }).map((_, i) => (
          <CardTab key={i} isLoading />
        ))}
      </motion.div>

      {/* Bars Chart Loading */}
      <div className="m-8 mb-5 px-1 py-6 border-l border-b border-secondary/30 space-y-2 min-h-[435px]">
        {Array.from({ length: 7 }).map((_, i) => (
          <HorizontalBar isLoading key={i} />
        ))}
      </div>
    </Card>
  );
};

export default EventsLoadingSkeleton;
