"use client";
import React, { useRef, useState, useEffect } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "../../../../../_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import ErrorState from "./ErrorState";
import usePagesInsightData from "./usePagesInsightData";
import { PageType } from "./pagesInsightService";
import { useProjectId } from "@/hooks/useProjectId";
import { useProjectDetails } from "@/hooks/useProjectDetails";
import type { TableData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
const PagesInsightTable = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);
  const { projectName } = useProjectContext();

  // Page type filters with user-friendly labels
  const pageTypeFilters: Array<{ value: PageType; label: string }> = [
    { value: "all-pages", label: "All Pages" },
    { value: "high-traffic", label: "High Traffic Pages" },
    { value: "landing-pages", label: "Landing Pages" },
  ];

  const [selectedPageType, setSelectedPageType] = useState<PageType>(
    pageTypeFilters[0].value
  );

  // Reset page to 1 when filter changes
  useEffect(() => {
    setPage(1);
  }, [selectedPageType]);

  const { data, isLoading, error } = usePagesInsightData({
    page,
    pageType: selectedPageType,
  });

  // Get project base URL for linking page paths
  const { projectId } = useProjectId();
  const { project, currentProject } = useProjectDetails({
    projectId,
    enabled: !!projectId,
  });
  const pageLinkBaseUrl = (project?.url || currentProject?.url) ?? undefined;

  const lastDataRef = useRef(data);

  if (data && data.tableData.tableBody.length > 0) {
    lastDataRef.current = data;
  }

  const stableData = data ?? lastDataRef.current;

  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */

  const handleRetry = () => {
    // Force refetch by changing a dependency
    setPage(1);
    // You could also use queryClient.invalidateQueries here if needed
  };

  const handlePageTypeChange = (newPageType: string) => {
    setSelectedPageType(newPageType as PageType);
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Show error state for primary data errors
  if (error.hasError && error.type === "primary" && !isLoading) {
    return (
      <Card className="w-full space-y-4 min-h-[520px] flex flex-col justify-between">
        <div className="flex w-full justify-between items-center">
          <Title>{projectName} Pages Performance</Title>
        </div>
        <div className="flex-1">
          <ErrorState
            message={error.message || "Failed to load page data"}
            onRetry={handleRetry}
            type={error.type}
          />
        </div>
      </Card>
    );
  }

  return (
    <Card className="w-full space-y-4 min-h-[520px] flex flex-col justify-between">
      {/* Header with title */}
      <div className="flex w-full justify-between items-center">
        <Title>{projectName} Pages Performance</Title>
      </div>

      {/* Show comparison error as a warning banner if main data is available */}
      {error.hasError && error.type === "comparison" && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center">
            <div className="text-yellow-600 mr-2">⚠️</div>
            <p className="text-sm text-yellow-800">
              Comparison data is unavailable, but current period data is shown
              below.
            </p>
          </div>
        </div>
      )}

      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="flex-1"
      >
        <DataTable
          title=""
          tableData={data?.tableData as TableData}
          isLoading={isLoading}
          badges={pageTypeFilters.map((filter) => filter.label)}
          selectedItem={
            pageTypeFilters.find((filter) => filter.value === selectedPageType)
              ?.label || pageTypeFilters[0].label
          }
          setSelectedItem={(next) => {
            const currentLabel =
              pageTypeFilters.find((f) => f.value === selectedPageType)
                ?.label || pageTypeFilters[0].label;
            const nextLabel =
              typeof next === "function" ? next(currentLabel) : next;
            const filter = pageTypeFilters.find((f) => f.label === nextLabel);
            if (filter) handlePageTypeChange(filter.value);
          }}
          currentPage={page}
          pageLinkBaseUrl={pageLinkBaseUrl}
          openLinksInNewTab
        />
      </motion.div>

      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={page}
        onPageChange={setPage}
      />
    </Card>
  );
};

export default PagesInsightTable;
