"use client";
import React from "react";
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import LineChartCard from "../../../../overview/_components/LineChartCard";
import SmallChartSkeleton from "../../../../_components/small-chart-skeleton/SmallChartSkeleton";

/* ================================== TYPES ================================= */
import type { AudienceResponse, LineChartCards } from "../Audience.types";

/* ================================= ZUSTAND ================================ */
import {
  useLineChartDataStore,
  useChartPopupStore,
} from "@/store/useChartPopupStore";

/* ========================================================================== */
interface AudienceSmallChartsProps {
  data: AudienceResponse | undefined;
  isLoading: boolean;
}

/* ========================================================================== */
const AudienceSmallCharts: React.FC<AudienceSmallChartsProps> = ({
  data,
  isLoading,
}) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const setChartData = useLineChartDataStore((setData) => setData.setChartData);
  const { show } = useChartPopupStore();

  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */
  const handleSetChartData = (chart: LineChartCards) => {
    const transformedData = chart.data.map((item) => ({
      name: item.name,
      primaryDate: (item as any).primaryDate,
      comparisonDate: (item as any).comparisonDate,
      value: item.value,
      comparisonValue: (item as any).comparisonValue,
    }));

    const hasComparison = transformedData.some(
      (item) => item.comparisonValue !== undefined
    );

    setChartData({
      title: chart.title,
      bigNumber: chart.bigNumber,
      smallNumber: chart.smallNumber,
      data: transformedData,
      hasComparison: hasComparison,
    });
    show();
  };

  /* ========================================================================== */
  /*                                UTILITIES                                   */
  /* ========================================================================== */
  const calculateAverage = (bigNumber: string): string => {
    const numericValue = parseFloat(bigNumber.replace(/[^\d.-]/g, ""));
    if (isNaN(numericValue)) return bigNumber;

    const average = numericValue / 30;
    return bigNumber.includes("%")
      ? `${average.toFixed(2)}%`
      : average.toFixed(2);
  };

  /* ========================================================================== */
  /*                              CHART COMPONENTS                             */
  /* ========================================================================== */
  const FirstChart = () => {
    const chartData = data?.lineChartCards[0];
    if (!chartData) return null;

    // Ensure data is always transformed to include comparisonValue
    const transformedData = chartData.data.map((item) => ({
      name: item.name,
      primaryDate: (item as any).primaryDate,
      comparisonDate: (item as any).comparisonDate,
      value: item.value,
      comparisonValue: (item as any).comparisonValue,
    }));

    return (
      <motion.div
        className="w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <LineChartCard
          data={transformedData}
          bigNumber={chartData.bigNumber}
          smallNumber={chartData.smallNumber}
          title={chartData.title}
          className="w-full cursor-pointer"
          onClick={() =>
            handleSetChartData({ ...chartData, data: transformedData })
          }
        />
      </motion.div>
    );
  };

  /* ========================================================================== */
  /*                            ACTIVE USER CALCULATION                           */
  /* ========================================================================== */
  // This function is no longer needed as the active_users_rate is now calculated in the transformAudienceData function
  // Keeping a simplified version for backward compatibility
  const calculateActiveUserRate = (): {
    bigNumber: string;
    data: any[];
  } | null => {
    const activeUserChartData = data?.lineChartCards[1];

    if (!activeUserChartData) return null;

    return {
      bigNumber: activeUserChartData.bigNumber,
      data: activeUserChartData.data,
    };
  };

  const SecondChart = () => {
    const chartData = data?.lineChartCards[1];
    if (!chartData) return null;

    // Ensure data is always transformed to include comparisonValue
    const transformedData = chartData.data.map((item) => ({
      name: item.name,
      primaryDate: (item as any).primaryDate,
      comparisonDate: (item as any).comparisonDate,
      value: item.value,
      comparisonValue: (item as any).comparisonValue,
    }));

    return (
      <motion.div
        className="w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <LineChartCard
          data={transformedData}
          bigNumber={chartData.bigNumber}
          smallNumber={chartData.smallNumber}
          title={chartData.title}
          className="w-full cursor-pointer"
          onClick={() =>
            handleSetChartData({ ...chartData, data: transformedData })
          }
        />
      </motion.div>
    );
  };

  const ThirdChart = () => {
    const chartData = data?.lineChartCards[2];
    if (!chartData) return null;

    // Ensure data is always transformed to include comparisonValue
    const transformedData = chartData.data.map((item) => ({
      name: item.name,
      primaryDate: (item as any).primaryDate,
      comparisonDate: (item as any).comparisonDate,
      value: item.value,
      comparisonValue: (item as any).comparisonValue,
    }));

    const averagedBigNumber = calculateAverage(chartData.bigNumber);

    return (
      <motion.div
        className="w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <LineChartCard
          data={transformedData}
          bigNumber={averagedBigNumber}
          smallNumber={chartData.smallNumber}
          title={chartData.title}
          className="w-full cursor-pointer"
          onClick={() =>
            handleSetChartData({
              ...chartData,
              bigNumber: averagedBigNumber,
              data: transformedData,
            })
          }
        />
      </motion.div>
    );
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="flex flex-col lg:flex-row w-full mt-8 lg:gap-2 px-8 min-h-[170px]">
      {isLoading ? (
        <>
          <motion.div
            className="w-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <SmallChartSkeleton className="w-full" />
          </motion.div>
          <motion.div
            className="w-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <SmallChartSkeleton className="w-full" />
          </motion.div>
          <motion.div
            className="w-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <SmallChartSkeleton className="w-full" />
          </motion.div>
        </>
      ) : (
        <>
          <FirstChart />
          <SecondChart />
          <ThirdChart />
        </>
      )}
    </div>
  );
};

export default AudienceSmallCharts;
