import type { EventsData, ConversionsData } from "../EventsAndConversions.types";

// Color constants for chart visualization
export const CHART_COLORS = {
  CURRENT_PERIOD: "bg-[#914AC4]", // Purple for current period
  PREVIOUS_PERIOD: "bg-[#FFCD29]", // Yellow for previous period
} as const;

// Filter types
export const FILTER_TYPES = {
  EVENTS: "Events",
  CONVERSIONS: "Conversions",
} as const;

// Metric tab configurations
export const METRIC_TABS = [
  { key: "all-users", title: "All Users" },
  { key: "active-users", title: "Active Users" },
  { key: "new-users", title: "New Users" },
  { key: "returning-users", title: "Returning Users" },
  { key: "sessions", title: "Sessions" },
  { key: "engaged-sessions", title: "Engaged Sessions" },
  { key: "views", title: "Views" },
] as const;

// Metric mappings for Events
export const EVENTS_METRIC_MAPPING: Record<string, keyof EventsData> = {
  "All Users": "all-events",
  "Active Users": "active-users-events",
  "New Users": "new-users-events",
  "Returning Users": "returning-users-events",
  "Sessions": "sessions-events",
  "Engaged Sessions": "engaged-sessions-events",
  "Views": "views-events",
};

// Metric mappings for Conversions
export const CONVERSIONS_METRIC_MAPPING: Record<string, keyof ConversionsData> = {
  "All Users": "all-conversions",
  "Active Users": "active-users-conversions",
  "New Users": "new-users-conversions",
  "Returning Users": "returning-users-conversions",
  "Sessions": "sessions-conversions",
  "Engaged Sessions": "engaged-sessions-conversions",
  "Views": "views-conversions",
};

// API endpoints
export const API_ENDPOINTS = {
  EVENTS: (projectId: string) => 
    `/api/project/GA4/eventsandconversions/overview/events/${projectId}/`,
  CONVERSIONS: (projectId: string) => 
    `/api/project/GA4/eventsandconversions/overview/conversions/${projectId}/`,
} as const;

// Default values
export const DEFAULTS = {
  ACTIVE_TAB: "All Users",
  ACTIVE_FILTER: FILTER_TYPES.EVENTS,
  CHANGE_VALUE: "+0%",
  MIN_MAX_VALUE: 1, // Minimum value to avoid division by zero
} as const;

// Query configuration
export const QUERY_CONFIG = {
  STALE_TIME: 5 * 60 * 1000, // 5 minutes
  GC_TIME: 10 * 60 * 1000, // 10 minutes
} as const;
