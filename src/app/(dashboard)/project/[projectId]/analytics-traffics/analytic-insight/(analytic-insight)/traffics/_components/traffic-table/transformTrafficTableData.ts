import type {
  TrafficDemographicResponse,
  TrafficMetrics,
} from "./trafficDemographicService";
import abbreviateNumber from "@/utils/abbreviateNumber";

interface TableRow {
  source: string;
  sessions: string | string[];
  engaged_sessions: string | string[];
  new_users: string | string[];
  total_users: string | string[];
  views: string | string[];
  engaged_time: string | string[];
  event_count: string | string[];
  conversions: string | string[];
  engaged_rate: string | string[];
}

interface TransformedTableData {
  tableData: {
    tableHeadings: string[];
    tableBody: { value: string; growth?: string }[][];
  };
  pagination: {
    totalPages: number;
    currentPage: number;
    totalItems: number;
  };
}

const ITEMS_PER_PAGE = 5;

// Helper function to calculate growth percentage
const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const growth = ((current - previous) / previous) * 100;
  const sign = growth >= 0 ? "+" : "";
  return `${sign}${growth.toFixed(1)}%`;
};

// Helper function to format time in seconds to readable format
const formatEngagedTime = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}s`;
  }
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
};

// Helper function to convert TableRow to DataTable format
const convertToDataTableFormat = (
  rows: TableRow[]
): { value: string; growth?: string }[][] => {
  return rows.map((row) => {
    const cells: { value: string; growth?: string }[] = [];

    // Convert each field to the expected format
    const fields = [
      "source",
      "sessions",
      "engaged_sessions",
      "new_users",
      "total_users",
      "views",
      "engaged_time",
      "event_count",
      "conversions",
      "engaged_rate",
    ];

    fields.forEach((field) => {
      const fieldValue = row[field as keyof TableRow];

      if (Array.isArray(fieldValue)) {
        // With comparison data - use current value and growth
        cells.push({
          value: fieldValue[0] as string,
          growth: fieldValue[2] as string,
        });
      } else {
        // Without comparison data - just the value
        cells.push({
          value: fieldValue as string,
        });
      }
    });

    return cells;
  });
};

// Helper function to transform metrics to table row
const transformMetricsToRow = (
  source: string,
  metrics: TrafficMetrics,
  comparisonMetrics?: TrafficMetrics
): TableRow => {
  try {
    // Validate metrics object
    if (!metrics || typeof metrics !== "object") {
      console.warn(`Invalid metrics for source ${source}:`, metrics);
      // Return empty row with default values
      const emptyRow = {
        source,
        sessions: "0",
        engaged_sessions: "0",
        new_users: "0",
        total_users: "0",
        views: "0",
        engaged_time: "0s",
        event_count: "0",
        conversions: "0",
        engaged_rate: "0%",
      };
      return comparisonMetrics
        ? {
            ...emptyRow,
            sessions: ["0", "0", "0%"],
            engaged_sessions: ["0", "0", "0%"],
            new_users: ["0", "0", "0%"],
            total_users: ["0", "0", "0%"],
            views: ["0", "0", "0%"],
            engaged_time: ["0s", "0s", "0%"],
            event_count: ["0", "0", "0%"],
            conversions: ["0", "0", "0%"],
            engaged_rate: ["0%", "0%", "0%"],
          }
        : emptyRow;
    }

    // Ensure all required fields exist with defaults
    const safeMetrics = {
      sessions: metrics.sessions || 0,
      engaged_sessions: metrics.engaged_sessions || 0,
      new_users: metrics.new_users || 0,
      total_users: metrics.total_users || 0,
      views: metrics.views || 0,
      engaged_time: metrics.engaged_time || 0,
      event_count: metrics.event_count || 0,
      conversions: metrics.conversions || 0,
      engaged_rate: metrics.engaged_rate || 0,
    };

    if (comparisonMetrics) {
      // Ensure comparison metrics also have defaults
      const safeComparisonMetrics = {
        sessions: comparisonMetrics.sessions || 0,
        engaged_sessions: comparisonMetrics.engaged_sessions || 0,
        new_users: comparisonMetrics.new_users || 0,
        total_users: comparisonMetrics.total_users || 0,
        views: comparisonMetrics.views || 0,
        engaged_time: comparisonMetrics.engaged_time || 0,
        event_count: comparisonMetrics.event_count || 0,
        conversions: comparisonMetrics.conversions || 0,
        engaged_rate: comparisonMetrics.engaged_rate || 0,
      };

      // With comparison data - create array format for each metric
      return {
        source,
        sessions: [
          abbreviateNumber(safeMetrics.sessions),
          abbreviateNumber(safeComparisonMetrics.sessions),
          calculateGrowth(safeMetrics.sessions, safeComparisonMetrics.sessions),
        ],
        engaged_sessions: [
          abbreviateNumber(safeMetrics.engaged_sessions),
          abbreviateNumber(safeComparisonMetrics.engaged_sessions),
          calculateGrowth(
            safeMetrics.engaged_sessions,
            safeComparisonMetrics.engaged_sessions
          ),
        ],
        new_users: [
          abbreviateNumber(safeMetrics.new_users),
          abbreviateNumber(safeComparisonMetrics.new_users),
          calculateGrowth(
            safeMetrics.new_users,
            safeComparisonMetrics.new_users
          ),
        ],
        total_users: [
          abbreviateNumber(safeMetrics.total_users),
          abbreviateNumber(safeComparisonMetrics.total_users),
          calculateGrowth(
            safeMetrics.total_users,
            safeComparisonMetrics.total_users
          ),
        ],
        views: [
          abbreviateNumber(safeMetrics.views),
          abbreviateNumber(safeComparisonMetrics.views),
          calculateGrowth(safeMetrics.views, safeComparisonMetrics.views),
        ],
        engaged_time: [
          formatEngagedTime(safeMetrics.engaged_time),
          formatEngagedTime(safeComparisonMetrics.engaged_time),
          calculateGrowth(
            safeMetrics.engaged_time,
            safeComparisonMetrics.engaged_time
          ),
        ],
        event_count: [
          abbreviateNumber(safeMetrics.event_count),
          abbreviateNumber(safeComparisonMetrics.event_count),
          calculateGrowth(
            safeMetrics.event_count,
            safeComparisonMetrics.event_count
          ),
        ],
        conversions: [
          abbreviateNumber(safeMetrics.conversions),
          abbreviateNumber(safeComparisonMetrics.conversions),
          calculateGrowth(
            safeMetrics.conversions,
            safeComparisonMetrics.conversions
          ),
        ],
        engaged_rate: [
          `${safeMetrics.engaged_rate.toFixed(1)}%`,
          `${safeComparisonMetrics.engaged_rate.toFixed(1)}%`,
          calculateGrowth(
            safeMetrics.engaged_rate,
            safeComparisonMetrics.engaged_rate
          ),
        ],
      };
    } else {
      // Without comparison data - simple string format
      return {
        source,
        sessions: abbreviateNumber(safeMetrics.sessions),
        engaged_sessions: abbreviateNumber(safeMetrics.engaged_sessions),
        new_users: abbreviateNumber(safeMetrics.new_users),
        total_users: abbreviateNumber(safeMetrics.total_users),
        views: abbreviateNumber(safeMetrics.views),
        engaged_time: formatEngagedTime(safeMetrics.engaged_time),
        event_count: abbreviateNumber(safeMetrics.event_count),
        conversions: abbreviateNumber(safeMetrics.conversions),
        engaged_rate: `${safeMetrics.engaged_rate.toFixed(1)}%`,
      };
    }
  } catch (error) {
    console.error(`Error transforming metrics for source ${source}:`, error);

    // Return fallback row
    const fallbackRow = {
      source,
      sessions: "0",
      engaged_sessions: "0",
      new_users: "0",
      total_users: "0",
      views: "0",
      engaged_time: "0s",
      event_count: "0",
      conversions: "0",
      engaged_rate: "0%",
    };

    return comparisonMetrics
      ? {
          ...fallbackRow,
          sessions: ["0", "0", "0%"],
          engaged_sessions: ["0", "0", "0%"],
          new_users: ["0", "0", "0%"],
          total_users: ["0", "0", "0%"],
          views: ["0", "0", "0%"],
          engaged_time: ["0s", "0s", "0%"],
          event_count: ["0", "0", "0%"],
          conversions: ["0", "0", "0%"],
          engaged_rate: ["0%", "0%", "0%"],
        }
      : fallbackRow;
  }
};

export const transformTrafficTableData = (
  apiResponse: TrafficDemographicResponse["data"],
  trafficSource: string,
  page: number,
  comparisonResponse?: TrafficDemographicResponse["data"] | null
): TransformedTableData => {
  try {
    console.log("🔍 Transform function called with:", {
      trafficSource,
      page,
      hasApiResponse: !!apiResponse,
      hasComparison: !!comparisonResponse,
      apiResponseKeys: apiResponse ? Object.keys(apiResponse) : [],
    });

    // Validate input parameters
    if (!apiResponse) {
      console.warn("API response is null or undefined");
      return {
        tableData: { tableHeadings: ["Source"], tableBody: [] },
        pagination: { totalPages: 1, currentPage: 1, totalItems: 0 },
      };
    }

    let rows: TableRow[] = [];
    const hasComparison = !!comparisonResponse;

    // Define headers - simplified for DataTable component
    const tableHeadings = [
      "Source",
      "Sessions",
      "Engaged Sessions",
      "New Users",
      "Total Users",
      "Views",
      "Engaged Time",
      "Event Count",
      "Conversions",
      "Engaged Rate",
    ];

    // Transform data based on selected traffic source with error handling
    try {
      switch (trafficSource) {
        case "Total Traffic":
          // Show all traffic sources from total_traffics
          const totalTrafficSources = [
            { key: "organic", name: "Organic" },
            { key: "referral", name: "Referral" },
            { key: "social", name: "Social" },
            { key: "direct", name: "Direct" },
            { key: "paid", name: "Paid" },
            { key: "mail", name: "Email" },
            { key: "unassigned", name: "Unassigned" },
          ];

          rows = totalTrafficSources
            .map(({ key, name }) => {
              try {
                const metrics =
                  apiResponse.total_traffics?.[
                    key as keyof typeof apiResponse.total_traffics
                  ];
                const comparisonMetrics =
                  comparisonResponse?.total_traffics?.[
                    key as keyof typeof comparisonResponse.total_traffics
                  ];

                if (!metrics) {
                  console.warn(`Missing metrics for ${key} in total_traffics`);
                  return null;
                }

                return transformMetricsToRow(name, metrics, comparisonMetrics);
              } catch (error) {
                console.warn(`Error processing ${key} traffic source:`, error);
                return null;
              }
            })
            .filter(Boolean) as TableRow[];
          break;

        case "Organic":
          // Show individual organic sources (google, bing, yahoo, etc.)
          console.log("🌱 Processing Organic traffic:", {
            hasOrganic: !!apiResponse.organic,
            organicType: typeof apiResponse.organic,
            organicKeys: apiResponse.organic
              ? Object.keys(apiResponse.organic)
              : [],
          });

          if (apiResponse.organic && typeof apiResponse.organic === "object") {
            const organicEntries = Object.entries(apiResponse.organic);
            console.log("🌱 Organic entries found:", organicEntries.length);

            if (organicEntries.length > 0) {
              rows = organicEntries
                .map(([source, metrics]) => {
                  try {
                    console.log(
                      `🌱 Processing organic source: ${source}`,
                      metrics
                    );
                    const comparisonMetrics =
                      comparisonResponse?.organic?.[source];
                    return transformMetricsToRow(
                      source.charAt(0).toUpperCase() + source.slice(1),
                      metrics,
                      comparisonMetrics
                    );
                  } catch (error) {
                    console.warn(
                      `Error processing organic source ${source}:`,
                      error
                    );
                    return null;
                  }
                })
                .filter(Boolean) as TableRow[];
              console.log("🌱 Organic rows created:", rows.length);
            } else {
              // No organic sources available, show the total organic data from total_traffics
              const organicTotalMetrics = apiResponse.total_traffics?.organic;
              const comparisonOrganicMetrics =
                comparisonResponse?.total_traffics?.organic;

              if (organicTotalMetrics) {
                rows = [
                  transformMetricsToRow(
                    "Organic Traffic",
                    organicTotalMetrics,
                    comparisonOrganicMetrics
                  ),
                ];
              }
            }
          }
          break;

        case "Referral":
          // Show individual referral sources
          if (
            apiResponse.referral &&
            typeof apiResponse.referral === "object"
          ) {
            const referralEntries = Object.entries(apiResponse.referral);

            if (referralEntries.length > 0) {
              rows = referralEntries
                .map(([source, metrics]) => {
                  try {
                    const comparisonMetrics =
                      comparisonResponse?.referral?.[source];
                    return transformMetricsToRow(
                      source,
                      metrics,
                      comparisonMetrics
                    );
                  } catch (error) {
                    console.warn(
                      `Error processing referral source ${source}:`,
                      error
                    );
                    return null;
                  }
                })
                .filter(Boolean) as TableRow[];
            } else {
              // No referral sources available, show the total referral data from total_traffics
              const referralTotalMetrics = apiResponse.total_traffics?.referral;
              const comparisonReferralMetrics =
                comparisonResponse?.total_traffics?.referral;

              if (referralTotalMetrics) {
                rows = [
                  transformMetricsToRow(
                    "Referral Traffic",
                    referralTotalMetrics,
                    comparisonReferralMetrics
                  ),
                ];
              }
            }
          }
          break;

        case "Social":
          // Show individual social sources
          if (apiResponse.social && typeof apiResponse.social === "object") {
            const socialEntries = Object.entries(apiResponse.social);

            if (socialEntries.length > 0) {
              rows = socialEntries
                .map(([source, metrics]) => {
                  try {
                    const comparisonMetrics =
                      comparisonResponse?.social?.[source];
                    return transformMetricsToRow(
                      source.charAt(0).toUpperCase() + source.slice(1),
                      metrics,
                      comparisonMetrics
                    );
                  } catch (error) {
                    console.warn(
                      `Error processing social source ${source}:`,
                      error
                    );
                    return null;
                  }
                })
                .filter(Boolean) as TableRow[];
            } else {
              // No social sources available, show the total social data from total_traffics
              const socialTotalMetrics = apiResponse.total_traffics?.social;
              const comparisonSocialMetrics =
                comparisonResponse?.total_traffics?.social;

              if (socialTotalMetrics) {
                rows = [
                  transformMetricsToRow(
                    "Social Traffic",
                    socialTotalMetrics,
                    comparisonSocialMetrics
                  ),
                ];
              }
            }
          }
          break;

        default:
          rows = [];
      }
    } catch (error) {
      console.error(`Error processing traffic source ${trafficSource}:`, error);
      rows = [];
    }

    // Sort by sessions (descending) with error handling
    try {
      rows.sort((a, b) => {
        try {
          const aSessionsValue = Array.isArray(a.sessions)
            ? a.sessions[0]
            : a.sessions;
          const bSessionsValue = Array.isArray(b.sessions)
            ? b.sessions[0]
            : b.sessions;
          const aValue =
            parseInt(String(aSessionsValue).replace(/[^0-9]/g, "")) || 0;
          const bValue =
            parseInt(String(bSessionsValue).replace(/[^0-9]/g, "")) || 0;
          return bValue - aValue;
        } catch (error) {
          console.warn("Error sorting individual rows:", error);
          return 0;
        }
      });
    } catch (error) {
      console.warn("Error sorting rows:", error);
    }

    // Pagination with error handling
    const totalItems = rows.length;
    const totalPages = Math.max(1, Math.ceil(totalItems / ITEMS_PER_PAGE));
    const startIndex = Math.max(0, (page - 1) * ITEMS_PER_PAGE);
    const endIndex = startIndex + ITEMS_PER_PAGE;
    const paginatedRows = rows.slice(startIndex, endIndex);

    // Convert to DataTable format
    const tableBody = convertToDataTableFormat(paginatedRows);

    console.log("✅ Transform result:", {
      trafficSource,
      totalRows: rows.length,
      paginatedRows: paginatedRows.length,
      tableBodyLength: tableBody.length,
      tableHeadings: tableHeadings.length,
      sampleRow: tableBody[0] || null,
    });

    return {
      tableData: {
        tableHeadings,
        tableBody,
      },
      pagination: {
        totalPages,
        currentPage: Math.max(1, Math.min(page, totalPages)),
        totalItems,
      },
    };
  } catch (error) {
    console.error("Error in transformTrafficTableData:", error);

    // Return empty table structure as fallback
    return {
      tableData: {
        tableHeadings: [
          "Source",
          "Sessions",
          "Engaged Sessions",
          "New Users",
          "Total Users",
          "Views",
          "Engaged Time",
          "Event Count",
          "Conversions",
          "Engaged Rate",
        ],
        tableBody: [],
      },
      pagination: {
        totalPages: 1,
        currentPage: 1,
        totalItems: 0,
      },
    };
  }
};
