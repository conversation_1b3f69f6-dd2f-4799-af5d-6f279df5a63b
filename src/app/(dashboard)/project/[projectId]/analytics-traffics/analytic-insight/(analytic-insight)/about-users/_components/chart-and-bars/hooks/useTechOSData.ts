import httpService from "@/services/httpService";
import { useQuery } from "@tanstack/react-query";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { ProgressbarData } from "../../../../../types/AnalyticsTraffics.types";

// Tech OS API Response Types
export interface TechOSData {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

export interface TechOSAPIResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: TechOSData[];
    daily_metrics: Array<{
      date: string;
      total_users: number;
      active_users: number;
      new_users: number;
      returning_users: number;
      sessions: number;
      engaged_sessions: number;
      views: number;
      avg_engagement_time: number;
      engagement_rate: number;
      event_count: number;
      conversions: number;
      dimensions: TechOSData[];
    }>;
  };
}

// Extended type to include comparison data
export interface TechOSDataResult {
  progressbarData: ProgressbarData[];
  comparisonProgressbarData?: ProgressbarData[];
}

/**
 * Check if Tech OS API response is valid
 * @param response - The Tech OS API response to validate
 * @returns Boolean indicating if response is valid
 */
function isValidTechOSResponse(
  response: unknown
): response is TechOSAPIResponse {
  return (
    response &&
    typeof response === "object" &&
    "status" in response &&
    response.status === "success" &&
    "data" in response &&
    response.data &&
    typeof response.data === "object" &&
    "dimension_breakdown" in response.data &&
    Array.isArray(response.data.dimension_breakdown)
  );
}

/**
 * Transform Tech OS API response data to progress bar format
 * @param apiResponse - The Tech OS API response containing OS data
 * @returns Array of progress bar data
 */
function transformTechOSToProgressBarData(
  apiResponse: TechOSAPIResponse
): ProgressbarData[] {
  const { dimension_breakdown } = apiResponse.data;

  // Sort by total_users count (descending) and take top 10
  const sortedOS = [...dimension_breakdown]
    .sort((a, b) => b.total_users - a.total_users)
    .slice(0, 10);

  return sortedOS.map((item) => ({
    title: item.name,
    percentage: Math.round(item.percentage),
  }));
}

/**
 * Custom hook for fetching tech OS data from GA4 API (for progress bars)
 * This hook is only called when the OS tab is selected in Tech Info
 * @param enabled - Whether to enable the query (should be true only for OS tab in Tech Info)
 * @returns Query result with tech OS progress bar data
 */
const useTechOSData = (enabled: boolean = false) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "tech-os-data",
      projectId,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<TechOSDataResult> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Build API URL with GA4 endpoint format for tech OS
      const apiUrl = `/api/project/GA4/user/tech/os/${projectId}`;

      try {
        // Fetch current period data
        const currentParams: Record<string, string> = {};
        if (startDate && endDate) {
          currentParams.start_date = startDate;
          currentParams.end_date = endDate;
        }

        const response = await httpService.get(apiUrl, {
          params:
            Object.keys(currentParams).length > 0 ? currentParams : undefined,
          useAuth: true,
        });

        // Validate response
        if (!isValidTechOSResponse(response.data)) {
          throw new Error("Invalid tech OS API response format");
        }

        // Transform to progress bar data
        const progressbarData = transformTechOSToProgressBarData(response.data);

        // Fetch comparison data if comparison is enabled
        let comparisonProgressbarData: ProgressbarData[] | undefined;
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          try {
            const comparisonParams: Record<string, string> = {
              start_date: comparisonStartDate,
              end_date: comparisonEndDate,
            };

            const comparisonResponse = await httpService.get(apiUrl, {
              params: comparisonParams,
              useAuth: true,
            });

            if (isValidTechOSResponse(comparisonResponse.data)) {
              comparisonProgressbarData = transformTechOSToProgressBarData(
                comparisonResponse.data
              );
            }
          } catch (error) {
            console.warn("Failed to fetch comparison tech OS data:", error);
          }
        }

        return {
          progressbarData,
          comparisonProgressbarData,
        };
      } catch (error) {
        console.error("Tech OS API call failed:", error);

        // Re-throw the error to let React Query handle it
        throw new Error(
          error instanceof Error
            ? `Failed to fetch tech OS data: ${error.message}`
            : "Failed to fetch tech OS data"
        );
      }
    },
    enabled: enabled && isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useTechOSData;