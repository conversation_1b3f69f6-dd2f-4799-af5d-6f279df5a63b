import http from "@/services/httpService";

export interface LandingPagesResponse {
  status: string;
  project_id: string;
  period: {
    start_date: string;
    end_date: string;
    days_count: number;
  };
  data: {
    "landing-pages": Record<string, LandingPageMetrics>;
  };
}

export interface LandingPageMetrics {
  SESSIONS: number;
  "ENGAGED SESSIONS": number;
  "NEW USERS": number;
  "TOTAL USERS": number;
  VIEWS: number;
  "ENGAGED TIME": number;
  "ENGAGED RATE": number;
  "EVENT COUNT": number;
  CONVERSIONS: number;
}

export const landingPagesService = {
  // Get landing pages data
  getLandingPages: async (
    projectId: string,
    startDate: string,
    endDate: string
  ): Promise<{ data: LandingPagesResponse["data"] }> => {
    try {
      console.log("🌐 Landing Pages API Call:", {
        url: `/api/project/GA4/page/demographic/landing-pages/${projectId}`,
        params: { start_date: startDate, end_date: endDate },
        projectId,
        startDate,
        endDate,
      });

      const response = await http.get(
        `/api/project/GA4/page/demographic/landing-pages/${projectId}`,
        {
          params: {
            start_date: startDate,
            end_date: endDate,
          },
          useAuth: true,
        }
      );

      console.log("🌐 Landing Pages API Response:", {
        status: response.status,
        hasData: !!response.data,
        dataKeys: response.data ? Object.keys(response.data) : [],
        responseStructure: {
          hasStatus: !!response.data?.status,
          hasDataProperty: !!response.data?.data,
          statusValue: response.data?.status,
          actualDataKeys: response.data?.data
            ? Object.keys(response.data.data)
            : [],
        },
      });

      // Validate response structure
      if (!response?.data) {
        throw new Error("Invalid API response: missing data");
      }

      // The API returns { status: "success", data: {...} }, so we need response.data.data
      const data = response.data.data || response.data;

      const validatedData = {
        "landing-pages": data["landing-pages"] || {},
      };

      return { data: validatedData };
    } catch (error) {
      console.error("Landing pages API error:", error);

      // Return empty data structure instead of throwing
      return {
        data: {
          "landing-pages": {},
        },
      };
    }
  },
};