import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";

// Types for tech device table data
interface TechDeviceTableItem {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

interface TechDeviceTableData {
  tableData: {
    tableHeadings: string[];
    tableBody: Array<Array<{ value: string; growth?: string }>>;
  };
  pagination: {
    totalPages: number;
    currentPage: number;
    totalItems: number;
  };
}

interface TechDeviceApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: TechDeviceTableItem[];
    daily_metrics: Array<{
      date: string;
      dimensions: TechDeviceTableItem[];
    }>;
  };
}

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// Helper function to format percentage
const formatPercentage = (rate: number): string => {
  return `${(rate * 100).toFixed(1)}%`;
};

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

/**
 * Hook to fetch tech device table data
 */
const useTechDeviceTableData = ({ page }: { page: number }) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: ["tech-device-table-data", projectId, page, getFormattedDates()],
    queryFn: async (): Promise<TechDeviceTableData> => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } = await httpService.get<TechDeviceApiResponse>(
        `/api/project/GA4/user/tech/device/${projectId}?start_date=${startDate}&end_date=${endDate}`,
        { useAuth: true }
      );

      let comparisonData: TechDeviceApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get<TechDeviceApiResponse>(
            `/api/project/GA4/user/tech/device/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison data:", error);
        }
      }

      // Create a map of comparison data for easy lookup
      const comparisonMap = new Map<string, TechDeviceTableItem>();
      if (comparisonData?.data?.dimension_breakdown) {
        comparisonData.data.dimension_breakdown.forEach((device) => {
          comparisonMap.set(device.name, device);
        });
      }

      // Transform API data to table format
      const allDevicesData = currentData.data.dimension_breakdown;

      // Table headings
      const tableHeadings = [
        "DEVICE",
        "TOTAL USERS",
        "NEW USERS",
        "SESSIONS",
        "ENGAGED SESSIONS",
        "VIEWS",
        "ENGAGEMENT RATE",
        "EVENT COUNT",
        "CONVERSIONS",
        "PERCENTAGE",
      ];

      // Transform data to table body format with comparison
      const allTableBody = allDevicesData.map((device) => {
        const comparisonDevice = comparisonMap.get(device.name);

        return [
          { value: device.name },
          {
            value: formatNumber(device.total_users),
            growth: comparisonDevice
              ? calculatePercentageChange(
                  device.total_users,
                  comparisonDevice.total_users
                )
              : undefined,
          },
          {
            value: formatNumber(device.new_users),
            growth: comparisonDevice
              ? calculatePercentageChange(
                  device.new_users,
                  comparisonDevice.new_users
                )
              : undefined,
          },
          {
            value: formatNumber(device.sessions),
            growth: comparisonDevice
              ? calculatePercentageChange(
                  device.sessions,
                  comparisonDevice.sessions
                )
              : undefined,
          },
          {
            value: formatNumber(device.engaged_sessions),
            growth: comparisonDevice
              ? calculatePercentageChange(
                  device.engaged_sessions,
                  comparisonDevice.engaged_sessions
                )
              : undefined,
          },
          {
            value: formatNumber(device.views),
            growth: comparisonDevice
              ? calculatePercentageChange(
                  device.views,
                  comparisonDevice.views
                )
              : undefined,
          },
          {
            value: formatPercentage(device.engagement_rate),
            growth: comparisonDevice
              ? calculatePercentageChange(
                  device.engagement_rate,
                  comparisonDevice.engagement_rate
                )
              : undefined,
          },
          {
            value: formatNumber(device.event_count),
            growth: comparisonDevice
              ? calculatePercentageChange(
                  device.event_count,
                  comparisonDevice.event_count
                )
              : undefined,
          },
          {
            value: formatNumber(device.conversions),
            growth: comparisonDevice
              ? calculatePercentageChange(
                  device.conversions,
                  comparisonDevice.conversions
                )
              : undefined,
          },
          {
            value: `${device.percentage.toFixed(1)}%`,
            growth: comparisonDevice
              ? calculatePercentageChange(
                  device.percentage,
                  comparisonDevice.percentage
                )
              : undefined,
          },
        ];
      });

      // Simple pagination - show 5 items per page
      const itemsPerPage = 5;
      const totalItems = allDevicesData.length;
      const totalPages = Math.ceil(totalItems / itemsPerPage);
      const startIndex = (page - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedTableBody = allTableBody.slice(startIndex, endIndex);

      return {
        tableData: {
          tableHeadings,
          tableBody: paginatedTableBody,
        },
        pagination: {
          totalPages,
          currentPage: page,
          totalItems,
        },
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export default useTechDeviceTableData;