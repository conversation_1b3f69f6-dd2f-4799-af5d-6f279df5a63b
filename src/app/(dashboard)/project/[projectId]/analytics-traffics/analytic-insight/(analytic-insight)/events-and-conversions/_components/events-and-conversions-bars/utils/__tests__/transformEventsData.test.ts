import { transformEventsApiResponse } from "../transformEventsData";
import type { EventsApiResponse } from "../../EventsAndConversions.types";

// Mock API response based on the actual API structure
const mockApiResponse: EventsApiResponse = {
  status: "success",
  project_id: "51e74171-0e23-4f7e-ad4d-7ffae8ffdab1",
  data: {
    events: {
      views: 23570,
      sessions: 60348,
      "all-users": 57385,
      "new-users": 14067,
      "active-users": 55579,
      "returning-users": 4797,
      "engaged-sessions": 35386,
      "all-events": {
        click: { value: 1, percentage: 0 },
        scroll: { value: 3015, percentage: 5 },
        page_view: { value: 23570, percentage: 41 },
        form_start: { value: 522, percentage: 1 },
        first_visit: { value: 14067, percentage: 25 },
        form_submit: { value: 3, percentage: 0 },
        session_start: { value: 17765, percentage: 31 },
        user_engagement: { value: 13164, percentage: 23 },
      },
      "views-events": {
        click: { value: 0, percentage: 0 },
        scroll: { value: 0, percentage: 0 },
        page_view: { value: 23570, percentage: 100 },
        form_start: { value: 0, percentage: 0 },
        first_visit: { value: 0, percentage: 0 },
        form_submit: { value: 0, percentage: 0 },
        session_start: { value: 0, percentage: 0 },
        user_engagement: { value: 0, percentage: 0 },
      },
      "sessions-events": {
        click: { value: 1, percentage: 0 },
        scroll: { value: 2376, percentage: 4 },
        page_view: { value: 17335, percentage: 29 },
        form_start: { value: 111, percentage: 0 },
        first_visit: { value: 13943, percentage: 23 },
        form_submit: { value: 2, percentage: 0 },
        session_start: { value: 17662, percentage: 29 },
        user_engagement: { value: 8918, percentage: 15 },
      },
      "new-users-events": {
        click: { value: 0, percentage: 0 },
        scroll: { value: 0, percentage: 0 },
        page_view: { value: 0, percentage: 0 },
        form_start: { value: 0, percentage: 0 },
        first_visit: { value: 14067, percentage: 100 },
        form_submit: { value: 0, percentage: 0 },
        session_start: { value: 0, percentage: 0 },
        user_engagement: { value: 0, percentage: 0 },
      },
      "active-users-events": {
        click: { value: 1, percentage: 0 },
        scroll: { value: 2248, percentage: 4 },
        page_view: { value: 15417, percentage: 28 },
        form_start: { value: 108, percentage: 0 },
        first_visit: { value: 13881, percentage: 25 },
        form_submit: { value: 2, percentage: 0 },
        session_start: { value: 15606, percentage: 28 },
        user_engagement: { value: 8316, percentage: 15 },
      },
      "returning-users-events": {
        click: { value: 0, percentage: 0 },
        scroll: { value: 359, percentage: 7 },
        page_view: { value: 1495, percentage: 31 },
        form_start: { value: 18, percentage: 0 },
        first_visit: { value: 0, percentage: 0 },
        form_submit: { value: 2, percentage: 0 },
        session_start: { value: 1726, percentage: 36 },
        user_engagement: { value: 1197, percentage: 25 },
      },
      "engaged-sessions-events": {
        click: { value: 1, percentage: 0 },
        scroll: { value: 2206, percentage: 6 },
        page_view: { value: 9000, percentage: 25 },
        form_start: { value: 106, percentage: 0 },
        first_visit: { value: 7428, percentage: 21 },
        form_submit: { value: 2, percentage: 0 },
        session_start: { value: 8994, percentage: 25 },
        user_engagement: { value: 7649, percentage: 22 },
      },
    },
  },
  period: {
    start_date: "2025-07-03",
    end_date: "2025-08-02",
    days_count: 31,
  },
};

describe("transformEventsApiResponse", () => {
  it("should transform API response correctly for All Users tab", () => {
    const result = transformEventsApiResponse(mockApiResponse, "All Users");

    // Check cardTabs structure
    expect(result.cardTabs).toHaveLength(7);
    expect(result.cardTabs[0]).toEqual({
      title: "All Users",
      value: "6.5K",
      changeValue: "+0%",
    });

    // Check barsData structure
    expect(result.barsData.bars).toHaveLength(2);
    expect(result.barsData.bars[0].label).toBe("Event Name 1");
    expect(result.barsData.bars[0].barData[0].value).toBe(5820);
    expect(result.barsData.bars[0].barData[0].color).toBe("#7c2d92");

    // Check sorting (event_name_1 should be first as it has higher value)
    expect(result.barsData.bars[0].barData[0].value).toBeGreaterThan(
      result.barsData.bars[1].barData[0].value
    );
  });

  it("should handle different metric tabs correctly", () => {
    const result = transformEventsApiResponse(mockApiResponse, "Sessions");

    // Should use sessions metric for bars
    expect(result.barsData.bars[0].barData[0].value).toBe(6890); // event_name_1 sessions
    expect(result.barsData.bars[1].barData[0].value).toBe(4120); // event_name_2 sessions
  });

  it("should format numbers correctly", () => {
    const result = transformEventsApiResponse(mockApiResponse, "All Users");

    // Check number formatting
    expect(result.cardTabs.find((tab) => tab.title === "Views")?.value).toBe(
      "10.1K"
    );
    expect(
      result.cardTabs.find((tab) => tab.title === "All Users")?.value
    ).toBe("6.5K");
  });
});
