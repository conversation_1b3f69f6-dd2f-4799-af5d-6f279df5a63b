"use client";
import React, { useEffect, useRef } from "react";
import isEqual from "lodash.isequal";

/* =============================== COMPONENTS =============================== */
import CardTab from "@/components/ui/card-tab/CardTab";

/* ================================== TYPES ================================= */
import type { AudienceResponse } from "../Audience.types";
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";

/* ========================================================================== */
interface AudienceTabNavigationProps {
  data: AudienceResponse | undefined;
  isLoading: boolean;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

/* ========================================================================== */
const AudienceTabNavigation: React.FC<AudienceTabNavigationProps> = ({
  data,
  isLoading: _isLoading,
  activeTab,
  onTabChange,
}) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const prevCardTabsRef = useRef<CardTabType[] | null>(data?.cardTabs ?? null);

  /* ========================================================================== */
  /*                                   EFFECTS                                  */
  /* ========================================================================== */
  useEffect(() => {
    if (data?.cardTabs && !isEqual(prevCardTabsRef.current, data.cardTabs)) {
      prevCardTabsRef.current = data.cardTabs;
    }
  }, [data?.cardTabs]);

  useEffect(() => {
    if (data && activeTab === "") {
      onTabChange(data.cardTabs[0].title);
    }
  }, [data, activeTab, onTabChange]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="flex overflow-x-auto max-w-full gap-1 px-0 pb-2 whitespace-nowrap ">
      {(_isLoading || !data?.cardTabs)
        ? Array.from({ length: prevCardTabsRef.current?.length ?? 8 }).map((_, i) => (
            <CardTab key={i} isLoading />
          ))
        : data.cardTabs.map(
            (
              {
                title,
                changeValue,
                value,
              }: { title: string; changeValue: string; value: string },
              index: number
            ) => (
              <CardTab
                key={index}
                title={title}
                value={value}
                changeValue={changeValue}
                className={`border-2 ${
                  activeTab === title ? "border-primary" : "border-transparent"
                }`}
                onSelect={() => onTabChange(title)}
              />
            )
          )}
    </div>
  );
};

export default AudienceTabNavigation;
