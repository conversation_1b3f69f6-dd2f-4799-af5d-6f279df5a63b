"use client";
import React from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";

/* ================================== TYPES ================================= */
interface EventsErrorStateProps {
  error: Error | null;
  title?: string;
  description?: string;
  onRetry?: () => void;
}

/* ========================================================================== */
const EventsErrorState: React.FC<EventsErrorStateProps> = ({
  error,
  title = "Events & Conversions",
  description = "Unable to load events and conversions data",
  onRetry,
}) => {
  return (
    <Card className="space-y-4">
      <div className="space-y-2">
        <Title>{title}</Title>
      </div>
      
      <div className="flex items-center justify-center min-h-[500px]">
        <div className="text-center space-y-4 max-w-md">
          <div className="text-6xl text-secondary/30">⚠️</div>
          
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-secondary">
              {description}
            </h3>
            
            {error && (
              <p className="text-sm text-secondary/70">
                {error.message || "An unexpected error occurred"}
              </p>
            )}
          </div>
          
          {onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
            >
              Try Again
            </button>
          )}
          
          <div className="text-xs text-secondary/50 space-y-1">
            <p>If this problem persists, please:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Check your internet connection</li>
              <li>Try refreshing the page</li>
              <li>Contact support if the issue continues</li>
            </ul>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EventsErrorState;
