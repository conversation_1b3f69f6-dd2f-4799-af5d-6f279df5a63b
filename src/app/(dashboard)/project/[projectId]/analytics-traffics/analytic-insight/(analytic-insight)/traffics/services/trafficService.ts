import http from "@/services/httpService";
import type { GA4TrafficResponse } from "../types/TrafficData.types";

export const trafficService = {
  // Get total traffic data
  getTotalTraffic: (
    projectId: string,
    startDate: string,
    endDate: string
  ): Promise<{ data: GA4TrafficResponse }> => {
    return http.get(`/api/project/GA4/traffic/overview/total/${projectId}/`, {
      params: {
        start_date: startDate,
        end_date: endDate,
      },
      useAuth: true,
    });
  },

  // Get paid traffic data
  getPaidTraffic: (
    projectId: string,
    startDate: string,
    endDate: string
  ): Promise<{ data: GA4TrafficResponse }> => {
    return http.get(
      `/api/project/GA4/traffic/overview/paid/${projectId}`,
      {
        params: {
          start_date: startDate,
          end_date: endDate,
        },
        useAuth: true,
      }
    );
  },

  // Get referral traffic data
  getReferralTraffic: (
    projectId: string,
    startDate: string,
    endDate: string
  ): Promise<{ data: GA4TrafficResponse }> => {
    return http.get(`/api/project/GA4/traffic/overview/referral/${projectId}`, {
      params: {
        start_date: startDate,
        end_date: endDate,
      },
      useAuth: true,
    });
  },

  // Get social traffic data
  getSocialTraffic: (
    projectId: string,
    startDate: string,
    endDate: string
  ): Promise<{ data: GA4TrafficResponse }> => {
    return http.get(
      `/api/project/GA4/traffic/overview/social/${projectId}`,
      {
        params: {
          start_date: startDate,
          end_date: endDate,
        },
        useAuth: true,
      }
    );
  },

  // Get organic traffic data
  getOrganicTraffic: (
    projectId: string,
    startDate: string,
    endDate: string
  ): Promise<{ data: GA4TrafficResponse }> => {
    return http.get(`/api/project/GA4/traffic/overview/organic/${projectId}`, {
      params: {
        start_date: startDate,
        end_date: endDate,
      },
      useAuth: true,
    });
  },
};
