import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";

// Types for the daily tech OS metrics API response
interface DailyTechOSData {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
}

interface DailyTechOSMetric {
  date: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  dimensions: DailyTechOSData[];
}

interface DailyTechOSApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: DailyTechOSData[];
    daily_metrics: DailyTechOSMetric[];
  };
}

/**
 * Hook to fetch daily tech OS metrics data with comparison support
 * This provides tech OS breakdown for each day in the selected date range
 */
const useDailyTechOSData = (enabled: boolean = true) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: [
      "daily-tech-os-data",
      projectId,
      getFormattedDates(),
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<{
      currentPeriod: DailyTechOSMetric[];
      comparisonPeriod: DailyTechOSMetric[] | null;
    }> => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } =
        await httpService.get<DailyTechOSApiResponse>(
          `/api/project/GA4/user/tech/os/${projectId}?start_date=${startDate}&end_date=${endDate}`,
          { useAuth: true }
        );

      let comparisonData: DailyTechOSApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get<DailyTechOSApiResponse>(
            `/api/project/GA4/user/tech/os/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison tech OS data:", error);
        }
      }

      return {
        currentPeriod: currentData.data?.daily_metrics || [],
        comparisonPeriod: comparisonData?.data?.daily_metrics || null,
      };
    },
    enabled: enabled && isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export default useDailyTechOSData;
export type { DailyTechOSMetric, DailyTechOSData };