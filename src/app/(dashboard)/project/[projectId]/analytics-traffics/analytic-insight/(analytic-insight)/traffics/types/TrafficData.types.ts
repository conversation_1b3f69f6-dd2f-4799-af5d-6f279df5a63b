// Types for the new GA4 traffic API response
export interface TrafficSourceData {
  value: number;
  percentage: number;
}

// Extended interface for referral traffic sources that include URL
export interface ReferralTrafficSourceData extends TrafficSourceData {
  url: string;
}

export interface DailyMetric {
  date: string;
  all_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  organic: TrafficSourceData;
  referral: TrafficSourceData;
  paid: TrafficSourceData;
  social: TrafficSourceData;
  direct: TrafficSourceData;
  unassigned: TrafficSourceData;
  mail: TrafficSourceData;
  // Support for social platform breakdowns (social traffic)
  instagram?: TrafficSourceData;
  facebook?: TrafficSourceData;
  youtube?: TrafficSourceData;
  linkedin?: TrafficSourceData;
  // Support for multiple referral sources
  referral_1?: ReferralTrafficSourceData;
  referral_2?: ReferralTrafficSourceData;
  referral_3?: ReferralTrafficSourceData;
  referral_4?: ReferralTrafficSourceData;
  referral_5?: ReferralTrafficSourceData;
  referral_6?: ReferralTrafficSourceData;
  referral_7?: ReferralTrafficSourceData;
  // Support for search engines (organic traffic)
  google?: TrafficSourceData;
  yahoo?: TrafficSourceData;
  yandex?: TrafficSourceData;
  bing?: TrafficSourceData;
  // Support for paid traffic sources
  google_ads?: TrafficSourceData;
  facebook_ads?: TrafficSourceData;
  microsoft_ads?: TrafficSourceData;
  linkedin_ads?: TrafficSourceData;
  display_ads?: TrafficSourceData;
  youtube_ads?: TrafficSourceData;
  other_paid?: TrafficSourceData;
}

export interface TrafficTotals {
  all_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  organic: TrafficSourceData;
  referral: TrafficSourceData;
  paid: TrafficSourceData;
  social: TrafficSourceData;
  direct: TrafficSourceData;
  unassigned: TrafficSourceData;
  mail: TrafficSourceData;
   // Support for social platform breakdowns (social traffic)
   instagram?: TrafficSourceData;
   facebook?: TrafficSourceData;
   youtube?: TrafficSourceData;
   linkedin?: TrafficSourceData;
  // Support for multiple referral sources
  referral_1?: ReferralTrafficSourceData;
  referral_2?: ReferralTrafficSourceData;
  referral_3?: ReferralTrafficSourceData;
  referral_4?: ReferralTrafficSourceData;
  referral_5?: ReferralTrafficSourceData;
  referral_6?: ReferralTrafficSourceData;
  referral_7?: ReferralTrafficSourceData;
  // Support for search engines (organic traffic)
  google?: TrafficSourceData;
  yahoo?: TrafficSourceData;
  yandex?: TrafficSourceData;
  bing?: TrafficSourceData;
  // Support for paid traffic sources
  google_ads?: TrafficSourceData;
  facebook_ads?: TrafficSourceData;
  microsoft_ads?: TrafficSourceData;
  linkedin_ads?: TrafficSourceData;
  display_ads?: TrafficSourceData;
  youtube_ads?: TrafficSourceData;
  other_paid?: TrafficSourceData;
}

export interface TrafficPeriod {
  start_date: string;
  end_date: string;
  days_count: number;
}

export interface TrafficData {
  totals: TrafficTotals;
  period: TrafficPeriod;
  daily_metrics: DailyMetric[];
  // Optional flags for fallback handling
  isFallback?: boolean;
  originalSource?: string;
}

export interface GA4TrafficResponse {
  status: string;
  project_id: string;
  traffic_source: string;
  data: TrafficData;
}

// Types for transformed data used by the chart component
export interface TransformedTrafficData {
  cardTabs: Array<{
    title: string;
    value: string;
    changeValue: string;
  }>;
  lineChartData: Array<{
    name: string;
    [key: string]: string | number;
  }>;
  colors: Array<{
    name: string;
    color: string;
  }>;
  selectedLines: string[];
  cardsData: Record<
    string,
    {
      amount: number;
      growth: string;
    }
  >;
  progressbarData: Array<{
    title: string;
    percentage: number;
    value?: number;
    color?: string;
    comparisonPercentage?: number;
    comparisonValue?: number;
    showComparison?: boolean;
  }>;
  hoveredData?: {
    date: string;
    progressbarData: Array<{
      title: string;
      percentage: number;
      value?: number;
      color?: string;
      comparisonPercentage?: number;
      comparisonValue?: number;
      showComparison?: boolean;
    }>;
    comparisonProgressbarData?: Array<{
      title: string;
      percentage: number;
      value?: number;
      color?: string;
    }>;
  };
}
