import type {
  LandingPagesApiResponse,
  AllPagesApiResponse,
  HighTrafficApiResponse,
  TransformedPagesData,
} from "../PagesBars.types";
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import type { BarsData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/HorizontalBars.types";
import {
  CHART_COLORS,
  METRIC_TABS,
  DEFAULTS,
} from "../constants/pages.constants";

// Define BarData type to match HorizontalBar component expectations
type BarData = {
  value: number;
  color: string;
  actualValue?: number;
  period?: "current" | "previous";
};

/**
 * Creates bar data for comparison visualization using stacking logic
 */
const createComparisonBarData = (
  currentValue: number,
  previousValue: number
): BarData[] => {
  const barData: BarData[] = [];

  if (currentValue > previousValue && previousValue > 0) {
    // Growth: show previous as base, current difference as additional
    barData.push({
      value: previousValue,
      color: CHART_COLORS.PREVIOUS_PERIOD,
      actualValue: previousValue,
      period: "previous",
    });
    barData.push({
      value: currentValue - previousValue,
      color: CHART_COLORS.CURRENT_PERIOD,
      actualValue: currentValue,
      period: "current",
    });
  } else if (currentValue < previousValue && currentValue > 0) {
    // Decline: show current as base, previous difference as additional
    barData.push({
      value: currentValue,
      color: CHART_COLORS.CURRENT_PERIOD,
      actualValue: currentValue,
      period: "current",
    });
    barData.push({
      value: previousValue - currentValue,
      color: CHART_COLORS.PREVIOUS_PERIOD,
      actualValue: previousValue,
      period: "previous",
    });
  } else if (currentValue > 0 && previousValue === 0) {
    // Only current value
    barData.push({
      value: currentValue,
      color: CHART_COLORS.CURRENT_PERIOD,
      actualValue: currentValue,
      period: "current",
    });
  } else if (previousValue > 0 && currentValue === 0) {
    // Only previous value
    barData.push({
      value: previousValue,
      color: CHART_COLORS.PREVIOUS_PERIOD,
      actualValue: previousValue,
      period: "previous",
    });
  } else if (currentValue === previousValue && currentValue > 0) {
    // Equal values: show current visually
    barData.push({
      value: currentValue,
      color: CHART_COLORS.CURRENT_PERIOD,
      actualValue: currentValue,
      period: "current",
    });
  }

  return barData;
};

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
};

// Helper function to format time (for avg_engagement_time)
const formatTime = (seconds: number): string => {
  if (seconds >= 60) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${Math.floor(seconds)}s`;
};

// Helper function to get top pages by metric
const getTopPagesByMetric = (
  pages: Record<string, { value: number; percentage: number }>,
  limit: number = 10,
  sortByPercentage: boolean = false
): Array<{ page: string; value: number; percentage: number }> => {
  return Object.entries(pages)
    .filter(([page]) => page !== "(not set)")
    .map(([page, data]) => ({ page, ...data }))
    .sort((a, b) => sortByPercentage ? b.percentage - a.percentage : b.value - a.value)
    .slice(0, limit);
};

export const transformLandingPagesData = (
  apiResponse: LandingPagesApiResponse,
  activeTab: string,
  comparisonData?: LandingPagesApiResponse | null,
  _filter: string = "All Pages"
): TransformedPagesData => {
  const landingPagesData = apiResponse.data["landing-pages"]["landing-pages"];
  const comparisonLandingPagesData =
    comparisonData?.data["landing-pages"]["landing-pages"];

  // Create card tabs with all metrics
  const cardTabs: CardTabType[] = METRIC_TABS.map((metric) => {
    const currentValue = landingPagesData[
      metric.key as keyof typeof landingPagesData
    ] as number;
    let changeValue: string = "";

    if (comparisonLandingPagesData) {
      const comparisonValue = comparisonLandingPagesData[
        metric.key as keyof typeof comparisonLandingPagesData
      ] as number;
      if (typeof comparisonValue === "number") {
        changeValue = calculatePercentageChange(currentValue, comparisonValue);
      }
    }

    // Format value based on metric type
    const formattedValue =
      metric.key === "avg_engagement_time"
        ? formatTime(currentValue)
        : formatNumber(currentValue);

    return {
      title: metric.title,
      value: formattedValue,
      changeValue,
    };
  });

  // Get the appropriate pages data based on active tab
  // Map tab names to data keys
  const dataKeyMap: Record<string, string> = {
    Views: "views_pages",
    Sessions: "sessions_pages",
    "New Users": "new_users_pages",
    "Total Users": "total_users_pages",
    "Returning Users": "returning_users_pages",
    "Engaged Sessions": "engaged_sessions_pages",
    "Avg Engagement Time": "avg_engagement_time_pages",
  };

  const dataKey = dataKeyMap[activeTab] || "views_pages";
  const pagesData = landingPagesData[
    dataKey as keyof typeof landingPagesData
  ] as Record<string, { value: number; percentage: number }>;
  const comparisonPagesData = comparisonLandingPagesData?.[
    dataKey as keyof typeof comparisonLandingPagesData
  ] as Record<string, { value: number; percentage: number }> | undefined;

  // Get top pages for the chart - sort by percentage for Avg Engagement Time
  const topPages = getTopPagesByMetric(pagesData, 10, activeTab === "Avg Engagement Time");

  // Use the total metric value as the scaling reference
  const totalMetricKey =
    activeTab === "Views"
      ? "views"
      : activeTab === "Sessions"
      ? "sessions"
      : activeTab === "New Users"
      ? "new_users"
      : activeTab === "Total Users"
      ? "total_users"
      : activeTab === "Returning Users"
      ? "returning_users"
      : activeTab === "Engaged Sessions"
      ? "engaged_sessions"
      : activeTab === "Avg Engagement Time"
      ? "avg_engagement_time"
      : "views";

  const currentTotal = landingPagesData[
    totalMetricKey as keyof typeof landingPagesData
  ] as number;
  const comparisonTotal =
    (comparisonLandingPagesData?.[
      totalMetricKey as keyof typeof comparisonLandingPagesData
    ] as number) || 0;

  // For Avg Engagement Time, use percentage-based scaling instead of value-based scaling
  let maxValue: number;
  let bars;
  
  if (activeTab === "Avg Engagement Time") {
    // Use 100 as maxValue since we're working with percentages
    maxValue = 100;
    
    // Create bars using percentage values instead of actual values
    bars = topPages.map(({ page, value, percentage }) => {
      const comparisonPageData = comparisonPagesData?.[page];
      const comparisonValue = comparisonPageData?.value || 0;
      const comparisonPercentage = comparisonPageData?.percentage || 0;

      // Clean up page URL for display
      const displayLabel =
        page === "(not set)"
          ? "Direct"
          : page.length > 50
          ? `${page.substring(0, 47)}...`
          : page;

      // Use percentage for bar scaling but keep actual values for tooltips
      const barData = comparisonPageData && comparisonData
        ? createComparisonBarData(percentage, comparisonPercentage).map(bar => ({
            ...bar,
            actualValue: bar.period === "current" ? value : comparisonValue,
          }))
        : [
            {
              value: percentage,
              color: CHART_COLORS.CURRENT_PERIOD,
              actualValue: value,
              period: "current" as const,
            },
          ];

      return {
        label: displayLabel,
        barData,
      };
    });
  } else {
    maxValue = Math.max(
      currentTotal,
      comparisonData ? comparisonTotal : 0,
      DEFAULTS.MIN_MAX_VALUE
    );

    // Create bars data with two-color comparison system (original logic for other metrics)
    bars = topPages.map(({ page, value }) => {
      const comparisonValue = comparisonPagesData?.[page]?.value || 0;

      // Clean up page URL for display
      const displayLabel =
        page === "(not set)"
          ? "Direct"
          : page.length > 50
          ? `${page.substring(0, 47)}...`
          : page;

      // Use helper function for comparison bar data or simple current-only bar
      const barData = comparisonPagesData?.[page] && comparisonData
        ? createComparisonBarData(value, comparisonValue)
        : [
            {
              value: value,
              color: CHART_COLORS.CURRENT_PERIOD,
              actualValue: value,
              period: "current" as const,
            },
          ];

      return {
        label: displayLabel,
        barData,
      };
    });
  }

  const barsData: BarsData = {
    bars,
    maxValue,
  };

  return {
    cardTabs,
    barsData,
  };
};
export const transformAllPagesData = (
  apiResponse: AllPagesApiResponse,
  activeTab: string,
  comparisonData?: AllPagesApiResponse | null,
  _filter: string = "All Pages"
): TransformedPagesData => {
  const allPagesData = apiResponse.data["all-pages"]["all-pages"];
  const comparisonAllPagesData = comparisonData?.data["all-pages"]["all-pages"];

  // Create card tabs with all metrics (same as landing pages)
  const cardTabs: CardTabType[] = METRIC_TABS.map((metric) => {
    const currentValue = allPagesData[
      metric.key as keyof typeof allPagesData
    ] as number;
    let changeValue: string = "";

    if (comparisonAllPagesData) {
      const comparisonValue = comparisonAllPagesData[
        metric.key as keyof typeof comparisonAllPagesData
      ] as number;
      if (typeof comparisonValue === "number") {
        changeValue = calculatePercentageChange(currentValue, comparisonValue);
      }
    }

    // Format value based on metric type
    const formattedValue =
      metric.key === "avg_engagement_time"
        ? formatTime(currentValue)
        : formatNumber(currentValue);

    return {
      title: metric.title,
      value: formattedValue,
      changeValue,
    };
  });

  // Get the appropriate pages data based on active tab
  const dataKeyMap: Record<string, string> = {
    Views: "views_pages",
    Sessions: "sessions_pages",
    "New Users": "new_users_pages",
    "Total Users": "total_users_pages",
    "Returning Users": "returning_users_pages",
    "Engaged Sessions": "engaged_sessions_pages",
    "Avg Engagement Time": "avg_engagement_time_pages",
  };

  const dataKey = dataKeyMap[activeTab] || "views_pages";
  const pagesData = allPagesData[
    dataKey as keyof typeof allPagesData
  ] as Record<string, { value: number; percentage: number }>;
  const comparisonPagesData = comparisonAllPagesData?.[
    dataKey as keyof typeof comparisonAllPagesData
  ] as Record<string, { value: number; percentage: number }> | undefined;

  // Get top pages for the chart - sort by percentage for Avg Engagement Time
  const topPages = getTopPagesByMetric(pagesData, 10, activeTab === "Avg Engagement Time");

  // Use the total metric value as the scaling reference
  const totalMetricKey =
    activeTab === "Views"
      ? "views"
      : activeTab === "Sessions"
      ? "sessions"
      : activeTab === "New Users"
      ? "new_users"
      : activeTab === "Total Users"
      ? "total_users"
      : activeTab === "Returning Users"
      ? "returning_users"
      : activeTab === "Engaged Sessions"
      ? "engaged_sessions"
      : activeTab === "Avg Engagement Time"
      ? "avg_engagement_time"
      : "views";

  const currentTotal = allPagesData[
    totalMetricKey as keyof typeof allPagesData
  ] as number;
  const comparisonTotal =
    (comparisonAllPagesData?.[
      totalMetricKey as keyof typeof comparisonAllPagesData
    ] as number) || 0;

  // For Avg Engagement Time, use percentage-based scaling instead of value-based scaling
  let maxValue: number;
  let bars;
  
  if (activeTab === "Avg Engagement Time") {
    // Use 100 as maxValue since we're working with percentages
    maxValue = 100;
    
    // Create bars using percentage values instead of actual values
    bars = topPages.map(({ page, value, percentage }) => {
      const comparisonPageData = comparisonPagesData?.[page];
      const comparisonValue = comparisonPageData?.value || 0;
      const comparisonPercentage = comparisonPageData?.percentage || 0;

      // Clean up page URL for display
      const displayLabel =
        page === "(not set)"
          ? "Direct"
          : page.length > 50
          ? `${page.substring(0, 47)}...`
          : page;

      // Use percentage for bar scaling but keep actual values for tooltips
      const barData = comparisonPageData && comparisonData
        ? createComparisonBarData(percentage, comparisonPercentage).map(bar => ({
            ...bar,
            actualValue: bar.period === "current" ? value : comparisonValue,
          }))
        : [
            {
              value: percentage,
              color: CHART_COLORS.CURRENT_PERIOD,
              actualValue: value,
              period: "current" as const,
            },
          ];

      return {
        label: displayLabel,
        barData,
      };
    });
  } else {
    maxValue = Math.max(
      currentTotal,
      comparisonData ? comparisonTotal : 0,
      DEFAULTS.MIN_MAX_VALUE
    );

    // Create bars data with two-color comparison system (original logic for other metrics)
    bars = topPages.map(({ page, value }) => {
      const comparisonValue = comparisonPagesData?.[page]?.value || 0;

      // Clean up page URL for display
      const displayLabel =
        page === "(not set)"
          ? "Direct"
          : page.length > 50
          ? `${page.substring(0, 47)}...`
          : page;

      // Use helper function for comparison bar data or simple current-only bar
      const barData = comparisonPagesData?.[page] && comparisonData
        ? createComparisonBarData(value, comparisonValue)
        : [
            {
              value: value,
              color: CHART_COLORS.CURRENT_PERIOD,
              actualValue: value,
              period: "current" as const,
            },
          ];

      return {
        label: displayLabel,
        barData,
      };
    });
  }

  const barsData: BarsData = {
    bars,
    maxValue,
  };

  return {
    cardTabs,
    barsData,
  };
};

export const transformHighTrafficData = (
  apiResponse: HighTrafficApiResponse,
  activeTab: string,
  comparisonData?: HighTrafficApiResponse | null,
  _filter: string = "High Traffic Pages"
): TransformedPagesData => {
  const highTrafficData = apiResponse.data["high-traffic"]["high-traffic"];
  const comparisonHighTrafficData =
    comparisonData?.data["high-traffic"]["high-traffic"];

  // Create card tabs with all metrics (same as other APIs)
  const cardTabs: CardTabType[] = METRIC_TABS.map((metric) => {
    const currentValue = highTrafficData[
      metric.key as keyof typeof highTrafficData
    ] as number;
    let changeValue: string = "";

    if (comparisonHighTrafficData) {
      const comparisonValue = comparisonHighTrafficData[
        metric.key as keyof typeof comparisonHighTrafficData
      ] as number;
      if (typeof comparisonValue === "number") {
        changeValue = calculatePercentageChange(currentValue, comparisonValue);
      }
    }

    // Format value based on metric type
    const formattedValue =
      metric.key === "avg_engagement_time"
        ? formatTime(currentValue)
        : formatNumber(currentValue);

    return {
      title: metric.title,
      value: formattedValue,
      changeValue,
    };
  });

  // Get the appropriate pages data based on active tab
  const dataKeyMap: Record<string, string> = {
    Views: "views_pages",
    Sessions: "sessions_pages",
    "New Users": "new_users_pages",
    "Total Users": "total_users_pages",
    "Returning Users": "returning_users_pages",
    "Engaged Sessions": "engaged_sessions_pages",
    "Avg Engagement Time": "avg_engagement_time_pages",
  };

  const dataKey = dataKeyMap[activeTab] || "views_pages";
  const pagesData = highTrafficData[
    dataKey as keyof typeof highTrafficData
  ] as Record<string, { value: number; percentage: number }>;
  const comparisonPagesData = comparisonHighTrafficData?.[
    dataKey as keyof typeof comparisonHighTrafficData
  ] as Record<string, { value: number; percentage: number }> | undefined;

  // Get top pages for the chart - sort by percentage for Avg Engagement Time
  const topPages = getTopPagesByMetric(pagesData, 10, activeTab === "Avg Engagement Time");

  // Use the total metric value as the scaling reference
  const totalMetricKey =
    activeTab === "Views"
      ? "views"
      : activeTab === "Sessions"
      ? "sessions"
      : activeTab === "New Users"
      ? "new_users"
      : activeTab === "Total Users"
      ? "total_users"
      : activeTab === "Returning Users"
      ? "returning_users"
      : activeTab === "Engaged Sessions"
      ? "engaged_sessions"
      : activeTab === "Avg Engagement Time"
      ? "avg_engagement_time"
      : "views";

  const currentTotal = highTrafficData[
    totalMetricKey as keyof typeof highTrafficData
  ] as number;
  const comparisonTotal =
    (comparisonHighTrafficData?.[
      totalMetricKey as keyof typeof comparisonHighTrafficData
    ] as number) || 0;

  // For Avg Engagement Time, use percentage-based scaling instead of value-based scaling
  let maxValue: number;
  let bars;
  
  if (activeTab === "Avg Engagement Time") {
    // Use 100 as maxValue since we're working with percentages
    maxValue = 100;
    
    // Create bars using percentage values instead of actual values
    bars = topPages.map(({ page, value, percentage }) => {
      const comparisonPageData = comparisonPagesData?.[page];
      const comparisonValue = comparisonPageData?.value || 0;
      const comparisonPercentage = comparisonPageData?.percentage || 0;

      // Clean up page URL for display
      const displayLabel =
        page === "(not set)"
          ? "Direct"
          : page.length > 50
          ? `${page.substring(0, 47)}...`
          : page;

      // Use percentage for bar scaling but keep actual values for tooltips
      const barData = comparisonPageData && comparisonData
        ? createComparisonBarData(percentage, comparisonPercentage).map(bar => ({
            ...bar,
            actualValue: bar.period === "current" ? value : comparisonValue,
          }))
        : [
            {
              value: percentage,
              color: CHART_COLORS.CURRENT_PERIOD,
              actualValue: value,
              period: "current" as const,
            },
          ];

      return {
        label: displayLabel,
        barData,
      };
    });
  } else {
    maxValue = Math.max(
      currentTotal,
      comparisonData ? comparisonTotal : 0,
      DEFAULTS.MIN_MAX_VALUE
    );

    // Create bars data with two-color comparison system (original logic for other metrics)
    bars = topPages.map(({ page, value }) => {
      const comparisonValue = comparisonPagesData?.[page]?.value || 0;

      // Clean up page URL for display
      const displayLabel =
        page === "(not set)"
          ? "Direct"
          : page.length > 50
          ? `${page.substring(0, 47)}...`
          : page;

      // Use helper function for comparison bar data or simple current-only bar
      const barData = comparisonPagesData?.[page] && comparisonData
        ? createComparisonBarData(value, comparisonValue)
        : [
            {
              value: value,
              color: CHART_COLORS.CURRENT_PERIOD,
              actualValue: value,
              period: "current" as const,
            },
          ];

      return {
        label: displayLabel,
        barData,
      };
    });
  }

  const barsData: BarsData = {
    bars,
    maxValue,
  };

  return {
    cardTabs,
    barsData,
  };
};
