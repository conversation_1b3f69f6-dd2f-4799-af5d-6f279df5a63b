"use client";
import React, { useEffect, useState, useMemo } from "react";
/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../../../_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import GSCLineChart from "../../../../_components/line-chart/LineChart";
import ProgressBar from "../../../../../overview/(overview)/users-overview/_components/ProgressBar";
import NoData from "../../../../_components/NoData";
import Dropdown from "@/components/ui/Dropdown";
import useTrafficChartAndBars from "./TrafficChartAndBars.hooks";
import { useDateRangeStore } from "@/store/useDateRangeStore";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================== TYPES ================================= */
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";
import LineChartSkeleton from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/line-chart-skeleton/LineChartSkeleton";
import { useProjectContext } from "@/contexts/ProjectContext";

/* ========================================================================== */
const TrafficChartAndBars = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState("");
  const [hoveredDate, setHoveredDate] = useState<string | undefined>(undefined);
  const { themeColor } = useAppThemeColor();
  const { projectName } = useProjectContext();
  const { isComparisonEnabled } = useDateRangeStore();
  const domesticInfoFilters = [
    "Total Traffics ",
    "Paid Traffics",
    "Referral Traffics",
    "Social Traffics",
    "Organic Traffics",
  ];
  const [activeDomesticFilter, setActiveDomesticFilter] = useState(
    domesticInfoFilters[0]
  );

  const { data, isError, error, isLoading } = useTrafficChartAndBars({
    tab: activeTab,
    domesticFilter: activeDomesticFilter,
    hoveredDate,
  });

  // Set initial active tab when data is available
  useEffect(() => {
    if (data?.cardTabs && data.cardTabs.length > 0 && !activeTab) {
      setActiveTab(data.cardTabs[0].title);
    }
  }, [data?.cardTabs, activeTab]);

  // Memoized current progress bar data - prioritize hovered data when available
  const currentProgressbarData = useMemo(() => {
    // When hovering on a date, use the hovered data which includes comparison for that specific date
    if (hoveredDate && data?.hoveredData?.progressbarData) {
      return data.hoveredData.progressbarData;
    }
    // Otherwise use the overall progress bar data
    return data?.progressbarData || [];
  }, [hoveredDate, data?.hoveredData?.progressbarData, data?.progressbarData]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Show error state with better error handling
  if (isError) {
    console.error("fetching chart and bars failed: ", error);
    return (
      <Card className="space-y-4">
        <div className="space-y-2">
          <div className="flex w-full justify-between items-center">
            <Title>{projectName} Traffics</Title>
            <Dropdown>
              <Dropdown.Button>{activeDomesticFilter}</Dropdown.Button>
              <Dropdown.Options>
                {domesticInfoFilters.map((filter, index) => (
                  <Dropdown.Option
                    key={index}
                    onClick={() => setActiveDomesticFilter(filter)}
                  >
                    {filter}
                  </Dropdown.Option>
                ))}
              </Dropdown.Options>
            </Dropdown>
          </div>
          <DateRange />
        </div>
        <NoData
          title={`${
            projectName + " " + activeDomesticFilter
          } - Error Loading Data`}
        />
      </Card>
    );
  }

  return (
    <Card className="space-y-4">
      <div className="space-y-2">
        <div className="flex w-full justify-between items-center">
          <Title>{projectName} Traffics</Title>
          <Dropdown>
            <Dropdown.Button>{activeDomesticFilter}</Dropdown.Button>
            <Dropdown.Options>
              {domesticInfoFilters.map((filter, index) => (
                <Dropdown.Option
                  key={index}
                  onClick={() => setActiveDomesticFilter(filter)}
                >
                  {filter}
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
        </div>
        <DateRange />
        {/* Show fallback indicator if data is from fallback */}
        {data?.lineChartData && (data as unknown)?.isFallback && (
          <div className="text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-md border border-amber-200">
            ⚠️ Showing total traffic data as{" "}
            {activeDomesticFilter.toLowerCase()} data is unavailable
          </div>
        )}
      </div>

      {/* Card Tabs Section */}
      <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap">
        {isLoading ? (
          <motion.div
            className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
            key={"loading"}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            {Array.from({ length: 7 }).map((_, i) => (
              <CardTab key={i} isLoading />
            ))}
          </motion.div>
        ) : (
          data?.cardTabs && (
            <motion.div
              className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
              key={"data"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              {data.cardTabs.map(
                (
                  {
                    title,
                    changeValue,
                    value,
                  }: { title: string; changeValue: string; value: string },
                  index: number
                ) => (
                  <CardTab
                    key={index}
                    title={title}
                    value={value}
                    changeValue={changeValue}
                    className={`border-2`}
                    style={
                      activeTab === title
                        ? { borderColor: themeColor }
                        : { borderColor: "transparent" }
                    }
                    onSelect={() => setActiveTab(title)}
                  />
                )
              )}
            </motion.div>
          )
        )}
      </div>

      {/* Chart and Progress Bars Section */}
      <div className="flex flex-col-reverse lg:flex-row items-center lg:items-start gap-y-16 min-h-[310px]">
        {/* Line Chart */}
        <div className="w-full">
          {isLoading ? (
            <motion.div
              key={"loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              <LineChartSkeleton />
            </motion.div>
          ) : (
            data && (
              <motion.div
                key={"data"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <GSCLineChart
                  lineChartData={data.lineChartData}
                  colors={data.colors}
                  selectedLines={data.selectedLines}
                  cardsData={data.cardsData}
                  onHover={(date) => setHoveredDate(date || undefined)}
                />
              </motion.div>
            )
          )}
        </div>

        {/* Progress Bars */}
        <div className="w-full lg:w-[40%] h-[310px] max-h-[310px] overflow-y-auto">
          <div className="space-y-2 pr-2">
            {isLoading
              ? Array.from({ length: 5 }).map((_, index) => (
                  <ProgressBar
                    key={`loading-${index}`}
                    isLoading={true}
                    percentage={0}
                    title="Loading..."
                    color=""
                  />
                ))
              : data && (
                  <>
                    <div className="text-sm font-semibold text-gray-700 mb-3 text-center">
                      {hoveredDate
                        ? `Traffic breakdown for ${hoveredDate}`
                        : "Traffic Breakdown Total"}
                    </div>
                    {currentProgressbarData.map((item, index) => {
                      const hasComparison =
                        isComparisonEnabled &&
                        item.comparisonPercentage !== undefined &&
                        item.comparisonPercentage !== null;

                      return (
                        <ProgressBar
                          key={`${item.title}-${index}-${
                            hoveredDate || "total"
                          }`}
                          isLoading={false}
                          percentage={item.percentage}
                          title={item.title}
                          color={hasComparison ? "" : "#914AC4"} // Use default purple when no comparison
                          comparisonPercentage={
                            isComparisonEnabled
                              ? item.comparisonPercentage
                              : undefined
                          }
                          isComparisonEnabled={hasComparison}
                        />
                      );
                    })}
                  </>
                )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default TrafficChartAndBars;
