// Color constants for chart visualization
export const CHART_COLORS = {
  CURRENT_PERIOD: "bg-[#914AC4]", // Purple for current period
  PREVIOUS_PERIOD: "bg-[#FFCD29]", // Yellow for previous period
} as const;

// Filter types
export const FILTER_TYPES = {
  ALL_PAGES: "All Pages",
  LANDING_PAGES: "Landing Pages",
  HIGH_TRAFFIC: "High Traffic Pages",
} as const;

// Metric tab configurations (same for both All Pages and Landing Pages APIs)
export const METRIC_TABS = [
  { key: "views", title: "Views" },
  { key: "sessions", title: "Sessions" },
  { key: "new_users", title: "New Users" },
  { key: "total_users", title: "Total Users" },
  { key: "returning_users", title: "Returning Users" },
  { key: "engaged_sessions", title: "Engaged Sessions" },
  { key: "avg_engagement_time", title: "Avg Engagement Time" },
] as const;

// Default values
export const DEFAULTS = {
  ACTIVE_TAB: "Views",
  ACTIVE_FILTER: FILTER_TYPES.ALL_PAGES,
  CHANGE_VALUE: "+0%",
  MIN_MAX_VALUE: 1, // Minimum value to avoid division by zero
} as const;

// Query configuration
export const QUERY_CONFIG = {
  STALE_TIME: 5 * 60 * 1000, // 5 minutes
  GC_TIME: 10 * 60 * 1000, // 10 minutes
} as const;
