import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { useProjectId } from "@/hooks/useProjectId";
import type {
  LandingPagesApiResponse,
  AllPagesApiResponse,
  HighTrafficApiResponse,
  TransformedPagesData,
} from "./PagesBars.types";
import {
  transformLandingPagesData,
  transformAllPagesData,
  transformHighTrafficData,
} from "./utils/transformPagesData";

// Raw data type for storing API responses
type RawPagesData = {
  landingPagesData?: LandingPagesApiResponse;
  landingPagesComparisonData?: LandingPagesApiResponse | null;
  allPagesData?: AllPagesApiResponse;
  allPagesComparisonData?: AllPagesApiResponse | null;
  highTrafficData?: HighTrafficApiResponse;
  highTrafficComparisonData?: HighTrafficApiResponse | null;
};

const usePagesBars = ({ tab, filter }: { tab: string; filter: string }) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  const query = useQuery({
    queryKey: [
      "pages-bars",
      projectId,
      getFormattedDates(),
      isComparisonEnabled,
      filter,
    ],
    queryFn: async (): Promise<RawPagesData> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      const result: RawPagesData = {};

      // Determine which API to call based on filter
      const isLandingPagesFilter = filter === "Landing Pages";
      const isHighTrafficFilter = filter === "High Traffic Pages";

      if (isLandingPagesFilter) {
        // Fetch Landing Pages data
        try {
          const landingPagesResponse = await httpService.get(
            `/api/project/GA4/page/overview/landing-pages/${projectId}/`,
            {
              params: {
                start_date: startDate,
                end_date: endDate,
              },
              useAuth: true,
            }
          );
          result.landingPagesData =
            landingPagesResponse.data as LandingPagesApiResponse;

          // Fetch Landing Pages comparison data if comparison is enabled
          if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
            try {
              const comparisonResponse = await httpService.get(
                `/api/project/GA4/page/overview/landing-pages/${projectId}/`,
                {
                  params: {
                    start_date: comparisonStartDate,
                    end_date: comparisonEndDate,
                  },
                  useAuth: true,
                }
              );
              result.landingPagesComparisonData =
                comparisonResponse.data as LandingPagesApiResponse;
            } catch (error) {
              console.warn(
                "Failed to fetch Landing Pages comparison data:",
                error
              );
              result.landingPagesComparisonData = null;
            }
          }
        } catch (error) {
          console.warn("Failed to fetch Landing Pages data:", error);
        }
      } else if (isHighTrafficFilter) {
        // Fetch High Traffic data
        try {
          const highTrafficResponse = await httpService.get(
            `/api/project/GA4/page/overview/high-traffic/${projectId}/`,
            {
              params: {
                start_date: startDate,
                end_date: endDate,
              },
              useAuth: true,
            }
          );
          result.highTrafficData =
            highTrafficResponse.data as HighTrafficApiResponse;

          // Fetch High Traffic comparison data if comparison is enabled
          if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
            try {
              const comparisonResponse = await httpService.get(
                `/api/project/GA4/page/overview/high-traffic/${projectId}/`,
                {
                  params: {
                    start_date: comparisonStartDate,
                    end_date: comparisonEndDate,
                  },
                  useAuth: true,
                }
              );
              result.highTrafficComparisonData =
                comparisonResponse.data as HighTrafficApiResponse;
            } catch (error) {
              console.warn(
                "Failed to fetch High Traffic comparison data:",
                error
              );
              result.highTrafficComparisonData = null;
            }
          }
        } catch (error) {
          console.warn("Failed to fetch High Traffic data:", error);
        }
      } else {
        // Fetch All Pages data (default for "All Pages")
        try {
          const allPagesResponse = await httpService.get(
            `/api/project/GA4/page/overview/all-pages/${projectId}/`,
            {
              params: {
                start_date: startDate,
                end_date: endDate,
              },
              useAuth: true,
            }
          );
          result.allPagesData = allPagesResponse.data as AllPagesApiResponse;

          // Fetch All Pages comparison data if comparison is enabled
          if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
            try {
              const comparisonResponse = await httpService.get(
                `/api/project/GA4/page/overview/all-pages/${projectId}/`,
                {
                  params: {
                    start_date: comparisonStartDate,
                    end_date: comparisonEndDate,
                  },
                  useAuth: true,
                }
              );
              result.allPagesComparisonData =
                comparisonResponse.data as AllPagesApiResponse;
            } catch (error) {
              console.warn("Failed to fetch All Pages comparison data:", error);
              result.allPagesComparisonData = null;
            }
          }
        } catch (error) {
          console.warn("Failed to fetch All Pages data:", error);
        }
      }

      return result;
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Transform the raw data for the specific tab and filter
  const transformDataForTab = (): TransformedPagesData | undefined => {
    if (!query.data) return undefined;

    const activeTab = tab || "Views";
    const isLandingPagesFilter = filter === "Landing Pages";
    const isHighTrafficFilter = filter === "High Traffic Pages";

    // Use the appropriate data based on filter selection
    if (isLandingPagesFilter && query.data.landingPagesData) {
      return transformLandingPagesData(
        query.data.landingPagesData,
        activeTab,
        query.data.landingPagesComparisonData,
        filter
      );
    } else if (isHighTrafficFilter && query.data.highTrafficData) {
      return transformHighTrafficData(
        query.data.highTrafficData,
        activeTab,
        query.data.highTrafficComparisonData,
        filter
      );
    } else if (
      !isLandingPagesFilter &&
      !isHighTrafficFilter &&
      query.data.allPagesData
    ) {
      return transformAllPagesData(
        query.data.allPagesData,
        activeTab,
        query.data.allPagesComparisonData,
        filter
      );
    }

    return undefined;
  };

  return {
    ...query,
    data: transformDataForTab(),
  };
};

export default usePagesBars;
