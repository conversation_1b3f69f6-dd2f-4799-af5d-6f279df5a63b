import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { trafficService } from "../../services/trafficService";
import {
  transformTrafficData,
  getTrafficSourceEndpoint,
  getMetricKeyFromDisplayName,
} from "../../utils/transformTrafficData";
import type {
  TransformedTrafficData,
  GA4TrafficResponse,
} from "../../types/TrafficData.types";

// Hook to fetch raw traffic data with comparison support
const useTrafficData = (domesticFilter: string) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API calls
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "traffic-data",
      projectId,
      domesticFilter,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<{
      primaryData: GA4TrafficResponse;
      comparisonData: GA4TrafficResponse | null;
    }> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Get the appropriate traffic source endpoint
      const trafficSource = getTrafficSourceEndpoint(domesticFilter);

      // Helper function to call the appropriate service method
      const callTrafficService = (
        source: string,
        start: string,
        end: string
      ) => {
        switch (source) {
          case "paid":
            return trafficService.getPaidTraffic(projectId, start, end);
          case "referral":
            return trafficService.getReferralTraffic(projectId, start, end);
          case "social":
            return trafficService.getSocialTraffic(projectId, start, end);
          case "organic":
            return trafficService.getOrganicTraffic(projectId, start, end);
          default:
            return trafficService.getTotalTraffic(projectId, start, end);
        }
      };

      let primaryResponse;
      let comparisonResponse = null;

      try {
        // Fetch primary data
        primaryResponse = await callTrafficService(
          trafficSource,
          startDate,
          endDate
        );
      } catch (error) {
        console.warn(`Failed to fetch ${trafficSource} traffic data:`, error);

        // If the specific traffic source fails, try to fall back to total traffic
        if (trafficSource !== "total") {
          console.log("Falling back to total traffic data...");
          try {
            primaryResponse = await trafficService.getTotalTraffic(
              projectId,
              startDate,
              endDate
            );
            // Add a flag to indicate fallback was used
            primaryResponse.data.isFallback = true;
            primaryResponse.data.originalSource = trafficSource;
          } catch (fallbackError) {
            console.error(
              "Failed to fetch fallback total traffic data:",
              fallbackError
            );
            throw new Error(
              `Failed to fetch traffic data for ${trafficSource} and total traffic fallback`
            );
          }
        } else {
          throw error;
        }
      }

      // Fetch comparison data if comparison is enabled
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          comparisonResponse = await callTrafficService(
            trafficSource,
            comparisonStartDate,
            comparisonEndDate
          );
        } catch (error) {
          console.warn("Failed to fetch comparison data:", error);
          // Try fallback to total traffic for comparison as well
          if (trafficSource !== "total") {
            try {
              comparisonResponse = await trafficService.getTotalTraffic(
                projectId,
                comparisonStartDate,
                comparisonEndDate
              );
              // Add fallback flag for comparison data too
              if (comparisonResponse) {
                comparisonResponse.data.isFallback = true;
                comparisonResponse.data.originalSource = trafficSource;
              }
            } catch (fallbackError) {
              console.warn(
                "Failed to fetch comparison fallback data:",
                fallbackError
              );
              // Continue without comparison data rather than failing completely
            }
          }
        }
      }

      const result = {
        primaryData: primaryResponse.data,
        comparisonData: comparisonResponse?.data || null,
      };

      return result;
    },
    enabled: isValidProjectId && !!projectId && !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Main hook that transforms data client-side (no API refetch on tab/hover changes)
const useTrafficChartAndBars = ({
  tab,
  domesticFilter,
  hoveredDate,
}: {
  tab: string;
  domesticFilter: string;
  hoveredDate?: string;
}) => {
  // Fetch raw data only when filter or date range changes
  const {
    data: rawData,
    isLoading,
    isError,
    error,
  } = useTrafficData(domesticFilter);

  // Transform data client-side when tab or hoveredDate changes
  const transformedData = useMemo((): TransformedTrafficData | undefined => {
    if (!rawData?.primaryData) {
      return undefined;
    }

    // Use default metric if no tab is selected yet
    const selectedMetric = tab ? getMetricKeyFromDisplayName(tab) : "all_users";

    return transformTrafficData(
      rawData.primaryData,
      selectedMetric,
      hoveredDate,
      rawData.comparisonData
    );
  }, [rawData, tab, hoveredDate]);

  return {
    data: transformedData,
    isLoading,
    isError,
    error,
  };
};

export default useTrafficChartAndBars;
