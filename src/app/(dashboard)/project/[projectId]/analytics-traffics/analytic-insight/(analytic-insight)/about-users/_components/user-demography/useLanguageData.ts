import React from "react";
import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { TableDataRequest } from "../../../../../_components/data-table/DataTable.types";

// Types for the API response
interface LanguageData {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

interface LanguageApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: LanguageData[];
    daily_metrics: any[];
  };
}

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// Helper function to format engagement time
const formatEngagementTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

// Helper function to format percentage
const formatPercentage = (rate: number): string => {
  return `${(rate * 100).toFixed(1)}%`;
};

// First, create a hook that fetches all the data
const useLanguageDataRaw = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: ["language-data", projectId, getFormattedDates()],
    queryFn: async () => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } = await httpService.get(
        `/api/project/GA4/user/demographic/detailed-language/${projectId}?start_date=${startDate}&end_date=${endDate}`,
        { useAuth: true }
      );

      let comparisonData: LanguageApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get(
            `/api/project/GA4/user/demographic/detailed-language/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison data:", error);
        }
      }

      // Create a map of comparison data for easy lookup
      const comparisonMap = new Map<string, LanguageData>();
      if (comparisonData?.data?.dimension_breakdown) {
        comparisonData.data.dimension_breakdown.forEach((language) => {
          comparisonMap.set(language.name, language);
        });
      }

      // Transform data for the table
      const tableHeadings = [
        "LANGUAGE",
        "TOTAL USERS",
        "NEW USERS",
        "SESSIONS",
        "ENGAGED SESSIONS",
        "VIEWS",
        "ENGAGEMENT RATE",
        "EVENT COUNT",
        "CONVERSIONS",
        "PERCENTAGE",
      ];

      // Transform all data (no pagination here)
      const allLanguageData = currentData.data.dimension_breakdown;

      const tableBody = allLanguageData.map((language: LanguageData) => {
        const comparisonLanguage = comparisonMap.get(language.name);

        return [
          { value: language.name },
          {
            value: formatNumber(language.total_users),
            growth: comparisonLanguage
              ? calculatePercentageChange(
                  language.total_users,
                  comparisonLanguage.total_users
                )
              : undefined,
          },
          {
            value: formatNumber(language.new_users),
            growth: comparisonLanguage
              ? calculatePercentageChange(
                  language.new_users,
                  comparisonLanguage.new_users
                )
              : undefined,
          },
          {
            value: formatNumber(language.sessions),
            growth: comparisonLanguage
              ? calculatePercentageChange(
                  language.sessions,
                  comparisonLanguage.sessions
                )
              : undefined,
          },
          {
            value: formatNumber(language.engaged_sessions),
            growth: comparisonLanguage
              ? calculatePercentageChange(
                  language.engaged_sessions,
                  comparisonLanguage.engaged_sessions
                )
              : undefined,
          },
          {
            value: formatNumber(language.views),
            growth: comparisonLanguage
              ? calculatePercentageChange(
                  language.views,
                  comparisonLanguage.views
                )
              : undefined,
          },
          {
            value: formatPercentage(language.engagement_rate),
            growth: comparisonLanguage
              ? calculatePercentageChange(
                  language.engagement_rate,
                  comparisonLanguage.engagement_rate
                )
              : undefined,
          },
          {
            value: formatNumber(language.event_count),
            growth: comparisonLanguage
              ? calculatePercentageChange(
                  language.event_count,
                  comparisonLanguage.event_count
                )
              : undefined,
          },
          {
            value: formatNumber(language.conversions),
            growth: comparisonLanguage
              ? calculatePercentageChange(
                  language.conversions,
                  comparisonLanguage.conversions
                )
              : undefined,
          },
          {
            value: `${language.percentage.toFixed(1)}%`,
            growth: comparisonLanguage
              ? calculatePercentageChange(
                  language.percentage,
                  comparisonLanguage.percentage
                )
              : undefined,
          },
        ];
      });

      // Calculate pagination
      const itemsPerPage = 5;
      const totalPages = Math.ceil(allLanguageData.length / itemsPerPage);

      return {
        tableHeadings,
        allTableBody: tableBody,
        totalPages,
        allData: allLanguageData,
        itemsPerPage,
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Main hook that handles pagination
const useLanguageData = ({ page }: { page: number }) => {
  const rawDataQuery = useLanguageDataRaw();

  // Handle pagination on the client side
  const paginatedData = React.useMemo(() => {
    if (!rawDataQuery.data) return null;

    const { allTableBody, tableHeadings, totalPages, itemsPerPage } =
      rawDataQuery.data;

    // Calculate pagination
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedTableBody = allTableBody.slice(startIndex, endIndex);

    return {
      tableData: {
        tableHeadings,
        tableBody: paginatedTableBody,
      },
      pagination: {
        totalPages,
        initialPage: page,
      },
    };
  }, [rawDataQuery.data, page]);

  return {
    ...rawDataQuery,
    data: paginatedData,
  };
};

export default useLanguageData;
