import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";

// Types for tech browser table data
interface TechBrowserTableItem {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

interface TechBrowserTableData {
  tableData: {
    tableHeadings: string[];
    tableBody: Array<Array<{ value: string; growth?: string }>>;
  };
  pagination: {
    totalPages: number;
    currentPage: number;
    totalItems: number;
  };
}

interface TechBrowserApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: TechBrowserTableItem[];
    daily_metrics: Array<{
      date: string;
      dimensions: TechBrowserTableItem[];
    }>;
  };
}

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// Helper function to format percentage
const formatPercentage = (rate: number): string => {
  return `${(rate * 100).toFixed(1)}%`;
};

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

/**
 * Hook to fetch tech browser table data
 */
const useTechBrowserTableData = ({ page }: { page: number }) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: ["tech-browser-table-data", projectId, page, getFormattedDates()],
    queryFn: async (): Promise<TechBrowserTableData> => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } = await httpService.get<TechBrowserApiResponse>(
        `/api/project/GA4/user/tech/browser/${projectId}?start_date=${startDate}&end_date=${endDate}`,
        { useAuth: true }
      );

      let comparisonData: TechBrowserApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get<TechBrowserApiResponse>(
            `/api/project/GA4/user/tech/browser/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison data:", error);
        }
      }

      // Create a map of comparison data for easy lookup
      const comparisonMap = new Map<string, TechBrowserTableItem>();
      if (comparisonData?.data?.dimension_breakdown) {
        comparisonData.data.dimension_breakdown.forEach((browser) => {
          comparisonMap.set(browser.name, browser);
        });
      }

      // Transform API data to table format
      const allBrowserData = currentData.data.dimension_breakdown;

      // Table headings
      const tableHeadings = [
        "BROWSER",
        "TOTAL USERS",
        "NEW USERS",
        "SESSIONS",
        "ENGAGED SESSIONS",
        "VIEWS",
        "ENGAGEMENT RATE",
        "EVENT COUNT",
        "CONVERSIONS",
        "PERCENTAGE",
      ];

      // Transform data to table body format with comparison
      const allTableBody = allBrowserData.map((browser) => {
        const comparisonBrowser = comparisonMap.get(browser.name);

        return [
          { value: browser.name },
          {
            value: formatNumber(browser.total_users),
            growth: comparisonBrowser
              ? calculatePercentageChange(
                  browser.total_users,
                  comparisonBrowser.total_users
                )
              : undefined,
          },
          {
            value: formatNumber(browser.new_users),
            growth: comparisonBrowser
              ? calculatePercentageChange(
                  browser.new_users,
                  comparisonBrowser.new_users
                )
              : undefined,
          },
          {
            value: formatNumber(browser.sessions),
            growth: comparisonBrowser
              ? calculatePercentageChange(
                  browser.sessions,
                  comparisonBrowser.sessions
                )
              : undefined,
          },
          {
            value: formatNumber(browser.engaged_sessions),
            growth: comparisonBrowser
              ? calculatePercentageChange(
                  browser.engaged_sessions,
                  comparisonBrowser.engaged_sessions
                )
              : undefined,
          },
          {
            value: formatNumber(browser.views),
            growth: comparisonBrowser
              ? calculatePercentageChange(
                  browser.views,
                  comparisonBrowser.views
                )
              : undefined,
          },
          {
            value: formatPercentage(browser.engagement_rate),
            growth: comparisonBrowser
              ? calculatePercentageChange(
                  browser.engagement_rate,
                  comparisonBrowser.engagement_rate
                )
              : undefined,
          },
          {
            value: formatNumber(browser.event_count),
            growth: comparisonBrowser
              ? calculatePercentageChange(
                  browser.event_count,
                  comparisonBrowser.event_count
                )
              : undefined,
          },
          {
            value: formatNumber(browser.conversions),
            growth: comparisonBrowser
              ? calculatePercentageChange(
                  browser.conversions,
                  comparisonBrowser.conversions
                )
              : undefined,
          },
          {
            value: `${browser.percentage.toFixed(1)}%`,
            growth: comparisonBrowser
              ? calculatePercentageChange(
                  browser.percentage,
                  comparisonBrowser.percentage
                )
              : undefined,
          },
        ];
      });

      // Simple pagination - show 5 items per page
      const itemsPerPage = 5;
      const totalItems = allBrowserData.length;
      const totalPages = Math.ceil(totalItems / itemsPerPage);
      const startIndex = (page - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedTableBody = allTableBody.slice(startIndex, endIndex);

      return {
        tableData: {
          tableHeadings,
          tableBody: paginatedTableBody,
        },
        pagination: {
          totalPages,
          currentPage: page,
          totalItems,
        },
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export default useTechBrowserTableData;