"use client";
import React, { useEffect, useMemo, useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../../../_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import GSCLineChart from "../../../../_components/line-chart/LineChart";
import ProgressBar from "../../../../../overview/(overview)/users-overview/_components/ProgressBar";
 
import NoData from "../../../../_components/NoData";
import ErrorDisplay from "../../../../_components/ErrorDisplay";
import Dropdown from "@/components/ui/Dropdown";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";
import useChartAndBarsData from "./ChartAndBars.hook";
import useDailyCountriesData from "./useDailyCountriesData";
import useDailyAgeData from "./useDailyAgeData";
import useDailyCitiesData from "./useDailyCitiesData";
import useDailyLanguageData from "./useDailyLanguageData";
import useDailyGenderData from "./useDailyGenderData";
import useTechChartAndBarsData from "./hooks/useTechChartAndBarsData";
import useDailyTechDeviceData from "./hooks/useDailyTechDeviceData";
import useDailyTechOSData from "./hooks/useDailyTechOSData";
import useDailyTechAppVersionData from "./hooks/useDailyTechAppVersionData";
import useDailyTechBrowserData from "./hooks/useDailyTechBrowserData";
import { useDateRangeStore } from "@/store/useDateRangeStore";
 

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion"; 

/* ================================== TYPES ================================= */
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import { calculateGrowth, formatDate, formatValue } from "../../../audience/Audience.utils";
import LineChartSkeleton from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/line-chart-skeleton/LineChartSkeleton";
import { useProjectContext } from "@/contexts/ProjectContext";

// Type for progress bar data with comparison support
interface ProgressBarDataItem {
  title: string;
  percentage: number;
  comparisonPercentage?: number;
  value?: number;
  comparisonValue?: number;
  growth?: string;
}

/* ========================================================================== */
type ChartAndBarsProps = {
  activeDomesticFilter: string;
  onChangeDomesticFilter: (value: string) => void;
};

const ChartAndBars: React.FC<ChartAndBarsProps> = ({
  activeDomesticFilter,
  onChangeDomesticFilter,
}) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState("");
  const { themeColor } = useAppThemeColor();
  const { isComparisonEnabled } = useDateRangeStore();
  const { projectName } = useProjectContext();

  // Local state for filter management
  const demographicFilters = useMemo(
    () => ["Countries", "Age", "Cities", "Language", "Gender"],
    []
  );
  const techFilters = useMemo(
    () => ["Device", "OS", "App version", "Browser"],
    []
  );
  const [activeTabFilter, setActiveTabFilter] = useState(demographicFilters[0]);

  // const domesticInfoFilters = ["Demographic info ", "Tech info "];

  // Determine if we're in tech mode
  const isTechMode = activeDomesticFilter.trim() === "Tech info";
  const currentFilters = isTechMode ? techFilters : demographicFilters;

  // State for tracking hovered date
  const [hoveredDate, setHoveredDate] = useState<string | null>(null);

  // Chart state derived from selected filter daily data
  const [chartCardTabs, setChartCardTabs] = useState<CardTabType[] | null>(null);
  const [chartLineData, setChartLineData] = useState<Record<string, string | number>[]>([]);
  const [chartColors, setChartColors] = useState<{ name: string; color: string }[]>([]);
  const [chartSelectedLines, setChartSelectedLines] = useState<string[]>([]);
  const [chartCardsData, setChartCardsData] = useState<Record<string, { amount: number; growth: string }>>({});
  const [chartError, setChartError] = useState<unknown>(null);
  const [isChartLoading, setIsChartLoading] = useState<boolean>(true);

  // Use custom hook for progress bar data based on selected filter and mode
  const {
    progressBarData: demographicProgressBarData,
    isLoading: isDemographicProgressBarLoading,
    error: demographicProgressBarError,
    hasData: hasDemographicProgressBarData,
  } = useChartAndBarsData(isTechMode ? "" : activeTabFilter);

  const {
    progressBarData: techProgressBarData,
    isLoading: isTechProgressBarLoading,
    error: techProgressBarError,
    hasData: hasTechProgressBarData,
  } = useTechChartAndBarsData(isTechMode ? activeTabFilter : "");

  // Use the appropriate data based on mode
  const progressBarData = isTechMode
    ? techProgressBarData
    : demographicProgressBarData;
  const isProgressBarLoading = isTechMode
    ? isTechProgressBarLoading
    : isDemographicProgressBarLoading;
  const progressBarError = isTechMode
    ? techProgressBarError
    : demographicProgressBarError;
  const hasProgressBarData = isTechMode
    ? hasTechProgressBarData
    : hasDemographicProgressBarData;

  // Determine which filter is active (demographic filters)
  const isCountriesFilter =
    !isTechMode && activeTabFilter.toLowerCase() === "countries";
  const isAgeFilter = !isTechMode && activeTabFilter.toLowerCase() === "age";
  const isCitiesFilter =
    !isTechMode && activeTabFilter.toLowerCase() === "cities";
  const isLanguageFilter =
    !isTechMode && activeTabFilter.toLowerCase() === "language";
  const isGenderFilter =
    !isTechMode && activeTabFilter.toLowerCase() === "gender";

  // Determine which tech filter is active
  const isTechDeviceFilter =
    isTechMode && activeTabFilter.toLowerCase() === "device";
  const isTechOSFilter = isTechMode && activeTabFilter.toLowerCase() === "os";
  const isTechAppVersionFilter =
    isTechMode && activeTabFilter.toLowerCase() === "app version";
  const isTechBrowserFilter =
    isTechMode && activeTabFilter.toLowerCase() === "browser";

  // Fetch daily data for each filter
  const {
    data: dailyCountriesData,
    isLoading: isDailyCountriesLoading,
    error: dailyCountriesError,
  } = useDailyCountriesData(isCountriesFilter);

  const {
    data: dailyAgeData,
    isLoading: isDailyAgeLoading,
    error: dailyAgeError,
  } = useDailyAgeData(isAgeFilter);

  const {
    data: dailyCitiesData,
    isLoading: isDailyCitiesLoading,
    error: dailyCitiesError,
  } = useDailyCitiesData(isCitiesFilter);

  const {
    data: dailyLanguageData,
    isLoading: isDailyLanguageLoading,
    error: dailyLanguageError,
  } = useDailyLanguageData(isLanguageFilter);

  const {
    data: dailyGenderData,
    isLoading: isDailyGenderLoading,
    error: dailyGenderError,
  } = useDailyGenderData(isGenderFilter);

  // Fetch daily data for tech filters
  const {
    data: dailyTechDeviceData,
    isLoading: isDailyTechDeviceLoading,
    error: dailyTechDeviceError,
  } = useDailyTechDeviceData(isTechDeviceFilter);

  const {
    data: dailyTechOSData,
    isLoading: isDailyTechOSLoading,
    error: dailyTechOSError,
  } = useDailyTechOSData(isTechOSFilter);

  const {
    data: dailyTechAppVersionData,
    isLoading: isDailyTechAppVersionLoading,
    error: dailyTechAppVersionError,
  } = useDailyTechAppVersionData(isTechAppVersionFilter);

  const {
    data: dailyTechBrowserData,
    isLoading: isDailyTechBrowserLoading,
    error: dailyTechBrowserError,
  } = useDailyTechBrowserData(isTechBrowserFilter);

  // Helper: map active tab title to metric key in daily data
  const metricKeyForTab = (tabTitle: string): string => {
    const key = tabTitle.toLowerCase();
    if (key.includes("total")) return "total_users";
    if (key.includes("new")) return "new_users";
    if (key.includes("returning")) return "returning_users";
    if (key.includes("active")) return "active_users";
    if (key.includes("engaged") && key.includes("sessions")) return "engaged_sessions";
    if (key === "sessions" || key.includes("sessions")) return "sessions";
    if (key.includes("views")) return "views";
    if (key.includes("avg") || key.includes("engagement time")) return "avg_engagement_time";
    return "total_users";
  };

  // Determine currently selected daily dataset based on active filter
  const activeDailyDataset = useMemo(() => {
    if (isCountriesFilter) return dailyCountriesData;
    if (isAgeFilter) return dailyAgeData;
    if (isCitiesFilter) return dailyCitiesData;
    if (isLanguageFilter) return dailyLanguageData;
    if (isGenderFilter) return dailyGenderData;
    if (isTechDeviceFilter) return dailyTechDeviceData;
    if (isTechOSFilter) return dailyTechOSData;
    if (isTechAppVersionFilter) return dailyTechAppVersionData;
    if (isTechBrowserFilter) return dailyTechBrowserData;
    return null;
  }, [
    isCountriesFilter,
    isAgeFilter,
    isCitiesFilter,
    isLanguageFilter,
    isGenderFilter,
    isTechDeviceFilter,
    isTechOSFilter,
    isTechAppVersionFilter,
    isTechBrowserFilter,
    dailyCountriesData,
    dailyAgeData,
    dailyCitiesData,
    dailyLanguageData,
    dailyGenderData,
    dailyTechDeviceData,
    dailyTechOSData,
    dailyTechAppVersionData,
    dailyTechBrowserData,
  ]);

  const activeDailyLoading =
    (isCountriesFilter && isDailyCountriesLoading) ||
    (isAgeFilter && isDailyAgeLoading) ||
    (isCitiesFilter && isDailyCitiesLoading) ||
    (isLanguageFilter && isDailyLanguageLoading) ||
    (isGenderFilter && isDailyGenderLoading) ||
    (isTechDeviceFilter && isDailyTechDeviceLoading) ||
    (isTechOSFilter && isDailyTechOSLoading) ||
    (isTechAppVersionFilter && isDailyTechAppVersionLoading) ||
    (isTechBrowserFilter && isDailyTechBrowserLoading);

  const activeDailyError =
    (isCountriesFilter && dailyCountriesError) ||
    (isAgeFilter && dailyAgeError) ||
    (isCitiesFilter && dailyCitiesError) ||
    (isLanguageFilter && dailyLanguageError) ||
    (isGenderFilter && dailyGenderError) ||
    (isTechDeviceFilter && dailyTechDeviceError) ||
    (isTechOSFilter && dailyTechOSError) ||
    (isTechAppVersionFilter && dailyTechAppVersionError) ||
    (isTechBrowserFilter && dailyTechBrowserError) ||
    null;

  // Build card tabs and chart series whenever dataset or tab changes
  useEffect(() => {
    setIsChartLoading(true);
    setChartError(null);

    if (!activeDailyDataset || activeDailyLoading || activeDailyError) {
      setChartCardTabs(null);
      setChartLineData([]);
      setChartColors([]);
      setChartSelectedLines([]);
      setChartCardsData({});
      setIsChartLoading(!!activeDailyLoading);
      setChartError(activeDailyError);
      return;
    }

    const current = activeDailyDataset.currentPeriod || [];
    const comparison = activeDailyDataset.comparisonPeriod || null;

    // Normalize to indexable records
    const currentMetrics = current as unknown as Array<Record<string, number | string>>;
    const comparisonMetrics = comparison as unknown as
      | Array<Record<string, number | string>>
      | null;

    const getMetricValue = (
      obj: Record<string, number | string>,
      key: string
    ): number => {
      const v = obj[key];
      const n = typeof v === "number" ? v : typeof v === "string" ? Number(v) : 0;
      return Number.isFinite(n) ? n : 0;
    };

    // Utility to sum or average metrics across the period
    const sumMetric = (
      metrics: Array<Record<string, number | string>>,
      key: string
    ) => metrics.reduce((acc, item) => acc + getMetricValue(item, key), 0);
    const avgMetric = (
      metrics: Array<Record<string, number | string>>,
      key: string
    ) => (metrics.length === 0 ? 0 : sumMetric(metrics, key) / metrics.length);

    const buildTabs = (): CardTabType[] => {
      const definitions = [
        { title: "Total Users", key: "total_users" },
        { title: "New Users", key: "new_users" },
        { title: "Returning Users", key: "returning_users" },
        { title: "Active Users", key: "active_users" },
        { title: "Sessions", key: "sessions" },
        { title: "Engaged Sessions", key: "engaged_sessions" },
        { title: "Views", key: "views" },
        { title: "Avg. Engagement Time", key: "avg_engagement_time", isTime: true },
      ] as const;

      return definitions.map((def) => {
        const primaryValue =
          def.key === "avg_engagement_time"
            ? avgMetric(currentMetrics, def.key)
            : sumMetric(currentMetrics, def.key);
        const comparisonValue = comparisonMetrics
          ? def.key === "avg_engagement_time"
            ? avgMetric(comparisonMetrics, def.key)
            : sumMetric(comparisonMetrics, def.key)
          : 0;

        return {
          title: def.title,
          value: formatValue(primaryValue, def.key === "avg_engagement_time" ? "time" : "number"),
          changeValue: comparison ? calculateGrowth(primaryValue, comparisonValue) : "",
        };
      });
    };

    const tabs = buildTabs();
    setChartCardTabs(tabs);

    // Initialize active tab on first load
    if (!activeTab && tabs.length > 0) {
      setActiveTab(tabs[0].title);
    }

    // Build line chart series for the selected tab
    const displayTitle = activeTab || tabs[0]?.title || "Total Users";
    const metricKey = metricKeyForTab(displayTitle);
    const primaryKey = displayTitle;
    const comparisonKey = `comparison_${displayTitle}`;

    // Map comparison by date for accurate alignment
    const comparisonByDate = new Map<string, Record<string, number | string>>();
    if (comparisonMetrics) {
      comparisonMetrics.forEach((m) => {
        const key = String(m.date);
        comparisonByDate.set(key, m);
      });
    }

    const series = currentMetrics.map((m, idx) => {
      const key = String(m.date);
      // Prefer date alignment; fallback to index alignment when date doesn't exist in comparison
      const comp = comparisonByDate.get(key) ?? (comparisonMetrics ? comparisonMetrics[idx] : undefined);
      const primaryValue = getMetricValue(m, metricKey);
      const compValue = comp ? getMetricValue(comp, metricKey) : 0;
      const row: Record<string, string | number> = {
        name: formatDate(String(m.date)),
        [primaryKey]: Number(primaryValue),
      };
      if (comparisonMetrics) {
        row[comparisonKey] = Number(compValue);
      }
      return row;
    });

    const colors = [
      { name: primaryKey, color: "#914AC4" },
      ...(comparisonMetrics ? [{ name: comparisonKey, color: "#FFCD29" }] : []),
    ];

    const selectedLines = comparisonMetrics
      ? [primaryKey, comparisonKey]
      : [primaryKey];

    // Cards data for tooltip header
    const primaryTotal =
      metricKey === "avg_engagement_time"
        ? avgMetric(currentMetrics, metricKey)
        : sumMetric(currentMetrics, metricKey);
    const comparisonTotal = comparisonMetrics
      ? metricKey === "avg_engagement_time"
        ? avgMetric(comparisonMetrics, metricKey)
        : sumMetric(comparisonMetrics, metricKey)
      : 0;

    const cards = {
      [primaryKey]: {
        amount: primaryTotal,
        growth: comparisonMetrics ? calculateGrowth(primaryTotal, comparisonTotal) : "",
      },
      ...(comparisonMetrics
        ? {
            [comparisonKey]: {
              amount: comparisonTotal,
              growth: "",
            },
          }
        : {}),
    } as Record<string, { amount: number; growth: string }>;

    setChartLineData(series);
    setChartColors(colors);
    setChartSelectedLines(selectedLines);
    setChartCardsData(cards);
    setIsChartLoading(false);
  }, [
    activeDailyDataset,
    activeDailyLoading,
    activeDailyError,
    activeTab,
  ]);

  // Reset hovered date when filter changes
  useEffect(() => {
    setHoveredDate(null);
  }, [activeTabFilter]);

  // Reset filter when switching between demographic and tech modes
  useEffect(() => {
    if (isTechMode) {
      setActiveTabFilter(techFilters[0]);
    } else {
      setActiveTabFilter(demographicFilters[0]);
    }
    setHoveredDate(null);
  }, [isTechMode, techFilters, demographicFilters]);

  // Function to convert chart date format to API date format
  const convertChartDateToApiDate = (chartDate: string): string | null => {
    if (!chartDate) return null;

    try {
      // Chart date is in format like "Jan 5" or "Dec 31"
      // We need to convert it to YYYYMMDD format to match API data
      const currentYear = new Date().getFullYear();
      const date = new Date(`${chartDate}, ${currentYear}`);

      if (isNaN(date.getTime())) return null;

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");

      return `${year}${month}${day}`;
    } catch {
      return null;
    }
  };

  // Function to calculate percentage change
  const calculatePercentageChange = (
    current: number,
    previous: number
  ): string => {
    if (previous === 0) {
      return current > 0 ? "+100%" : "0%";
    }
    const change = ((current - previous) / previous) * 100;
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(1)}%`;
  };

  // Generic function to get data for a specific date for any filter
  type DailyDataset = {
    currentPeriod: Array<{
      date: string;
      dimensions?: Array<{ name: string; total_users: number }>;
    }>;
    comparisonPeriod: Array<{
      date: string;
      dimensions?: Array<{ name: string; total_users: number }>;
    }> | null;
  } | null;

  const getDataForDate = (
    date: string | null,
    dailyData: DailyDataset,
    _filterType: string
  ): ProgressBarDataItem[] => {
    if (!date || !dailyData) {
      return [];
    }

    // Convert chart date format to API date format
    const apiDate = convertChartDateToApiDate(date);
    if (!apiDate) return [];

    // Find the daily metric for the hovered date in current period
    const dayData = dailyData.currentPeriod?.find(
      (day) => day.date === apiDate
    );
    if (!dayData || !dayData.dimensions) {
      return [];
    }

    // Find comparison data for the corresponding date (by position, not exact date)
    const currentPeriodIndex = dailyData.currentPeriod.findIndex(
      (day) => day.date === apiDate
    );
    const comparisonDayData =
      currentPeriodIndex >= 0 && dailyData.comparisonPeriod
        ? dailyData.comparisonPeriod[currentPeriodIndex]
        : null;

    // Create a map of comparison data for easy lookup
    const comparisonMap = new Map<string, { name: string; total_users: number }>();
    if (comparisonDayData?.dimensions) {
      comparisonDayData.dimensions.forEach((item) => {
        comparisonMap.set(item.name, item);
      });
    }

    // Calculate total users for percentage calculation (current period)
    const totalUsers = dayData.dimensions.reduce(
      (sum: number, item) => sum + item.total_users,
      0
    );

    // Calculate total users for comparison period
    const comparisonTotalUsers =
      comparisonDayData?.dimensions?.reduce(
        (sum: number, item) => sum + item.total_users,
        0
      ) || 0;

    // Determine the currently selected metric key if possible
    const selectedMetricKey = metricKeyForTab(activeTab || chartCardTabs?.[0]?.title || "Total Users");

    // Totals for selected metric for accurate percentage computations
    const totalForMetricCurrent = dayData.dimensions.reduce((sum: number, dim) => {
      const v = (dim as any)[selectedMetricKey];
      const n = typeof v === "number" ? v : typeof v === "string" ? Number(v) : undefined;
      return sum + (Number.isFinite(n as number) ? (n as number) : dim.total_users || 0);
    }, 0);
    const totalForMetricComparison = comparisonDayData?.dimensions?.reduce((sum: number, dim) => {
      const v = (dim as any)[selectedMetricKey];
      const n = typeof v === "number" ? v : typeof v === "string" ? Number(v) : undefined;
      return sum + (Number.isFinite(n as number) ? (n as number) : dim.total_users || 0);
    }, 0) || 0;

    // Transform data to progress bar format with proper comparison support
    return dayData.dimensions
      .map((item) => {
        const comparisonItem = comparisonMap.get(item.name);

        // Try to use the selected metric for breakdown if it exists on the item; fallback to total_users
        const currentValueForMetric =
          (item as any)[selectedMetricKey] ?? item.total_users ?? 0;
        const comparisonValueForMetric = comparisonItem
          ? (comparisonItem as any)[selectedMetricKey] ?? comparisonItem.total_users ?? 0
          : 0;

        const currentPercentage =
          totalForMetricCurrent > 0
            ? parseFloat(((currentValueForMetric / totalForMetricCurrent) * 100).toFixed(2))
            : 0;

        // Calculate comparison percentage based on the same item in comparison period
        let comparisonPercentage: number | undefined = undefined;
        if (comparisonItem && totalForMetricComparison > 0) {
          comparisonPercentage = parseFloat(
            ((comparisonValueForMetric / totalForMetricComparison) * 100).toFixed(2)
          );
        }

        return {
          title: item.name,
          percentage: currentPercentage,
          comparisonPercentage,
          value: currentValueForMetric,
          comparisonValue: comparisonItem ? comparisonValueForMetric : undefined,
          growth: comparisonItem
            ? calculatePercentageChange(
                currentValueForMetric,
                comparisonValueForMetric
              )
            : undefined,
        };
      })
      .sort((a, b) => b.percentage - a.percentage)
      .slice(0, 10); // Show top 10 items
  };

  // Handle chart hover for all filters
  const handleChartHover = (date: string | null) => {
    // Enable hover functionality for all filters that have daily data
    if (
      isCountriesFilter ||
      isAgeFilter ||
      isCitiesFilter ||
      isLanguageFilter ||
      isGenderFilter ||
      isTechDeviceFilter ||
      isTechOSFilter ||
      isTechAppVersionFilter ||
      isTechBrowserFilter
    ) {
      setHoveredDate(date);
    }
  };

  // Get the appropriate progress bar data
  const getProgressBarData = (): ProgressBarDataItem[] => {
    // Check if we have a hovered date and should show daily data
    if (hoveredDate) {
      let dailyDataForDate: ProgressBarDataItem[] = [];

      // Demographic filters
      if (isCountriesFilter && dailyCountriesData) {
        dailyDataForDate = getDataForDate(
          hoveredDate,
          dailyCountriesData,
          "countries"
        );
      } else if (isAgeFilter && dailyAgeData) {
        dailyDataForDate = getDataForDate(hoveredDate, dailyAgeData, "age");
      } else if (isCitiesFilter && dailyCitiesData) {
        dailyDataForDate = getDataForDate(
          hoveredDate,
          dailyCitiesData,
          "cities"
        );
      } else if (isLanguageFilter && dailyLanguageData) {
        dailyDataForDate = getDataForDate(
          hoveredDate,
          dailyLanguageData,
          "language"
        );
      } else if (isGenderFilter && dailyGenderData) {
        dailyDataForDate = getDataForDate(
          hoveredDate,
          dailyGenderData,
          "gender"
        );
      }
      // Tech filters
      else if (isTechDeviceFilter && dailyTechDeviceData) {
        dailyDataForDate = getDataForDate(
          hoveredDate,
          dailyTechDeviceData,
          "device"
        );
      } else if (isTechOSFilter && dailyTechOSData) {
        dailyDataForDate = getDataForDate(hoveredDate, dailyTechOSData, "os");
      } else if (isTechAppVersionFilter && dailyTechAppVersionData) {
        dailyDataForDate = getDataForDate(
          hoveredDate,
          dailyTechAppVersionData,
          "app version"
        );
      } else if (isTechBrowserFilter && dailyTechBrowserData) {
        dailyDataForDate = getDataForDate(
          hoveredDate,
          dailyTechBrowserData,
          "browser"
        );
      }

      if (dailyDataForDate.length > 0) {
        return dailyDataForDate;
      }
    }

    // Fallback to regular progress bar data
    return Array.isArray(progressBarData) ? progressBarData : [];
  };

  const currentProgressBarData = getProgressBarData();

  // Custom tooltip to show totals, changes, and breakdown list
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const CustomTooltipContent = ({ payload, label, cardsData }: any) => {
    if (!payload || !payload.length) return null;
    // Pair primary and comparison into fixed layout rows
    const entries = payload as Array<{ dataKey: string; value: number; color: string }>;
    const primary = entries.filter((e) => !e.dataKey.startsWith("comparison_") && !e.dataKey.includes("dotted_"));
    const pairs = primary.map((p) => {
      const comp = entries.find(
        (e) => e.dataKey === `comparison_${p.dataKey}` || e.dataKey === `dotted_${p.dataKey}`
      );
      return { primary: p, comparison: comp };
    });

    const breakdownItems = currentProgressBarData as ProgressBarDataItem[];

    return (
      <div className="rounded-lg p-3 text-sm grid gap-3 border border-[#E0E0E0] bg-white" style={{ boxShadow: "0px 4px 8px 0px #3440541A", minWidth: 260 }}>
        <div className="space-y-2">
          {pairs.map(({ primary: p, comparison: c }) => {
            const info = cardsData?.[p.dataKey];
            return (
              <div key={p.dataKey} className="flex items-center gap-1.5">
                <div className="flex items-center gap-1.5 min-w-[90px]">
                  <span className="inline-block w-2 h-2 rounded-full" style={{ backgroundColor: p.color }} />
                  <span className="font-bold text-[11px] text-right tabular-nums">
                    {typeof p.value === "string" ? p.value : Number(p.value).toLocaleString()}
                  </span>
                </div>
                <span className="text-[10px] text-gray-500">vs</span>
                <div className="flex items-center gap-1.5 min-w-[90px] justify-end">
                  {c ? (
                    <>
                      <span
                        className="inline-block w-2 h-2 rounded-full"
                        style={{ backgroundColor: "transparent", borderColor: c.color, borderWidth: 2, borderStyle: "solid" }}
                      />
                      <span className="font-bold text-[11px] text-right tabular-nums">
                        {typeof c.value === "string" ? c.value : Number(c.value).toLocaleString()}
                      </span>
                    </>
                  ) : (
                    <span className="text-[10px] text-gray-400">—</span>
                  )}
                </div>
                {info?.growth && (
                  <span className={`text-[10px] font-semibold ml-2 ${info.growth.startsWith("+") ? "text-primary-green" : info.growth.startsWith("-") ? "text-primary-red" : "text-secondary"}`}>
                    {info.growth}
                  </span>
                )}
              </div>
            );
          })}
        </div>

        {breakdownItems && breakdownItems.length > 0 && (
          <div className="pt-2 border-t border-gray-100">
            <div className="text-[11px] font-semibold text-gray-700 mb-2">
              {activeTabFilter} breakdown
            </div>
            <div className="space-y-1 max-h-40 overflow-y-auto pr-1">
              {breakdownItems.map((it, idx) => (
                <div key={`${it.title}-${idx}`} className="flex items-center text-[11px]">
                  <span className="truncate max-w-[40%]">{it.title}</span>
                  <span className="ml-2 text-gray-500 tabular-nums">{it.percentage.toFixed(2)}%</span>
                  <div className="ml-auto flex items-center gap-1.5">
                    <span className="font-medium tabular-nums min-w-[64px] text-right">{(it.value ?? 0).toLocaleString()}</span>
                    {typeof it.comparisonValue !== "undefined" && isComparisonEnabled && (
                      <>
                        <span className="text-[10px] text-gray-500">vs</span>
                        <span className="font-medium text-gray-600 tabular-nums min-w-[64px] text-right">{Number(it.comparisonValue).toLocaleString()}</span>
                      </>
                    )}
                    {isComparisonEnabled && it.growth && (
                      <span className={`ml-2 ${it.growth.startsWith("+") ? "text-primary-green" : it.growth.startsWith("-") ? "text-primary-red" : "text-secondary"}`}>
                        {it.growth}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Handle error state first - but only show error display for critical errors
  if (chartError) {
    const err = chartError as { code?: string; response?: { status?: number }; status?: number };
    const errorCode = err?.code;
    const errorStatus = err?.response?.status ?? err?.status;

    // Don't show error display for 404 (not found) - just show no data instead
    if (errorCode === "not_found" || errorStatus === 404) {
      return <NoData title="About Users" />;
    }

    // Show error display for other errors (500, network issues, auth, etc.)
    return (
      <ErrorDisplay
        error={chartError}
        onRetry={() => window.location.reload()}
        title="About Users Data"
        className="min-h-[400px]"
      />
    );
  }

  // Handle no data state (when not loading and no error)
  if (!chartCardTabs && !isChartLoading) {
    return <NoData title="About Users" />;
  }

  // Main render - loading or data available
  return (
    <Card className="space-y-4">
      <div className="space-y-2">
        <div className="flex w-full justify-between items-center">
          <Title>{projectName} About Users</Title>
          <Dropdown>
            <Dropdown.Button className="">
              {activeDomesticFilter}
            </Dropdown.Button>
            <Dropdown.Options>
              {["Demographic info ", "Tech info "].map((filter, index) => (
                <Dropdown.Option
                  key={index}
                  onClick={() => onChangeDomesticFilter(filter)}
                >
                  {filter}
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
        </div>
        <DateRange />
      </div>

      {/* Card Tabs Section - Using Audience data */}
      <div className="flex overflow-x-auto max-w-full gap-1 px-0 pb-2 whitespace-nowrap">
         {isChartLoading || !chartCardTabs ? (
          <motion.div
            className="flex overflow-x-auto max-w-full gap-1 px-0 pb-2 whitespace-nowrap"
            key={"loading"}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            {Array.from({ length: 7 }).map((_, i) => (
              <CardTab key={i} isLoading />
            ))}
          </motion.div>
        ) : (
          chartCardTabs && (
            <motion.div
              className="flex overflow-x-auto max-w-full gap-1 px-0 pb-2 whitespace-nowrap"
              key={"data"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
               {chartCardTabs.map(
                (
                  {
                    title,
                    changeValue,
                    value,
                  }: { title: string; changeValue: string; value: string },
                  index: number
                ) => (
                  <CardTab
                    key={index}
                    title={title}
                    value={value}
                    changeValue={changeValue}
                    className={`border-2 ${
                      activeTab === title
                        ? "border-primary"
                        : "border-transparent"
                    }`}
                    style={
                      activeTab === title
                        ? { borderColor: themeColor }
                        : { borderColor: "transparent" }
                    }
                    onSelect={() => setActiveTab(title)}
                  />
                )
              )}
            </motion.div>
          )
        )}
      </div>

      <div className="w-full flex justify-end">
        <Dropdown>
          <Dropdown.Button className="min-w-24 text-sm">
            {activeTabFilter}
          </Dropdown.Button>
          <Dropdown.Options>
            {currentFilters.map((filter, index) => (
              <Dropdown.Option
                key={index}
                onClick={() => {
                  setActiveTabFilter(filter);
                }}
              >
                {filter}
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
      </div>

      {/* Chart and Progress Bars Section */}
      <div className="flex flex-col-reverse lg:flex-row items-center lg:items-start gap-y-16 min-h-[310px]">
        {/* Chart Section - Using Audience chart data */}
        <div className="w-full min-h-[310px] flex items-center">
          {isChartLoading ? (
            <motion.div
              key={"loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              className="w-full"
            >
              <LineChartSkeleton />
            </motion.div>
          ) : (
            chartLineData && chartColors && chartSelectedLines && chartCardsData && (
              <motion.div
                key={"data"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
                className="w-full"
              >
                <GSCLineChart
                  lineChartData={chartLineData}
                  colors={chartColors}
                  selectedLines={chartSelectedLines}
                  cardsData={chartCardsData}
                  onHover={handleChartHover}
                  customTooltipContent={CustomTooltipContent}
                />
              </motion.div>
            )
          )}
        </div>

        {/* Progress Bars Section - Real API data with hover support for all filters */}
        <div className="w-full lg:w-[40%] h-[310px] max-h-[310px] overflow-y-auto">
          <div className="space-y-2 pr-2">
            {/* Show header for progress bars */}
            <div className="text-sm font-semibold text-gray-700 mb-3 text-center">
              {hoveredDate
                ? `${activeTabFilter} breakdown for ${hoveredDate}`
                : `${activeTabFilter} breakdown`}
            </div>

            {/* Check loading states for all filters */}
            {isProgressBarLoading ||
            (isCountriesFilter && isDailyCountriesLoading) ||
            (isAgeFilter && isDailyAgeLoading) ||
            (isCitiesFilter && isDailyCitiesLoading) ||
            (isLanguageFilter && isDailyLanguageLoading) ||
            (isGenderFilter && isDailyGenderLoading) ||
            (isTechDeviceFilter && isDailyTechDeviceLoading) ||
            (isTechOSFilter && isDailyTechOSLoading) ||
            (isTechAppVersionFilter && isDailyTechAppVersionLoading) ||
            (isTechBrowserFilter && isDailyTechBrowserLoading) ? (
              // Show loading skeleton for progress bars
              Array.from({ length: 5 }).map((_, index) => (
                <ProgressBar
                  key={index}
                  isLoading={true}
                  percentage={0}
                  title=""
                  color=""
                />
              ))
            ) : progressBarError ||
              (isCountriesFilter && dailyCountriesError) ||
              (isAgeFilter && dailyAgeError) ||
              (isCitiesFilter && dailyCitiesError) ||
              (isLanguageFilter && dailyLanguageError) ||
              (isGenderFilter && dailyGenderError) ||
              (isTechDeviceFilter && dailyTechDeviceError) ||
              (isTechOSFilter && dailyTechOSError) ||
              (isTechAppVersionFilter && dailyTechAppVersionError) ||
              (isTechBrowserFilter && dailyTechBrowserError) ? (
              // Show error state for progress bars
              <div className="text-center text-red-500 py-4">
                <p>Error loading {activeTabFilter.toLowerCase()} data</p>
              </div>
            ) : hasProgressBarData ||
              (Array.isArray(currentProgressBarData) &&
                currentProgressBarData.length > 0) ? (
              // Show actual progress bar data
              currentProgressBarData.map(
                (item: ProgressBarDataItem, index: number) => {
                  const hasComparison =
                    isComparisonEnabled &&
                    item.comparisonPercentage !== undefined;

                  return (
                    <ProgressBar
                      key={`${item.title}-${index}`}
                      isLoading={false}
                      percentage={item.percentage}
                      title={item.title}
                      color={hasComparison ? "" : "#914AC4"} // Use default purple when no comparison
                      comparisonPercentage={item.comparisonPercentage}
                      isComparisonEnabled={hasComparison}
                      value={item.value}
                      comparisonValue={item.comparisonValue}
                    />
                  );
                }
              )
            ) : (
              // Show no data message
              <div className="text-center text-gray-500 py-4">
                <p>No {activeTabFilter.toLowerCase()} data available</p>
                {!hoveredDate && (
                  <p className="text-xs mt-1">
                    Hover over the chart to see {activeTabFilter.toLowerCase()}{" "}
                    breakdown
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ChartAndBars;
