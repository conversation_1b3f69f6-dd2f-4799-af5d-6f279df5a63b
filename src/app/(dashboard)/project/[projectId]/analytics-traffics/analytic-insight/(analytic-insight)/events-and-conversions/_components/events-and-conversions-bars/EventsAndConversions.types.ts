import { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import { BarsData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/HorizontalBars.types";

// Common types
export interface MetricBreakdown {
  value: number;
  percentage: number;
}

export interface MetricBreakdownData {
  [metricName: string]: MetricBreakdown;
}

// Base interface for common metrics structure
interface BaseMetricsData {
  views: number;
  sessions: number;
  "all-users": number;
  "new-users": number;
  "active-users": number;
  "returning-users": number;
  "engaged-sessions": number;
}

// Events-specific data structure
export interface EventsData extends BaseMetricsData {
  "all-events": MetricBreakdownData;
  "views-events": MetricBreakdownData;
  "sessions-events": MetricBreakdownData;
  "new-users-events": MetricBreakdownData;
  "active-users-events": MetricBreakdownData;
  "returning-users-events": MetricBreakdownData;
  "engaged-sessions-events": MetricBreakdownData;
}

// Conversions-specific data structure
export interface ConversionsData extends BaseMetricsData {
  "all-conversions": MetricBreakdownData;
  "views-conversions": MetricBreakdownData;
  "sessions-conversions": MetricBreakdownData;
  "new-users-conversions": MetricBreakdownData;
  "active-users-conversions": MetricBreakdownData;
  "returning-users-conversions": MetricBreakdownData;
  "engaged-sessions-conversions": MetricBreakdownData;
}

// Union type for generic handling
export type AnalyticsData = EventsData | ConversionsData;

// Common API response structure
export interface ApiPeriod {
  start_date: string;
  end_date: string;
  days_count: number;
}

interface BaseApiResponse {
  status: string;
  project_id: string;
  period: ApiPeriod;
}

export interface EventsApiResponse extends BaseApiResponse {
  data: {
    events: EventsData;
  };
}

export interface ConversionsApiResponse extends BaseApiResponse {
  data: {
    conversions: ConversionsData;
  };
}

// Union type for generic API response handling
export type AnalyticsApiResponse = EventsApiResponse | ConversionsApiResponse;

// Transformed data for UI components
export interface TransformedAnalyticsData {
  cardTabs: CardTabType[];
  barsData: BarsData;
}
