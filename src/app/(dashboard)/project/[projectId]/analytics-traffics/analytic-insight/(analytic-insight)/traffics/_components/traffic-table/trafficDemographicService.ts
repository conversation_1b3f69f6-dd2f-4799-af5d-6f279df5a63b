import http from "@/services/httpService";

export interface TrafficDemographicResponse {
  status: string;
  data: {
    organic: Record<string, TrafficMetrics>;
    referral: Record<string, TrafficMetrics>;
    social: Record<string, TrafficMetrics>;
    total_traffics: {
      organic: TrafficMetrics;
      referral: TrafficMetrics;
      social: TrafficMetrics;
      direct: TrafficMetrics;
      paid: TrafficMetrics;
      mail: TrafficMetrics;
      unassigned: TrafficMetrics;
    };
    period: {
      start_date: string;
      end_date: string;
      days_count: number;
    };
  };
}

export interface TrafficMetrics {
  sessions: number;
  engaged_sessions: number;
  new_users: number;
  total_users: number;
  views: number;
  engaged_time: number;
  event_count: number;
  conversions: number;
  engaged_rate: number;
}

export const trafficDemographicService = {
  // Get traffic demographic data
  getTrafficDemographic: async (
    projectId: string,
    startDate: string,
    endDate: string
  ): Promise<{ data: TrafficDemographicResponse["data"] }> => {
    try {
      console.log("🌐 API Call:", {
        url: `/api/project/GA4/traffic/demographic/${projectId}/`,
        params: { start_date: startDate, end_date: endDate },
        projectId,
        startDate,
        endDate,
      });

      const response = await http.get(
        `/api/project/GA4/traffic/demographic/${projectId}/`,
        {
          params: {
            start_date: startDate,
            end_date: endDate,
          },
          useAuth: true,
        }
      );

      console.log("🌐 API Response:", {
        status: response.status,
        hasData: !!response.data,
        dataKeys: response.data ? Object.keys(response.data) : [],
        responseStructure: {
          hasStatus: !!response.data?.status,
          hasDataProperty: !!response.data?.data,
          statusValue: response.data?.status,
          actualDataKeys: response.data?.data
            ? Object.keys(response.data.data)
            : [],
        },
      });

      // Validate response structure
      if (!response?.data) {
        throw new Error("Invalid API response: missing data");
      }

      // Ensure required data structure exists with defaults
      // The API returns { status: "success", data: {...} }, so we need response.data.data
      const data = response.data.data || response.data;

      const validatedData = {
        organic: data.organic || {},
        referral: data.referral || {},
        social: data.social || {},
        total_traffics: {
          organic: data.total_traffics?.organic || {
            sessions: 0,
            engaged_sessions: 0,
            new_users: 0,
            total_users: 0,
            views: 0,
            engaged_time: 0,
            event_count: 0,
            conversions: 0,
            engaged_rate: 0,
          },
          referral: data.total_traffics?.referral || {
            sessions: 0,
            engaged_sessions: 0,
            new_users: 0,
            total_users: 0,
            views: 0,
            engaged_time: 0,
            event_count: 0,
            conversions: 0,
            engaged_rate: 0,
          },
          social: data.total_traffics?.social || {
            sessions: 0,
            engaged_sessions: 0,
            new_users: 0,
            total_users: 0,
            views: 0,
            engaged_time: 0,
            event_count: 0,
            conversions: 0,
            engaged_rate: 0,
          },
          direct: data.total_traffics?.direct || {
            sessions: 0,
            engaged_sessions: 0,
            new_users: 0,
            total_users: 0,
            views: 0,
            engaged_time: 0,
            event_count: 0,
            conversions: 0,
            engaged_rate: 0,
          },
          paid: data.total_traffics?.paid || {
            sessions: 0,
            engaged_sessions: 0,
            new_users: 0,
            total_users: 0,
            views: 0,
            engaged_time: 0,
            event_count: 0,
            conversions: 0,
            engaged_rate: 0,
          },
          mail: data.total_traffics?.mail || {
            sessions: 0,
            engaged_sessions: 0,
            new_users: 0,
            total_users: 0,
            views: 0,
            engaged_time: 0,
            event_count: 0,
            conversions: 0,
            engaged_rate: 0,
          },
          unassigned: data.total_traffics?.unassigned || {
            sessions: 0,
            engaged_sessions: 0,
            new_users: 0,
            total_users: 0,
            views: 0,
            engaged_time: 0,
            event_count: 0,
            conversions: 0,
            engaged_rate: 0,
          },
        },
        period: data.period || {
          start_date: startDate,
          end_date: endDate,
          days_count: 0,
        },
      };

      return { data: validatedData };
    } catch (error) {
      console.error("Traffic demographic API error:", error);

      // Return empty data structure instead of throwing
      const emptyMetrics = {
        sessions: 0,
        engaged_sessions: 0,
        new_users: 0,
        total_users: 0,
        views: 0,
        engaged_time: 0,
        event_count: 0,
        conversions: 0,
        engaged_rate: 0,
      };

      return {
        data: {
          organic: {},
          referral: {},
          social: {},
          total_traffics: {
            organic: emptyMetrics,
            referral: emptyMetrics,
            social: emptyMetrics,
            direct: emptyMetrics,
            paid: emptyMetrics,
            mail: emptyMetrics,
            unassigned: emptyMetrics,
          },
          period: {
            start_date: startDate,
            end_date: endDate,
            days_count: 0,
          },
        },
      };
    }
  },
};
