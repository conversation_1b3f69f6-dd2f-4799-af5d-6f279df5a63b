import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { pagesInsightService, PageType } from "./pagesInsightService";
import { transformPagesInsightData, TransformedPagesInsightData } from "./transformPagesInsightData";
import type { TableData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";

interface UsePagesInsightDataProps {
  page: number;
  pageType: PageType;
}

type PagesInsightErrorInfo = {
  hasError: boolean;
  message: string | null;
  type: "primary" | "comparison" | null;
};

interface UsePagesInsightDataReturn {
  data: TransformedPagesInsightData;
  isLoading: boolean;
  error: PagesInsightErrorInfo;
}

const usePagesInsightData = ({ page, pageType }: UsePagesInsightDataProps): UsePagesInsightDataReturn => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API calls
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  // Fetch primary data
  const {
    data: primaryData,
    isLoading: primaryLoading,
    isError: primaryError,
    error: primaryErrorDetails,
  } = useQuery({
    queryKey: ["pages-insight-primary", projectId, startDate, endDate, pageType],
    queryFn: async () => {
      if (!projectId || !startDate || !endDate) {
        throw new Error("Missing required parameters");
      }
      return pagesInsightService.getPages(
        projectId,
        startDate,
        endDate,
        pageType
      );
    },
    enabled: isValidProjectId && !!projectId && !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors (client errors)
      if (error instanceof Error && error.message.includes('4')) {
        return false;
      }
      return failureCount < 2;
    },
    // Prevent query from throwing and breaking the component
    throwOnError: false,
  });

  // Fetch comparison data if comparison is enabled
  const {
    data: comparisonData,
    isLoading: comparisonLoading,
    isError: comparisonError,
    error: comparisonErrorDetails,
  } = useQuery({
    queryKey: [
      "pages-insight-comparison",
      projectId,
      comparisonStartDate,
      comparisonEndDate,
      pageType,
    ],
    queryFn: async () => {
      if (!projectId || !comparisonStartDate || !comparisonEndDate) {
        throw new Error("Missing required parameters");
      }
      return pagesInsightService.getPages(
        projectId,
        comparisonStartDate,
        comparisonEndDate,
        pageType
      );
    },
    enabled:
      isComparisonEnabled &&
      isValidProjectId &&
      !!projectId &&
      !!comparisonStartDate &&
      !!comparisonEndDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors (client errors)
      if (error instanceof Error && error.message.includes('4')) {
        return false;
      }
      return failureCount < 2;
    },
    // Prevent query from throwing and breaking the component
    throwOnError: false,
  });

  // Transform data client-side with error handling
  const transformedData: TransformedPagesInsightData | undefined = useMemo(() => {
    console.log("🔄 usePagesInsightData transform memo:", {
      hasPrimaryData: !!primaryData?.data,
      hasComparisonData: !!comparisonData?.data,
      pageType,
      page,
      primaryLoading,
      primaryError,
      primaryDataKeys: primaryData?.data ? Object.keys(primaryData.data) : [],
      primaryDataSample: primaryData?.data ? Object.keys(primaryData.data).slice(0, 3) : [],
    });

    // Always return a valid structure
    const emptyTableStructure: TransformedPagesInsightData = {
      tableData: {
        tableHeadings: [
          "Page",
          "Sessions",
          "Engaged Sessions",
          "New Users",
          "Total Users",
          "Views",
          "Engaged Time",
          "Event Count",
          "Conversions",
          "Engaged Rate",
        ],
        tableBody: [] as TableData["tableBody"],
      },
      pagination: {
        totalPages: 1,
        currentPage: 1,
        totalItems: 0,
      },
    };

    try {
      if (!primaryData?.data) {
        console.log("❌ No primary data available");
        return emptyTableStructure;
      }

      // Check if data is empty object
      const dataKeys = Object.keys(primaryData.data);
      if (dataKeys.length === 0) {
        console.log("❌ Primary data is empty object");
        return emptyTableStructure;
      }

      console.log("✅ Primary data available, transforming...", {
        dataKeysCount: dataKeys.length,
        firstKey: dataKeys[0],
        firstValue: primaryData.data[dataKeys[0]],
      });
      
      return transformPagesInsightData(
        primaryData.data,
        pageType,
        page,
        comparisonData?.data || null
      );
    } catch (error) {
      console.error("Error transforming pages insight data:", error);
      return emptyTableStructure;
    }
  }, [primaryData, comparisonData, pageType, page]);

  // Determine loading state - don't wait for comparison if primary fails
  const isLoading =
    primaryLoading ||
    (isComparisonEnabled && comparisonLoading && !primaryError);

  // Combine error information for better error handling
  const errorInfo: PagesInsightErrorInfo = useMemo(() => {
    if (primaryError) {
      return {
        hasError: true,
        message: primaryErrorDetails?.message || "Failed to load page data",
        type: "primary" as const,
      };
    }
    
    if (isComparisonEnabled && comparisonError) {
      return {
        hasError: true,
        message: comparisonErrorDetails?.message || "Failed to load comparison data",
        type: "comparison" as const,
      };
    }

    return {
      hasError: false,
      message: null,
      type: null,
    };
  }, [primaryError, comparisonError, primaryErrorDetails, comparisonErrorDetails, isComparisonEnabled]);

  // If queries are disabled (e.g., missing projectId), still return valid structure
  const finalData: TransformedPagesInsightData = transformedData || {
    tableData: {
      tableHeadings: [
        "Page",
        "Sessions",
        "Engaged Sessions",
        "New Users",
        "Total Users",
        "Views",
        "Engaged Time",
        "Event Count",
        "Conversions",
        "Engaged Rate",
      ],
      tableBody: [] as TableData["tableBody"],
    },
    pagination: {
      totalPages: 1,
      currentPage: 1,
      totalItems: 0,
    },
  };

  return {
    data: finalData, // Always returns valid structure
    isLoading,
    error: errorInfo,
  };
};

export default usePagesInsightData;