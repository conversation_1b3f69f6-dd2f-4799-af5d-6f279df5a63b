"use client";
import React, { useMemo, memo } from "react";

/* ================================ RECHARTS ================================ */
import {
  LineChart,
  Line,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts";

/* ================================== TYPES ================================= */
import type { LineChartType } from "./LineChart.types";
type PayloadEntry = {
  dataKey: string;
  value: number | string;
  color: string;
};
type TooltipContentProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cardsData: any;
  payload?: PayloadEntry[];
  label?: string;
  tooltipItemsClassName?: string;
};

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import abbreviateNumber from "@/utils/abbreviateNumber";
import Skeleton from "react-loading-skeleton";
import { cn } from "@/utils/cn";
import { formatTooltipDate } from "@/utils/formatTooltipDate";

/* ========================================================================== */
/**
 * Custom tooltip content for the Recharts LineChart.
 * Displays values with growth indicators and colored dots for each data line.
 *
 * @component
 * @param props - The props object.
 * @param props.payload - The array of data points currently hovered.
 * @param props.label - The label (usually the x-axis value) for the current tooltip.
 * @param props.cardsData - Metadata for each data key, including growth info.
 * @returns A styled card with tooltip content or null if no data.
 */
const TooltipContent = memo(
  ({
    payload,
    label,
    cardsData,
    tooltipItemsClassName,
  }: TooltipContentProps) => {
    if (!payload || !payload.length) return null;

    // Group primary and comparison data for better 2-column display
    const primaryData = payload.filter(
      (entry) =>
        !entry.dataKey.includes("dotted_") &&
        !entry.dataKey.startsWith("comparison_")
    );
    const dottedComparison = payload.filter((entry) =>
      entry.dataKey.includes("dotted_")
    );
    const solidComparison = payload.filter((entry) =>
      entry.dataKey.startsWith("comparison_")
    );
    // Check if we have comparison data in the payload (supports dotted_ and comparison_ styles)
    const hasComparison =
      dottedComparison.length > 0 || solidComparison.length > 0;

    // Support modal audience overview case where keys are `current_period` and `previous_period`
    const hasModalComparison =
      payload.some((e: any) => e.dataKey === "current_period") &&
      payload.some((e: any) => e.dataKey === "previous_period");

    const isComparisonMode = hasComparison || hasModalComparison;

    // Resolve dates from the original data point when available
    const source = (payload as unknown as any[])[0]?.payload as
      | { primaryDate?: string; comparisonDate?: string }
      | undefined;
    const formattedCurrentDate = formatTooltipDate(
      (source?.primaryDate as string) || label || ""
    );
    const formattedComparisonDate = source?.comparisonDate
      ? formatTooltipDate(source.comparisonDate)
      : formatTooltipDate(label || "");

    return (
      <Card
        className="rounded-lg p-4 text-sm grid gap-3 border border-[#E0E0E0]"
        style={{ boxShadow: "0px 4px 8px 0px #3440541A" }}
      >
        {isComparisonMode ? (
          // Two column grid layout for comparison mode
          hasModalComparison ? (
            // Audience Overview modal case: explicit current/previous keys
            <div className="space-y-2">
              {(() => {
                const currentEntry = payload.find(
                  (e: any) => e.dataKey === "current_period"
                ) as any;
                const previousEntry = payload.find(
                  (e: any) => e.dataKey === "previous_period"
                ) as any;
                return (
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span
                        className="inline-block w-2 h-2 rounded-full"
                        style={{ backgroundColor: currentEntry?.color }}
                      />
                      <span className="text-xs text-gray-700">
                        {formattedCurrentDate}
                      </span>
                      <span className="font-bold text-xs ml-auto">
                        {typeof currentEntry?.value === "string"
                          ? currentEntry?.value
                          : abbreviateNumber(currentEntry?.value)}
                      </span>
                    </div>
                    {previousEntry && (
                      <div className="flex items-center gap-2">
                        <span
                          className="inline-block w-2 h-2 rounded-full"
                          style={{ backgroundColor: previousEntry.color }}
                        />
                        <span className="text-xs text-gray-700">
                          {formattedComparisonDate}
                        </span>
                        <span className="font-bold text-xs ml-auto">
                          {typeof previousEntry.value === "string"
                            ? previousEntry.value
                            : abbreviateNumber(previousEntry.value)}
                        </span>
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>
          ) : (
            <div className="space-y-2">
              {primaryData.map((primaryEntry: PayloadEntry, index: number) => {
                // Find the corresponding comparison data
                const baseKey = primaryEntry.dataKey;
                const comparisonEntry =
                  dottedComparison.find(
                    (comp) => comp.dataKey === `dotted_${baseKey}`
                  ) ||
                  solidComparison.find(
                    (comp) => comp.dataKey === `comparison_${baseKey}`
                  );

                return (
                  <div key={index} className="space-y-1">
                    {/* Primary Value with Date and Circle */}
                    <div className="flex items-center gap-2">
                      <span
                        className="inline-block w-2 h-2 rounded-full"
                        style={{ backgroundColor: primaryEntry.color }}
                      />
                      <span className="text-xs text-gray-700">
                        {formattedCurrentDate}
                      </span>
                      <span className="font-bold text-xs ml-auto">
                        {typeof primaryEntry.value === "string"
                          ? primaryEntry.value
                          : abbreviateNumber(primaryEntry.value)}
                      </span>
                    </div>

                    {/* Comparison Value with Date and Circle */}
                    {comparisonEntry && (
                      <div className="flex items-center gap-2">
                        <span
                          className="inline-block w-2 h-2 rounded-full"
                          style={{
                            backgroundColor: "transparent",
                            borderColor: comparisonEntry.color,
                            borderWidth: 2,
                            borderStyle: "solid",
                          }}
                        />
                        <span className="text-xs text-gray-700">
                          {formattedComparisonDate}
                        </span>
                        <span className="font-bold text-xs ml-auto">
                          {typeof comparisonEntry.value === "string"
                            ? comparisonEntry.value
                            : abbreviateNumber(comparisonEntry.value)}
                        </span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )
        ) : (
          // Single column layout for primary data only
          <div className="space-y-2">
            {primaryData.map((entry: PayloadEntry, index: number) => (
              <div key={index} className="flex items-center gap-2">
                <span
                  className="inline-block w-2 h-2 rounded-full"
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-xs text-gray-700">
                  {formattedCurrentDate}
                </span>
                <span className="font-bold text-xs ml-auto">
                  {typeof entry.value === "string"
                    ? entry.value
                    : abbreviateNumber(entry.value)}
                </span>
              </div>
            ))}
          </div>
        )}
      </Card>
    );
  }
);
TooltipContent.displayName = "TooltipContent";

/* ========================================================================== */
/**
 * Renders a responsive multi-line chart using Recharts with custom tooltips.
 * It dynamically renders lines based on selected keys and supports "dotted_" lines for comparisons.
 *
 * @component
 * @param props - The props object.
 * @param props.lineChartData - Array of chart data points. Each item should include a "name" and any number of data series.
 * @param props.colors - An array of objects mapping each data key to a color.
 * @param props.selectedLines - List of active lines (keys) to display.
 * @param props.cardsData - Metadata for each data key used in tooltip.
 * @returns A fully responsive line chart.
 */

const GSCLineChart = memo(
  ({
    lineChartData,
    colors,
    selectedLines,
    cardsData,
    isLoading,
    className,
    tooltipItemsClassName,
    onHover,
    customTooltipContent,
    shouldShowComparison = true,
  }: LineChartType) => {
    /* ========================================================================== */
    /*                                  constants                                 */
    /* ========================================================================== */

    const keys = useMemo(() => {
      const safeData = lineChartData ?? [];
      const allKeys =
        safeData.length > 0
          ? Object.keys(safeData[0]).filter(
              (k) =>
                k !== "name" && k !== "primaryDate" && k !== "comparisonDate"
            )
          : [];

      return allKeys;
    }, [lineChartData]);
    const memoizedSelectedLines = useMemo(() => selectedLines, [selectedLines]);

    /* ========================================================================== */
    /*                                   RENDER                                   */
    /* ========================================================================== */
    if (isLoading)
      return (
        <div className={cn(className)}>
          <Skeleton height={300} />
        </div>
      );

    if (!lineChartData || !colors || !selectedLines) return null;

    return (
      <div className={cn("h-[300px] w-full", className)}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            width={500}
            height={300}
            data={lineChartData}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
            onMouseMove={(data) => {
              if (data && data.activeLabel && onHover) {
                onHover(data.activeLabel as string);
              }
            }}
            onMouseLeave={() => {
              if (onHover) {
                onHover(null);
              }
            }}
          >
            <XAxis
              interval="preserveStartEnd"
              dataKey="name"
              type="category"
              tickLine={false}
              axisLine={false}
              tick={{
                fill: "#6C757D",
                fontSize: 11,
                fontWeight: "bold",
              }}
              tickMargin={16}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis
              interval={0}
              type="number"
              tickLine={false}
              axisLine={false}
              tick={{
                fill: "#6C757D",
                fontSize: 12,
                fontWeight: "bold",
              }}
              tickMargin={16}
              domain={["dataMin", "dataMax"]}
              tickFormatter={(value: number) => abbreviateNumber(value)}
            />
            <CartesianGrid stroke="#EAEAEA" />
            {cardsData && (
              <Tooltip
                content={
                  customTooltipContent
                    ? (props: any) =>
                        (React.createElement as any)(customTooltipContent, {
                          ...props,
                          cardsData,
                          tooltipItemsClassName,
                        })
                    : (props: any) => (
                        <TooltipContent
                          {...props}
                          cardsData={cardsData}
                          tooltipItemsClassName={tooltipItemsClassName}
                        />
                      )
                }
              />
            )}
            {keys.map((key, index) => {
              const color = colors.find((col) => col.name === key);
              const isSelected = memoizedSelectedLines?.includes(key);
              const isDotted = key.includes("dotted_");

              // Render solid lines for primary data
              if (isSelected && !isDotted) {
                return (
                  <Line
                    key={index}
                    type="monotone"
                    dataKey={key}
                    stroke={color?.color || "#914AC4"}
                    strokeWidth={2}
                    strokeDasharray="0"
                    strokeLinecap="round"
                    activeDot={{ r: 4 }}
                    dot={false}
                    connectNulls={false}
                  />
                );
              }

              // Render dotted lines for comparison data only if shouldShowComparison is true
              if (isDotted && shouldShowComparison) {
                const baseKey = key.replace("dotted_", "");
                const isBaseSelected = memoizedSelectedLines?.includes(baseKey);
                const baseColor = colors.find((col) => col.name === baseKey);

                if (isBaseSelected) {
                  return (
                    <Line
                      key={index}
                      type="monotone"
                      dataKey={key}
                      stroke={baseColor?.color || "#914AC4"}
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      strokeLinecap="round"
                      activeDot={{ r: 4 }}
                      dot={false}
                      connectNulls={false}
                    />
                  );
                }
              }

              return null;
            })}
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  }
);
GSCLineChart.displayName = "GSCLineChart";

export default GSCLineChart;
