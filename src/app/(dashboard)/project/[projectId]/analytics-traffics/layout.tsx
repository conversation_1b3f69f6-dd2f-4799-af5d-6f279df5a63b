"use client";

import React, { useEffect, useMemo } from "react";
import { AnalyticsConnectionProvider } from "@/contexts/AnalyticsConnectionContext";
import { useProjectId } from "@/hooks/useProjectId";
import { usePathname } from "next/navigation";
import { useProjectDetails } from "@/hooks/useProjectDetails";

interface AnalyticsTrafficsLayoutProps {
  children: React.ReactNode;
}

const AnalyticsTrafficsLayout: React.FC<AnalyticsTrafficsLayoutProps> = ({
  children,
}) => {
  const { projectId } = useProjectId();
  const pathname = usePathname();
  const { project, currentProject } = useProjectDetails({ projectId, enabled: !!projectId });

  const pageName = useMemo(() => {
    if (!pathname) return "";
    const parts = pathname.split("/").filter(Boolean);
    const projectIndex = parts.findIndex((p) => p === "project");
    // Segments after /project/[projectId]/analytics-traffics
    const afterAnalyticsSegments = projectIndex >= 0 ? parts.slice(projectIndex + 3) : [];
    if (afterAnalyticsSegments.length === 0) return "Analytics Traffics";
    
    // Map specific routes to human-readable names
    const routeNames: Record<string, string> = {
      "analytic-insight": "Analytic Insight",
      "audience": "Audience",
      "events-and-conversions": "Events & Conversions",
      "pages-insight": "Pages Insight",
      "traffics": "Traffics",
      "gsc-insight": "GSC Insight",
      "overview": "Overview"
    };
    
    const last = afterAnalyticsSegments[afterAnalyticsSegments.length - 1];
    return routeNames[last] || last
      .replace(/-/g, " ")
      .replace(/\b\w/g, (c) => c.toUpperCase());
  }, [pathname]);

  useEffect(() => {
    const name = project?.project_name || currentProject?.project_name;
    const base = name ? `${name}` : "Project";
    const suffix = pageName ? ` — ${pageName}` : "";
    if (typeof document !== "undefined") {
      document.title = `${base}${suffix}`;
    }
  }, [project?.project_name, currentProject?.project_name, pageName]);

  return (
    <AnalyticsConnectionProvider
      projectId={projectId}
      enableAutoDialogs={true} // Enable auto dialogs for all analytics pages
    >
      {children}
    </AnalyticsConnectionProvider>
  );
};

export default AnalyticsTrafficsLayout;
