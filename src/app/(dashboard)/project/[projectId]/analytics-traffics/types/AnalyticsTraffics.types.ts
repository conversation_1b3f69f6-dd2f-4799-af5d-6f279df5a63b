export type CardTabType = {
  title: string;
  value: string;
  changeValue: string;
};

export type LineChartPoint = {
  name: string;
  [key: string]: string | number | undefined;
};

export type ColorConfig = {
  name: string;
  color: string;
};

export type CardsData = {
  [key: string]: {
    amount: number;
    growth: string;
  };
};

export type ProgressbarData = {
  title: string;
  percentage: number;
  value?: number;
  comparisonPercentage?: number;
  comparisonValue?: number;
};
