import React from "react";
import { IoClose } from "react-icons/io5";
import Card from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import { useClickOutside } from "@mantine/hooks";

interface SharePopupProps {
  onClose: () => void;
}

const SharePopup: React.FC<SharePopupProps> = ({ onClose }) => {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText("https://seoanalyser/guestlink");
      toast.success("Copied to clipboard");
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };
  const ref = useClickOutside(() => {
    onClose();
  });

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="fixed top-0 left-0 w-screen h-screen bg-black/10 backdrop-blur-[1px] z-50 flex justify-center items-center">
      <Card
        className="absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 mx-auto w-[660px] max-w-[1000px] border shadow-md "
        ref={ref}
      >
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <span className="text-secondary font-medium text-sm">Share</span>
          <button
            onClick={onClose}
            className="text-secondary hover:text-secondary/80 transition-colors"
          >
            <IoClose size={20} />
          </button>
        </div>
        {/* Title */}
        <p className="text-[18px] font-bold text-secondary text-center mb-2">
          Your sharing URL
        </p>
        {/* Description */}
        <p className="text-[16px] text-secondary/80 text-center mb-6">
          Copy and paste the link below into emails, chats or browsers
        </p>
        {/* URL Section */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-secondary">
            Guest link URL
          </label>
          <div className="flex justify-between items-center border border-gray-200 rounded-lg p-4">
            <span className="text-sm text-secondary truncate mr-2">
              https://seoanalyser/guestlink
            </span>
            <Button
              onClick={handleCopy}
              variant="default"
              size="sm"
              className="shrink-0"
            >
              Copy
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SharePopup;
