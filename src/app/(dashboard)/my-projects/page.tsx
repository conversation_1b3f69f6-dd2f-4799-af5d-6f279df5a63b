"use client";
import { Suspense, useEffect } from "react";
import MyProjectsContent from "@/components/dashboard/my-projects/MyProjectsContent";

// Main page component with Suspense boundary
export default function Page() {
  // Set document title for client-side component
  useEffect(() => {
    document.title = "My Projects | SEO Analyser";
  }, []);

  return (
    <Suspense
      fallback={<div className="animate-pulse w-full bg-gray-200 rounded-xl h-96" />}
    >
      <MyProjectsContent />
    </Suspense>
  );
}
