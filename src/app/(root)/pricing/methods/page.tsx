import <PERSON><PERSON>oader from "@/components/loading/Loading";
import Accordion from "@/ui/Accordion";
import Image from "next/image";
import React from "react";
import PriceDisplay from "@/components/ui/PriceDisplay";

const page = () => {
  return (
    <div className="containerw-full mt-8 lg:mt-[84px] container">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <div className="bg-white rounded-2xl p-8 flex flex-col gap-8">
            <div className="flex flex-col gap-4">
              <p className="text-secondary text-lg lg:text-xl font-black ">
                1 - Enter an email address for your Account
              </p>
              <label>
                Email Address
                <input
                  type="email"
                  placeholder="Type Your Email Address here."
                  className=" placeholder:text-light-gray placeholder:text-base text-base border border-light-gray rounded-lg p-4 w-full focus:outline-none focus:border-primary focus:outline-0 focus-visible:outline-0"
                />
              </label>
            </div>
            <div className="flex flex-col gap-4">
              <p className="text-secondary text-lg lg:text-xl font-black">
                2 - Select a payment method
              </p>
              <Accordion
                label={
                  <div className="flex items-center gap-2">
                    PayPal{" "}
                    <Image
                      src={`/images/paypal.png`}
                      width={19}
                      height={22}
                      alt="paypal"
                    />
                  </div>
                }
                content={
                  <div className="flex flex-col p-4 gap-6">
                    <div className="bg-[#914AC41A] rounded-lg p-4 text-primary text-sm lg:text-base">
                      Services are subscription based. You can manage your
                      subscription (extend,etc.) at any time from your Account
                      settings
                    </div>
                    <p className="text-secondary text-sm lg:text-base">
                      By purchasing this subscription and clicking "Continue",
                      you agree to the terms of service, electronic document
                      delivery, and acknowledge the privacy policy.
                    </p>
                    <button className="btn btn--primary">
                      Continue with PayPal
                    </button>
                    <p className="text-secondary text-sm lg:text-base text-center">
                      Payments are processed in USD. Payment provider fees may
                      apply.
                    </p>
                  </div>
                }
                customClassName="border border-light-gray !p-4"
              />
              <Accordion
                label={
                  <div className="flex items-center gap-2">
                    Credit Card{" "}
                    <Image
                      src={`/images/creditcard.png`}
                      width={140}
                      height={22}
                      alt="paypal"
                    />
                  </div>
                }
                content={
                  <div className="flex flex-col p-4 gap-6">
                    <div className="bg-[#914AC41A] rounded-lg p-4 text-primary text-sm lg:text-base">
                      Services are subscription based. You can manage your
                      subscription (extend,etc.) at any time from your Account
                      settings
                    </div>
                    <p className="text-secondary text-sm lg:text-base">
                      By purchasing this subscription and clicking "Continue",
                      you agree to the terms of service, electronic document
                      delivery, and acknowledge the privacy policy.
                    </p>
                    <button className="btn btn--primary">
                      Continue with Credit Card
                    </button>
                    <p className="text-secondary text-sm lg:text-base text-center">
                      Payments are processed in USD. Payment provider fees may
                      apply.
                    </p>
                  </div>
                }
                customClassName="border border-light-gray !p-4"
              />
              <Accordion
                label={
                  <div className="flex items-center gap-2">
                    Crypto Currency{" "}
                    <Image
                      src={`/images/crypto.png`}
                      width={90}
                      height={22}
                      alt="paypal"
                    />
                  </div>
                }
                content={
                  <div className="flex flex-col p-4 gap-6">
                    <div className="bg-[#914AC41A] rounded-lg p-4 text-primary text-sm lg:text-base">
                      Services are subscription based. You can manage your
                      subscription (extend,etc.) at any time from your Account
                      settings
                    </div>
                    <p className="text-secondary text-sm lg:text-base">
                      By purchasing this subscription and clicking "Continue",
                      you agree to the terms of service, electronic document
                      delivery, and acknowledge the privacy policy.
                    </p>
                    <button className="btn btn--primary">
                      Continue with Crypto Currency
                    </button>
                    <p className="text-secondary text-sm lg:text-base text-center">
                      Payments are processed in USD. Payment provider fees may
                      apply.
                    </p>
                  </div>
                }
                customClassName="border border-light-gray !p-4"
              />
            </div>
          </div>
        </div>
        <div className="lg: col-span-1 w-full order-1 lg:order-2">
          <div className="bg-white rounded-2xl p-8 flex flex-col gap-5">
            <p className="text-secondary font-black text-lg lg:text-xl ">
              Order Summary
            </p>
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between">
                <p className="text-sm lg:text-base text-secondary">3 Months</p>
                <div className="text-sm lg:text-base text-black">$5.99</div>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-sm lg:text-base text-secondary">Subtotal</p>
                <div className="text-sm lg:text-base text-black">$5.99</div>
              </div>
              <div className="flex items-center justify-between font-bold pt-4 border-t border-light-gray">
                <p className="text-sm lg:text-base text-secondary">Total</p>
                <div className="text-sm lg:text-base text-black">$5.99</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <div className="flex justify-center items-center mt-8">
       <DinoLoader/>

      </div> */}
    </div>
  );
};

export default page;
