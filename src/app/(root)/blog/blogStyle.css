@import "tailwindcss";

@theme {
  --font-sans: var(--font-nunito-sans);
  --color-primary: var(--color-primary);
  --color-secondary: var(--color-secondary);
  --color-background: var(--background);
  --color-light-gray: var(--color-light-gray);
  --color-light-gray-2: var(--color-light-gray-2);
  --color-light-gray-3: var(--color-light-gray-3);
  --color-light-gray-4: var(--color-light-gray-4);
  --color-light-gray-5: var(--color-light-gray-5);
  --color-light-gray-6: var(--color-light-gray-6);
  --color-primary-gray: var(--color-primary-gray);
  --color-light-blue: var(--color-light-blue);
  --color-primary-yellow: var(--color-primary-yellow);
  --color-primary-red: var(--color-primary-red);
  --color-primary-red-2: var(--color-primary-red-2);
  --color-primary-green: var(--color-primary-green);
  --color-primary-green-2: var(--color-primary-green-2);
  --color-primary-orange: var(--color-primary-orange);
  --color-primary-pink: var(--color-primary-pink);
}

/* max-width: 1240px; */
@utility container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1240px;

  @media (max-width: 64rem) {
    padding-inline: 16px;
  }

  @media (min-width: 64rem) and (max-width: 1300px) {
    padding-inline: 40px;
  }

  @media (min-width: 1300px) {
    padding-inline: 0;
  }
}

:root {
  --background: rgba(244, 244, 244, 1);
  --color-primary: rgba(145, 74, 196, 1);
  --color-secondary: rgba(42, 54, 74, 1);
  --color-light-gray: rgba(194, 194, 194, 1);
  --color-light-gray-2: rgba(204, 204, 204, 1);
  --color-light-gray-3: rgba(88, 97, 105, 1);
  --color-light-gray-4: rgba(145, 145, 145, 1);
  --color-light-gray-5: rgba(175, 175, 175, 1);
  --color-light-gray-6: rgba(214, 214, 214, 1);
  --color-primary-gray: rgba(75, 82, 88, 1);
  --color-light-blue: rgba(182, 212, 225, 1);
  --color-primary-yellow: rgba(225, 174, 0, 1);
  --color-primary-red: rgba(225, 29, 18, 1);
  --color-primary-red-2: rgba(216, 80, 82, 1);
  --color-primary-green: rgba(22, 169, 59, 1);
  --color-primary-green-2: rgba(37, 146, 89, 1);
  --color-primary-orange: rgba(225, 138, 0, 1);
  --color-primary-pink: rgba(225, 135, 188, 1);
}

@layer components {
  .max-w-full__customeLG {
    @apply lg:!max-w-full min-[1300px]:!px-10;
  }

  .btn {
    @apply rounded-lg py-3 px-6 border flex items-center justify-center gap-[9px] cursor-pointer relative overflow-hidden;
  }

  .btn--sm {
    @apply !px-4 !py-2;
  }

  .btn--primary {
    @apply bg-primary border-primary text-white font-bold !leading-[21px] text-base hover:opacity-80 duration-200;
  }

  .btn--outline-light {
    @apply !py-2 !text-sm !border-light-gray !text-secondary/70;
  }

  .btn--outline {
    @apply border-secondary text-secondary font-bold !leading-[21px] text-base;
  }

  .btn--primary__outline {
    @apply bg-primary/10 border-primary text-primary font-bold !leading-[21px] text-base;
    transition-duration: 700ms;
  }

  .btn--primary__outline::before {
    content: "";
    @apply absolute inset-0 w-0 h-full bg-gradient-to-r from-primary to-primary opacity-10;
    transition-duration: 700ms;
  }

  .btn--primary__outline * {
    @apply z-10;
  }

  .btn--primary__outline:hover {
    @apply text-white;
  }

  .btn--primary__outline:hover::before {
    @apply w-full opacity-100;
  }

  .btn--secondary {
    @apply text-secondary bg-secondary/10 border-transparent text-sm !leading-[19px] font-black;
  }

  .badge {
    @apply p-2 rounded-lg flex items-center justify-center border border-light-gray text-secondary/80 text-xs font-medium;
  }

  .badge--primary {
    @apply border-primary text-primary bg-primary/10;
  }

  .badge--danger {
    @apply border-primary-red text-primary-red bg-primary-red/15 font-semibold;
  }

  .badge--warning {
    @apply border-primary-yellow text-[#c7a00e] bg-primary-yellow/20 font-semibold;
  }

  .badge--success {
    @apply border-primary-green text-primary-green bg-primary-green/15 font-semibold;
  }

  /* Recommendation badges */
  .recommendation-badge {
    @apply min-w-[70px] sm:min-w-[90px] md:min-w-[100px] text-center py-1 px-2 whitespace-nowrap text-xs font-semibold transition-all duration-100 ease-in-out;
  }

  .recommendation-category {
    @apply bg-gray-100 text-gray-700;
  }

  .recommendation-category.active {
    @apply bg-primary/15 text-primary;
  }

  /* Improved recommendation section styling */
  .recommendations-container {
    @apply shadow-md rounded-lg overflow-hidden;
  }

  .recommendation-item {
    @apply transition-all duration-300 hover:shadow-md;
  }

  table thead tr th {
    @apply text-xs font-semibold text-secondary text-left break-words;
  }

  table thead tr th:first-child {
    @apply pl-2 sm:pl-4;
  }

  table tbody tr td {
    @apply text-sm text-secondary text-left first:pl-2 sm:first:pl-4 overflow-hidden last:pr-2 sm:last:pr-4 first:rounded-l-lg last:rounded-r-lg first:border-l last:border-r border-y border-light-gray group-odd:!border-0 break-words;
  }

  .textField__input {
    @apply appearance-auto border outline-0 border-light-gray p-4 rounded-lg text-secondary placeholder-light-gray;
  }
}
html {
  scroll-behavior: smooth;
}
html,
body {
  padding: 0;
  margin: 0;
  min-height: 100vh;
  user-select: none;
  background-color: var(--color-background);
}

body {
  overflow-x: hidden;
}

* {
  user-select: text;
}

button {
  cursor: pointer;
  outline: none;
}

.custome-scrollbar::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.custome-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border: 1px solid var(--color-light-gray);
  border-radius: 4px;
}

.custome-scrollbar::-webkit-scrollbar-thumb {
  background: var(--color-secondary);
  border-radius: 4px;
}

/* **** animations **** */
@keyframes floating {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(25px);
  }
  100% {
    transform: translateY(0);
  }
}

/* Login button pulse animation */
@keyframes login-button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(145, 74, 196, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(145, 74, 196, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(145, 74, 196, 0);
  }
}

.login-btn.animate-pulse {
  animation: login-button-pulse 2s infinite;
}

.floating-animate {
  animation: floating 7s ease-in-out infinite;
}

@keyframes floating-reverse {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-25px);
  }
  100% {
    transform: translateY(0);
  }
}

.floating-animate-reverse {
  animation: floating-reverse 7s ease-in-out infinite;
}

@keyframes our-customer-animate-top {
  100% {
    transform: translateX(calc(-322px * 12));
  }
}

@keyframes our-customer-animate-bottom {
  100% {
    transform: translateX(calc(322px * 12));
  }
}

/* **** charts **** */
.apexcharts-radar-series line {
  stroke: var(--color-light-gray-2) !important;
  stroke-width: 0.7px !important;
}

.apexcharts-xaxis-label {
  fill: var(--color-light-gray-3) !important;
  color: var(--color-light-gray-3) !important;
  font-size: 8px !important;
  font-weight: 800 !important;
  font-family: var(--font-sans) !important;
}

.recharts-layer .recharts-line-dots *:not(:last-child) {
  display: none;
}

.recharts-cartesian-grid-vertical line:first-child,
.recharts-cartesian-grid-vertical line:last-child {
  stroke: var(--color-light-gray-5) !important;
}

/* **** loading **** */
.loader {
  display: block;
  --height-of-loader: 6px;
  --loader-color: #0071e2;
  width: 150px;
  height: var(--height-of-loader);
  border-radius: 30px;
  background-color: rgba(0, 0, 0, 0.2);
  position: relative;
}

/* Custom animation for analyzing text */
@keyframes analyzing-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Skeleton animation for analyzing text - optimized for performance */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.analyzing-text {
  /* Base color is a dark gray */
  color: transparent; /* Must be transparent for background-clip to work */

  /* Enhanced gradient overlay for the skeleton effect - simplified for better performance */
  background: linear-gradient(
    90deg,
    rgba(146, 146, 152, 0.8) 0%,
    rgba(104, 104, 110, 0.9) 35%,
    rgb(188, 188, 194) 50%,
    rgba(121, 120, 121, 0.9) 65%,
    rgba(90, 90, 95, 0.8) 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;

  /* Reduced animation speed for better performance */
  animation: skeleton-loading 3s infinite linear;

  /* Typography improvements */
  font-weight: 900;
  letter-spacing: 0.4em; /* Increased letter spacing to match JSX */

  /* Use transform: translateZ(0) to enable GPU acceleration */
  transform: translateZ(0);
  will-change: background-position;
}

/* Animated dots for the analyzing text - optimized for performance */
.analyzing-text .dots::after {
  content: "";
  animation: dots 1.8s infinite; /* Reduced animation time for better performance */
  display: inline-block;
  background: inherit;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent; /* Must be transparent for background-clip to work */
  letter-spacing: 0.2em;
  margin-left: 0.2em;
  font-weight: 900;
  /* Use transform: translateZ(0) to enable GPU acceleration */
  transform: translateZ(0);
  will-change: content;
}

@keyframes dots {
  0%,
  20% {
    content: ".";
  }
  40%,
  60% {
    content: "..";
  }
  80%,
  100% {
    content: "...";
  }
}

.loader::before {
  content: "";
  position: absolute;
  background: var(--loader-color);
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  border-radius: 30px;
  animation: moving 1s ease-in-out infinite;
}

@keyframes moving {
  50% {
    width: 100%;
  }

  100% {
    width: 0;
    right: 0;
    left: unset;
  }
}

/* Hide scrollbar but keep functionality */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* for having no default style on auto complete */
input:-webkit-autofill {
  box-shadow: 0 0 0px 1000px rgba(255, 255, 255, 0) inset !important;
  -webkit-text-fill-color: #000;
}

/* Badge styles for recommendations */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  text-transform: capitalize;
}

.badge--success {
  background-color: rgba(76, 175, 80, 0.15);
  color: #2e7d32;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.badge--warning {
  background-color: rgba(255, 193, 7, 0.15);
  color: #f57c00;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.badge--danger {
  background-color: rgba(244, 67, 54, 0.15);
  color: #d32f2f;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* A3 page setup */
@page {
  size: A3 portrait;
  margin: 0.5cm;
}

/* PDF container styles */
.pdf-container {
  margin: 0 auto;
  padding: 0;
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 1300px;
  height: auto;
  overflow: visible;
  transform-origin: top center;
}

.pdf-container > div {
  margin-top: 0;
  width: 100%;
}

/* PDF modal content styles */
.pdf-modal-content {
  padding-top: 0 !important;
  height: calc(100% - 60px) !important; /* Adjust for header height */
  display: flex;
  flex-direction: column;
}

/* Mobile keyboard handling for modals */
@media (max-width: 768px) {
  /* Improve scrolling behavior when keyboard is visible */
  .pdf-modal-content {
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    overscroll-behavior: contain; /* Prevent scroll chaining */
  }

  /* Ensure inputs are visible when focused */
  input:focus,
  textarea:focus {
    position: relative;
    z-index: 2;
  }

  /* Additional styles for when keyboard is visible */
  .pdf-modal-content.keyboard-visible {
    padding-bottom: 20px;
    margin-bottom: 20px;
  }

  /* Adjust form elements for better visibility with keyboard */
  .keyboard-visible input,
  .keyboard-visible textarea {
    font-size: 16px; /* Prevent iOS zoom on focus */
    margin-bottom: 16px; /* Add more space between inputs */
  }

  /* Ensure buttons remain accessible when keyboard is visible */
  .keyboard-visible button[type="submit"],
  .keyboard-visible .btn {
    margin-top: 8px;
    margin-bottom: 20px;
  }
}

/* Print-specific styles for PDF */
@media print {
  /* Hide everything except the PDF when printing */
  body * {
    visibility: hidden;
  }

  .print-pdf,
  .print-pdf * {
    visibility: visible;
  }

  .print-pdf {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  .pdf-container .grid {
    display: grid !important;
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .pdf-container .grid-cols-1 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  /* Recommendations sections should use full width in print */
  .pdf-container .recommendations-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .pdf-container .overall-scores-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }

  .pdf-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .pdf-container * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Ensure consistent font sizes */
  .pdf-container h1 {
    font-size: 24pt !important;
  }
  .pdf-container h2 {
    font-size: 20pt !important;
  }
  .pdf-container h3 {
    font-size: 16pt !important;
  }
  .pdf-container h4 {
    font-size: 14pt !important;
  }
  .pdf-container h5 {
    font-size: 12pt !important;
  }
  .pdf-container p,
  .pdf-container li,
  .pdf-container td {
    font-size: 10pt !important;
  }

  /* Ensure all backgrounds and colors print */
  .pdf-container .bg-green-500,
  .pdf-container .bg-yellow-500,
  .pdf-container .bg-red-500,
  .pdf-container .bg-primary,
  .pdf-container .bg-green-100,
  .pdf-container .bg-yellow-100,
  .pdf-container .bg-red-100 {
    print-color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
  }

  /* Ensure proper page breaks */
  .pdf-container .mb-8 {
    page-break-inside: avoid;
  }

  /* Ensure consistent spacing */
  .pdf-container .gap-4 {
    gap: 1rem !important;
  }

  .pdf-container .gap-6 {
    gap: 1.5rem !important;
  }

  /* Fix for images */
  .pdf-container img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    object-fit: contain !important;
    visibility: visible !important;
  }

  /* Force image loading */
  .pdf-container img[loading="lazy"] {
    /* Use attribute selector instead of loading property */
    display: block !important;
    visibility: visible !important;
  }

  /* Ensure Next.js images are visible */
  .pdf-container span[style*="box-sizing: border-box"] {
    display: block !important;
    visibility: visible !important;
  }

  .pdf-container span[style*="box-sizing: border-box"] img {
    object-fit: contain !important;
    visibility: visible !important;
  }

  /* Fix for SVG elements */
  .pdf-container svg {
    display: block !important;
    visibility: visible !important;
  }

  /* Reset scaling for print */
  .pdf-container {
    transform: scale(1) !important;
    max-width: 100% !important;
  }

  /* Ensure modal content is properly displayed */
  .pdf-modal-content {
    height: auto !important;
    overflow: visible !important;
  }
}
.mobile-zoom {
  zoom: 0.8;
}

@media (max-width: 768px) {
  .mobile-zoom {
    zoom: 0.5;
  }
}

@media print {
  .mobile-zoom {
    zoom: 0.9 !important;
  }
}

h1 {
  @apply text-3xl font-bold mt-12 mb-4;
  letter-spacing: -0.025em;
}

h2 {
  @apply text-2xl font-bold mt-10 mb-3;
  letter-spacing: -0.025em;
}

h3 {
  @apply text-xl font-semibold mt-8 mb-3;
  letter-spacing: -0.02em;
}

h4 {
  @apply text-lg font-semibold mt-6 mb-2;
  letter-spacing: -0.015em;
}

h5 {
  @apply text-base font-semibold mt-6 mb-2;
  letter-spacing: -0.01em;
}

h6 {
  @apply text-sm font-medium mt-5 mb-1;
  letter-spacing: -0.005em;
}

.text-big {
  display: inline-block;
  font-size: 1.5rem;
  margin-top: 12px;
  margin-bottom: 12px;
}

img {
  @apply my-3;
}
