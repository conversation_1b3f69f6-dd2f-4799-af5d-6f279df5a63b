import BannerScroll from "./components/BannerScroll";
import Blog<PERSON>ard, { BlogCardProps } from "./components/BlogCard";
import SearchInput from "./components/SearchInput";
import CategoryList from "./components/CategoryList";
import { Pagination } from "./components/Pagination";
import http from "@/services/httpService";
import Link from "next/link";

// Define the API response interface
interface BlogPostsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BlogPost[];
  categories: Category[];
}

interface Category {
  name: string;
  slug: string;
}

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  author: {
    id: number;
    email: string;
    display_name: string;
  };
  body_json?: {
    text: string;
    blocks: Array<{
      type: string;
      text?: string;
      level?: number;
      src?: string;
      alt?: string;
      items?: string[];
    }>;
  };
  body?: string;
  snippet?: string;
  cover_image?: string;
  publish_timestamp: number;
  status: string;
  url: string;
  tags: string[];
  similar_posts: Array<{
    id: number;
    title: string;
    slug: string;
    author: {
      id: number;
      email: string;
      display_name: string;
    };
    publish_timestamp: number;
    tags: string[];
  }>;
}

interface BlogPageProps {
  searchParams: {
    page?: string;
    q?: string; // Search query parameter
    tag?: string; // Tag filter parameter
  };
}

// Interface for search results
interface SearchResponse {
  query: string;
  results: SearchResult[];
  count: number;
  categories: Category[];
}

interface SearchResult {
  id: number;
  title: string;
  slug: string;
  category: {
    name: string;
    slug: string;
  };
  author: string;
  publish_timestamp: number;
  snippet: string;
  body_json?: {
    text: string;
    blocks: any[];
  };
  body?: string;
  cover_image?: string;
  tags: string[];
  url: string;
}

const page = async ({ searchParams }: BlogPageProps) => {
  // Await searchParams to avoid Next.js sync-dynamic-apis error
  const params = await Promise.resolve(searchParams);
  const currentPage = Number(params?.page ?? 1);
  const searchQuery = params?.q || "";
  const tagFilter = params?.tag || "";

  // Ensure current page is valid
  if (isNaN(currentPage) || currentPage < 1) {
    return {
      redirect: {
        destination: "/blog",
        permanent: false,
      },
    };
  }

  try {
    let data: BlogPostsResponse | SearchResponse;
    let blogPosts: BlogCardProps[] = [];
    let categories: Category[] = [];
    let nextPageUrl: string | null = null;
    let previousPageUrl: string | null = null;

    // If search query is provided, fetch search results
    if (searchQuery) {
      try {
        console.log(
          `Fetching search results for query: "${searchQuery}", page: ${currentPage}`
        );

        // Construct the search URL
        const searchUrl = `/api/blog/search/?q=${encodeURIComponent(
          searchQuery
        )}${currentPage > 1 ? `&page=${currentPage}` : ""}`;

        console.log(`Search URL: ${searchUrl}`);

        const response = await http.get(searchUrl);
        data = response.data as SearchResponse;
        categories = data.categories || [];

        console.log(
          `Search results received: ${data.results?.length || 0} items`
        );

        // Map search results to BlogCardProps format
        blogPosts = (data.results || []).map((post) => ({
          id: post.id,
          title: post.title,
          slug: post.slug,
          author: post.author,
          snippet: post.snippet,
          publish_timestamp: post.publish_timestamp,
          url: post.url,
          tags: post.tags || [],
          category: post.category,
          cover_image: post.cover_image,
        }));

        // If tag filter is applied, filter posts client-side if the API didn't filter them
        // Note: This is less likely in search results, but we'll handle it for consistency
        if (tagFilter) {
          const filteredPosts = blogPosts.filter(
            (post) =>
              post.tags &&
              post.tags.some(
                (tag) => tag.toLowerCase() === tagFilter.toLowerCase()
              )
          );

          // Only update if we found filtered posts (to avoid empty results if API already filtered)
          if (filteredPosts.length > 0) {
            blogPosts = filteredPosts;
            // Update the count for display
            data.count = filteredPosts.length;
          }
        }
      } catch (searchError) {
        console.error("Error in search API call:", searchError);

        // Fallback to regular posts if search fails
        console.log("Falling back to regular posts API");

        // Build the URL with pagination and tag filter if provided
        let apiUrl = `/api/blog/posts/?`;

        // Add pagination if needed
        if (currentPage > 1) {
          apiUrl += `page=${currentPage}`;
        }

        // Add tag filter if provided
        if (tagFilter) {
          apiUrl += `${apiUrl.includes("?") ? "&" : ""}tag=${encodeURIComponent(
            tagFilter
          )}`;
        }

        const response = await http.get(apiUrl);
        data = response.data as BlogPostsResponse;
        categories = data.categories;
        nextPageUrl = data.next;
        previousPageUrl = data.previous;

        // Map API data to BlogCardProps format
        blogPosts = data.results.map((post) => ({
          id: post.id,
          title: post.title,
          slug: post.slug,
          author: post.author,
          snippet: post.snippet || "",
          publish_timestamp: post.publish_timestamp,
          url: post.url,
          tags: post.tags,
          cover_image: post.cover_image,
        }));

        // If tag filter is applied, filter posts client-side if the API didn't filter them
        if (tagFilter) {
          const filteredPosts = blogPosts.filter(
            (post) =>
              post.tags &&
              post.tags.some(
                (tag) => tag.toLowerCase() === tagFilter.toLowerCase()
              )
          );

          // Only update if we found filtered posts (to avoid empty results if API already filtered)
          if (filteredPosts.length > 0) {
            blogPosts = filteredPosts;
            // Update the count for display
            data.count = filteredPosts.length;
          }
        }
      }
    } else {
      // Otherwise fetch regular blog posts with pagination
      // Build the URL with pagination and tag filter if provided
      let apiUrl = `/api/blog/posts/?`;

      // Add pagination if needed
      if (currentPage > 1) {
        apiUrl += `page=${currentPage}`;
      }

      // Add tag filter if provided
      if (tagFilter) {
        apiUrl += `${apiUrl.includes("?") ? "&" : ""}tag=${encodeURIComponent(
          tagFilter
        )}`;
      }

      const response = await http.get(apiUrl);
      data = response.data as BlogPostsResponse;
      categories = data.categories;
      nextPageUrl = data.next;
      previousPageUrl = data.previous;

      // Map API data to BlogCardProps format
      blogPosts = data.results.map((post) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        author: post.author,
        // Use snippet from API response
        snippet: post.snippet || "",
        publish_timestamp: post.publish_timestamp,
        url: post.url,
        tags: post.tags,
        cover_image: post.cover_image,
      }));

      // If tag filter is applied, filter posts client-side if the API didn't filter them
      if (tagFilter) {
        const filteredPosts = blogPosts.filter(
          (post) =>
            post.tags &&
            post.tags.some(
              (tag) => tag.toLowerCase() === tagFilter.toLowerCase()
            )
        );

        // Only update if we found filtered posts (to avoid empty results if API already filtered)
        if (filteredPosts.length > 0) {
          blogPosts = filteredPosts;
          // Update the count for display
          data.count = filteredPosts.length;
        }
      }
    }

    // Calculate total pages based on count and items per page
    const itemsPerPage = 10; // Assuming 10 items per page based on API response
    const totalPages = Math.ceil(data.count / itemsPerPage);

    return (
      <div className="w-full mt-8 lg:mt-[84px] container">
        <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
          <div className="col-span-1 flex flex-col gap-4">
            <SearchInput defaultValue={searchQuery} />
            <CategoryList categories={categories} currentCategory={null} />
          </div>
          <div className="col-span-3">
            {/* Show search results title if searching */}
            {searchQuery && (
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-secondary">
                  {data.hasOwnProperty("query")
                    ? `Search Results for "${searchQuery}"`
                    : `Showing all posts (search for "${searchQuery}" failed)`}
                </h2>
                <p className="text-gray-500 mt-2">
                  Found {data.count} {data.count === 1 ? "result" : "results"}
                  {!data.hasOwnProperty("query") && (
                    <span className="ml-2 text-red-500">
                      (Search API error - showing regular posts instead)
                    </span>
                  )}
                </p>
              </div>
            )}

            {/* Show tag filter title if filtering by tag */}
            {!searchQuery && tagFilter && (
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-secondary">
                  Posts tagged with "{tagFilter}"
                </h2>
                <p className="text-gray-500 mt-2">
                  Found {data.count} {data.count === 1 ? "post" : "posts"}
                  <Link
                    href="/blog"
                    className="ml-2 text-primary hover:underline"
                  >
                    (Clear filter)
                  </Link>
                </p>
              </div>
            )}

            {/* Only show banner if not searching and not filtering by tag */}
            {!searchQuery && !tagFilter && blogPosts.length >= 3 && (
              <BannerScroll blogData={blogPosts.slice(0, 3)} />
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
              {blogPosts.length > 0 ? (
                blogPosts.map((post) => (
                  <BlogCard
                    key={post.id}
                    id={post.id}
                    title={post.title}
                    slug={post.slug}
                    author={post.author}
                    snippet={post.snippet}
                    publish_timestamp={post.publish_timestamp}
                    url={post.url}
                    tags={post.tags}
                    category={post.category}
                    cover_image={post.cover_image}
                  />
                ))
              ) : (
                <div className="col-span-2 py-10 text-center">
                  <h3 className="text-xl font-semibold text-secondary">
                    No posts found
                  </h3>
                  <p className="mt-2 text-gray-500">
                    Try a different search term
                  </p>
                </div>
              )}
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              searchQuery={searchQuery}
              nextPageUrl={nextPageUrl}
              previousPageUrl={previousPageUrl}
              tagFilter={tagFilter}
            />
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching blog posts:", error);

    // Return error state with more detailed message
    return (
      <div className="w-full mt-8 lg:my-[84px] container">
        <div className="text-center py-18 bg-gray-200 border border-gray-300 rounded-lg">
          <h2 className="text-2xl font-bold text-secondary mb-4">
            Unable to load blog posts
          </h2>
          <p className="mb-4">There was an error connecting to the blog API.</p>
          <p className="text-sm text-gray-500">
            {error instanceof Error ? error.message : "Please try again later."}
          </p>
          <Link
            href={`/blog${
              searchQuery
                ? `?q=${encodeURIComponent(searchQuery)}`
                : tagFilter
                ? `?tag=${encodeURIComponent(tagFilter)}`
                : ""
            }`}
            className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-opacity-90 transition-all inline-block"
          >
            Try Again
          </Link>
        </div>
      </div>
    );
  }
};

export default page;
