"use client";

import ShareIcon from "@/ui/icons/action/ShareIcon";
import { useEffect, useState } from "react";

export default function ShareButton() {
  const [canShare, setCanShare] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined" && typeof navigator.share === "function") {
      setCanShare(true);
    }
  }, []);

  const handleShare = async () => {
    try {
      await navigator.share({
        title: document.title,
        url: window.location.href,
      });
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  return (
    <button
      onClick={handleShare}
      disabled={!canShare}
      className="p-4 rounded-lg border border-secondary bg-white text-secondary hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
    >   
    <ShareIcon/>
    </button>
  );
}
