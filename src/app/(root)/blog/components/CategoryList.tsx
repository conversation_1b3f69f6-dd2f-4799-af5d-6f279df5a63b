"use client";
import CatI<PERSON> from "./CatItem";
import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface Category {
  name: string;
  slug: string;
}

interface CategoryListProps {
  categories: Category[];
  currentCategory?: string | null;
}

const CategoryList = ({
  categories = [],
  currentCategory,
}: CategoryListProps) => {
  const [activeCategory, setActiveCategory] = useState<string | null>(
    currentCategory || null
  );
  const pathname = usePathname();

  // Update active category based on URL path when component mounts or pathname changes
  useEffect(() => {
    if (pathname) {
      const pathParts = pathname.split("/");
      if (pathParts.length >= 3 && pathParts[1] === "blog") {
        const categoryFromPath = pathParts[2];
        setActiveCategory(categoryFromPath);
      } else {
        // If we're on the main blog page, reset active category
        setActiveCategory(null);
      }
    }
  }, [pathname]);

  // Add "All" category at the beginning
  const allCategories = [{ name: "All Posts", slug: "" }, ...categories];

  return (
    <div className="flex flex-row flex-wrap lg:flex-col hover:cursor-pointer">
      {allCategories.map((category, index) => {
        const isActive =
          category.slug === activeCategory ||
          (category.slug === "" && activeCategory === null);

        return (
          <Link
            href={category.slug ? `/blog/${category.slug}` : "/blog"}
            key={index}
          >
            <CatItem
              title={category.name}
              isActive={isActive}
              onClick={() => setActiveCategory(category.slug || null)}
            />
          </Link>
        );
      })}
    </div>
  );
};

export default CategoryList;
