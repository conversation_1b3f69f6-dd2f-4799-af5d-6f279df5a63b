import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";
import BlogPic from "../../blog.jpg";
import DateHolder from "../../components/DateHolder";
import SearchInput from "../../components/SearchInput";
import CategoryList from "../../components/CategoryList";
import blogService, { BlogPost } from "@/services/blogService";
import "../../blogStyle.css";
// Define the props for the page component
interface BlogPostPageProps {
  params: {
    category: string;
    slug: string;
  };
}

// Generate metadata for the page
export async function generateMetadata({
  params,
}: BlogPostPageProps): Promise<Metadata> {
  try {
    const blogPost = await blogService.getBlogPostBySlug(params.slug);

    // Use the snippet for the description if available, otherwise extract from body
    let description = blogPost.snippet || "";

    if (!description && blogPost.body) {
      // Strip HTML tags and limit to 160 characters for meta description
      description = blogPost.body
        .replace(/<[^>]*>?/gm, "") // Remove HTML tags
        .trim()
        .substring(0, 160);

      if (blogPost.body.length > 160) description += "...";
    }

    return {
      title: blogPost.title,
      description: description || "Read our latest blog post",
      openGraph: {
        title: blogPost.title,
        description: description || "Read our latest blog post",
        type: "article",
        publishedTime: new Date(
          blogPost.publish_timestamp * 1000
        ).toISOString(),
        authors: [blogPost.author.display_name],
        tags: blogPost.tags,
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Blog Post",
      description: "Read our latest blog post",
    };
  }
}

// The main page component
export default async function BlogPostPage({ params }: BlogPostPageProps) {
  try {
    const blogPost = await blogService.getBlogPostBySlug(params.slug);

    // Verify that the URL matches the expected format
    // The URL should be /blog/category/slug
    // If the category in the URL doesn't match the category from the API, we'll still show the post
    // but we might want to implement a redirect in the future

    // Log the blog post data for debugging
    console.log("Blog post data:", {
      title: blogPost.title,
      author: blogPost.author.display_name,
      bodyLength: blogPost.body?.length || 0,
      hasBody: !!blogPost.body,
      bodyPreview: blogPost.body?.substring(0, 100) || "No body content",
    });

    return (
      <div className="w-full mt-8 lg:mt-[84px] mb-12 container flex justify-center">
        <div className="flex flex-col w-full gap-6 max-w-5xl">
          {/* Sidebar */}
          {/* <div className="lg:col-span-1 flex flex-col gap-4">
            <SearchInput initialQuery="" />
            <CategoryList />
          </div> */}

          {/* Main content */}
          <div className="lg:col-span-3">
            <div className="mb-6 flex">
              <Link
                href="/blog"
                className="bg-gray-200 border flex items-center border-gray-400 rounded-md px-3 py-2.5   text-purple-700 hover:underline font-semiboild lg:text-base text-sm"
              >
                ← Back to Blog
              </Link>
            </div>

            <div className="mb-6">
              <div className="flex flex-wrap gap-2 text-xs  lg:text-base   mb-4">
                <Link
                  href="/blog"
                  className="text-primary hover:underline inline-block"
                >
                  Blog
                </Link>
                <span className="text-gray-400">/</span>
                <Link
                  href={`/blog/${params.category}`}
                  className="text-primary hover:underline inline-block capitalize"
                >
                  {decodeURIComponent(params.category)}
                </Link>
                <span className="text-gray-400">/</span>
                <span className="text-gray-600 truncate">{blogPost.title}</span>
              </div>
              <h1 className="text-secondary font-black text-2xl lg:text-4xl mt-2">
                {blogPost.title}
              </h1>
            </div>

            {/* Blog thumbnail */}
            <div className="overflow-hidden rounded-2xl relative w-full h-[216px] lg:h-[400px] mb-6">
              <Image
                src={blogPost.cover_image || BlogPic}
                alt={blogPost.title}
                width={930}
                height={400}
                className="w-full h-full object-cover object-center"
                unoptimized={blogPost.cover_image ? true : false} // Don't optimize external images
              />
              <div className="p-6 absolute top-0 left-0 w-full h-full bg-gradient-to-b from-[#15233000] to-[#152330]">
                <div className="flex items-start gap-2 sm:gap-3 md:gap-4 justify-between">
                  <DateHolder date={blogPost.publish_timestamp} />
                  <div className="bg-white flex flex-col gap-2 rounded-lg px-4 py-2 text-[10px] lg:text-[12px]">
                    <div>
                      Written by <b>{blogPost.author.display_name}</b>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Tags */}
            {blogPost.tags && blogPost.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {blogPost.tags.map((tag, index) => (
                  <Link
                    key={index}
                    href={`/blog?tag=${encodeURIComponent(tag)}`}
                    className="bg-[#914AC41A] text-xs lg:text-sm text-primary  px-2 lg:px-3 py-1 rounded-full  hover:bg-[#914AC430] transition-colors"
                  >
                    {tag}
                  </Link>
                ))}
              </div>
            )}

            {/* Blog content */}
            <article
              className="ck-content prose prose-lg max-w-none text-secondary prose-headings:text-secondary prose-headings:font-bold prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-img:rounded-lg leading-relaxed"
              dangerouslySetInnerHTML={{ __html: blogPost.body }}
            ></article>

            {/* Similar posts */}
            {blogPost.similar_posts && blogPost.similar_posts.length > 0 && (
              <div className="mt-12">
                <h2 className="text-secondary font-black text-xl mb-6">
                  Similar Posts
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {blogPost.similar_posts.map((similarPost) => {
                    // Extract category from the URL if available
                    // Default to the current category if URL is not available
                    let category = params.category;

                    // If the similar post has a URL, try to extract the category from it
                    if (similarPost.url) {
                      const urlParts = similarPost.url
                        .split("/")
                        .filter(Boolean);
                      if (urlParts.length > 1) {
                        category = urlParts[1];
                      }
                    }

                    return (
                      <Link
                        key={similarPost.id}
                        href={`/blog/${category}/${similarPost.slug}`}
                        className="p-4 border border-gray-200 rounded-lg hover:border-primary transition-colors"
                      >
                        <h3 className="text-secondary font-bold mb-2">
                          {similarPost.title}
                        </h3>
                        <div className="flex items-center justify-between text-sm text-gray-500">
                          <span>By {similarPost.author.display_name}</span>
                          <span>
                            {new Date(
                              similarPost.publish_timestamp * 1000
                            ).toLocaleDateString()}
                          </span>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching blog post:", error);

    // Return error state
    return (
      <div className="w-full mt-8 lg:mt-[84px] container">
        <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1 flex flex-col gap-4">
            <SearchInput defaultValue="" />
            <CategoryList categories={[]} />
          </div>
          <div className="lg:col-span-3">
            <div className="p-6 py-18 bg-red-50 border border-red-300 rounded-lg">
              <h2 className="text-xl font-bold text-red-700 mb-2">
                Blog Post Not Found
              </h2>
              <p className="text-red-600 mb-4">
                We couldn't find the blog post you're looking for. It may have
                been removed or the URL might be incorrect.
              </p>
              <Link
                href="/blog"
                className="text-primary font-bold hover:underline"
              >
                Back to Blog
              </Link>
            </div>

            {/* Suggest other blog posts */}
            <div className="mt-8">
              <h3 className="text-lg font-bold text-secondary mb-4">
                You might be interested in these posts:
              </h3>
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <p className="text-gray-600 mb-2">
                  Please visit our{" "}
                  <Link href="/blog" className="text-primary hover:underline">
                    blog page
                  </Link>{" "}
                  to see all available posts.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
