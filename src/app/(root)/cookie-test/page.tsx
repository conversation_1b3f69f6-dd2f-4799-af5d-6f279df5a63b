"use client";

import { useState, useEffect } from "react";
import CookiePreferences from "@/components/cookie-consent/CookiePreferences";
import { hasUserConsent, hasUserMadeChoice, resetConsent } from "@/components/cookie-consent/CookieConsentModal";

export default function CookieTestPage() {
  const [consent, setConsent] = useState<boolean | null>(null);
  const [madeChoice, setMadeChoice] = useState(false);

  useEffect(() => {
    // Update state when component mounts
    setConsent(hasUserConsent());
    setMadeChoice(hasUserMadeChoice());
  }, []);

  const handleResetConsent = () => {
    resetConsent();
    setConsent(null);
    setMadeChoice(false);
    // Reload to show the modal again
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Cookie Consent Test Page
          </h1>

          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-blue-900 mb-2">
                Current Cookie Status
              </h2>
              <div className="space-y-2 text-sm">
                <p>
                  <span className="font-medium">Has made choice:</span>{" "}
                  <span className={madeChoice ? "text-green-600" : "text-red-600"}>
                    {madeChoice ? "Yes" : "No"}
                  </span>
                </p>
                <p>
                  <span className="font-medium">Analytics consent:</span>{" "}
                  <span className={consent ? "text-green-600" : "text-red-600"}>
                    {consent ? "Accepted" : "Declined/Not set"}
                  </span>
                </p>
              </div>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                What Data We Collect
              </h2>
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Essential Data (Always Active)</h3>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Authentication tokens (localStorage)</li>
                    <li>• User profile data (localStorage)</li>
                    <li>• Device ID for security (localStorage)</li>
                    <li>• Session audit count (sessionStorage)</li>
                    <li>• Application state (localStorage)</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Analytics Data (Optional)</h3>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Google Analytics tracking</li>
                    <li>• Page views and user interactions</li>
                    <li>• Performance metrics</li>
                    <li>• Feature usage statistics</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-yellow-900 mb-2">
                Storage Locations
              </h2>
              <div className="text-sm text-yellow-800 space-y-1">
                <p><strong>localStorage:</strong> Persistent data (auth tokens, user profile, device ID, app state)</p>
                <p><strong>sessionStorage:</strong> Temporary data (audit count for current session)</p>
                <p><strong>Cookies:</strong> Google Analytics tracking (only with consent)</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-4">
              <CookiePreferences className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors" />
              
              <button
                onClick={handleResetConsent}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors text-sm"
              >
                Reset Consent (Show Modal Again)
              </button>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-green-900 mb-2">
                How It Works
              </h2>
              <ol className="list-decimal list-inside space-y-2 text-sm text-green-800">
                <li>When you first visit, a cookie consent modal appears</li>
                <li>Choose "Accept All" to enable analytics or "Essential Only" to decline</li>
                <li>Your choice is saved in localStorage and the modal won't show again</li>
                <li>Google Analytics only loads if you've given consent</li>
                <li>You can change preferences anytime using the Cookie Preferences button</li>
                <li>Essential functionality (auth, device tracking) always works regardless of choice</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
