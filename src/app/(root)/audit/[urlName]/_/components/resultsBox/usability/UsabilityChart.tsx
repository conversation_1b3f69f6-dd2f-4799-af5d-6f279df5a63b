import BarHorizontalChart from "@/ui/charts/BarHorizontalChart";
import { CheckIcon } from "@/ui/icons/general";

type UsabilityChartProps = {
  deviceRendering: number;
  viewportUsage: number;
  flashUsage: number;
  iframesUsage: number;
  iframeProtection: number;
  faviconPresence: number;
  emailPrivacy: number;
  fontLegibility: number;
  tapTargetSizing: number;
};

export default function UsabilityChart({
  deviceRendering,
  viewportUsage,
  flashUsage,
  iframesUsage,
  iframeProtection,
  faviconPresence,
  emailPrivacy,
  fontLegibility,
  tapTargetSizing,
}: UsabilityChartProps) {
  // Create data for the chart
  const chartData = [
    {
      name: "Device Rendering",
      uv: 0,
      pv: 0,
      amt: deviceRendering,
    },
    {
      name: "Viewport Usage",
      uv: 0,
      pv: 0,
      amt: viewportUsage,
    },
    {
      name: "Flash Usage",
      uv: 0,
      pv: 0,
      amt: flashUsage,
    },
    {
      name: "iFrames Usage",
      uv: 0,
      pv: 0,
      amt: iframesUsage,
    },
    {
      name: "iFrame Protection",
      uv: 0,
      pv: 0,
      amt: iframeProtection,
    },
    {
      name: "Favicon",
      uv: 0,
      pv: 0,
      amt: faviconPresence,
    },
    {
      name: "Email Privacy",
      uv: 0,
      pv: 0,
      amt: emailPrivacy,
    },
    {
      name: "Font Legibility",
      uv: 0,
      pv: 0,
      amt: fontLegibility,
    },
    {
      name: "Tap Target Sizing",
      uv: 0,
      pv: 0,
      amt: tapTargetSizing,
    },
  ];

  return (
    <div className="w-full py-4 lg:py-6 border border-light-gray rounded-lg pr-6 lg:pr-8.5 pl-4 mt-4 lg:mt-6">
      <div className="text-[17px] font-bold text-secondary pl-14 pb-3">
        Usability Scores
      </div>
      <BarHorizontalChart data={chartData} />
      <div className="flex items-center gap-1.5 pl-14">
        <div className="flex items-center gap-1">
          <div className="w-3.5 h-3.5 rounded flex items-center justify-center bg-primary">
            <CheckIcon className="w-2.5 h-2.5 text-white" />
          </div>
          <div className="text-[8px] text-primary-gray font-semibold">
            Score
          </div>
        </div>
      </div>
    </div>
  );
}
