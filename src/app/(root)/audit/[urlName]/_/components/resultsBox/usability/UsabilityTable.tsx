import Table from "@/ui/Table";

type Props = {
  data: { labData: string; value: string }[];
  title?: string;
};

export default function UsabilityTable({ data, title }: Props) {
  return (
    <div className="w-full">
      {/* {title && <h5 className="font-bold text-secondary mb-2">{title}</h5>} */}
      <Table>
        <Table.Header>
          <th className="!text-base font-semibold">{title ? title : "Lab Data"}</th>
          <th className="!text-base text-end pr-4">Value</th>
        </Table.Header>
        <Table.Body>
          {data.map((item, index) => (
            <Table.Row key={index}>
              <td>{item.labData}</td>
              <td className="text-end">{item.value}s</td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
}
