import { AnalyticsIcon } from "@/ui/icons/general";
import LinkCard from "../../LinkCard";

type RecommendationType = {
  text: string;
  priority: string;
};

type SeoDataBase = {
  status: boolean;
  pass?: boolean;
  description: string;
  recommendation: string | RecommendationType;
  importance: string;
  blog_url: string;
};

type RobotsTxtData = SeoDataBase & {
  blocked?: boolean;
};

type XmlSitemapData = SeoDataBase & {
  urls?: string[];
};

type AnalyticsData = SeoDataBase & {
  tools?: string[];
};

type AnalyticsProps = {
  robots: RobotsTxtData;
  sitemap: XmlSitemapData;
  analytics: AnalyticsData;
};

export default function Analytics({
  robots,
  sitemap,
  analytics,
}: AnalyticsProps) {
  // Helper function to get recommendation text from either string or object format
  const getRecommendationText = (
    recommendation: string | RecommendationType
  ): string => {
    if (
      typeof recommendation === "object" &&
      recommendation !== null &&
      "text" in recommendation
    ) {
      return recommendation.text;
    }
    return recommendation as string;
  };

  // Check if any of the required props are missing or incomplete
  const hasRobots = robots && typeof robots === "object" && "status" in robots;
  const hasSitemap =
    sitemap && typeof sitemap === "object" && "status" in sitemap;
  const hasAnalytics =
    analytics && typeof analytics === "object" && "status" in analytics;

  // If all required data is missing, show a loading state
  if (!hasRobots && !hasSitemap && !hasAnalytics) {
    return (
      <div className="w-full p-3 sm:p-4 rounded-lg border border-light-gray">
        <h4 className="font-semibold text-secondary pb-2">Analytics</h4>
        <div className="text-xs sm:text-sm text-secondary/60 py-2">
          Loading analytics data...
        </div>
      </div>
    );
  }

  return (
    <div className="w-full p-3 sm:p-4 rounded-lg border border-light-gray">
      <h4 className="font-semibold text-secondary pb-2">Analytics</h4>

      {hasRobots && robots.pass && (
        <div className="space-y-2">
          <div className="text-xs sm:text-sm text-secondary/60 break-words">
            {robots.description}
          </div>

          {robots.blog_url && <LinkCard title={robots.blog_url} />}
        </div>
      )}

      {hasSitemap && sitemap.pass && (
        <div className="space-y-2 mt-3 sm:mt-4">
          <div className="text-xs sm:text-sm text-secondary/60 break-words">
            {sitemap.description}
          </div>

          <div className="space-y-2 max-w-full overflow-hidden">
            {sitemap.urls &&
              sitemap.urls.length > 0 &&
              sitemap.urls.map((url, index) => (
                <LinkCard key={index} title={url} />
              ))}
          </div>
        </div>
      )}

      {hasAnalytics && analytics.pass && (
        <div className="space-y-2 mt-3 sm:mt-4">
          <div className="text-xs sm:text-sm text-secondary/60 break-words">
            {analytics.description}
          </div>
          <div className="text-xs sm:text-sm text-secondary/80 mt-2">
            <strong>Recommendation:</strong>{" "}
            {getRecommendationText(analytics.recommendation)}
          </div>
          <div className="max-w-full overflow-hidden">
            {analytics.tools &&
              analytics.tools.length > 0 &&
              analytics.tools.map((tool, index) => (
                <LinkCard key={index} title={tool}>
                  <AnalyticsIcon className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5" />
                </LinkCard>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}
