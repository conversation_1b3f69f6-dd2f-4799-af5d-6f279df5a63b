import Table from "@/ui/Table";

// Define the structure based on the actual API response
type DomainItem = {
  domain?: string;
  count?: number;
};

type TopPageBacklinkProps = {
  domains?: DomainItem[];
  title?: string;
};

export default function TopPageBacklink({
  domains = [],
  title = "Top Domains by Backlinks",
}: TopPageBacklinkProps) {
  // Check if we have domain data
  const hasDomains = domains && domains.length > 0;

  // Calculate percentages based on count
  const processedDomains = hasDomains
    ? domains.map((domain, index, arr) => {
        // Calculate percentage based on count
        const maxCount = Math.max(
          ...arr.map((d) => (typeof d.count === "number" ? d.count : 0))
        );

        const domainCount = typeof domain.count === "number" ? domain.count : 0;

        const calculatedPercentage =
          maxCount > 0 ? Math.round((domainCount / maxCount) * 100) : 0;

        return {
          ...domain,
          percentage: calculatedPercentage,
        };
      })
    : [];

  return (
    <div className={title ? "mt-6" : ""}>
      {title && (
        <h4 className="font-semibold text-secondary pb-3 text-base sm:text-lg">
          {title}
        </h4>
      )}

      {hasDomains ? (
        <Table>
          <Table.Header>
            <th className="pb-4 whitespace-nowrap">Domain</th>
            <th className="text-center pb-4">Backlinks</th>
            <th className="text-center pb-4 w-24">Percentage</th>
          </Table.Header>
          <Table.Body>
            {processedDomains.map((item, index) => (
              <Table.Row key={index}>
                <td className="w-full sm:min-w-[150px] sm:max-w-[200px] py-4 pr-2 sm:pr-4 break-words">
                  <span className="text-xs sm:text-sm font-medium">
                    {item.domain || "N/A"}
                  </span>
                </td>
                <td className="w-full sm:min-w-[100px] sm:max-w-[150px] py-4 break-words text-xs sm:text-sm text-center">
                  <span className="font-medium">
                    {(item.count || 0).toLocaleString()}
                  </span>
                </td>
                <td className="w-24 py-4 text-center">
                  <div className="flex items-center justify-center">
                    <div className="w-16">
                      <div className="bg-gray-200 rounded-full h-1.5">
                        <div
                          className="bg-primary h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${item.percentage || 0}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="p-4 bg-light-gray-6 rounded-lg text-center text-secondary/60">
          No domain data available
        </div>
      )}
    </div>
  );
}
