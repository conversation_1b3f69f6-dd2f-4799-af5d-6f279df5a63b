import React, { memo, useState, useMemo } from "react";
import BoxPrimary from "../BoxPrimary";
import Image from "next/image";
import { MonitorIcon, ImageIcon } from "@/ui/icons/general";
import ProgressChart from "@/ui/charts/ProgressChart";
import ScoreMeter from "@/ui/charts/ScoreMeter";

// Define Grade type from ProgressChart
type Grade =
  | "A+"
  | "A"
  | "A-"
  | "B+"
  | "B"
  | "B-"
  | "C+"
  | "C"
  | "C-"
  | "D+"
  | "D"
  | "D-"
  | "F";

// Define TotalScore type
type TotalScore = {
  score?: number;
  grade?: Grade;
};

// Define RecommendationType
type RecommendationType = {
  text: string;
  priority: string;
};

// Define SocialAnalysisType
type SocialAnalysisType = {
  total_score?: TotalScore;
  facebook?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  twitter?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  instagram?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  linkedin?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  youtube?: {
    pass?: boolean;
    profile_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
  telegram?: {
    pass?: boolean;
    channel_url?: string | null;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    score?: number;
  };
};

// Define PerformanceAnalysisType
type PerformanceAnalysisType = {
  total_score?: TotalScore;
  javascript_errors?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  compression?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  resource_count?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  [key: string]: unknown;
};

// Define LinksAnalysisType
type LinksAnalysisType = {
  total_score?: TotalScore;
  backlinks?: {
    pass?: boolean;
    total_backlinks?: number;
    unique_domains?: number;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  competitors?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  mentions?: {
    pass?: boolean;
    description?: string;
    recommendation?: RecommendationType;
    importance?: string;
    blog?: string;
    score?: number;
  };
  [key: string]: unknown;
};

// Define PageSpeedAnalysisType
type PageSpeedAnalysisType = {
  "Google's Core Web Vitals"?: {
    is_optimized?: boolean;
    "Largest Contentful Paint (LCP)"?: number;
    "Interaction to Next Paint (INP)"?: number | null;
    "Cumulative Layout Shift (CLS)"?: number;
    description?: string;
    importance?: string;
    recommendation?: RecommendationType;
  };
  performance_desktop?: {
    pass?: boolean;
    performance_score?: number;
    recommendation?: RecommendationType;
    description?: string;
    importance?: string;
    blog?: string;
  };
  [key: string]: unknown;
};

type AuditProps = {
  results: {
    desktop_screenshot_url?: string;
    onpage_analysis?: {
      total_score?: TotalScore;
    };
    usability_analysis?: {
      total_score?: TotalScore;
    };
    technology_review_analysis?: {
      total_score?: TotalScore;
    };
    social_analysis?: SocialAnalysisType;
    performance_analysis?: PerformanceAnalysisType & {
      total_score?: TotalScore;
    };
    links_analysis?: LinksAnalysisType & {
      total_score?: TotalScore;
    };
    localseo_analysis?: {
      total_score?: TotalScore;
    };
    pagespeed_analysis?: PageSpeedAnalysisType;
  };
  urlName: string;
};

// Memoized Screenshot component to prevent unnecessary re-renders
const MemoizedScreenshot = memo(({ src }: { src: string }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  return (
    <div className="w-full h-full relative">
      {isLoading && !hasError && (
        <div className="w-full h-full rounded-2xl bg-gray-200 animate-pulse flex items-center justify-center">
          <ImageIcon className="w-16 h-16 text-gray-400" />
        </div>
      )}
      <Image
        src={src}
        alt="Website Screenshot"
        width={1000}
        height={1000}
        className={`w-full h-full object-contain object-top rounded-2xl ${
          isLoading && !hasError ? "opacity-0" : "opacity-100"
        }`}
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setIsLoading(false);
          setHasError(true);
        }}
        unoptimized // Prevents Next.js from optimizing the image, which can cause re-fetching
        priority // Load this image with higher priority since it's above the fold
      />
      {/* {hasError && (
        <div className="w-full h-full rounded-2xl bg-gray-200 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <ImageIcon className="w-16 h-16 text-gray-400" />
            <p className="text-gray-500 mt-2">Failed to load screenshot</p>
          </div>
        </div>
      )} */}
    </div>
  );
});

MemoizedScreenshot.displayName = "MemoizedScreenshot";

function Audit({ results, urlName }: AuditProps) {
  // Memoize time calculation to prevent unnecessary recalculations on re-renders
  const time = useMemo(() => {
    const today = new Date();
    return {
      day: today.getDate(),
      month: today.toLocaleString("default", { month: "short" }),
      year: today.getFullYear(),
      hours: today.getHours(),
      minutes: today.getMinutes().toString().padStart(2, "0"),
    };
  }, []); // Empty dependency array means this only runs once
  // Memoize score calculations to prevent unnecessary recalculations on re-renders
  const {
    onPageScore,
    onPageGrade,
    usabilityScore,
    usabilityGrade,
    techSeoScore,
    techSeoGrade,
    socialScore,
    socialGrade,
    performanceScore,
    performanceGrade,
    linksScore,
    linksGrade,
    overallScore,
  } = useMemo(() => {
    // Extract scores and grades from the results
    const onPageScore = results?.onpage_analysis?.total_score?.score || 0;
    const onPageGrade =
      (results?.onpage_analysis?.total_score?.grade as Grade) || "F";

    const usabilityScore = results?.usability_analysis?.total_score?.score || 0;
    const usabilityGrade =
      (results?.usability_analysis?.total_score?.grade as Grade) || "F";

    const techSeoScore =
      results?.technology_review_analysis?.total_score?.score || 0;
    const techSeoGrade =
      (results?.technology_review_analysis?.total_score?.grade as Grade) || "F";

    const socialScore = results?.social_analysis?.total_score?.score || 0;
    const socialGrade =
      (results?.social_analysis?.total_score?.grade as Grade) || "F";

    // Extract scores for performance and links analysis
    const performanceScore =
      results?.performance_analysis?.total_score?.score || 0;
    const performanceGrade =
      (results?.performance_analysis?.total_score?.grade as Grade) || "F";

    const linksScore = results?.links_analysis?.total_score?.score || 0;
    const linksGrade =
      (results?.links_analysis?.total_score?.grade as Grade) || "F";

    // Calculate overall score and grade
    const scores = [
      onPageScore,
      usabilityScore,
      techSeoScore,
      socialScore,
      performanceScore,
      linksScore,
    ].filter((score) => score > 0);
    const overallScore =
      scores.length > 0
        ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length)
        : 0;

    return {
      onPageScore,
      onPageGrade,
      usabilityScore,
      usabilityGrade,
      techSeoScore,
      techSeoGrade,
      socialScore,
      socialGrade,
      performanceScore,
      performanceGrade,
      linksScore,
      linksGrade,
      overallScore,
    };
  }, [results]); // Only recalculate when results change

  // Note: We've removed the unused getGradeFromScore function since it's not needed

  // Get summary based on overall score - commented out as it's not currently used
  // const getSummary = (score: number): string => {
  //   if (score >= 80) return "Your site is performing well";
  //   if (score >= 60) return "Your site needs some improvements";
  //   if (score >= 40) return "Your site needs significant improvements";
  //   return "Your site needs critical attention";
  // };

  // Memoize the analysis text to prevent unnecessary recalculations
  const analysisText = useMemo(() => {
    // Get detailed analysis text
    const issues = [];

    if (onPageScore < 70) {
      issues.push("on-page SEO elements like meta tags, headings, and content");
    }

    if (usabilityScore < 70) {
      issues.push(
        "usability factors including mobile responsiveness and font legibility"
      );
    }

    if (techSeoScore < 70) {
      issues.push(
        "Technology SEO aspects such as SSL, server configuration, and schema markup"
      );
    }

    if (socialScore < 70) {
      issues.push(
        "social media integration including Open Graph tags, Twitter cards, and social platform connections"
      );
    }

    if (performanceScore < 70) {
      issues.push(
        "performance factors like page speed, resource optimisation, and loading times"
      );
    }

    if (linksScore < 70) {
      issues.push(
        "link structure including backlinks, internal links, and competitor analysis"
      );
    }

    if (issues.length === 0) {
      return "Your website is performing well across all key SEO areas. Continue monitoring and making minor improvements to maintain your competitive edge.";
    } else if (issues.length === 1) {
      return `Your website needs improvement in ${issues[0]}. Focus on addressing these issues to improve your overall SEO performance.`;
    } else {
      const lastIssue = issues.pop();
      return `Your website needs improvement in ${issues.join(
        ", "
      )} and ${lastIssue}. Addressing these areas will significantly boost your SEO performance.`;
    }
  }, [
    onPageScore,
    usabilityScore,
    techSeoScore,
    socialScore,
    performanceScore,
    linksScore,
  ]); // Only recalculate when scores change

  // Navigation functions for ProgressChart clicks
  const navigateToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <BoxPrimary title={`SEO Audit Overview For  ${urlName}`}>
      <div className="flex items-center gap-2 py-2  ">
        <MonitorIcon
          strokeColor="#34405480"
          className="w-6 h-6 text-secondary"
        />
        <div className="text-secondary font-semibold">Your Desktop View</div>
      </div>{" "}
      <div className="w-full h-[266px] lg:h-[385px] relative">
        {results?.desktop_screenshot_url ? (
          <MemoizedScreenshot src={results.desktop_screenshot_url} />
        ) : (
          <div className="w-full h-full rounded-2xl bg-gray-200 animate-pulse flex items-center justify-center">
            <ImageIcon className="w-16 h-16 text-gray-400" />
          </div>
        )}
      </div>
      <div className="mt-6">
        <div className="text-sm lg:text-base font-bold text-gray-700">
          Report Generated at {time.day} {time.month} {time.year} - {time.hours}
          :{time.minutes}
        </div>
      </div>
      <div className="w-full bg-secondary/5 text-sm text-secondary/80 mt-6 p-4 lg:p-6 rounded-lg">
        <p>{analysisText}</p>
        <div className="my-1">
          Find More by checking out the{" "}
          <a
            href="#Recommendations"
            className="mx-1 font-bold text-purple-800 "
          >
            Recommendations
          </a>
        </div>
      </div>
      <div className="mt-4 lg:mt-6 flex flex-col items-center justify-center lg:flex-row lg:items-end gap-8 mb-5.5">
        <ScoreMeter
          score={overallScore}
          title="Overall Performance"
          size="lg"
        />
      </div>
      <div className="mt-4 lg:mt-6 flex flex-col items-center justify-center lg:flex-row lg:items-end gap-8 mb-5.5">
        <ProgressChart
          value={onPageGrade}
          title="On Page SEO"
          progressStates={[
            { label: "Score", value: onPageScore, isNoColor: true },
            { label: "Target", value: 100 },
          ]}
          onClick={() => navigateToSection("onPageSEO")}
        />
        <ProgressChart
          value={techSeoGrade}
          title="Technology Review"
          progressStates={[
            { label: "Score", value: techSeoScore, isNoColor: true },
            { label: "Target", value: 100 },
          ]}
          onClick={() => navigateToSection("technology")}
        />
        <ProgressChart
          value={socialGrade}
          title="Social Media"
          progressStates={[
            { label: "Score", value: socialScore, isNoColor: true },
            { label: "Target", value: 100 },
          ]}
          onClick={() => navigateToSection("social")}
        />
      </div>
      <div className="mt-4 lg:mt-6 flex flex-col items-center justify-center lg:flex-row lg:items-end gap-8 mb-5.5">
        <ProgressChart
          value={usabilityGrade}
          title="Usability"
          progressStates={[
            { label: "Score", value: usabilityScore, isNoColor: true },
            { label: "Target", value: 100 },
          ]}
          onClick={() => navigateToSection("usability")}
        />{" "}
        <ProgressChart
          value={performanceGrade}
          title="Performance"
          progressStates={[
            { label: "Score", value: performanceScore, isNoColor: true },
            { label: "Target", value: 100 },
          ]}
          onClick={() => navigateToSection("performance")}
        />
        <ProgressChart
          value={linksGrade}
          title="Backlinks Analysis"
          progressStates={[
            { label: "Score", value: linksScore, isNoColor: true },
            { label: "Target", value: 100 },
          ]}
          onClick={() => navigateToSection("backlinks")}
        />
      </div>
      <div className="mt-4 lg:mt-6 flex flex-col items-center justify-center lg:flex-row lg:items-end gap-8 mb-5.5"></div>
    </BoxPrimary>
  );
}

// Export the memoized component to prevent unnecessary re-renders
const MemoizedAudit = memo(Audit);
MemoizedAudit.displayName = "Audit";
export default MemoizedAudit;
