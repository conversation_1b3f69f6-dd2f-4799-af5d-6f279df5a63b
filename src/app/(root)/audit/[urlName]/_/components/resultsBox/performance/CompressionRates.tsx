import { PageSizeAnalysis } from "@/types/seoAnalyzerTypes";

type CompressionRatesProps = {
  pageSizeData: PageSizeAnalysis;
  className?: string;
};

const CompressionRates = ({ pageSizeData, className = "" }: CompressionRatesProps) => {
  // Extract data from the page size breakdown
  const { breakdown_estimated_mb } = pageSizeData;

  // Calculate other resources (if any difference between total and sum of known types)
  const totalSize = pageSizeData.total_estimated_size_mb;
  const knownSize =
    breakdown_estimated_mb.html_mb +
    breakdown_estimated_mb.est_css_mb +
    breakdown_estimated_mb.est_js_mb +
    breakdown_estimated_mb.est_images_mb;

  const otherSize = Math.max(0, totalSize - knownSize);

  // Calculate the maximum size to determine the width of progress bars
  const maxSize = Math.max(
    breakdown_estimated_mb.html_mb,
    breakdown_estimated_mb.est_css_mb,
    breakdown_estimated_mb.est_images_mb,
    breakdown_estimated_mb.est_js_mb,
    otherSize
  );

  // Function to calculate the width percentage for each bar
  const getWidthPercentage = (size: number) => {
    return (size / maxSize) * 100;
  };

  return (
    <div className={`w-full ${className}`}>
      <h3 className="text-base font-semibold text-secondary mb-4">Compression Rates</h3>

      {/* HTML */}
      <div className="flex items-center mb-4">
        <div className="w-16 text-sm text-secondary">HTML</div>
        <div className="flex-1 mx-2">
          <div className="w-full h-5 bg-gray-100 rounded-full overflow-hidden">
            <div
              className="h-full rounded-full bg-blue-500"
              style={{ width: `${getWidthPercentage(breakdown_estimated_mb.html_mb)}%` }}
            ></div>
          </div>
        </div>
        <div className="w-20 text-right text-sm text-secondary/70">
          {breakdown_estimated_mb.html_mb.toFixed(2)} MB
        </div>
      </div>

      {/* CSS */}
      <div className="flex items-center mb-4">
        <div className="w-16 text-sm text-secondary">CSS</div>
        <div className="flex-1 mx-2">
          <div className="w-full h-5 bg-gray-100 rounded-full overflow-hidden">
            <div
              className="h-full rounded-full bg-[#FF9BC2]"
              style={{ width: `${getWidthPercentage(breakdown_estimated_mb.est_css_mb)}%` }}
            ></div>
          </div>
        </div>
        <div className="w-20 text-right text-sm text-secondary/70">
          {breakdown_estimated_mb.est_css_mb.toFixed(2)} MB
        </div>
      </div>

      {/* Images */}
      <div className="flex items-center mb-4">
        <div className="w-16 text-sm text-secondary">Images</div>
        <div className="flex-1 mx-2">
          <div className="w-full h-5 bg-gray-100 rounded-full overflow-hidden">
            <div
              className="h-full rounded-full bg-[#2DD4BF]"
              style={{ width: `${getWidthPercentage(breakdown_estimated_mb.est_images_mb)}%` }}
            ></div>
          </div>
        </div>
        <div className="w-20 text-right text-sm text-secondary/70">
          {breakdown_estimated_mb.est_images_mb.toFixed(2)} MB
        </div>
      </div>

      {/* JS */}
      <div className="flex items-center mb-4">
        <div className="w-16 text-sm text-secondary">JS</div>
        <div className="flex-1 mx-2">
          <div className="w-full h-5 bg-gray-100 rounded-full overflow-hidden">
            <div
              className="h-full rounded-full bg-[#9D7BEA]"
              style={{ width: `${getWidthPercentage(breakdown_estimated_mb.est_js_mb)}%` }}
            ></div>
          </div>
        </div>
        <div className="w-20 text-right text-sm text-secondary/70">
          {breakdown_estimated_mb.est_js_mb.toFixed(2)} MB
        </div>
      </div>

      {/* Others (if any) */}
      {otherSize > 0 && (
        <div className="flex items-center mb-4">
          <div className="w-16 text-sm text-secondary">Others</div>
          <div className="flex-1 mx-2">
            <div className="w-full h-5 bg-gray-100 rounded-full overflow-hidden">
              <div
                className="h-full rounded-full bg-[#FBBF24]"
                style={{ width: `${getWidthPercentage(otherSize)}%` }}
              ></div>
            </div>
          </div>
          <div className="w-20 text-right text-sm text-secondary/70">
            {otherSize.toFixed(2)} MB
          </div>
        </div>
      )}
    </div>
  );
};

export default CompressionRates;
