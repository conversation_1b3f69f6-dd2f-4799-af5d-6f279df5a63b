import { FacebookIcon } from "@/ui/icons/socialMedia";
import LinkCard from "../../LinkCard";
import ResultCard from "../../ResultCard";

export default function LinkdinPageLinked() {
  return (
    <div>
      <div>
        <h4 className="text-secondary font-semibold">Linkedin Page Linked</h4>
      </div>
      
      <LinkCard title="http://facbook.com">
        <FacebookIcon />
      </LinkCard>
      <div className="mt-2 flex flex-col gap-2">
        <ResultCard
          title="HTTPS Redirect"
          description="cursus vitae congue mauris rhoncus aenean vel nisl nisi Arcu cursus vitae purus viverra "
          passed
        />
        <ResultCard
          title="BackLink Summary"
          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore rhoncus aenean vel elit scelerisque purus viverra accumsan in nisl nisi Arcu cursus vitae purus viverra "
        />
        <ResultCard
          title="BackLink Summary"
          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore rhoncus aenean vel elit scelerisque purus viverra accumsan in nisl nisi Arcu cursus vitae purus viverra "
          btnLink="#"
        />
      </div>
    </div>
  );
}
