"use client";
import React from "react";
import { TechnologyAnalysis } from "@/types/seoAnalyzerTypes";
import { TechnologySection } from "../../pdf/TechnologySection";
import BoxPrimary from "../../BoxPrimary";
import ProgressChart from "@/ui/charts/ProgressChart";
import OverallSection from "../OverallSection";

type TechnologyWebProps = {
  results: Partial<TechnologyAnalysis> | null;
};

export default function TechnologyWeb({ results }: TechnologyWebProps) {
  // Handle null or undefined results
  if (!results) {
    return (
      <BoxPrimary title="Technology Review">
        <div className="w-full flex flex-col items-center gap-4 lg:gap-0 lg:flex-row lg:items-start">
          <ProgressChart
            value="F"
            title="Technology Score"
            size="lg"
            progressStates={[
              { label: "Grade", value: 0, isNoColor: false },
            ]}
          />

          <OverallSection
            title="Technology Review Analysis"
            description="Analyzing your website's technology stack..."
          />
        </div>
      </BoxPrimary>
    );
  }

  // Extract the total score and grade - show default values if not available yet
  const totalScore = results?.total_score || { score: 0, grade: "F" };

  // Ensure grade is properly typed for ProgressChart
  const grade =
    (totalScore.grade as
      | "A+"
      | "A"
      | "A-"
      | "B+"
      | "B"
      | "B-"
      | "C+"
      | "C"
      | "C-"
      | "D+"
      | "D"
      | "D-"
      | "F") || "F";

  return (
    <BoxPrimary title="Technology Review">
      <div className="w-full flex flex-col items-center gap-4 lg:gap-0 lg:flex-row lg:items-start">
        <ProgressChart
          value={grade}
          title="Technology Score"
          size="lg"
          progressStates={[
            { label: "Grade", value: totalScore.score, isNoColor: false },
          ]}
        />

        <OverallSection
          title={results.overall_title || "Technology Review Analysis"}
          description={
            results.overall_description ||
            "Analyzing your website's technology stack..."
          }
        />
      </div>

      {/* Use the new modular PDF component for detailed analysis */}
      <div className="mt-8">
        <TechnologySection
          technologyData={results}
          brand_name="SEO Analyzer"
          brand_website="seoanalyser.com.au"
          brand_photo={null}
          onImageLoad={() => {}}
          onImageError={() => {}}
          onTechnologyIconLoad={() => {}}
        />
      </div>
    </BoxPrimary>
  );
}
