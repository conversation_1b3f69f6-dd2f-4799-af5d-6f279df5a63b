import MiniLine<PERSON>hart from "@/ui/charts/MiniLineChart";
import ProgressChart from "@/ui/charts/ProgressChart";
import { UnionDownIcon } from "@/ui/icons/navigation";
import React from "react";

export default function VisitorsOverview() {
  return (
    <div className="flex w-full flex-col lg:flex-row items-start gap-6">
      <div className="w-full lg:!w-[321px] border border-light-gray rounded-lg p-4 py-6">
        <div className="flex items-center pb-6 mb-6 border-b border-b-light-gray-4/50">
          <div>
            <MiniLineChart
              variant="red"
              data={[
                { name: "1", pv: 10 },
                { name: "2", pv: 100 },
                { name: "3", pv: 20 },
                { name: "4", pv: 100 },
                { name: "4", pv: 30 },
                { name: "5", pv: 130 },
              ]}
            />
          </div>
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-1">
              <span className="text-2xl font-semibold text-secondary">
                1,235
              </span>
              <UnionDownIcon className="text-primary-red-2 rotate-180" />
            </div>
            <p className="text-xs text-secondary/50">Visitors this month</p>
          </div>
        </div>

        <div className="flex items-center">
          <div>
            <MiniLineChart
              variant="green"
              data={[
                { name: "1", pv: 130 },
                { name: "2", pv: 5 },
                { name: "3", pv: 120 },
                { name: "4", pv: 5 },
                { name: "4", pv: 100 },
                { name: "5", pv: 140 },
              ]}
            />
          </div>
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-1">
              <span className="text-2xl font-semibold text-secondary">456</span>
              <UnionDownIcon className="text-primary-green-2" />
            </div>
            <p className="text-xs text-secondary/50">Visitors this month</p>
          </div>
        </div>
      </div>
      <div className=" hidden lg:flex items-center gap-6">
        <ProgressChart score={30} title="Backlink Quality" size="md" />
        <ProgressChart score={2} title="Backlink Growth" size="md" />
      </div>
      <div className="w-full lg:hidden flex items-center justify-center gap-6">
        <ProgressChart score={30} title="Backlink Quality" size="sm" />
        <ProgressChart score={2} title="Backlink Growth" size="sm" />
      </div>
    </div>
  );
}
