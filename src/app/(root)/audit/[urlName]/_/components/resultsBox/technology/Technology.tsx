import ProgressChart from "@/ui/charts/ProgressChart";
import BoxPrimary from "../../BoxPrimary";
import TechnologySocials from "./TechnologySocials";
import StateCard from "../../StateCard";
import {
  QuoteIcon,
  ScreenIcon,
  DocumentCheckIcon,
  DocumentCrossIcon,
} from "@/ui/icons/general";
import { CategoryOutlineIcon } from "@/ui/icons/categories";
import { ListLeftIcon } from "@/ui/icons/general/ListLeftIcon";
import ShowMoreSection from "../usability/ShowMoreSection";
import { TechSEOAnalysis } from "@/types/seoAnalyzerTypes";
import LoadingSlate from "@/components/loading/LoadingSlate";
import OverallSection from "../OverallSection";
import abbreviateNumber from "@/utils/abbreviateNumber";

type TechnologyProps = {
  results: TechSEOAnalysis | string | null;
};

export default function Technology({ results }: TechnologyProps) {
  // Optimized data handling - show content immediately when any meaningful data is available
  const hasAnyData =
    results &&
    typeof results === "object" &&
    results !== null &&
    (results.total_score ||
      results.technologies ||
      results.ssl_enabled ||
      results.dns_servers ||
      results.server_ip ||
      results.overall_title ||
      Object.keys(results).length > 0);

  // Only show loading state if we have absolutely no data
  if (!hasAnyData) {
    return (
      <BoxPrimary title="Technology Review Results">
        <LoadingSlate
          title="Loading Technology Review results..."
          showHeader={true}
          showCards={true}
          cardCount={4}
          showChart={false}
          showProgress={true}
          height="lg"
        />
      </BoxPrimary>
    );
  }

  // Check if we have header data
  const hasHeaderData = results.overall_title || results.overall_description;

  // Safe access to nested properties with fallbacks
  const totalScore = results.total_score || { score: 0, grade: "F" };

  return (
    <BoxPrimary title="Technology Review Results">
      {/* Display technologies */}
      {results.technologies && results.technologies.technologies && (
        <TechnologySocials data={results.technologies.technologies} />
      )}

      {/* Progress charts */}
      <div className="mt-4 lg:mt-6 flex justify-center gap-6">
        <ProgressChart
          value={
            totalScore.grade as
              | "A+"
              | "A"
              | "A-"
              | "B+"
              | "B"
              | "B-"
              | "C+"
              | "C"
              | "C-"
              | "D+"
              | "D"
              | "D-"
              | "F"
          }
          title="Technology Review Score"
          size="md"
          progressStates={[
            {
              label: "Grade",
              value: totalScore.score,
              isNoColor: false,
            },
          ]}
        />
      </div>

      {/* State cards */}
      <div className="w-full grid grid-cols-2 lg:grid-cols-4 gap-4 mt-4 lg:mt-6">
        <StateCard
          icon={<ScreenIcon className="w-6 h-6" />}
          value={results.technologies?.technology_count?.toString() || "0"}
          label="Technologies Used"
        />
        <StateCard
          icon={<CategoryOutlineIcon className="w-6 h-6" />}
          value={results.dns_servers?.count?.toString() || "0"}
          label="DNS Servers"
        />
        <StateCard
          icon={<ListLeftIcon className="w-6 h-6" />}
          value={results.server_ip?.all_ips?.length?.toString() || "0"}
          label="Server IPs"
        />
        <StateCard
          icon={<QuoteIcon className="w-6 h-6" />}
          value={results.ssl_enabled?.pass ? "Yes" : "No"}
          label="SSL Enabled"
        />
      </div>

      {/* Summary section */}
      {hasHeaderData ? (
        <div className="mt-4 lg:mt-6">
          <OverallSection
            title={results.overall_title || "Technology Review Analysis"}
            description={
              results.overall_description ||
              "Technology Review is the foundation of your website's search engine visibility. It ensures search engines can properly crawl, index, and understand your content."
            }
          />
        </div>
      ) : (
        <div className="w-full p-4 lg:p-6 rounded-lg bg-primary/10 mt-4 lg:mt-6 animate-pulse">
          <div className="h-5 bg-primary/20 rounded w-3/4 mb-3"></div>
          <div className="h-4 bg-primary/15 rounded w-full mb-2"></div>
          <div className="h-4 bg-primary/15 rounded w-5/6"></div>
        </div>
      )}

      {/* Technology Review details */}
      <div className="mt-6 space-y-6">
        {/* SSL */}
        {results.ssl_enabled && (
          <ShowMoreSection
            title="SSL Certificate"
            passed={results.ssl_enabled.pass}
            description={results.ssl_enabled.description}
            icon={
              results.ssl_enabled.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.ssl_enabled.importance}
            recommendation={results.ssl_enabled.recommendation}
          >
            <div className="p-4">
              <div className="flex items-center mb-2">
                <span className="font-semibold mr-2 text-secondary/85">
                  Status:
                </span>
                {results.ssl_enabled.pass ? (
                  <span className="text-primary-green flex items-center text-secondary/85">
                    <DocumentCheckIcon className="w-4 h-4 mr-1" /> Enabled
                  </span>
                ) : (
                  <span className="text-primary-red flex items-center text-secondary/85">
                    <DocumentCrossIcon className="w-4 h-4 mr-1" /> Not Enabled
                  </span>
                )}
              </div>
            </div>
          </ShowMoreSection>
        )}

        {/* Robots Meta */}
        {results.robots_meta && (
          <ShowMoreSection
            title="Robots Meta Tag"
            passed={!results.robots_meta.noindex}
            description={results.robots_meta.description}
            icon={
              !results.robots_meta.noindex ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.robots_meta.importance}
            recommendation={results.robots_meta.recommendation}
          >
            <div className="p-4">
              <div className="flex items-center mb-2">
                <span className="font-semibold mr-2 text-secondary/85">
                  Indexing Status:
                </span>
                {!results.robots_meta.noindex ? (
                  <span className="text-primary-green flex items-center text-secondary/85">
                    <DocumentCheckIcon className="w-4 h-4 mr-1" /> Page is
                    indexable
                  </span>
                ) : (
                  <span className="text-primary-red flex items-center text-secondary/85">
                    <DocumentCrossIcon className="w-4 h-4 mr-1" /> Page is not
                    indexable (noindex)
                  </span>
                )}
              </div>
            </div>
          </ShowMoreSection>
        )}

        {/* DNS Servers */}
        {results.dns_servers && (
          <ShowMoreSection
            title="DNS Servers"
            passed={results.dns_servers.pass}
            description={results.dns_servers.description}
            icon={
              results.dns_servers.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.dns_servers.importance}
            recommendation={results.dns_servers.recommendation}
          >
            <div className="p-4  rounded-lg">
              <div className="mb-3">
                <span className="font-semibold text-secondary/85">
                  Number of DNS Servers:{" "}
                </span>
                <span className="text-secondary/85">
                  {results.dns_servers.count}
                </span>
              </div>

              {results.dns_servers.nameservers &&
                results.dns_servers.nameservers.length > 0 && (
                  <div>
                    <h6 className="font-semibold mb-2 text-secondary/85">
                      Nameservers:
                    </h6>
                    <ul className="list-disc pl-5 space-y-1">
                      {results.dns_servers.nameservers.map((server, index) => (
                        <li key={index} className="text-sm text-secondary/85">
                          {server}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* Web Server */}
        {results.web_server && (
          <ShowMoreSection
            title="Web Server"
            passed={results.web_server.pass}
            description={results.web_server.description}
            icon={
              results.web_server.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.web_server.importance}
            recommendation={results.web_server.recommendation}
          >
            <div className="p-4  rounded-lg">
              <div className="mb-2">
                <span className="font-semibold text-secondary/85">
                  Server Type:{" "}
                </span>
                <span className="text-secondary/85">
                  {results.web_server.server || "Unknown"}
                </span>
              </div>

              <div className="mt-2 text-sm text-secondary/85">
                {results.web_server.server === "Unknown" ? (
                  <p>
                    Server information is hidden, which is a good security
                    practice.
                  </p>
                ) : (
                  <p>
                    Server information is visible. Consider hiding this
                    information for better security.
                  </p>
                )}
              </div>
            </div>
          </ShowMoreSection>
        )}

        {/* Charset */}
        {results.charset && (
          <ShowMoreSection
            title="Character Encoding"
            passed={results.charset.is_standard}
            description={results.charset.description}
            icon={
              results.charset.is_standard ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.charset.importance}
            recommendation={results.charset.recommendation}
          >
            <div className="p-4  rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="mb-2">
                    <span className="font-semibold text-secondary/85">
                      Charset:{" "}
                    </span>
                    <span className="text-secondary/85">
                      {results.charset.charset}
                    </span>
                  </div>
                  <div className="mb-2">
                    <span className="font-semibold text-secondary/85">
                      Source:{" "}
                    </span>
                    <span className="text-secondary/85">
                      {results.charset.source}
                    </span>
                  </div>
                </div>
                <div>
                  <div className="mb-2">
                    <span className="font-semibold text-secondary/85">
                      Standard Encoding:{" "}
                    </span>
                    <span
                      className={`${
                        results.charset.is_standard
                          ? "text-primary-green"
                          : "text-primary-red"
                      } text-secondary/85`}
                    >
                      {results.charset.is_standard ? "Yes" : "No"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </ShowMoreSection>
        )}

        {/* Server IP */}
        {results.server_ip && (
          <ShowMoreSection
            title="Server IP"
            passed={results.server_ip.pass}
            description={results.server_ip.description}
            icon={
              results.server_ip.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.server_ip.importance}
            recommendation={results.server_ip.recommendation}
          >
            <div className="p-4  rounded-lg">
              <div className="mb-3">
                <span className="font-semibold text-secondary/85">
                  Primary IP:{" "}
                </span>
                <span className="font-mono text-secondary/85">
                  {results.server_ip.ip}
                </span>
              </div>

              {results.server_ip.all_ips &&
                results.server_ip.all_ips.length > 0 && (
                  <div>
                    <h6 className="font-semibold mb-2 text-secondary/85">
                      All Server IPs:
                    </h6>
                    <ul className="list-disc pl-5 space-y-1">
                      {results.server_ip.all_ips.map((ip, index) => (
                        <li
                          key={index}
                          className="text-sm font-mono text-secondary/85"
                        >
                          {ip}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
            </div>
          </ShowMoreSection>
        )}

        {/* Technologies */}
        {results.technologies && (
          <ShowMoreSection
            title="Technologies"
            passed={results.technologies.pass}
            description={results.technologies.description}
            icon={
              results.technologies.pass ? (
                <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
              ) : (
                <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
              )
            }
            importance={results.technologies.importance}
            recommendation={results.technologies.recommendations?.[0] || null}
          >
            {/* <div className="p-4  rounded-lg">
              <div className="mb-3">
                <span className="font-semibold text-secondary/85">Technologies Detected: </span>
                <span className="text-secondary/85">{results.technologies.technology_count}</span>
              </div>

              {results.technologies.technologies && results.technologies.technologies.length > 0 && (
                <div className="mt-3">
                  <h6 className="font-semibold mb-2 text-secondary/85">Technology Stack:</h6>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {results.technologies.technologies.map((tech, index) => (
                      <div key={index} className="bg-white p-3 rounded border border-gray-200">
                        <div className="font-medium text-secondary/85">{tech.name}</div>
                        <div className="text-sm text-secondary/85 mt-1">
                          <span className="font-semibold">Category: </span>
                          {tech.category}
                        </div>
                        {tech.version && tech.version !== "Detected" && (
                          <div className="text-sm text-secondary/85">
                            <span className="font-semibold">Version: </span>
                            {tech.version}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div> */}
          </ShowMoreSection>
        )}
      </div>

      {/* Email Security section */}
      {results.dmarc_record && results.spf_record && (
        <div className="mt-8 lg:mt-10">
          <div>
            <h4 className="text-secondary font-semibold">
              Email Security Information
            </h4>
            <p className="text-sm text-secondary/60 mt-4">
              Email security records help protect your domain from email
              spoofing and phishing attacks.
            </p>
          </div>

          <div className="mt-6 space-y-6">
            {/* DMARC Record */}
            <ShowMoreSection
              title="DMARC Record"
              passed={results.dmarc_record.pass}
              description={results.dmarc_record.description}
              icon={
                results.dmarc_record.pass ? (
                  <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
                ) : (
                  <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
                )
              }
              importance={results.dmarc_record.importance}
              recommendation={results.dmarc_record.recommendation}
            >
              <div className="p-4  rounded-lg">
                <div className="mb-3">
                  <span className="font-semibold text-secondary/85">
                    Policy:{" "}
                  </span>
                  <span
                    className={`${
                      results.dmarc_record.policy === "reject"
                        ? "text-primary-green"
                        : results.dmarc_record.policy === "quarantine"
                        ? "text-yellow-600"
                        : "text-primary-red"
                    } text-secondary/85`}
                  >
                    {results.dmarc_record.policy || "none"}
                  </span>
                </div>

                {results.dmarc_record.record && (
                  <div>
                    <h6 className="font-semibold mb-2 text-secondary/85">
                      DMARC Record:
                    </h6>
                    <div className="bg-gray-100 p-3 rounded font-mono text-sm break-all text-secondary/85">
                      {results.dmarc_record.record}
                    </div>
                  </div>
                )}
              </div>
            </ShowMoreSection>

            {/* SPF Record */}
            <ShowMoreSection
              title="SPF Record"
              passed={results.spf_record.pass}
              description={results.spf_record.description}
              icon={
                results.spf_record.pass ? (
                  <DocumentCheckIcon className="w-16 h-16 text-primary-green" />
                ) : (
                  <DocumentCrossIcon className="w-16 h-16 text-primary-red" />
                )
              }
              importance={results.spf_record.importance}
              recommendation={results.spf_record.recommendation}
            >
              <div className="p-4  rounded-lg">
                <div className="mb-3">
                  <span className="font-semibold text-secondary/85">
                    Policy Strength:{" "}
                  </span>
                  <span
                    className={`${
                      results.spf_record.policy_strength === "strict"
                        ? "text-primary-green"
                        : results.spf_record.policy_strength === "moderate"
                        ? "text-yellow-600"
                        : "text-primary-red"
                    } text-secondary/85`}
                  >
                    {results.spf_record.policy_strength || "none"}
                  </span>
                </div>

                {results.spf_record.record && (
                  <div>
                    <h6 className="font-semibold mb-2 text-secondary/85">
                      SPF Record:
                    </h6>
                    <div className="bg-gray-100 p-3 rounded font-mono text-sm break-all text-secondary/85">
                      {results.spf_record.record}
                    </div>
                  </div>
                )}
              </div>
            </ShowMoreSection>
          </div>
        </div>
      )}
    </BoxPrimary>
  );
}
