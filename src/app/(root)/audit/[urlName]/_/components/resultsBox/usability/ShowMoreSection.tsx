"use client";
import React, { ReactNode, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

type RecommendationType = {
  text: string;
  priority: string;
};

type ShowMoreSectionProps = {
  title: string;
  description: string | ReactNode;
  passed?: boolean;
  icon?: ReactNode;
  importance?: string;
  recommendation?: string | RecommendationType;
  children?: ReactNode;
  actionButton?: ReactNode; // Optional action button to display before Show More button
};

export default function ShowMoreSection({
  title,
  description,
  passed,
  icon,
  importance,
  children,
  recommendation,
  actionButton,
}: ShowMoreSectionProps) {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <motion.div
      className="w-full flex flex-col p-3 sm:p-4 py-4 rounded-lg border border-light-gray overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{
        opacity: 1,
        transition: {
          duration: 0.25,
        },
      }}
    >
      <div className="flex items-start gap-2 sm:gap-2.5 !flex-row">
        {icon && (
          <div className="flex self-stretch items-center justify-center mr-3">
            {icon}
          </div>
        )}
        <motion.div
          className="flex-1 flex flex-col gap-1.5 sm:gap-2"
          initial={{ opacity: 0, x: -5 }}
          animate={{
            opacity: 1,
            x: 0,
            transition: {
              delay: 0.15,
              duration: 0.2,
            },
          }}
        >
          <h5 className="text-secondary font-bold text-sm sm:text-base">
            {title}
          </h5>
          <div className="text-xs sm:text-sm text-secondary/80">
            {description}
          </div>
        </motion.div>
      </div>

      {/* Main content - always visible */}
      {children && (
        <motion.div
          className="mt-3 sm:mt-4"
          initial={{ opacity: 0 }}
          animate={{
            opacity: 1,
            transition: {
              delay: 0.2,
              duration: 0.3,
            },
          }}
        >
          {children}
        </motion.div>
      )}

      {/* Recommendation and Importance section - shown when "Show More" is clicked */}
      <AnimatePresence>
        {showDetails && (
          <motion.div
            initial={{ opacity: 0, height: 0, marginTop: 0 }}
            animate={{
              opacity: 1,
              height: "auto",
              marginTop: "0.75rem",
              transition: {
                height: {
                  duration: 0.3,
                  ease: [0.04, 0.62, 0.23, 0.98],
                },
                opacity: {
                  duration: 0.25,
                  delay: 0.05,
                },
              },
            }}
            exit={{
              opacity: 0,
              height: 0,
              marginTop: 0,
              transition: {
                height: {
                  duration: 0.25,
                  ease: [0.04, 0.62, 0.23, 0.98],
                },
                opacity: {
                  duration: 0.2,
                },
              },
            }}
            className="overflow-hidden"
          >
            {/* Recommendation section - only show when test failed (passed is false) */}
            {recommendation && passed === false && (
              <motion.div
                initial={{ y: 10, opacity: 0 }}
                animate={{
                  y: 0,
                  opacity: 1,
                  transition: {
                    delay: 0.1,
                    duration: 0.2,
                  },
                }}
                className="mb-3 sm:mb-4"
              >
                <div
                  className={`p-3 sm:p-6 rounded-md ${
                    typeof recommendation === "object" &&
                    "priority" in recommendation
                      ? recommendation.priority.toLowerCase() === "high"
                        ? "bg-red-100 text-red-800"
                        : recommendation.priority.toLowerCase() === "medium"
                        ? "bg-yellow-100 text-yellow-900"
                        : "bg-green-100 text-green-800"
                      : "bg-blue-50 text-blue-700"
                  }`}
                >
                  <h6 className="font-bold mb-1.5 sm:mb-2 text-sm sm:text-base">
                    Recommendation
                  </h6>
                  <p className="text-xs sm:text-sm mt-1">
                    {typeof recommendation === "object" &&
                    "text" in recommendation
                      ? recommendation.text
                      : String(recommendation)}
                  </p>
                  {typeof recommendation === "object" &&
                    "priority" in recommendation && (
                      <motion.div
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{
                          scale: 1,
                          opacity: 1,
                          transition: {
                            delay: 0.2,
                            duration: 0.15,
                          },
                        }}
                        className="mt-2"
                      >
                        <span
                          className={`inline-block px-2 sm:px-3 py-0.5 sm:py-1 text-xs font-medium rounded-full ${
                            recommendation.priority.toLowerCase() === "high"
                              ? "bg-red-200 text-red-800"
                              : recommendation.priority.toLowerCase() ===
                                "medium"
                              ? "bg-yellow-300 text-yellow-800"
                              : "bg-green-300 text-green-900"
                          }`}
                        >
                          {recommendation.priority} Priority
                        </span>
                      </motion.div>
                    )}
                </div>
              </motion.div>
            )}

            {/* Importance section */}
            {importance && (
              <motion.div
                initial={{ y: 10, opacity: 0 }}
                animate={{
                  y: 0,
                  opacity: 1,
                  transition: {
                    delay: recommendation && passed === false ? 0.15 : 0.1,
                    duration: 0.2,
                  },
                }}
                className="p-3 sm:p-6 bg-primary/10 rounded-md text-primary gap-3 sm:gap-5"
              >
                <h6 className="font-bold mb-1.5 sm:mb-2 text-sm sm:text-base text-primary">
                  Importance
                </h6>
                <p className="text-xs sm:text-sm text-primary/70 mt-1">
                  {importance}
                </p>
                {/* <a href="" className="text-primary font-semibold mt-3 text-sm">
                  Learn More </a> */}
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Action button and Show More/Less button */}
      <div className="flex justify-between items-center mt-3 sm:mt-4">
        {/* Action Button (if provided) */}
        {actionButton && <div className="flex-1">{actionButton}</div>}

        {/* Show More/Less button - only show when there's content to display */}
        {((recommendation && passed === false) || importance) && (
          <div className={`${actionButton ? "ml-2" : "ml-auto"}`}>
            <motion.button
              onClick={() => setShowDetails(!showDetails)}
              className="btn btn--outline-light text-xs sm:text-sm px-2.5 sm:px-4 py-1.5 sm:py-2 font-bold"
              whileTap={{ scale: 0.9 }}
            >
              {showDetails ? "Show Less" : "Show More"}
            </motion.button>
          </div>
        )}
      </div>
    </motion.div>
  );
}
