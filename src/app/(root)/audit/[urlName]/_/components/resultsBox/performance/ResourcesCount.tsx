import { CssIcon, HashtagIcon, HtmlIcon, JsIcon } from "@/ui/icons/resources";
import { ImageIcon } from "@/ui/icons/general";

type ResourcesCountProps = {
  html: number;
  css: number;
  js: number;
  images: number;
  other: number;
  description?: string;
};

export default function ResourcesCount({
  html,
  css,
  js,
  images,
  other,
  description = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Egestas",
}: ResourcesCountProps) {
  return (
    <div className="w-full">
      <h3 className="text-lg font-semibold text-secondary mb-3">
        Number of Resources
      </h3>
      <p className="text-sm text-secondary/70 mb-6">{description}</p>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4">
        {/* HTML Resources */}
        <div className="flex flex-col items-center">
          <div className="mb-2">
            <HtmlIcon className="w-16 h-16 text-secondary" />
          </div>
          <div className="text-xl font-bold text-secondary">{html}</div>
          <div className="text-sm text-secondary/70 text-center">HTML</div>
        </div>

        {/* CSS Resources */}
        <div className="flex flex-col items-center">
          <div className="mb-2">
            <CssIcon className="w-16 h-16 text-secondary" />
          </div>
          <div className="text-xl font-bold text-secondary">{css}</div>
          <div className="text-sm text-secondary/70 text-center">CSS </div>
        </div>

        {/* JS Resources */}
        <div className="flex flex-col items-center">
          <div className="mb-2">
            <JsIcon className="w-16 h-16 text-secondary" />
          </div>
          <div className="text-xl font-bold text-secondary">{js}</div>
          <div className="text-sm text-secondary/70 text-center">
            Javascript
          </div>
        </div>

        {/* Images Resources */}
        <div className="flex flex-col items-center">
          <div className="mb-2">
            <ImageIcon className="w-16 h-16 text-secondary" />
          </div>
          <div className="text-xl font-bold text-secondary">{images}</div>
          <div className="text-sm text-secondary/70 text-center">Images</div>
        </div>

        {/* Other Resources */}
        <div className="flex flex-col items-center">
          <div className="mb-2">
            <HashtagIcon className="w-16 h-16 text-secondary" />
          </div>
          <div className="text-xl font-bold text-secondary">{other}</div>
          <div className="text-sm text-secondary/70 text-center">Hashtags</div>
        </div>

        {/* Total Resources */}
        <div className="flex flex-col items-center">
          <div className="mb-2 flex items-center justify-center">
            <div className="text-4xl text-center font-bold text-secondary h-16 w-16">
              ...
            </div>
          </div>
          <div className="text-xl font-bold text-secondary">
            {html + css + js + images + other}
          </div>
          <div className="text-sm text-secondary/70 text-center">Total</div>
        </div>
      </div>
    </div>
  );
}
