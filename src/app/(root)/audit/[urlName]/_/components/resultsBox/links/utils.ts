import { LinksAnalysisData, ActiveSectionType, MetricInfo } from "./types";
import abbreviateNumber from "@/utils/abbreviateNumber";

// Helper function to get metric value safely
export const getMetricValue = (
  section: any,
  metricName: string,
  fallback: any = 0
) => {
  return section?.metrics?.[metricName]?.value ?? fallback;
};

// Helper function to get metric info (importance, description, recommendation)
export const getMetricInfo = (section: any, metricName: string): MetricInfo => {
  const metric = section?.metrics?.[metricName];
  return {
    importance: metric?.importance,
    description: metric?.description,
    recommendation: metric?.recommendation,
  };
};

// Helper function to format values with appropriate prefixes based on field type
export const formatMetricValue = (
  section: any,
  metricName: string,
  fallback: any = 0,
  formatType: "count" | "score" | "percentage" = "count"
) => {
  const value = getMetricValue(section, metricName, fallback);

  switch (formatType) {
    case "count":
      return typeof value === "number" ? abbreviateNumber(value) : value;
    case "score":
      return value.toString();
    case "percentage":
      return `${value}%`;
    default:
      return typeof value === "number"
        ? abbreviateNumber(value)
        : value.toString();
  }
};

// Helper function to format values for ShowMoreSection with prefixes
export const formatMetricValueWithPrefix = (
  section: any,
  metricName: string,
  fallback: any = 0,
  formatType: "count" | "score" | "percentage" = "count"
) => {
  const value = getMetricValue(section, metricName, fallback);
  const formattedValue =
    typeof value === "number" ? value.toLocaleString() : value;

  switch (formatType) {
    case "count":
      return formattedValue;
    case "score":
      return value.toString();
    case "percentage":
      return `${value}%`;
    default:
      return formattedValue;
  }
};

// Helper function to get the total score from main field or fallback to total_score
export const getTotalScore = (effectiveResults: LinksAnalysisData) => {
  // First try to get from main.totalScore (new format)
  if (effectiveResults?.main?.totalScore?.score !== undefined) {
    return effectiveResults.main.totalScore.score;
  }
  // Fallback to total_score field (existing LinksAnalysis format)
  const linksAnalysis = effectiveResults as any;
  return linksAnalysis?.total_score?.score ?? 0;
};

// Helper function to get the total grade from main field or fallback to total_score
export const getTotalGrade = (effectiveResults: LinksAnalysisData) => {
  // First try to get from main.totalScore (new format)
  if (effectiveResults?.main?.totalScore?.Grade !== undefined) {
    return effectiveResults.main.totalScore.Grade;
  }
  // Fallback to total_score field (existing LinksAnalysis format)
  const linksAnalysis = effectiveResults as any;
  return linksAnalysis?.total_score?.grade ?? "N/A";
};

// Get the active data based on the selected section
export const getActiveData = (
  activeSection: ActiveSectionType,
  effectiveResults: LinksAnalysisData
) => {
  const brokenLinks = effectiveResults.broken_links;
  const domainInsight = effectiveResults.domain_insight;
  const friendlyLinks = effectiveResults.friendly_links;
  const backlinksDetail = effectiveResults.backlinks_detail;
  const overallBacklinks = effectiveResults.overall_backlinks;

  switch (activeSection) {
    case "overall_backlinks":
      return {
        title: overallBacklinks?.overall_title || "Backlinks Overview",
        description:
          overallBacklinks?.overall_description ||
          "Overview of all link metrics including backlinks and more.",
        importance: "Links are a crucial factor in search engine rankings.",
      };
    case "backlinks_detail":
      return {
        title: backlinksDetail?.overall_title || "Backlinks Detail",
        description:
          backlinksDetail?.overall_description ||
          "Detailed backlink analysis helps understand link quality and sources.",
        importance:
          "Detailed backlink analysis helps understand link quality and sources.",
      };
    case "domain_insight":
      return {
        title: domainInsight?.overall_title || "Domain Insight",
        description:
          domainInsight?.overall_description ||
          "Domain authority and page metrics analysis.",
        importance:
          "Domain authority metrics help understand your site's credibility and ranking potential.",
      };
    case "friendly_links":
      return {
        title: friendlyLinks?.overall_title || "Friendly Links",
        description:
          friendlyLinks?.overall_description ||
          "Analysis of anchor text readability.",
        importance: "Clear anchor text improves user experience.",
      };
    case "broken_links":
      return {
        title: brokenLinks?.overall_title || "Broken Links",
        description:
          brokenLinks?.overall_description ||
          "Analysis of broken links on the page.",
        importance: "Broken links negatively impact user experience.",
      };
    default:
      return {
        title: "Links Analysis",
        description: "Data unavailable.",
        importance: "This metric is important for SEO.",
      };
  }
};
