import Table from "@/ui/Table";
import ProgressPercent from "../../ProgressPercent";

type HeadersData = {
  h2?: { count: number; content: string[] };
  h3?: { count: number; content: string[] };
  h4?: { count: number; content: string[] };
  h5?: { count: number; content: string[] };
  h6?: { count: number; content: string[] };
};

type HeaderTagsProps = {
  headers?: HeadersData;
};

export default function HeaderTags({ headers }: HeaderTagsProps = {}) {
  // If no headers data is provided, use empty data
  const headersData = headers || {
    h2: { count: 0, content: [] },
    h3: { count: 0, content: [] },
    h4: { count: 0, content: [] },
    h5: { count: 0, content: [] },
    h6: { count: 0, content: [] }
  };

  // Calculate total count of all header tags
  const h2Count = headersData.h2?.count || 0;
  const h3Count = headersData.h3?.count || 0;
  const h4Count = headersData.h4?.count || 0;
  const h5Count = headersData.h5?.count || 0;
  const h6Count = headersData.h6?.count || 0;
  const totalCount = h2Count + h3Count + h4Count + h5Count + h6Count;

  // Calculate percentages based on total count
  const calculatePercentage = (count: number): number => {
    if (totalCount === 0) return 0;
    return Math.round((count / totalCount) * 100);
  };

  // Create table data from headers with dynamic percentages
  const tableData = [
    { tag: "H2", frequency: h2Count.toString(), percentage: calculatePercentage(h2Count) },
    { tag: "H3", frequency: h3Count.toString(), percentage: calculatePercentage(h3Count) },
    { tag: "H4", frequency: h4Count.toString(), percentage: calculatePercentage(h4Count) },
    { tag: "H5", frequency: h5Count.toString(), percentage: calculatePercentage(h5Count) },
    { tag: "H6", frequency: h6Count.toString(), percentage: calculatePercentage(h6Count) },
  ];

  return (
    <div className="space-y-6">
      <Table>
        <Table.Header>
          <th className="w-[58px] lg:w-[267px] whitespace-nowrap pr-4 lg:pr-0">Header-Tag</th>
          <th className="w-[58px] lg:w-[267px]">Frequency</th>
          <th className="w-[188px] lg:w-[230px]"></th>
        </Table.Header>
        <Table.Body>
          {tableData.map((item, index) => (
            <Table.Row key={index}>
              <td>{item.tag}</td>
              <td>{item.frequency}</td>
              <td>
                <div className="w-full flex justify-end">
                  <div className="w-[188px] lg:w-[230px]">
                    <ProgressPercent percentage={item.percentage} />
                  </div>
                </div>
              </td>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </div>
  );
}
