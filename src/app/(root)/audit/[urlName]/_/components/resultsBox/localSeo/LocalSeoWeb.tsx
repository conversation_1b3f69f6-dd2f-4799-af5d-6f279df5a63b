"use client";
import React from "react";
import { LocalSEOAnalysis } from "@/types/seoAnalyzerTypes";
import { LocalSeoSection } from "../../pdf/LocalSeoSection";
import BoxPrimary from "../../BoxPrimary";
import ProgressChart from "@/ui/charts/ProgressChart";
import OverallSection from "../OverallSection";

type LocalSeoWebProps = {
  results: Partial<LocalSEOAnalysis> | null;
  urlName?: string;
};

export default function LocalSeoWeb({ results, urlName }: LocalSeoWebProps) {
  // Handle null or undefined results
  if (!results) {
    return (
      <BoxPrimary title="Local SEO Analysis">
        <div className="w-full flex flex-col items-center gap-4 lg:gap-0 lg:flex-row lg:items-start">
          <ProgressChart
            value="F"
            title="Local SEO Score"
            size="lg"
            progressStates={[
              { label: "Grade", value: 0, isNoColor: false },
            ]}
          />

          <OverallSection
            title="Local SEO Analysis"
            description="Analyzing your website's local SEO factors..."
          />
        </div>
      </BoxPrimary>
    );
  }

  // Extract the total score and grade - show default values if not available yet
  const totalScore = results?.total_score || { score: 0, grade: "F" };

  // Ensure grade is properly typed for ProgressChart
  const grade =
    (totalScore.grade as
      | "A+"
      | "A"
      | "A-"
      | "B+"
      | "B"
      | "B-"
      | "C+"
      | "C"
      | "C-"
      | "D+"
      | "D"
      | "D-"
      | "F") || "F";

  return (
    <BoxPrimary title="Local SEO Analysis">
      <div className="w-full flex flex-col items-center gap-4 lg:gap-0 lg:flex-row lg:items-start">
        <ProgressChart
          value={grade}
          title="Local SEO Score"
          size="lg"
          progressStates={[
            { label: "Grade", value: totalScore.score, isNoColor: false },
          ]}
        />

        <OverallSection
          title={results.overall_title || "Local SEO Analysis"}
          description={
            results.overall_description ||
            "Analyzing your website's local SEO factors..."
          }
        />
      </div>

      {/* Use the new modular PDF component for detailed analysis */}
      <div className="mt-8">
        <LocalSeoSection
          localSeoData={results}
          urlName={urlName || ""}
          brand_name="SEO Analyzer"
          brand_website="seoanalyser.com.au"
          brand_photo={null}
          onImageLoad={() => {}}
          onImageError={() => {}}
        />
      </div>
    </BoxPrimary>
  );
}
