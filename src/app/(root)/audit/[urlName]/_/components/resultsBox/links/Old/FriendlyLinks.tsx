import Table from "@/ui/Table";
import ProgressPercent from "../../ProgressPercent";

type UnfriendlyLinkItem = {
  url: string;
  text: string;
  score: number;
};

type FriendlyLinksProps = {
  friendlyPercentage: number;
  unfriendlyLinks: UnfriendlyLinkItem[];
};

export default function FriendlyLinks({
  friendlyPercentage,
  unfriendlyLinks = [],
}: FriendlyLinksProps) {
  const hasUnfriendlyLinks = unfriendlyLinks && unfriendlyLinks.length > 0;

  return (
    <div className="mt-4 lg:mt-6">
      <div className="mb-6">
        <h4 className="font-semibold text-secondary pb-2">Link Readability Analysis</h4>
        <span className="text-sm text-secondary/60">
          {hasUnfriendlyLinks
            ? `${friendlyPercentage}% of your links have readable anchor text. Below are examples of links that could be improved.`
            : "All of your links have readable anchor text."}
        </span>
      </div>

      {hasUnfriendlyLinks ? (
        <Table>
          <Table.Header>
            <th className="pb-4 whitespace-nowrap">URL</th>
            <th className="text-center pb-4">Anchor Text</th>
            <th className="text-center pb-4">Readability Score</th>
          </Table.Header>
          <Table.Body>
            {unfriendlyLinks.map((item, index) => (
              <Table.Row key={index}>
                <td className="w-full sm:min-w-[150px] sm:max-w-[200px] py-4 pr-2 sm:pr-4 break-words">
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline text-xs sm:text-sm"
                  >
                    {item.url || "N/A"}
                  </a>
                </td>
                <td className="w-full sm:min-w-[150px] sm:max-w-[200px] py-4 break-words text-xs sm:text-sm text-center">
                  {item.text || "N/A"}
                </td>
                <td className="w-full sm:min-w-[150px] sm:max-w-[200px] py-4 text-center">
                  <div className="flex justify-center">
                    <div className="w-[100px] sm:w-[150px]">
                      {/* Normalize score to 0-100 range for display */}
                      <ProgressPercent 
                        percentage={Math.max(0, Math.min(100, (item.score + 100) / 2))} 
                      />
                    </div>
                  </div>
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="p-4 bg-light-gray-6 rounded-lg text-center text-secondary/60">
          No unfriendly links detected
        </div>
      )}
    </div>
  );
}
