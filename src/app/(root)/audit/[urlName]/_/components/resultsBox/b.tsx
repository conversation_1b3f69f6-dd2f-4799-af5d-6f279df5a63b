"use client";
import { useState, useCallback, useEffect } from "react";
import Modal from "@/ui/Modal";
import StepProgressBar from "@/ui/StepProgressBar";
import pricingService, { PricingPlan } from "@/services/pricingService";
import {
  WhiteLabelInfoStep,
  PlanSelectionStep,
  PaymentMethodStep,
  DownloadStep,
} from "./WhiteLabelSteps";

// Define types for the White Label steps
type WhiteLabelStep = "white-label" | "select-plan" | "payment" | "download";

// Define types for the White Label form data
interface WhiteLabelFormData {
  brandName: string;
  logoImage: File | null;
  phoneNumber: string;
  website: string;
}

// Define types for billing period
type BillingPeriod = "monthly" | "annually";

// Define types for the White Label payment method
type PaymentMethod = "paypal" | "credit-card" | "crypto" | "stripe";

// Define props for the White Label Modal component
interface WhiteLabelModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function WhiteLabelModal({
  isOpen,
  onClose,
  onSuccess,
}: WhiteLabelModalProps) {
  // State for the current step
  const [currentStep, setCurrentStep] = useState<WhiteLabelStep>("white-label");

  // State for the form data
  const [formData, setFormData] = useState<WhiteLabelFormData>({
    brandName: "",
    logoImage: null,
    phoneNumber: "",
    website: "",
  });

  // State for the selected plan
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  // State for billing period (monthly or annually)
  const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>("monthly");

  // State for the selected payment method
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(
    null
  );

  // State for API pricing data
  const [apiPricingData, setApiPricingData] = useState<PricingPlan[]>([]);
  const [premiumPlanData, setPremiumPlanData] = useState<PricingPlan[]>([]);

  // State for logo preview URL
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(null);

  // Fetch pricing data from API when component mounts
  useEffect(() => {
    const fetchPricingData = async () => {
      try {
        // Get all pricing data
        const allPricingData = await pricingService.getPricingData();

        // Extract Pro Plan pricing data
        const proPlanData = allPricingData["Pro Plan"] || [];
        setApiPricingData(proPlanData);

        // Extract Pro Plan & Embedding plans
        const premiumData = allPricingData["Pro Plan & Embedding"] || [];
        setPremiumPlanData(premiumData);
      } catch (error) {
        console.error("Error fetching pricing data:", error);
      }
    };

    fetchPricingData();
  }, []);

  // Effect to create preview URL for logo if it exists
  useEffect(() => {
    if (formData.logoImage) {
      const objectUrl = URL.createObjectURL(formData.logoImage);
      setLogoPreviewUrl(objectUrl);

      return () => {
        URL.revokeObjectURL(objectUrl);
      };
    }
  }, [formData.logoImage]);

  // Effect to clean up the URL when component unmounts
  useEffect(() => {
    return () => {
      if (logoPreviewUrl) {
        URL.revokeObjectURL(logoPreviewUrl);
      }
    };
  }, [logoPreviewUrl]);

  // Handle white label info step completion
  const handleWhiteLabelInfoComplete = useCallback(
    (updatedFormData: WhiteLabelFormData) => {
      setFormData(updatedFormData);
      setCurrentStep("select-plan");
    },
    []
  );

  // Handle plan selection step completion
  const handlePlanSelectionComplete = useCallback(
    (planId: string, period: BillingPeriod) => {
      setSelectedPlan(planId);
      setBillingPeriod(period);
      setCurrentStep("payment");
    },
    []
  );

  // Handle payment method step completion
  const handlePaymentMethodComplete = useCallback((method: PaymentMethod) => {
    setPaymentMethod(method);
    setCurrentStep("download");
  }, []);

  // Handle download step completion
  const handleDownloadComplete = useCallback(() => {
    onSuccess();
    onClose();

    // Navigate to white-label page
    if (typeof window !== "undefined") {
      window.location.href = "/white-label";
    }
  }, [onSuccess, onClose]);

  // Handle back button click
  const handleBackClick = useCallback(() => {
    if (currentStep === "select-plan") {
      setCurrentStep("white-label");
    } else if (currentStep === "payment") {
      setCurrentStep("select-plan");
    }
  }, [currentStep]);

  // Steps array for the progress bar
  const steps = [
    {
      id: "authentication",
      label: "Auth",
    },
    {
      id: "white-label",
      label: "White Label Setting",
    },
    {
      id: "select-plan",
      label: "Billing",
    },
    {
      id: "payment",
      label: "Payment",
    },
    {
      id: "download",
      label: "Download",
    },
  ];

  // Render step indicator
  const renderStepIndicator = useCallback(() => {
    return (
      <div className="px-2 lg:px-6 pt-6 pb-4">
        <StepProgressBar
          steps={steps}
          currentStepId={currentStep}
          className="mb-4"
        />
      </div>
    );
  }, [currentStep]);

  // Render the current step content
  const renderStepContent = useCallback(() => {
    switch (currentStep) {
      case "white-label":
        return (
          <WhiteLabelInfoStep
            onNext={handleWhiteLabelInfoComplete}
            initialFormData={formData}
          />
        );
      case "select-plan":
        return (
          <PlanSelectionStep
            onNext={handlePlanSelectionComplete}
            onBack={handleBackClick}
            apiPricingData={apiPricingData}
            premiumPlanData={premiumPlanData}
            initialSelectedPlan={selectedPlan}
            initialBillingPeriod={billingPeriod}
          />
        );
      case "payment":
        return (
          <PaymentMethodStep
            onNext={handlePaymentMethodComplete}
            onBack={handleBackClick}
            selectedPlan={selectedPlan || "basic"}
            billingPeriod={billingPeriod}
            apiPricingData={apiPricingData}
            premiumPlanData={premiumPlanData}
            initialPaymentMethod={paymentMethod}
          />
        );
      case "download":
        return (
          <DownloadStep
            onComplete={handleDownloadComplete}
            formData={formData}
            selectedPlan={selectedPlan || "basic"}
            billingPeriod={billingPeriod}
            apiPricingData={apiPricingData}
            logoPreviewUrl={logoPreviewUrl}
          />
        );
      default:
        return null;
    }
  }, [
    currentStep,
    formData,
    selectedPlan,
    billingPeriod,
    paymentMethod,
    apiPricingData,
    premiumPlanData,
    logoPreviewUrl,
    handleWhiteLabelInfoComplete,
    handlePlanSelectionComplete,
    handlePaymentMethodComplete,
    handleDownloadComplete,
    handleBackClick,
  ]);

  // Get the modal title based on the current step
  const getModalTitle = useCallback(() => {
    switch (currentStep) {
      case "white-label":
        return "White Label Settings";
      case "select-plan":
        return "Select White Label Plan";
      case "payment":
        return "Payment Method";
      case "download":
        return "White Label PDF Download";
      default:
        return "White Label";
    }
  }, [currentStep]);

  return (
    <Modal open={isOpen} onClose={onClose} title={getModalTitle()} size="xl">
      <style jsx global>{`
        .overflow-y-auto {
          scrollbar-width: thin;
          scrollbar-color: rgba(156, 163, 175, 0.5) rgba(229, 231, 235, 0.5);
        }
        .overflow-y-auto::-webkit-scrollbar {
          width: 8px;
        }
        .overflow-y-auto::-webkit-scrollbar-track {
          background: rgba(229, 231, 235, 0.5);
          border-radius: 4px;
        }
        .overflow-y-auto::-webkit-scrollbar-thumb {
          background-color: rgba(156, 163, 175, 0.5);
          border-radius: 4px;
          border: 2px solid rgba(229, 231, 235, 0.5);
        }
        .overflow-y-auto::-webkit-scrollbar-thumb:hover {
          background-color: rgba(156, 163, 175, 0.7);
        }
      `}</style>
      {renderStepIndicator()}
      {renderStepContent()}
    </Modal>
  );
}
