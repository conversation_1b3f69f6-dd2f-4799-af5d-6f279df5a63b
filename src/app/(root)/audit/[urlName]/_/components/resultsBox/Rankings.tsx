import Table from "@/ui/Table";
import BoxPrimary from "../BoxPrimary";
import Flag from "react-world-flags";
import ProgressPercent from "../ProgressPercent";
const tableData = [
  {
    keyword: "seoptimer",
    country: "DE",
    position: 1,
    totalSearches: "18,100",
    esitmatedTraffic: "5,502",
    percentage: 100,
  },
  {
    keyword: "seoptimer",
    country: "IR",
    position: 1,
    totalSearches: "18,100",
    esitmatedTraffic: "5,502",
    percentage: 90,
  },
  {
    keyword: "seoptimer",
    country: "FR",
    position: 1,
    totalSearches: "18,100",
    esitmatedTraffic: "5,502",
    percentage: 100,
  },
  {
    keyword: "seoptimer",
    country: "IR",
    position: 1,
    totalSearches: "18,100",
    esitmatedTraffic: "5,502",
    percentage: 60,
  },
  {
    keyword: "seoptimer",
    country: "KR",
    position: 1,
    totalSearches: "18,100",
    esitmatedTraffic: "5,502",
    percentage: 30,
  },
  {
    keyword: "seoptimer",
    country: "DE",
    position: 1,
    totalSearches: "18,100",
    esitmatedTraffic: "5,502",
    percentage: 100,
  },
  {
    keyword: "seoptimer",
    country: "DE",
    position: 1,
    totalSearches: "18,100",
    esitmatedTraffic: "5,502",
    percentage: 80,
  },
  {
    keyword: "seoptimer",
    country: "IT",
    position: 1,
    totalSearches: "18,100",
    esitmatedTraffic: "5,502",
    percentage: 100,
  },
];

const barData = [
  { percentage: 100, isSelect: false },
  { percentage: 80, isSelect: false },
  { percentage: 60, isSelect: false },
  { percentage: 70, isSelect: false },
  { percentage: 90, isSelect: false },
  { percentage: 60, isSelect: false },
  { percentage: 100, isSelect: false },
  { percentage: 70, isSelect: true },
  { percentage: 30, isSelect: false },
];

export default function Rankings() {
  return (
    <BoxPrimary title="Rankings">
      <div>
        <div className="mb-6">
          <h4 className="font-semibold text-secondary pb-2">
            Top Keywords Rankings
          </h4>
          <span className="text-sm text-secondary/60">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
            eiusmod tempor incididunt ut labore et dolore rhoncus aenean vel
          </span>
        </div>

        <div>
          <Table>
            <Table.Header>
              <th className="pb-2 pr-[52px] lg:pr-0">Keyword</th>
              <th className="text-center pb-2">Country</th>
              <th className="text-center pb-2 px-4 lg:px-0">position</th>
              <th className="text-center pb-2">
                Total
                <br /> Searches
              </th>
              <th className="text-center pb-2 px-4 lg:px-0">
                Esitmated
                <br />
                Traffic
              </th>
              <th className="w-[180px]"></th>
            </Table.Header>
            <Table.Body>
              {tableData.map((data, index) => (
                <Table.Row key={index}>
                  <td>{data.keyword}</td>
                  <td>
                    <div className="w-full flex justify-center">
                      <div className="w-6 rounded-[3px] overflow-hidden">
                        <Flag code={data.country} />
                      </div>
                    </div>
                  </td>
                  <td className="text-center">{data.position}</td>
                  <td className="text-center">{data.totalSearches}</td>
                  <td className="text-center">{data.esitmatedTraffic}</td>
                  <td className="pl-4 lg:pl-[85px]">
                    <div className="flex justify-end">
                      <div className="w-[180px]">
                        <ProgressPercent percentage={data.percentage} />
                      </div>
                    </div>
                  </td>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
          <button className="btn btn--primary text-sm !px-6 !py-2 mt-4">
            Track Keyword Ranking
          </button>
        </div>
      </div>

      <div className="mt-6">
        <div className="w-full rounded-lg border border-light-gray p-6 ">
          <h4 className="font-semibold text-secondary">
            Total Traffic From Search
          </h4>

          <div className="h-[133px] flex items-end gap-4 my-2">
            {barData.map((item, index) => (
              <ProgressPercent
                key={index}
                percentage={item.percentage}
                layout="vertical"
                className={item.isSelect ? "bg-primary" : "!bg-[#E9ECF1]"}
              />
            ))}
          </div>

          <div className="flex flex-col gap-2">
            <span className="text-secondary font-semibold">645,700</span>
            <span className="text-sm text-secondary/60">
              Monthly Traffic Volume
            </span>
          </div>
        </div>
        <button className="btn btn--primary text-sm !px-6 !py-2 mt-6">
          Track Keyword Ranking
        </button>
      </div>
    </BoxPrimary>
  );
}
