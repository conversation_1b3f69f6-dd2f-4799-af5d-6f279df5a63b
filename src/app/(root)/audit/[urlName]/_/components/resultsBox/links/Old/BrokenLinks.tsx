import Table from "@/ui/Table";

type BrokenLinkItem = {
  url: string;
  status: string;
  details: number;
};

type BrokenLinksProps = {
  totalChecked: number;
  brokenCount: number;
  brokenPercentage: number;
  brokenLinks: BrokenLinkItem[];
};

export default function BrokenLinks({
  totalChecked,
  brokenCount,
  brokenPercentage,
  brokenLinks = [],
}: BrokenLinksProps) {
  const hasBrokenLinks = brokenLinks && brokenLinks.length > 0;

  return (
    <div className="mt-4 lg:mt-6">
      <div className="mb-6">
        <h4 className="font-semibold text-secondary pb-2">Broken Links Analysis</h4>
        <span className="text-sm text-secondary/60">
          {hasBrokenLinks
            ? `Found ${brokenCount} broken links (${brokenPercentage}%) out of ${totalChecked} checked. These should be fixed to improve user experience and SEO.`
            : "No broken links were found on your page."}
        </span>
      </div>

      {hasBrokenLinks ? (
        <Table>
          <Table.Header>
            <th className="pb-4 whitespace-nowrap">URL</th>
            <th className="text-center pb-4">Status</th>
            <th className="text-center pb-4">Error Code</th>
          </Table.Header>
          <Table.Body>
            {brokenLinks.map((item, index) => (
              <Table.Row key={index}>
                <td className="w-full sm:min-w-[150px] sm:max-w-[200px] py-4 pr-2 sm:pr-4 break-words">
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:underline text-xs sm:text-sm"
                  >
                    {item.url || "N/A"}
                  </a>
                </td>
                <td className="w-full sm:min-w-[100px] sm:max-w-[150px] py-4 break-words text-xs sm:text-sm text-center">
                  <span className="px-2 py-1 bg-primary-red/10 text-primary-red rounded">
                    {item.status || "Broken"}
                  </span>
                </td>
                <td className="w-full sm:min-w-[100px] sm:max-w-[150px] py-4 break-words text-xs sm:text-sm text-center">
                  {item.details || "Unknown"}
                </td>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="p-4 bg-light-gray-6 rounded-lg text-center text-secondary/60">
          No broken links detected
        </div>
      )}
    </div>
  );
}
