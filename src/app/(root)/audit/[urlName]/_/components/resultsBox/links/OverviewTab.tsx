import { motion } from "framer-motion";
import {
  CrownIcon,
  DocumentCrossIcon,
  CheckIcon,
  ChainIcon,
} from "@/ui/icons/general";
import ProgressChart from "@/ui/charts/ProgressChart";
import StateCard from "../../StateCard";
import OverallSection from "../OverallSection";
import ShowMoreSection from "../usability/ShowMoreSection";
import { LinksAnalysisData } from "./types";
import {
  formatMetricValue,
  formatMetricValueWithPrefix,
  getMetricInfo,
  getTotalScore,
  getTotalGrade,
  getActiveData,
} from "./utils";

type OverviewTabProps = {
  data: LinksAnalysisData;
  itemVariants: any;
};

export default function OverviewTab({ data, itemVariants }: OverviewTabProps) {
  const overallBacklinks = data.overall_backlinks;
  const activeData = getActiveData("overall_backlinks", data);

  return (
    <>
      <motion.div
        variants={itemVariants}
        className="flex flex-col lg:flex-row items-center gap-6"
      >
        <ProgressChart
          value={getTotalGrade(data)}
          score={getTotalScore(data)}
          title={`Backlinks Analysis`}
          size="md"
          progressStates={[
            {
              label: "Score",
              value: getTotalScore(data),
              isNoColor: false,
            },
          ]}
        />

        <OverallSection
          title={activeData.title}
          description={activeData.description}
        />
      </motion.div>

      {/* State Cards Overview */}
      <motion.div
        variants={itemVariants}
        className="w-full grid grid-cols-2 lg:grid-cols-4 gap-4"
      >
        <StateCard
          icon={<ChainIcon className="w-6 h-6" />}
          value={formatMetricValue(
            overallBacklinks,
            "total_backlinks",
            0,
            "count"
          )}
          label="Total Backlinks"
        />
        <StateCard
          icon={<CrownIcon className="w-6 h-6" />}
          value={formatMetricValue(
            overallBacklinks,
            "domain_authority",
            0,
            "score"
          )}
          label="Domain Authority"
        />
        <StateCard
          icon={<DocumentCrossIcon className="w-6 h-6" />}
          value={formatMetricValue(
            overallBacklinks,
            "broken_links_count",
            0,
            "count"
          )}
          label="Broken Links"
        />
        <StateCard
          icon={<CheckIcon className="w-6 h-6" />}
          value={formatMetricValue(
            overallBacklinks,
            "friendly_links_percentage",
            0,
            "percentage"
          )}
          label="Friendly Links"
        />
      </motion.div>

      {/* ShowMoreSection components for each metric */}
      <motion.div variants={itemVariants} className="space-y-6">
        {/* Total Backlinks Section */}
        <div className="bg-white rounded-xl border border-gray-200">
          <ShowMoreSection
            title="Total Backlinks"
            description={
              <div className="text-sm text-gray-700 leading-relaxed">
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatMetricValueWithPrefix(
                    overallBacklinks,
                    "total_backlinks",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(overallBacklinks, "total_backlinks")
                  ?.description ||
                  "The total number of external pages linking to your entire root domain."}
              </div>
            }
            importance={
              getMetricInfo(overallBacklinks, "total_backlinks")?.importance
            }
            recommendation={
              getMetricInfo(overallBacklinks, "total_backlinks")
                ?.recommendation || undefined
            }
            icon={<ChainIcon className="w-12 h-12 text-blue-600" />}
          />
        </div>

        {/* Domain Authority Section */}
        <div className="bg-white rounded-xl border border-gray-200">
          <ShowMoreSection
            title="Domain Authority"
            description={
              <div className="text-sm text-gray-700 leading-relaxed">
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatMetricValueWithPrefix(
                    overallBacklinks,
                    "domain_authority",
                    0,
                    "score"
                  )}
                </div>
                {getMetricInfo(overallBacklinks, "domain_authority")
                  ?.description ||
                  "Domain Authority is a score predicting a website's ranking strength based on its overall backlink profile."}
              </div>
            }
            importance={
              getMetricInfo(overallBacklinks, "domain_authority")?.importance
            }
            recommendation={
              getMetricInfo(overallBacklinks, "domain_authority")
                ?.recommendation || undefined
            }
            icon={<CrownIcon className="w-12 h-12 text-purple-600" />}
          />
        </div>

        {/* Broken Links Section */}
        <div className="bg-white rounded-xl border border-gray-200">
          <ShowMoreSection
            title="Broken Links"
            description={
              <div className="text-sm text-gray-700 leading-relaxed">
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatMetricValueWithPrefix(
                    overallBacklinks,
                    "broken_links_count",
                    0,
                    "count"
                  )}
                </div>
                {getMetricInfo(overallBacklinks, "broken_links_count")
                  ?.description ||
                  "The number of broken links found on the analyzed page during the on-page crawl."}
              </div>
            }
            importance={
              getMetricInfo(overallBacklinks, "broken_links_count")?.importance
            }
            recommendation={
              getMetricInfo(overallBacklinks, "broken_links_count")
                ?.recommendation || undefined
            }
            icon={<DocumentCrossIcon className="w-12 h-12 text-red-600" />}
          />
        </div>

        {/* Friendly Links Section */}
        <div className="bg-white rounded-xl border border-gray-200">
          <ShowMoreSection
            title="Friendly Link Percentage"
            description={
              <div className="text-sm text-gray-700 leading-relaxed">
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {formatMetricValueWithPrefix(
                    overallBacklinks,
                    "friendly_links_percentage",
                    0,
                    "percentage"
                  )}
                </div>
                {getMetricInfo(overallBacklinks, "friendly_links_percentage")
                  ?.description ||
                  "Percentage of text links on this page with anchor text deemed easily readable."}
              </div>
            }
            importance={
              getMetricInfo(overallBacklinks, "friendly_links_percentage")
                ?.importance
            }
            recommendation={
              getMetricInfo(overallBacklinks, "friendly_links_percentage")
                ?.recommendation || undefined
            }
            icon={<CheckIcon className="w-12 h-12 text-green-600" />}
          />
        </div>
      </motion.div>
    </>
  );
}
