import React from 'react'

type Props = {
    icon:React.ReactNode;
    value:string;
    label:string;
}

export default function StateCard({icon,label,value}: Props) {
  return (
    <div className='rounded-lg p-4 border border-light-gray'>
        <div className='flex items-center justify-center gap-2 text-secondary mb-4'>
            {icon}
            <span className='font-semibold'>
                {value}
            </span>
        </div>
        <div className='text-center text-sm text-secondary/60'>
            {label}
        </div>
    </div>
  )
}