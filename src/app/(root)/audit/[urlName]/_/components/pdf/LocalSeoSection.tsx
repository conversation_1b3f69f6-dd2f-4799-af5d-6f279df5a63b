"use client";
import React from "react";
import { LocalSEOAnalysis } from "@/types/seoAnalyzerTypes";
import { SectionHeader, DataRow, RecommendationCard } from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";

export interface LocalSeoSectionProps {
  localSeoData: LocalSEOAnalysis;
  urlName: string;
  onPageSeoData?: {
    serp_preview?: {
      url?: string;
    };
  };
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const LocalSeoSection: React.FC<LocalSeoSectionProps> = ({
  localSeoData,
  urlName,
  onPageSeoData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "seoanalyser.com.au"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoLocal")}
        onLogoError={() => onImageError?.("sectionLogoLocal")}
        sectionId="local-seo-details"
      />

      <SectionHeader
        title={`Local SEO Audit for ${
          onPageSeoData?.serp_preview?.url || urlName
        }`}
        brandName={brand_name}
        brandWebsite={brand_website}
        scoreGrade={localSeoData.total_score}
        showScore={!!localSeoData.total_score}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contact Information */}
        {localSeoData.contact_info && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Contact Information
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Phone Number"
                value={
                  localSeoData.contact_info.phone_number
                    ? "✓ Found"
                    : "✗ Missing"
                }
              />
              <DataRow
                label="Email Address"
                value={
                  localSeoData.contact_info.email_address
                    ? "✓ Found"
                    : "✗ Missing"
                }
              />
              <DataRow
                label="Physical Address"
                value={
                  localSeoData.contact_info.physical_address
                    ? "✓ Found"
                    : "✗ Missing"
                }
              />
              <DataRow
                label="Status"
                value={
                  localSeoData.contact_info.pass ? "✓ Good" : "⚠ Incomplete"
                }
              />
              <DataRow
                label="Score"
                value={`${localSeoData.contact_info.score || 0}/5`}
              />
            </div>

            {/* Display actual contact info if available */}
            {(localSeoData.contact_info.phone_number ||
              localSeoData.contact_info.email_address ||
              localSeoData.contact_info.physical_address) && (
              <div className="mt-4 pt-3 border-t border-gray-100">
                <h4 className="font-medium text-gray-700 mb-2">
                  Found Contact Details:
                </h4>
                <div className="space-y-1">
                  {localSeoData.contact_info.phone_number && (
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Phone:</span>{" "}
                      {localSeoData.contact_info.phone_number}
                    </p>
                  )}
                  {localSeoData.contact_info.email_address && (
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Email:</span>{" "}
                      {localSeoData.contact_info.email_address}
                    </p>
                  )}
                  {localSeoData.contact_info.physical_address && (
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Address:</span>{" "}
                      {localSeoData.contact_info.physical_address}
                    </p>
                  )}
                </div>
              </div>
            )}

            {localSeoData.contact_info.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {localSeoData.contact_info.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Business Schema */}
        {localSeoData.business_schema && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Business Schema Markup
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Schema Present"
                value={
                  localSeoData.business_schema.has_business_schema
                    ? "✓ Yes"
                    : "✗ No"
                }
              />
              <DataRow
                label="Organization Schema"
                value={
                  localSeoData.business_schema.has_organization_schema
                    ? "✓ Yes"
                    : "✗ No"
                }
              />
              <DataRow
                label="Local Business Schema"
                value={
                  localSeoData.business_schema.has_local_business_schema
                    ? "✓ Yes"
                    : "✗ No"
                }
              />
              <DataRow
                label="Status"
                value={
                  localSeoData.business_schema.pass
                    ? "✓ Good"
                    : "⚠ Missing Schema"
                }
              />
              <DataRow
                label="Score"
                value={`${localSeoData.business_schema.score || 0}/5`}
              />
            </div>

            {localSeoData.business_schema.schema_types &&
              localSeoData.business_schema.schema_types.length > 0 && (
                <div className="mt-4 pt-3 border-t border-gray-100">
                  <h4 className="font-medium text-gray-700 mb-2">
                    Schema Types Found:
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {localSeoData.business_schema.schema_types.map(
                      (type, index) => (
                        <span
                          key={index}
                          className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded"
                        >
                          {type}
                        </span>
                      )
                    )}
                  </div>
                </div>
              )}

            {localSeoData.business_schema.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {localSeoData.business_schema.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Google Business Profile */}
        {localSeoData.google_business && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Google Business Profile
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Google Business Listed"
                value={
                  localSeoData.google_business.is_listed ? "✓ Yes" : "✗ No"
                }
              />
              <DataRow
                label="Verified"
                value={
                  localSeoData.google_business.is_verified ? "✓ Yes" : "✗ No"
                }
              />
              <DataRow
                label="Status"
                value={
                  localSeoData.google_business.pass
                    ? "✓ Good"
                    : "⚠ Needs Attention"
                }
              />
              <DataRow
                label="Score"
                value={`${localSeoData.google_business.score || 0}/5`}
              />
            </div>

            {localSeoData.google_business.business_name && (
              <div className="mt-4 pt-3 border-t border-gray-100">
                <h4 className="font-medium text-gray-700 mb-2">
                  Business Details:
                </h4>
                <div className="space-y-1">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Name:</span>{" "}
                    {localSeoData.google_business.business_name}
                  </p>
                  {localSeoData.google_business.business_address && (
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Address:</span>{" "}
                      {localSeoData.google_business.business_address}
                    </p>
                  )}
                  {localSeoData.google_business.business_phone && (
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Phone:</span>{" "}
                      {localSeoData.google_business.business_phone}
                    </p>
                  )}
                </div>
              </div>
            )}

            {localSeoData.google_business.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {localSeoData.google_business.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* NAP Consistency */}
        {localSeoData.nap_consistency && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                NAP Consistency
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Name Consistency"
                value={
                  localSeoData.nap_consistency.name_consistent
                    ? "✓ Consistent"
                    : "⚠ Inconsistent"
                }
              />
              <DataRow
                label="Address Consistency"
                value={
                  localSeoData.nap_consistency.address_consistent
                    ? "✓ Consistent"
                    : "⚠ Inconsistent"
                }
              />
              <DataRow
                label="Phone Consistency"
                value={
                  localSeoData.nap_consistency.phone_consistent
                    ? "✓ Consistent"
                    : "⚠ Inconsistent"
                }
              />
              <DataRow
                label="Overall Status"
                value={
                  localSeoData.nap_consistency.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
              />
              <DataRow
                label="Score"
                value={`${localSeoData.nap_consistency.score || 0}/5`}
              />
            </div>

            {localSeoData.nap_consistency.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {localSeoData.nap_consistency.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Local Citations */}
        {localSeoData.local_citations && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Local Citations
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Citations Found"
                value={localSeoData.local_citations.citation_count || 0}
              />
              <DataRow
                label="Quality Score"
                value={`${localSeoData.local_citations.quality_score || 0}/10`}
              />
              <DataRow
                label="Status"
                value={
                  localSeoData.local_citations.pass
                    ? "✓ Good"
                    : "⚠ Needs More Citations"
                }
              />
              <DataRow
                label="Score"
                value={`${localSeoData.local_citations.score || 0}/5`}
              />
            </div>

            {localSeoData.local_citations.top_directories &&
              localSeoData.local_citations.top_directories.length > 0 && (
                <div className="mt-4 pt-3 border-t border-gray-100">
                  <h4 className="font-medium text-gray-700 mb-2">
                    Top Directories:
                  </h4>
                  <div className="space-y-1">
                    {localSeoData.local_citations.top_directories
                      .slice(0, 5)
                      .map((directory, index) => (
                        <p
                          key={index}
                          className="text-sm text-gray-600 bg-gray-50 p-2 rounded"
                        >
                          {directory}
                        </p>
                      ))}
                  </div>
                </div>
              )}

            {localSeoData.local_citations.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {localSeoData.local_citations.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Reviews Analysis */}
        {localSeoData.reviews_analysis && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Reviews Audit</h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Average Rating"
                value={`${
                  localSeoData.reviews_analysis.average_rating || 0
                }/5 ⭐`}
              />
              <DataRow
                label="Total Reviews"
                value={localSeoData.reviews_analysis.total_reviews || 0}
              />
              <DataRow
                label="Recent Reviews"
                value={localSeoData.reviews_analysis.recent_reviews_count || 0}
              />
              <DataRow
                label="Status"
                value={
                  localSeoData.reviews_analysis.pass
                    ? "✓ Good"
                    : "⚠ Needs Improvement"
                }
              />
              <DataRow
                label="Score"
                value={`${localSeoData.reviews_analysis.score || 0}/5`}
              />
            </div>

            {localSeoData.reviews_analysis.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {localSeoData.reviews_analysis.description}
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Comprehensive Local SEO Recommendations - Moved outside grid for full width */}
      <div className="mt-8">
        <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
          All Local SEO Recommendations
        </h3>
        <div className="grid grid-cols-1 gap-4 recommendations-grid">
          {/* Business Listings Recommendations */}
          {localSeoData.business_listings?.recommendation && (
            <RecommendationCard
              recommendation={localSeoData.business_listings.recommendation}
            />
          )}

          {/* NAP Consistency Recommendations */}
          {localSeoData.nap_consistency?.recommendation && (
            <RecommendationCard
              recommendation={localSeoData.nap_consistency.recommendation}
            />
          )}

          {/* Local Schema Recommendations */}
          {localSeoData.local_schema?.recommendation && (
            <RecommendationCard
              recommendation={localSeoData.local_schema.recommendation}
            />
          )}

          {/* Google My Business Recommendations */}
          {localSeoData.google_my_business?.recommendation && (
            <RecommendationCard
              recommendation={localSeoData.google_my_business.recommendation}
            />
          )}

          {/* Reviews Analysis Recommendations */}
          {localSeoData.reviews_analysis?.recommendation && (
            <RecommendationCard
              recommendation={localSeoData.reviews_analysis.recommendation}
            />
          )}

          {/* Local Citations Recommendations */}
          {localSeoData.local_citations?.recommendation && (
            <RecommendationCard
              recommendation={localSeoData.local_citations.recommendation}
            />
          )}

          {/* If no recommendations found */}
          {!localSeoData.business_listings?.recommendation &&
            !localSeoData.nap_consistency?.recommendation &&
            !localSeoData.local_schema?.recommendation &&
            !localSeoData.google_my_business?.recommendation &&
            !localSeoData.reviews_analysis?.recommendation &&
            !localSeoData.local_citations?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: "Your local SEO appears to be well optimized. Continue maintaining consistent NAP information and gathering positive reviews.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
