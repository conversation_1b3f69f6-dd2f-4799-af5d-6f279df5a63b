"use client";
import React from "react";
import {
  OnPageAnalysis,
  UsabilityAnalysis,
  TechSEOAnalysis,
  SocialAnalysis,
  PerformanceAnalysis,
  LinksAnalysis,
  LocalSEOAnalysis,
} from "@/types/seoAnalyzerTypes";
import { SectionHeader } from "./BaseComponents";
import ProgressChart from "@/ui/charts/ProgressChart";
import ScoreMeter from "@/ui/charts/ScoreMeter";

// Define Grade type to match ProgressChart
type Grade =
  | "A+"
  | "A"
  | "A-"
  | "B+"
  | "B"
  | "B-"
  | "C+"
  | "C"
  | "C-"
  | "D+"
  | "D"
  | "D-"
  | "F";

export interface OverallScoresSectionProps {
  urlName: string;
  onPageSeoData?: OnPageAnalysis;
  usabilityData?: UsabilityAnalysis;
  technologyData?: TechSEOAnalysis;
  socialData?: SocialAnalysis;
  performanceData?: PerformanceAnalysis;
  linksData?: LinksAnalysis;
  localSeoData?: LocalSEOAnalysis;
  brand_name?: string;
  brand_website?: string;
}

export const OverallScoresSection: React.FC<OverallScoresSectionProps> = ({
  urlName,
  onPageSeoData,
  usabilityData,
  technologyData,
  socialData,
  performanceData,
  linksData,
  localSeoData,
  brand_name,
  brand_website,
}) => {
  // Calculate overall score - synchronized with main audit calculation
  const calculateOverallScore = (): number => {
    // Extract scores exactly like the main audit component
    const onPageScore = onPageSeoData?.total_score?.score || 0;
    const usabilityScore = usabilityData?.total_score?.score || 0;
    const techSeoScore = technologyData?.total_score?.score || 0;
    const socialScore = socialData?.total_score?.score || 0;
    const performanceScore = performanceData?.total_score?.score || 0;
    const linksScore = linksData?.total_score?.score || 0;
    // Note: localSeoData is NOT included in overall score calculation to match main audit

    // Filter out scores that are 0, just like the main audit
    const scores = [
      onPageScore,
      usabilityScore,
      techSeoScore,
      socialScore,
      performanceScore,
      linksScore,
    ].filter((score) => score > 0);

    // Calculate average and round, just like the main audit
    const overallScore =
      scores.length > 0
        ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length)
        : 0;

    return overallScore;
  };

  return (
    <div className="relative">
      <SectionHeader
        title="Overall Performance"
        brandName={brand_name}
        brandWebsite={brand_website}
      />

      {/* Professional URL info card */}
      <div className="mb-6 p-4 bg-gradient-to-r from-primary/5 to-primary/8 rounded-xl border border-primary/20 shadow-sm ">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-semibold text-primary mb-1">
              Website Analysis
            </p>
            <p className="text-gray-700 text-sm break-all">
              {onPageSeoData?.serp_preview?.url || urlName}
            </p>
          </div>
          {brand_name && (
            <div className="text-right">
              <p className="text-xs text-primary/70 font-medium">Analyzed by</p>
              <p className="text-sm font-semibold text-primary">{brand_name}</p>
            </div>
          )}
        </div>
      </div>

      {/* Overall Performance - Large ScoreMeter at top center */}
      <div className="mt-4 lg:mt-6 flex flex-col items-center justify-center lg:flex-row lg:items-end gap-8 mb-5.5 scale-110">
        <ScoreMeter
          score={calculateOverallScore()}
          title="Overall Performance"
          size="lg"
        />
      </div>

      {/* First row of charts - On Page SEO, Technology Review, Social Media */}
      <div className="mt-4 lg:mt-6 flex flex-row items-end justify-center gap-8 mb-5.5 print:flex-row print:justify-center print:items-end">
        {onPageSeoData?.total_score && (
          <ProgressChart
            value={onPageSeoData.total_score.grade as Grade}
            title="On Page SEO"
            progressStates={[
              {
                label: "Score",
                value: onPageSeoData.total_score.score || 0,
                isNoColor: true,
              },
              { label: "Target", value: 100 },
            ]}
          />
        )}
        {technologyData?.total_score && (
          <ProgressChart
            value={technologyData.total_score.grade as Grade}
            title="Technology Review"
            progressStates={[
              {
                label: "Score",
                value: technologyData.total_score.score || 0,
                isNoColor: true,
              },
              { label: "Target", value: 100 },
            ]}
          />
        )}
        {socialData?.total_score && (
          <ProgressChart
            value={socialData.total_score.grade as Grade}
            title="Social Media"
            progressStates={[
              {
                label: "Score",
                value: socialData.total_score.score || 0,
                isNoColor: true,
              },
              { label: "Target", value: 100 },
            ]}
          />
        )}
      </div>

      {/* Second row of charts - Usability, Performance, Links */}
      <div className="mt-4 lg:mt-6 flex flex-row items-end justify-center gap-8 mb-5.5 print:flex-row print:justify-center print:items-end">
        {usabilityData?.total_score && (
          <ProgressChart
            value={usabilityData.total_score.grade as Grade}
            title="Usability"
            progressStates={[
              {
                label: "Score",
                value: usabilityData.total_score.score || 0,
                isNoColor: true,
              },
              { label: "Target", value: 100 },
            ]}
          />
        )}
        {performanceData?.total_score && (
          <ProgressChart
            value={performanceData.total_score.grade as Grade}
            title="Performance"
            progressStates={[
              {
                label: "Score",
                value: performanceData.total_score.score || 0,
                isNoColor: true,
              },
              { label: "Target", value: 100 },
            ]}
          />
        )}
        {linksData?.total_score && (
          <ProgressChart
            value={linksData.total_score.grade as Grade}
            title="Backlinks Analysis"
            progressStates={[
              {
                label: "Score",
                value: linksData.total_score.score || 0,
                isNoColor: true,
              },
              { label: "Target", value: 100 },
            ]}
          />
        )}
      </div>

      {/* Third row for Local SEO if available */}
      {localSeoData?.total_score && (
        <div className="mt-4 lg:mt-6 flex flex-row items-end justify-center gap-8 mb-5.5 print:flex-row print:justify-center print:items-end">
          <ProgressChart
            value={localSeoData.total_score.grade as Grade}
            title="Local SEO"
            progressStates={[
              {
                label: "Score",
                value: localSeoData.total_score.score || 0,
                isNoColor: true,
              },
              { label: "Target", value: 100 },
            ]}
          />
        </div>
      )}
    </div>
  );
};
