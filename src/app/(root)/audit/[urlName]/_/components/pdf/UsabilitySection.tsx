"use client";
import React from "react";
import { UsabilityAnalysis } from "@/types/seoAnalyzerTypes";
import { SectionHeader, DataRow, RecommendationCard } from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";

export interface UsabilitySectionProps {
  usabilityData: UsabilityAnalysis;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const UsabilitySection: React.FC<UsabilitySectionProps> = ({
  usabilityData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "seoanalyser.com.au"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoUsability")}
        onLogoError={() => onImageError?.("sectionLogoUsability")}
        sectionId="usability-details"
      />

      <SectionHeader
        title="Usability Audit Details"
        brandName={brand_name}
        brandWebsite={brand_website}
        scoreGrade={usabilityData.total_score}
        showScore={!!usabilityData.total_score}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Device Rendering */}
        {usabilityData.device_rendering && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Device Rendering
              </h3>
            </div>

            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Mobile Friendly"
                value={
                  usabilityData.device_rendering.mobile_friendly
                    ? "✓ Yes"
                    : "✗ No"
                }
                important={!usabilityData.device_rendering.mobile_friendly}
              />
              <DataRow
                label="Responsive Design"
                value={
                  usabilityData.device_rendering.responsive ? "✓ Yes" : "✗ No"
                }
                important={!usabilityData.device_rendering.responsive}
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.device_rendering.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
                important={!usabilityData.device_rendering.pass}
              />
              <DataRow
                label="Score"
                value={`${usabilityData.device_rendering.score || 0}/5`}
                important={true}
              />
            </div>
            {usabilityData.device_rendering.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.device_rendering.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Viewport Configuration */}
        {usabilityData.viewport_usage && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Viewport Configuration
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Viewport Meta Tag"
                value={
                  usabilityData.viewport_usage.has_viewport_meta
                    ? "✓ Present"
                    : "✗ Missing"
                }
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.viewport_usage.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
              />
              <DataRow
                label="Score"
                value={`${usabilityData.viewport_usage.score || 0}/5`}
              />
            </div>
            {usabilityData.viewport_usage.viewport_content && (
              <div className="mt-3">
                <h4 className="font-medium text-gray-700 mb-1">
                  Viewport Content:
                </h4>
                <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                  {usabilityData.viewport_usage.viewport_content}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Font Legibility */}
        {usabilityData.font_legibility && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Font Legibility
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Legible Fonts"
                value={
                  usabilityData.font_legibility.legible_font_size
                    ? "✓ Yes"
                    : "✗ Too Small"
                }
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.font_legibility.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
              />
              <DataRow
                label="Score"
                value={`${usabilityData.font_legibility.score || 0}/5`}
              />
            </div>
            {usabilityData.font_legibility.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.font_legibility.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Tap Target Sizing */}
        {usabilityData.tap_target_sizing && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Tap Target Sizing
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Appropriate Size"
                value={
                  usabilityData.tap_target_sizing.appropriate_tap_targets
                    ? "✓ Yes"
                    : "✗ Too Small"
                }
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.tap_target_sizing.pass
                    ? "✓ Good"
                    : "⚠ Issues Found"
                }
              />
              <DataRow
                label="Score"
                value={`${usabilityData.tap_target_sizing.score || 0}/5`}
              />
            </div>
            {usabilityData.tap_target_sizing.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.tap_target_sizing.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Flash Usage */}
        {usabilityData.flash_usage && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Flash Usage</h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Flash Detected"
                value={usabilityData.flash_usage.uses_flash ? "⚠ Yes" : "✓ No"}
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.flash_usage.pass ? "✓ Good" : "⚠ Flash Found"
                }
              />
              <DataRow
                label="Score"
                value={`${usabilityData.flash_usage.score || 0}/5`}
              />
            </div>
            {usabilityData.flash_usage.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.flash_usage.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Iframe Usage */}
        {usabilityData.iframe_usage && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">Iframe Usage</h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Iframe Count"
                value={usabilityData.iframe_usage.iframe_count || 0}
              />
              <DataRow
                label="Status"
                value={
                  usabilityData.iframe_usage.pass ? "✓ Good" : "⚠ Issues Found"
                }
              />
              <DataRow
                label="Score"
                value={`${usabilityData.iframe_usage.score || 0}/5`}
              />
            </div>
            {usabilityData.iframe_usage.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {usabilityData.iframe_usage.description}
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Usability Recommendations - Moved outside grid for full width */}
      <div className="mt-8">
        <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
          Usability Recommendations
        </h3>
        <div className="grid grid-cols-1 gap-4 recommendations-grid">
          {/* Mobile Responsiveness Recommendations */}
          {usabilityData.mobile_responsiveness?.recommendation && (
            <RecommendationCard
              recommendation={
                usabilityData.mobile_responsiveness.recommendation
              }
            />
          )}

          {/* Font Legibility Recommendations */}
          {usabilityData.font_legibility?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.font_legibility.recommendation}
            />
          )}

          {/* Tap Target Sizing Recommendations */}
          {usabilityData.tap_target_sizing?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.tap_target_sizing.recommendation}
            />
          )}

          {/* Flash Usage Recommendations */}
          {usabilityData.flash_usage?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.flash_usage.recommendation}
            />
          )}

          {/* iFrame Usage Recommendations */}
          {usabilityData.iframe_usage?.recommendation && (
            <RecommendationCard
              recommendation={usabilityData.iframe_usage.recommendation}
            />
          )}

          {/* General Usability Recommendation if no specific ones */}
          {!usabilityData.mobile_responsiveness?.recommendation &&
            !usabilityData.font_legibility?.recommendation &&
            !usabilityData.tap_target_sizing?.recommendation &&
            !usabilityData.flash_usage?.recommendation &&
            !usabilityData.iframe_usage?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: "Your website's usability appears to be well optimized. Continue monitoring mobile responsiveness and user experience metrics.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
