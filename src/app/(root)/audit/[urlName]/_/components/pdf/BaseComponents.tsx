"use client";
import React from "react";
import { RecommendationType, ScoreGrade } from "@/types/seoAnalyzerTypes";
import ProgressChart from "@/ui/charts/ProgressChart";

// Base Props Types
export interface SectionWatermarkProps {
  brandName?: string;
  brandWebsite?: string;
  brandPhoto?: string | null;
  onLogoLoad?: () => void;
  onLogoError?: () => void;
  showLogo?: boolean;
  logoSize?: "small" | "medium" | "large";
  sectionId?: string;
}

export interface SectionHeaderProps {
  title: string;
  brandName?: string;
  brandWebsite?: string;
  scoreGrade?: ScoreGrade;
  showScore?: boolean;
}

export interface ScoreChartProps {
  score: number;
  size?: string;
  compact?: boolean;
}

export interface ScoreDisplayProps {
  scoreGrade: ScoreGrade;
  compact?: boolean;
}

export interface RecommendationCardProps {
  recommendation: RecommendationType;
}

export interface DataRowProps {
  label: string;
  value: string | number | React.ReactNode;
  important?: boolean;
}

// Chart component for score visualization
export const ScoreChart: React.FC<ScoreChartProps> = ({
  score,
  size = "w-24 h-24",
  compact = false,
}) => {
  const circumference = 2 * Math.PI * 42; // r = 42 for better visual balance
  const strokeDashoffset = circumference - (score / 100) * circumference;

  // Use app's color scheme with primary color integration
  let color = "rgba(52, 199, 89, 1)"; // Primary green for excellent scores
  let bgColor = "rgba(52, 199, 89, 0.1)";

  if (score < 30) {
    color = "rgba(255, 59, 48, 1)"; // Primary red for poor scores
    bgColor = "rgba(255, 59, 48, 0.1)";
  } else if (score < 50) {
    color = "rgba(255, 168, 24, 1)"; // Primary orange for below average
    bgColor = "rgba(255, 168, 24, 0.1)";
  } else if (score < 70) {
    color = "rgba(255, 204, 0, 1)"; // Primary yellow for average
    bgColor = "rgba(255, 204, 0, 0.1)";
  } else if (score < 85) {
    color = "rgba(67, 176, 119, 1)"; // Secondary green for good
    bgColor = "rgba(67, 176, 119, 0.1)";
  }

  // Improved text sizing for better readability
  const scoreTextSize = compact ? "text-lg" : "text-xl";
  const labelTextSize = compact ? "text-[9px]" : "text-[10px]";
  const strokeWidth = compact ? "5" : "6";

  return (
    <div className={`relative ${size} flex items-center justify-center`}>
      {/* Subtle background circle for depth */}
      <div
        className="absolute inset-1 rounded-full opacity-20"
        style={{ backgroundColor: bgColor }}
      />
      <svg className="w-full h-full drop-shadow-sm" viewBox="0 0 100 100">
        {/* Background circle with subtle styling */}
        <circle
          cx="50"
          cy="50"
          r="42"
          fill="none"
          stroke="rgba(224, 224, 224, 0.8)"
          strokeWidth={strokeWidth}
        />
        {/* Score circle with smooth animation */}
        <circle
          cx="50"
          cy="50"
          r="42"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform="rotate(-90 50 50)"
          className="transition-all duration-300 ease-out"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center flex-col">
        <span
          className={`${scoreTextSize} font-bold tracking-tight`}
          style={{ color }}
        >
          {Math.round(score)}
        </span>
        <span
          className={`${labelTextSize} text-gray-600 font-medium uppercase tracking-wide`}
        >
          SCORE
        </span>
      </div>
    </div>
  );
};

// Score display with grade
export const ScoreDisplay: React.FC<ScoreDisplayProps> = ({
  scoreGrade,
  compact = false,
}) => {
  const { grade, score } = scoreGrade;

  // Use app's primary color scheme for grades
  let color = "rgba(52, 199, 89, 1)"; // Primary green for excellent grades
  let bgColor = "rgba(52, 199, 89, 0.12)";
  let borderColor = "rgba(52, 199, 89, 0.3)";

  if (grade.startsWith("A")) {
    color = "rgba(52, 199, 89, 1)"; // Primary green
    bgColor = "rgba(52, 199, 89, 0.12)";
    borderColor = "rgba(52, 199, 89, 0.3)";
  } else if (grade.startsWith("B")) {
    color = "rgba(67, 176, 119, 1)"; // Secondary green
    bgColor = "rgba(67, 176, 119, 0.12)";
    borderColor = "rgba(67, 176, 119, 0.3)";
  } else if (grade.startsWith("C")) {
    color = "rgba(255, 204, 0, 1)"; // Primary yellow
    bgColor = "rgba(255, 204, 0, 0.12)";
    borderColor = "rgba(255, 204, 0, 0.3)";
  } else if (grade.startsWith("D")) {
    color = "rgba(255, 168, 24, 1)"; // Primary orange
    bgColor = "rgba(255, 168, 24, 0.12)";
    borderColor = "rgba(255, 168, 24, 0.3)";
  } else if (grade === "F") {
    color = "rgba(255, 59, 48, 1)"; // Primary red
    bgColor = "rgba(255, 59, 48, 0.12)";
    borderColor = "rgba(255, 59, 48, 0.3)";
  }

  // Improved sizing for better visual hierarchy
  const chartSize = compact ? "w-20 h-20" : "w-28 h-28";
  const gapSize = compact ? "gap-2" : "gap-3";
  const textSize = compact ? "text-sm" : "text-base";
  const paddingSize = compact ? "px-3 py-1" : "px-4 py-1.5";
  const marginTop = compact ? "mt-1" : "mt-2";

  return (
    <div className={`flex flex-col items-center ${gapSize}`}>
      <ScoreChart score={score} size={chartSize} compact={compact} />
      <div className={`text-center ${marginTop}`}>
        <div
          className={`${textSize} font-semibold ${paddingSize} rounded-lg border shadow-sm transition-all duration-200`}
          style={{
            color: color,
            backgroundColor: bgColor,
            borderColor: borderColor,
          }}
        >
          Grade {grade}
        </div>
      </div>
    </div>
  );
};

// Section header component
export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  brandName,
  brandWebsite,
  scoreGrade,
  showScore = false,
}) => {
  const displayBrand = brandName ?? brandWebsite ?? "seoanalyser.com.au";

  return (
    <div className="w-full mb-8 relative pdf-section-header">
      {/* Enhanced header with distinct section box styling */}
      <div className="bg-gradient-to-r from-primary/8 to-primary/12 rounded-xl p-8 border-2 border-primary/25 shadow-lg print:shadow-md print:border print:border-gray-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-5">
            {/* Enhanced primary accent bar */}
            <div className="w-2 h-16 bg-gradient-to-b from-primary to-primary/70 rounded-full shadow-md" />
            <div>
              <h2 className="text-3xl font-bold text-primary tracking-tight leading-tight print:text-2xl">
                {title}
              </h2>
              <div className="h-1 w-20 bg-primary/50 rounded-full mt-3" />
            </div>
          </div>

          {/* Score display and brand watermark */}
          <div className="flex items-center gap-6">
            {/* Section Score Chart */}
            {showScore && scoreGrade && (
              <div className="flex flex-col items-center">
                <ProgressChart
                  value={scoreGrade.grade}
                  score={scoreGrade.score}
                  title=""
                  size="sm"
                  progressStates={[
                    {
                      label: "Score",
                      value: scoreGrade.score || 0,
                      isNoColor: true,
                    },
                  ]}
                />
                <span className="text-xs text-primary/70 font-medium mt-1">
                  Section Score
                </span>
              </div>
            )}

            {/* Enhanced brand watermark */}
            <div className="hidden sm:block pointer-events-none">
              <div className="text-base font-semibold text-primary/70 select-none bg-white/60 px-4 py-2 rounded-xl border-2 border-primary/25 shadow-sm">
                {displayBrand}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Recommendation card component
export const RecommendationCard: React.FC<RecommendationCardProps> = ({
  recommendation,
}) => {
  // Use app's color scheme for priorities
  let priorityColor = "rgba(52, 199, 89, 1)"; // Primary green for low priority
  let priorityBg = "rgba(52, 199, 89, 0.12)";
  let borderColor = "rgba(52, 199, 89, 0.3)";
  let priorityIcon = "✓";

  if (recommendation.priority === "High") {
    priorityColor = "rgba(255, 59, 48, 1)"; // Primary red for high priority
    priorityBg = "rgba(255, 59, 48, 0.12)";
    borderColor = "rgba(255, 59, 48, 0.3)";
    priorityIcon = "!";
  } else if (recommendation.priority === "Medium") {
    priorityColor = "rgba(255, 168, 24, 1)"; // Primary orange for medium priority
    priorityBg = "rgba(255, 168, 24, 0.12)";
    borderColor = "rgba(255, 168, 24, 0.3)";
    priorityIcon = "⚠";
  }

  return (
    <div className="p-5 border rounded-xl shadow-sm hover:shadow-md transition-all duration-200 bg-white border-gray-200/80">
      <div className="flex items-start gap-4">
        <div
          className="w-10 h-10 rounded-xl flex items-center justify-center text-white font-bold text-sm flex-shrink-0 shadow-sm"
          style={{ backgroundColor: priorityColor }}
        >
          {priorityIcon}
        </div>
        <div className="flex-grow">
          <p className="text-sm text-gray-700 leading-relaxed font-medium">
            {recommendation.text}
          </p>
          <div className="mt-3 flex items-center gap-2">
            <span
              className="text-xs px-3 py-1.5 rounded-lg font-semibold border"
              style={{
                color: priorityColor,
                backgroundColor: priorityBg,
                borderColor: borderColor,
              }}
            >
              {recommendation.priority} Priority
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Data row component
export const DataRow: React.FC<DataRowProps> = ({
  label,
  value,
  important = false,
}) => (
  <div
    className={`flex justify-between items-center py-3 px-4 border-b border-gray-100/80 hover:bg-primary/5 transition-all duration-200 rounded-lg ${
      important ? "bg-primary/8 border-primary/20 shadow-sm" : ""
    }`}
  >
    <span
      className={`${
        important ? "text-primary font-semibold" : "text-gray-700 font-medium"
      } text-sm leading-relaxed`}
    >
      {label}
    </span>
    <span
      className={`${
        important ? "text-primary font-bold" : "text-gray-800 font-semibold"
      } text-sm text-right`}
    >
      {value}
    </span>
  </div>
);
