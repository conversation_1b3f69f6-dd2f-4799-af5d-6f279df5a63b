"use client";
import React, { useState } from "react";
import {
  OnPageAnalysis,
  UsabilityAnalysis,
  TechSEOAnalysis,
  SocialAnalysis,
  PerformanceAnalysis,
  LinksAnalysis,
  PageSpeedAnalysis,
  PageSpeedMobileAnalysis,
  LocalSEOAnalysis,
} from "@/types/seoAnalyzerTypes";
import { SectionHeader } from "./BaseComponents";
import {
  Watermark,
  LogoWatermark,
  SectionWatermark,
} from "./WatermarkComponents";
import { HeaderSection } from "./HeaderSection";
import { ScreenshotSection } from "./ScreenshotSection";
import { OverallScoresSection } from "./OverallScoresSection";
import { OnPageSeoSection } from "./OnPageSeoSection";
import { UsabilitySection } from "./UsabilitySection";
import { TechnologySection } from "./TechnologySection";
import { SocialMediaSection } from "./SocialMediaSection";
import { PerformanceSection } from "./PerformanceSection";
import { PageSpeedSection } from "./PageSpeedSection";
import { LinksSection } from "./LinksSection";
import { LocalSeoSection } from "./LocalSeoSection";

// Main component props type
type SeoAnalyzerPdfProps = {
  urlName: string;
  screenshotUrl: string | null;
  onPageSeoData: OnPageAnalysis;
  usabilityData: UsabilityAnalysis;
  technologyData: TechSEOAnalysis; // Field name changed from technology_review_analysis to technology_review_analysis
  socialData: SocialAnalysis;
  performanceData: PerformanceAnalysis;
  linksData: LinksAnalysis;
  pagespeedData: PageSpeedAnalysis;
  pagespeedMobileData: PageSpeedMobileAnalysis;
  localSeoData: LocalSEOAnalysis;
  childPagesData: string[];
  // White label branding properties
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
};

// PlaceholderIcon is now imported from WatermarkComponents

// TechnologyIcon is now imported from TechnologyIcon.tsx

// Watermark is now imported from WatermarkComponents

// LogoWatermark is now imported from WatermarkComponents

// SectionWatermark is now imported from WatermarkComponents

// This will be moved to base components

const SeoAnalyzerPdf: React.FC<SeoAnalyzerPdfProps> = ({
  urlName,
  screenshotUrl,
  onPageSeoData,
  usabilityData,
  technologyData,
  socialData,
  performanceData,
  linksData,
  pagespeedData,
  pagespeedMobileData,
  localSeoData,
  childPagesData,
  brand_name,
  brand_website,
  brand_photo,
}) => {
  // Track which images have failed to load
  const [failedImages, setFailedImages] = useState<Record<string, boolean>>({});
  const [imagesLoaded, setImagesLoaded] = useState<Record<string, boolean>>({});
  const [allImagesLoaded, setAllImagesLoaded] = useState(false);
  const [renderStage, setRenderStage] = useState<
    "initial" | "critical" | "complete"
  >("initial");

  // Track technology icon loading states
  const [technologyIconsLoaded, setTechnologyIconsLoaded] = useState<
    Record<number, boolean>
  >({});
  const [technologyIconsFailed, setTechnologyIconsFailed] = useState<
    Record<number, boolean>
  >({});

  // Effect for progressive rendering - first render critical content, then complete
  React.useEffect(() => {
    // First render critical content immediately
    setRenderStage("critical");

    // Then schedule complete render
    const completeRender = setTimeout(() => {
      setRenderStage("complete");

      // Notify that critical content is ready
      if (typeof window !== "undefined") {
        const event = new CustomEvent("pdf-critical-content-ready", {
          detail: { ready: true },
        });
        window.dispatchEvent(event);
      }
    }, 100); // Very short delay for critical content

    return () => clearTimeout(completeRender);
  }, []);

  // Effect to check if all images are loaded or failed - optimized version
  React.useEffect(() => {
    // Check if we have any images to load
    const hasScreenshot = !!screenshotUrl;
    const hasBrandPhoto = !!brand_photo;
    const hasLogo = true; // Always check for logo loading for watermark

    // If we have no images, mark as all loaded
    if (!hasScreenshot && !hasLogo && !hasBrandPhoto) {
      setAllImagesLoaded(true);

      // Dispatch ready event immediately if no images to load
      if (typeof window !== "undefined") {
        const event = new CustomEvent("pdf-content-ready", {
          detail: { ready: true },
        });
        window.dispatchEvent(event);
      }
      return;
    }

    // Check if all images are either loaded or failed
    const allLoaded =
      (!hasScreenshot ||
        imagesLoaded["screenshot"] ||
        failedImages["screenshot"]) &&
      (!hasLogo || imagesLoaded["logo"] || failedImages["logo"]) &&
      (!hasBrandPhoto ||
        imagesLoaded["brandLogo"] ||
        failedImages["brandLogo"]);

    if (allLoaded) {
      setAllImagesLoaded(true);

      // Dispatch a custom event that the parent component can listen for
      if (typeof window !== "undefined") {
        const event = new CustomEvent("pdf-content-ready", {
          detail: { ready: true },
        });
        window.dispatchEvent(event);
      }
    }
  }, [screenshotUrl, brand_photo, imagesLoaded, failedImages]);

  // Effect to set a shorter timeout to mark as ready even if images don't load properly
  React.useEffect(() => {
    // Use a shorter timeout for better user experience
    const timeout = setTimeout(() => {
      if (!allImagesLoaded) {
        setAllImagesLoaded(true);

        // Dispatch a custom event that the parent component can listen for
        if (typeof window !== "undefined") {
          const event = new CustomEvent("pdf-content-ready", {
            detail: { ready: true, timedOut: true },
          });
          window.dispatchEvent(event);
        }
      }
    }, 2000); // Reduced from 5s to 2s for faster experience

    return () => clearTimeout(timeout);
  }, [allImagesLoaded]);

  // Handle image load error
  const handleImageError = (id: string) => {
    setFailedImages((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  // Handle image load success
  const handleImageLoad = (id: string) => {
    setImagesLoaded((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  // Handle technology icon load result
  const handleTechnologyIconLoad = (index: number, success: boolean) => {
    if (success) {
      setTechnologyIconsLoaded((prev) => ({
        ...prev,
        [index]: true,
      }));
    } else {
      setTechnologyIconsFailed((prev) => ({
        ...prev,
        [index]: true,
      }));
    }
  };

  // Get current date for report generation timestamp
  const today = new Date();
  const time = {
    day: today.getDate(),
    month: today.toLocaleString("default", { month: "long" }),
    year: today.getFullYear(),
    hours: today.getHours(),
    minutes: today.getMinutes().toString().padStart(2, "0"),
  };

  // Calculate overall score
  const calculateOverallScore = (): number => {
    let totalScore = 0;
    let count = 0;

    if (onPageSeoData?.total_score) {
      totalScore += onPageSeoData.total_score.score;
      count++;
    }
    if (usabilityData?.total_score) {
      totalScore += usabilityData.total_score.score;
      count++;
    }
    if (technologyData?.total_score) {
      totalScore += technologyData.total_score.score;
      count++;
    }
    if (socialData?.total_score) {
      totalScore += socialData.total_score.score;
      count++;
    }
    if (performanceData?.total_score) {
      totalScore += performanceData.total_score.score;
      count++;
    }
    if (linksData?.total_score) {
      totalScore += linksData.total_score.score;
      count++;
    }
    if (localSeoData?.total_score) {
      totalScore += localSeoData.total_score.score;
      count++;
    }

    return count > 0 ? totalScore / count : 0;
  };

  // Note: calculateOverallScore is used directly in the JSX

  // renderGrade is now replaced by ScoreDisplay component

  // renderRecommendation is now replaced by RecommendationCard component

  // renderSectionHeader is now replaced by SectionHeader component

  // renderDataRow is now replaced by DataRow component

  // Preload critical resources
  React.useEffect(() => {
    // Preload critical resources for faster rendering
    if (typeof window !== "undefined") {
      // Preload the brand logo if available
      if (brand_photo) {
        const preloadBrandPhoto = document.createElement("link");
        preloadBrandPhoto.rel = "preload";
        preloadBrandPhoto.as = "image";
        preloadBrandPhoto.href = brand_photo;
        document.head.appendChild(preloadBrandPhoto);
      }

      // Preload screenshot if available
      if (screenshotUrl) {
        const preloadScreenshot = document.createElement("link");
        preloadScreenshot.rel = "preload";
        preloadScreenshot.as = "image";
        preloadScreenshot.href = screenshotUrl;
        document.head.appendChild(preloadScreenshot);
      }

      // Preload brand photo if available
      if (brand_photo) {
        const preloadBrandPhoto = document.createElement("link");
        preloadBrandPhoto.rel = "preload";
        preloadBrandPhoto.as = "image";
        preloadBrandPhoto.href = brand_photo;
        document.head.appendChild(preloadBrandPhoto);
      }

      return () => {
        if (brand_photo) {
          const brandPhotoLink = document.querySelector(
            `link[href="${brand_photo}"]`
          );
          if (brandPhotoLink && document.head.contains(brandPhotoLink)) {
            document.head.removeChild(brandPhotoLink);
          }
        }

        if (screenshotUrl) {
          const screenshotLink = document.querySelector(
            `link[href="${screenshotUrl}"]`
          );
          if (screenshotLink && document.head.contains(screenshotLink)) {
            document.head.removeChild(screenshotLink);
          }
        }
      };
    }
  }, [screenshotUrl, brand_photo]);

  return (
    <div className="w-full mobile-zoom bg-white p-8 font-sans max-w-[1300px] mx-auto shadow-xl rounded-2xl relative print-pdf print:p-4 print:shadow-none print:border-none print:rounded-none">
      {/* Main watermark for the entire document - visible in both normal and print states */}
      <Watermark brandName={brand_name} brandWebsite={brand_website} />
      {/* Main logo watermark for the entire document - show brand photo or default logo */}
      <div className="fixed inset-0 w-full h-full pointer-events-none flex justify-center items-center z-50 overflow-visible">
        {/* <LogoWatermark
          brandPhoto={brand_photo || "/images/appLogo.svg"}
          onLoad={() => handleImageLoad("logo")}
          onError={() => handleImageError("logo")}
          size="large"
        /> */}
      </div>
      {/* Simplified watermark for better print performance */}
      <div className="absolute bottom-4 right-4 pointer-events-none print-watermark">
        <div className="text-lg font-medium text-gray-500/70 select-none">
          {brand_name ?? brand_website ?? "seoanalyser.com.au"}
        </div>
      </div>
      {/* Combined First Page Section - Header, Screenshot, and Overall Scores */}
      <div
        className="mb-8 print-section relative first-page-combined"
        data-watermark={brand_website ?? brand_name ?? "seoanalyser.com.au"}
      >
        {/* Header Section */}
        <div className="mb-4 header-section">
          <HeaderSection
            urlName={urlName}
            onPageSeoData={onPageSeoData}
            brand_name={brand_name}
            brand_website={brand_website}
            brand_photo={brand_photo}
            onImageLoad={handleImageLoad}
            onImageError={handleImageError}
          />
        </div>

        {/* Screenshot Section - Only render if we're in critical or complete stage */}
        {renderStage !== "initial" && (
          <div className="mb-3">
            <ScreenshotSection
              screenshotUrl={screenshotUrl}
              brand_name={brand_name}
              brand_website={brand_website}
              brand_photo={brand_photo}
              onImageLoad={handleImageLoad}
              onImageError={handleImageError}
            />
          </div>
        )}

        {/* Overall Scores Section */}
        <div className="mt-1">
          <OverallScoresSection
            urlName={urlName}
            onPageSeoData={onPageSeoData}
            usabilityData={usabilityData}
            technologyData={technologyData}
            socialData={socialData}
            performanceData={performanceData}
            linksData={linksData}
            localSeoData={localSeoData}
            brand_name={brand_name}
            brand_website={brand_website}
          />
        </div>
      </div>

      {/* Social Media Preview Section - Show key social platforms on first page */}
      {socialData && (
        <div className="mb-8 print-section relative">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
            <h2 className="text-2xl font-bold text-gray-800">
              Social Media Overview
            </h2>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Facebook */}
            {socialData.facebook && (
              <div className="p-4 border border-gray-200/80 rounded-lg bg-white shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <h4 className="font-semibold text-gray-800 text-sm">
                    Facebook
                  </h4>
                </div>
                <p className="text-xs text-gray-600">
                  {socialData.facebook.pass ? "✓ Connected" : "✗ Not Connected"}
                </p>
                <div className="text-xs text-gray-500 mt-1">
                  Score: {socialData.facebook.score || 0}/5
                </div>
              </div>
            )}

            {/* Twitter */}
            {socialData.twitter && (
              <div className="p-4 border border-gray-200/80 rounded-lg bg-white shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-sky-500 rounded-full"></div>
                  <h4 className="font-semibold text-gray-800 text-sm">
                    Twitter
                  </h4>
                </div>
                <p className="text-xs text-gray-600">
                  {socialData.twitter.pass ? "✓ Connected" : "✗ Not Connected"}
                </p>
                <div className="text-xs text-gray-500 mt-1">
                  Score: {socialData.twitter.score || 0}/5
                </div>
              </div>
            )}

            {/* Instagram */}
            {socialData.instagram && (
              <div className="p-4 border border-gray-200/80 rounded-lg bg-white shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                  <h4 className="font-semibold text-gray-800 text-sm">
                    Instagram
                  </h4>
                </div>
                <p className="text-xs text-gray-600">
                  {socialData.instagram.pass
                    ? "✓ Connected"
                    : "✗ Not Connected"}
                </p>
                <div className="text-xs text-gray-500 mt-1">
                  Score: {socialData.instagram.score || 0}/5
                </div>
              </div>
            )}

            {/* LinkedIn */}
            {socialData.linkedin && (
              <div className="p-4 border border-gray-200/80 rounded-lg bg-white shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-blue-700 rounded-full"></div>
                  <h4 className="font-semibold text-gray-800 text-sm">
                    LinkedIn
                  </h4>
                </div>
                <p className="text-xs text-gray-600">
                  {socialData.linkedin.pass ? "✓ Connected" : "✗ Not Connected"}
                </p>
                <div className="text-xs text-gray-500 mt-1">
                  Score: {socialData.linkedin.score || 0}/5
                </div>
              </div>
            )}
          </div>

          {/* Social Meta Tags Summary */}
          {socialData.social_meta_tags && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200/80">
              <h4 className="font-semibold text-gray-800 text-sm mb-2">
                Social Meta Tags
              </h4>
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <span className="text-gray-600">Open Graph Tags: </span>
                  <span
                    className={
                      socialData.social_meta_tags.og_tags_present
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    {socialData.social_meta_tags.og_tags_present
                      ? `✓ ${socialData.social_meta_tags.og_tags_count} found`
                      : "✗ Missing"}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Twitter Cards: </span>
                  <span
                    className={
                      socialData.social_meta_tags.twitter_tags_present
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    {socialData.social_meta_tags.twitter_tags_present
                      ? `✓ ${socialData.social_meta_tags.twitter_tags_count} found`
                      : "✗ Missing"}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500 italic">
              See detailed social media analysis on the following pages
            </p>
          </div>
        </div>
      )}
      {/* Detailed Analysis Sections - Only render in complete stage for faster initial loading */}
      {/* Following the same order as main audit page: Audit, Recommendations, OnPageSEO, Backlinks, Usability, Performance, Technology, LocalSEO, Social, Child Pages */}
      {renderStage === "complete" && (
        <div>
          {/* All Recommendations Section - moved to match main audit order */}
          <div
            className="mb-8 print-section relative recommendations-section"
            data-watermark={brand_website ?? brand_name ?? "seoanalyser.com.au"}
          >
            {/* Section watermark */}
            <SectionWatermark
              brandName={brand_name}
              brandWebsite={brand_website}
              brandPhoto={brand_photo}
              logoSize="medium"
              onLogoLoad={() => handleImageLoad("sectionLogo3")}
              onLogoError={() => handleImageError("sectionLogo3")}
              sectionId="recommendations"
            />

            {/* Note: Detailed recommendations are now included within each analysis section */}
          </div>

          {/* On-Page SEO Details Section - moved to match main audit order */}
          {onPageSeoData && (
            <div className="print-section pdf-section-box">
              <OnPageSeoSection
                onPageSeoData={onPageSeoData}
                brand_name={brand_name}
                brand_website={brand_website}
                brand_photo={brand_photo}
                onImageLoad={handleImageLoad}
                onImageError={handleImageError}
              />
            </div>
          )}

          {/* Links Analysis - moved to match main audit order (Backlinks) */}
          {linksData && (
            <div className="print-section pdf-section-box">
              <LinksSection
                linksData={linksData}
                urlName={urlName}
                onPageSeoData={onPageSeoData}
                brand_name={brand_name}
                brand_website={brand_website}
                brand_photo={brand_photo}
                onImageLoad={handleImageLoad}
                onImageError={handleImageError}
              />
            </div>
          )}

          {/* Usability Analysis - moved to match main audit order */}
          {usabilityData && (
            <div className="print-section pdf-section-box">
              <UsabilitySection
                usabilityData={usabilityData}
                brand_name={brand_name}
                brand_website={brand_website}
                brand_photo={brand_photo}
                onImageLoad={handleImageLoad}
                onImageError={handleImageError}
              />
            </div>
          )}

          {/* Performance Analysis - moved to match main audit order */}
          {performanceData && (
            <div className="print-section pdf-section-box">
              <PerformanceSection
                performanceData={performanceData}
                brand_name={brand_name}
                brand_website={brand_website}
                brand_photo={brand_photo}
                onImageLoad={handleImageLoad}
                onImageError={handleImageError}
              />
            </div>
          )}

          {/* PageSpeed Analysis - combined with Performance */}
          {/* Technology Review Details Section - moved to match main audit order */}
          {technologyData && (
            <div className="print-section pdf-section-box">
              <TechnologySection
                technologyData={technologyData}
                brand_name={brand_name}
                brand_website={brand_website}
                brand_photo={brand_photo}
                onImageLoad={handleImageLoad}
                onImageError={handleImageError}
                onTechnologyIconLoad={handleTechnologyIconLoad}
              />
            </div>
          )}

          {/* Local SEO Analysis - moved to match main audit order */}
          {localSeoData && (
            <div className="print-section pdf-section-box">
              <LocalSeoSection
                localSeoData={localSeoData}
                urlName={urlName}
                brand_name={brand_name}
                brand_website={brand_website}
                brand_photo={brand_photo}
                onImageLoad={handleImageLoad}
                onImageError={handleImageError}
              />
            </div>
          )}

          {/* Social Media Analysis - moved to match main audit order */}
          {socialData && (
            <div className="print-section pdf-section-box">
              <SocialMediaSection
                socialData={socialData}
                brand_name={brand_name}
                brand_website={brand_website}
                brand_photo={brand_photo}
                onImageLoad={handleImageLoad}
                onImageError={handleImageError}
              />
            </div>
          )}

          {/* PageSpeed Analysis - included as part of Performance section */}
          {(pagespeedData || pagespeedMobileData) && (
            <div className="print-section pdf-section-box">
              <PageSpeedSection
                pagespeedData={pagespeedData}
                pagespeedMobileData={pagespeedMobileData}
                brand_name={brand_name}
                brand_website={brand_website}
                brand_photo={brand_photo}
                onImageLoad={handleImageLoad}
                onImageError={handleImageError}
              />
            </div>
          )}
        </div>
      )}

      {/* Child Pages Section */}
      {renderStage === "complete" &&
        Array.isArray(childPagesData) &&
        childPagesData.length > 0 && (
          <div
            className="mb-8 print-section relative"
            data-watermark={brand_website ?? brand_name ?? "seoanalyser.com.au"}
          >
            <SectionWatermark
              brandName={brand_name}
              brandWebsite={brand_website}
              brandPhoto={brand_photo}
              logoSize="medium"
              onLogoLoad={() => handleImageLoad("sectionLogoPages")}
              onLogoError={() => handleImageError("sectionLogoPages")}
              sectionId="child-pages"
            />
            <SectionHeader
              title={`Child Pages Audit (${childPagesData.length} pages found)`}
              brandName={brand_name}
              brandWebsite={brand_website}
            />

            <div className="grid grid-cols-1 gap-2">
              {childPagesData
                .slice(0, 20)
                .map((page: string, index: number) => (
                  <div
                    key={index}
                    className="p-3 bg-gray-50 rounded-lg border-l-4 border-primary/30 hover:border-primary/60 transition-colors"
                  >
                    <p
                      className="text-sm text-gray-700 font-medium truncate"
                      title={page}
                    >
                      {page}
                    </p>
                  </div>
                ))}
              {childPagesData.length > 20 && (
                <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-300 text-center">
                  <p className="text-sm text-blue-700 font-medium">
                    ... and {childPagesData.length - 20} more pages
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

      {/* Footer */}
      <div
        className="mt-12 pt-4 border-t border-gray-200 text-center text-sm text-gray-500 print-section relative"
        data-watermark={brand_website ?? brand_name ?? "seoanalyser.com.au"}
      >
        <p className="font-bold">{brand_name ?? "SEO Analyzer"} Report</p>
        <p>
          Generated on {time.day} {time.month} {time.year} at {time.hours}:
          {time.minutes}
        </p>
        {brand_website && (
          <p>
            Visit us at{" "}
            <a
              href={brand_website}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              {brand_website}
            </a>
          </p>
        )}
      </div>
    </div>
  );
};

export default SeoAnalyzerPdf;
