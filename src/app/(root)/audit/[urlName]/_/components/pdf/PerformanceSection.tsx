"use client";
import React from "react";
import { PerformanceAnalysis } from "@/types/seoAnalyzerTypes";
import { SectionHeader, DataRow, RecommendationCard } from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";

export interface PerformanceSectionProps {
  performanceData: PerformanceAnalysis;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const PerformanceSection: React.FC<PerformanceSectionProps> = ({
  performanceData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "seoanalyser.com.au"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoPerf")}
        onLogoError={() => onImageError?.("sectionLogoPerf")}
        sectionId="performance-details"
      />

      <SectionHeader
        title="Performance Audit Details"
        brandName={brand_name}
        brandWebsite={brand_website}
        scoreGrade={performanceData.total_score}
        showScore={!!performanceData.total_score}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Page Size Analysis */}
        {performanceData.page_size && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Page Size Audit
              </h3>
            </div>

            {/* Total size highlight */}
            <div className="mb-4 p-4 bg-gradient-to-r from-primary/8 to-primary/5 rounded-xl border border-primary/20">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {performanceData.page_size.total_estimated_size_mb || 0} MB
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  Total Page Size
                </div>
              </div>
            </div>

            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="HTML Size"
                value={`${
                  performanceData.page_size.breakdown_estimated_mb?.html_mb || 0
                } MB`}
              />
              <DataRow
                label="JavaScript Size"
                value={`${
                  performanceData.page_size.breakdown_estimated_mb?.est_js_mb ||
                  0
                } MB`}
              />
              <DataRow
                label="CSS Size"
                value={`${
                  performanceData.page_size.breakdown_estimated_mb
                    ?.est_css_mb || 0
                } MB`}
              />
              <DataRow
                label="Images Size"
                value={`${
                  performanceData.page_size.breakdown_estimated_mb
                    ?.est_images_mb || 0
                } MB`}
              />
              <DataRow
                label="Status"
                value={
                  performanceData.page_size.pass ? "✓ Good" : "⚠ Too Large"
                }
              />
              <DataRow
                label="Score"
                value={`${performanceData.page_size.score || 0}/5`}
              />
            </div>
            {performanceData.page_size.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {performanceData.page_size.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Compression Analysis */}
        {performanceData.compression && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Compression Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Compression Ratio"
                value={`${
                  performanceData.compression.compression_ratio?.toFixed(1) || 0
                }%`}
              />
              <DataRow
                label="Status"
                value={
                  performanceData.compression.pass
                    ? "✓ Enabled"
                    : "⚠ Not Enabled"
                }
              />
              <DataRow
                label="Score"
                value={`${performanceData.compression.score || 0}/5`}
              />
            </div>
            {performanceData.compression.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {performanceData.compression.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* JavaScript Errors */}
        {performanceData.javascript_errors && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                JavaScript Errors
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Error Count"
                value={performanceData.javascript_errors.error_count || 0}
              />
              <DataRow
                label="Status"
                value={
                  performanceData.javascript_errors.pass
                    ? "✓ No Errors"
                    : "⚠ Errors Found"
                }
              />
              <DataRow
                label="Score"
                value={`${performanceData.javascript_errors.score || 0}/5`}
              />
            </div>
            {performanceData.javascript_errors.errors &&
              performanceData.javascript_errors.errors.length > 0 && (
                <div className="mt-3">
                  <h4 className="font-medium text-gray-700 mb-2">
                    Recent Errors:
                  </h4>
                  <div className="space-y-1">
                    {/* Removed scrollbar - max-h-32 overflow-y-auto */}
                    {performanceData.javascript_errors.errors
                      .slice(0, 3)
                      .map((error, index) => (
                        <p
                          key={index}
                          className="text-xs text-red-600 bg-red-50 p-2 rounded"
                        >
                          {error}
                        </p>
                      ))}
                  </div>
                </div>
              )}
          </div>
        )}

        {/* Image Optimization */}
        {performanceData.image_optimization && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Image Optimization
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Total Images"
                value={performanceData.image_optimization.total_images || 0}
              />
              <DataRow
                label="Optimized Images"
                value={performanceData.image_optimization.optimized_images || 0}
              />
              <DataRow
                label="Status"
                value={
                  performanceData.image_optimization.pass
                    ? "✓ Well Optimized"
                    : "⚠ Needs Optimization"
                }
              />
              <DataRow
                label="Score"
                value={`${performanceData.image_optimization.score || 0}/5`}
              />
            </div>
            {performanceData.image_optimization.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {performanceData.image_optimization.description}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Minification */}
        {performanceData.minification && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Code Minification
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="CSS Minified"
                value={
                  performanceData.minification.css_minified ? "✓ Yes" : "✗ No"
                }
              />
              <DataRow
                label="JS Minified"
                value={
                  performanceData.minification.js_minified ? "✓ Yes" : "✗ No"
                }
              />
              <DataRow
                label="HTML Minified"
                value={
                  performanceData.minification.html_minified ? "✓ Yes" : "✗ No"
                }
              />
              <DataRow
                label="Status"
                value={
                  performanceData.minification.pass
                    ? "✓ Good"
                    : "⚠ Needs Improvement"
                }
              />
              <DataRow
                label="Score"
                value={`${performanceData.minification.score || 0}/5`}
              />
            </div>
          </div>
        )}

        {/* Resource Count */}
        {performanceData.resource_count && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Resource Count
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Total Resources"
                value={performanceData.resource_count.total_resources || 0}
              />
              <DataRow
                label="CSS Files"
                value={performanceData.resource_count.css_count || 0}
              />
              <DataRow
                label="JS Files"
                value={performanceData.resource_count.js_count || 0}
              />
              <DataRow
                label="Image Files"
                value={performanceData.resource_count.image_count || 0}
              />
              <DataRow
                label="Status"
                value={
                  performanceData.resource_count.pass
                    ? "✓ Reasonable"
                    : "⚠ Too Many Resources"
                }
              />
              <DataRow
                label="Score"
                value={`${performanceData.resource_count.score || 0}/5`}
              />
            </div>
          </div>
        )}

        {/* Performance Timing */}
        {performanceData.performance_timing && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-lg">
                Performance Timing
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Time to First Byte"
                value={`${
                  performanceData.performance_timing.time_to_first_byte_s || 0
                } seconds`}
              />
              <DataRow
                label="Status"
                value={
                  performanceData.performance_timing.pass
                    ? "✓ Good"
                    : "⚠ Needs Improvement"
                }
              />
              <DataRow
                label="Score"
                value={`${performanceData.performance_timing.score || 0}/5`}
              />
            </div>
            {performanceData.performance_timing.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {performanceData.performance_timing.description}
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Performance Recommendations - Moved outside grid for full width */}
      <div className="mt-8">
        <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
          Performance Recommendations
        </h3>
        <div className="grid grid-cols-1 gap-4 recommendations-grid">
          {/* Page Size Recommendations */}
          {performanceData.page_size?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.page_size.recommendation}
            />
          )}

          {/* Compression Recommendations */}
          {performanceData.compression?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.compression.recommendation}
            />
          )}

          {/* JavaScript Errors Recommendations */}
          {performanceData.javascript_errors?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.javascript_errors.recommendation}
            />
          )}

          {/* AMP Recommendations */}
          {performanceData.amp?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.amp.recommendation}
            />
          )}

          {/* Inline Styles Recommendations */}
          {performanceData.inline_styles?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.inline_styles.recommendation}
            />
          )}

          {/* Performance Timing Recommendations */}
          {performanceData.performance_timing?.recommendation && (
            <RecommendationCard
              recommendation={performanceData.performance_timing.recommendation}
            />
          )}

          {/* General Performance Recommendation if no specific ones */}
          {!performanceData.page_size?.recommendation &&
            !performanceData.compression?.recommendation &&
            !performanceData.javascript_errors?.recommendation &&
            !performanceData.amp?.recommendation &&
            !performanceData.inline_styles?.recommendation &&
            !performanceData.performance_timing?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: "Your website's performance appears to be well optimized. Continue monitoring page load times and resource optimization.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
