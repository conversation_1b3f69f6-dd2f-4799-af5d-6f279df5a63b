"use client";
import React from "react";
import { OnPageAnalysis } from "@/types/seoAnalyzerTypes";
import { SectionHeader, DataRow, RecommendationCard } from "./BaseComponents";
import { SectionWatermark } from "./WatermarkComponents";

export interface OnPageSeoSectionProps {
  onPageSeoData: OnPageAnalysis;
  brand_name?: string;
  brand_website?: string;
  brand_photo?: string | null;
  onImageLoad?: (id: string) => void;
  onImageError?: (id: string) => void;
}

export const OnPageSeoSection: React.FC<OnPageSeoSectionProps> = ({
  onPageSeoData,
  brand_name,
  brand_website,
  brand_photo,
  onImageLoad,
  onImageError,
}) => {
  return (
    <div
      className="mb-8 print-section relative"
      data-watermark={brand_website || brand_name || "seoanalyser.com.au"}
    >
      <SectionWatermark
        brandName={brand_name}
        brandWebsite={brand_website}
        brandPhoto={brand_photo}
        logoSize="medium"
        onLogoLoad={() => onImageLoad?.("sectionLogoOnPage")}
        onLogoError={() => onImageError?.("sectionLogoOnPage")}
        sectionId="onpage-details"
      />

      <SectionHeader
        title="On-Page SEO Audit"
        brandName={brand_name}
        brandWebsite={brand_website}
        scoreGrade={onPageSeoData.total_score}
        showScore={!!onPageSeoData.total_score}
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Title Tag Analysis */}
        {onPageSeoData.title_tag && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Title Tag Audit
              </h3>
            </div>

            <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-50/50 rounded-xl mb-4 border border-gray-100">
              <p className="text-sm font-medium text-gray-800 break-words leading-relaxed">
                "{onPageSeoData.title_tag.title || "No title found"}"
              </p>
            </div>

            <div className="space-y-1 bg-gray-50/50 rounded-lg p-3">
              <DataRow
                label="Length"
                value={`${onPageSeoData.title_tag.length || 0} characters`}
              />
              <DataRow
                label="Optimal Length"
                value={
                  onPageSeoData.title_tag.is_optimal_length ? "✓ Yes" : "✗ No"
                }
                important={!onPageSeoData.title_tag.is_optimal_length}
              />
              <DataRow
                label="Score"
                value={`${onPageSeoData.title_tag.score || 0}/5`}
                important={true}
              />
              {onPageSeoData.title_tag.description && (
                <DataRow
                  label="Status"
                  value={onPageSeoData.title_tag.description}
                />
              )}
            </div>

            {/* Title Tag Recommendation */}
            {onPageSeoData.title_tag.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.title_tag.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Meta Description Analysis */}
        {onPageSeoData.meta_description && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Meta Description Audit
              </h3>
            </div>

            <div className="p-4 bg-gradient-to-r from-gray-50 to-gray-50/50 rounded-xl mb-4 border border-gray-100">
              <p className="text-sm font-medium text-gray-800 break-words leading-relaxed">
                "
                {onPageSeoData.meta_description.content ||
                  "No meta description found"}
                "
              </p>
            </div>

            <div className="space-y-1 bg-gray-50/50 rounded-lg p-3">
              <DataRow
                label="Length"
                value={`${
                  onPageSeoData.meta_description.length || 0
                } characters`}
              />
              <DataRow
                label="Optimal Length"
                value={
                  onPageSeoData.meta_description.is_optimal_length
                    ? "✓ Yes"
                    : "✗ No"
                }
              />
              <DataRow
                label="Score"
                value={`${onPageSeoData.meta_description.score || 0}/5`}
              />
            </div>

            {/* Meta Description Recommendation */}
            {onPageSeoData.meta_description.recommendation && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <RecommendationCard
                  recommendation={onPageSeoData.meta_description.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* SERP Preview */}
        {onPageSeoData.serp_preview && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Search Engine Preview
              </h3>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
              <h4 className="text-blue-600 text-lg font-medium mb-1 truncate">
                {onPageSeoData.serp_preview.title ||
                  onPageSeoData.title_tag?.title ||
                  "Page Title"}
              </h4>
              <p className="text-green-700 text-sm mb-2 truncate">
                {onPageSeoData.serp_preview.url}
              </p>
              <p className="text-gray-600 text-sm line-clamp-3">
                {onPageSeoData.serp_preview.caption ||
                  onPageSeoData.meta_description?.content ||
                  "No description available"}
              </p>
            </div>
            <div className="mt-3 space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Preview Status"
                value={
                  onPageSeoData.serp_preview.pass
                    ? "✓ Good"
                    : "⚠ Needs Improvement"
                }
                important={!onPageSeoData.serp_preview.pass}
              />
            </div>

            {/* SERP Preview Recommendation */}
            {onPageSeoData.serp_preview.recommendation && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RecommendationCard
                  recommendation={onPageSeoData.serp_preview.recommendation}
                />
              </div>
            )}
          </div>
        )}

        {/* Content Analysis */}
        {onPageSeoData.content_amount && (
          <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
              <h3 className="font-bold text-gray-800 text-xl print:text-lg">
                Content Audit
              </h3>
            </div>
            <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
              <DataRow
                label="Word Count"
                value={`${onPageSeoData.content_amount.word_count || 0} words`}
              />
              <DataRow
                label="Text/HTML Ratio"
                value={`${
                  onPageSeoData.content_amount.text_html_ratio_percent || 0
                }%`}
              />
              <DataRow
                label="Content Status"
                value={
                  onPageSeoData.content_amount.pass
                    ? "✓ Sufficient"
                    : "⚠ Insufficient"
                }
              />
              <DataRow
                label="Score"
                value={`${onPageSeoData.content_amount.score || 0}/5`}
              />
            </div>
            {onPageSeoData.content_amount.description && (
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  {onPageSeoData.content_amount.description}
                </p>
              </div>
            )}

            {/* Content Analysis Recommendation */}
            {onPageSeoData.content_amount.recommendation && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <RecommendationCard
                  recommendation={onPageSeoData.content_amount.recommendation}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Headers Analysis - synchronized with main audit component */}
      {onPageSeoData.headers && (
        <div className="mt-6">
          <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
            Headers Structure Audit
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Header Distribution Chart - using correct data structure */}
            <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-1.5 h-6 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                <h4 className="font-bold text-gray-800 text-base">
                  Header Distribution
                </h4>
              </div>
              <div className="flex items-end justify-around h-32 mb-4">
                {/* H1 Bar */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-blue-500 w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.h1?.count || 0) * 20,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-2 font-medium">H1</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.h1?.count || 0}
                  </span>
                </div>
                {/* H2 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-green-500 w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h2?.count || 0) *
                          5,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-2 font-medium">H2</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h2?.count || 0}
                  </span>
                </div>
                {/* H3 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-yellow-500 w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h3?.count || 0) *
                          3,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-2 font-medium">H3</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h3?.count || 0}
                  </span>
                </div>
                {/* H4 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-orange-500 w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h4?.count || 0) *
                          2,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-2 font-medium">H4</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h4?.count || 0}
                  </span>
                </div>
                {/* H5 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-red-500 w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h5?.count || 0) *
                          2,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-2 font-medium">H5</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h5?.count || 0}
                  </span>
                </div>
                {/* H6 Bar - using other_headers structure */}
                <div className="flex flex-col items-center">
                  <div
                    className="bg-purple-500 w-8 rounded-t"
                    style={{
                      height: `${Math.min(
                        (onPageSeoData.headers.other_headers?.h6?.count || 0) *
                          2,
                        100
                      )}px`,
                    }}
                  ></div>
                  <span className="text-xs mt-2 font-medium">H6</span>
                  <span className="text-xs text-gray-600">
                    {onPageSeoData.headers.other_headers?.h6?.count || 0}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <DataRow
                  label="H1 Status"
                  value={
                    onPageSeoData.headers.h1?.pass ? "✓ Good" : "⚠ Issues Found"
                  }
                />
                <DataRow
                  label="Total Headers"
                  value={`${
                    (onPageSeoData.headers.h1?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h2?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h3?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h4?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h5?.count || 0) +
                    (onPageSeoData.headers.other_headers?.h6?.count || 0)
                  }`}
                />
              </div>
            </div>

            {/* Header Details - synchronized with main audit component */}
            <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-1.5 h-6 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                <h4 className="font-bold text-gray-800 text-base">
                  Header Content
                </h4>
              </div>
              <div className="space-y-4">
                {/* Removed scrollbar - max-h-80 overflow-y-auto */}
                {/* H1 Content */}
                {onPageSeoData.headers.h1?.content &&
                  onPageSeoData.headers.h1.content.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-700 mb-2">
                        H1 Headers:
                      </h5>
                      <ul className="list-disc pl-5 space-y-1">
                        {onPageSeoData.headers.h1.content.map((item, index) => (
                          <li key={index} className="text-sm text-gray-600">
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                {/* H2 Content */}
                {onPageSeoData.headers.other_headers?.h2?.content &&
                  onPageSeoData.headers.other_headers.h2.content.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-700 mb-2">
                        H2 Headers:
                      </h5>
                      <ul className="list-disc pl-5 space-y-1">
                        {onPageSeoData.headers.other_headers.h2.content
                          .slice(0, 5)
                          .map((item, index) => (
                            <li key={index} className="text-sm text-gray-600">
                              {item}
                            </li>
                          ))}
                        {onPageSeoData.headers.other_headers.h2.content.length >
                          5 && (
                          <li className="text-xs text-gray-500">
                            ... and{" "}
                            {onPageSeoData.headers.other_headers.h2.content
                              .length - 5}{" "}
                            more
                          </li>
                        )}
                      </ul>
                    </div>
                  )}

                {/* H3 Content */}
                {onPageSeoData.headers.other_headers?.h3?.content &&
                  onPageSeoData.headers.other_headers.h3.content.length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-700 mb-2">
                        H3 Headers:
                      </h5>
                      <ul className="list-disc pl-5 space-y-1">
                        {onPageSeoData.headers.other_headers.h3.content
                          .slice(0, 3)
                          .map((item, index) => (
                            <li key={index} className="text-sm text-gray-600">
                              {item}
                            </li>
                          ))}
                        {onPageSeoData.headers.other_headers.h3.content.length >
                          3 && (
                          <li className="text-xs text-gray-500">
                            ... and{" "}
                            {onPageSeoData.headers.other_headers.h3.content
                              .length - 3}{" "}
                            more
                          </li>
                        )}
                      </ul>
                    </div>
                  )}
              </div>

              <div className="mt-4 pt-3 border-t space-y-2">
                <DataRow
                  label="Structure Score"
                  value={`${onPageSeoData.headers.score || 0}/5`}
                />
                <DataRow
                  label="Hierarchy Status"
                  value={
                    onPageSeoData.headers.pass
                      ? "✓ Good"
                      : "⚠ Needs Improvement"
                  }
                />
              </div>

              {/* Headers Recommendation */}
              {onPageSeoData.headers.hierarchy_recommendation && (
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <RecommendationCard
                    recommendation={
                      onPageSeoData.headers.hierarchy_recommendation
                    }
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Keyword Consistency Analysis */}
      {onPageSeoData.keyword_consistency && (
        <div className="mt-6">
          <h3 className="font-bold text-gray-800 mb-4 pb-2 border-b">
            Keyword Consistency Audit
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-1.5 h-6 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                <h4 className="font-bold text-gray-800 text-base">
                  Detailed Keywords Analysis
                </h4>
              </div>
              {onPageSeoData.keyword_consistency.keywords &&
              onPageSeoData.keyword_consistency.keywords.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="p-2 text-left font-medium text-gray-700">
                          Keyword
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Title
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Meta Desc
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Headings
                        </th>
                        <th className="p-2 text-center font-medium text-gray-700">
                          Frequency
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {onPageSeoData.keyword_consistency.keywords
                        .slice(0, 10)
                        .map((keyword, index) => (
                          <tr key={index} className="border-b border-gray-100">
                            <td className="p-2 font-medium text-gray-800">
                              {keyword.keyword}
                            </td>
                            <td className="p-2 text-center">
                              {keyword.in_title ? (
                                <span className="text-green-600 font-bold">
                                  ✓
                                </span>
                              ) : (
                                <span className="text-red-600 font-bold">
                                  ✗
                                </span>
                              )}
                            </td>
                            <td className="p-2 text-center">
                              {keyword.in_meta_description ? (
                                <span className="text-green-600 font-bold">
                                  ✓
                                </span>
                              ) : (
                                <span className="text-red-600 font-bold">
                                  ✗
                                </span>
                              )}
                            </td>
                            <td className="p-2 text-center">
                              {keyword.in_headings ? (
                                <span className="text-green-600 font-bold">
                                  ✓
                                </span>
                              ) : (
                                <span className="text-red-600 font-bold">
                                  ✗
                                </span>
                              )}
                            </td>
                            <td className="p-2 text-center font-medium">
                              {keyword.frequency}x
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                  {onPageSeoData.keyword_consistency.keywords.length > 10 && (
                    <p className="text-xs text-gray-500 mt-2 text-center">
                      Showing top 10 of{" "}
                      {onPageSeoData.keyword_consistency.keywords.length}{" "}
                      keywords
                    </p>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 text-sm">No keywords found</p>
              )}
            </div>

            <div className="p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-1.5 h-6 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
                <h4 className="font-bold text-gray-800 text-base">
                  Keyword Audit
                </h4>
              </div>
              <div className="space-y-1 bg-gray-50/50 rounded-lg p-4">
                <DataRow
                  label="Total Keywords"
                  value={
                    onPageSeoData.keyword_consistency.keywords?.length || 0
                  }
                />
                <DataRow
                  label="Consistency Score"
                  value={`${onPageSeoData.keyword_consistency.score || 0}/5`}
                />
                <DataRow
                  label="Status"
                  value={
                    onPageSeoData.keyword_consistency.pass
                      ? "✓ Good"
                      : "⚠ Needs Improvement"
                  }
                />
              </div>
              {onPageSeoData.keyword_consistency.description && (
                <div className="mt-3 pt-2 border-t border-gray-100">
                  <p className="text-sm text-gray-600">
                    {onPageSeoData.keyword_consistency.description}
                  </p>
                </div>
              )}

              {/* Keyword Consistency Recommendation */}
              {onPageSeoData.keyword_consistency.recommendation && (
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <RecommendationCard
                    recommendation={
                      onPageSeoData.keyword_consistency.recommendation
                    }
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Comprehensive On-Page SEO Recommendations */}
      <div className="mt-8 p-6 border border-gray-200/80 rounded-xl bg-white shadow-sm pdf-section-box print-section">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-2 h-8 bg-gradient-to-b from-primary to-primary/70 rounded-full"></div>
          <h3 className="text-xl font-bold text-gray-800 print:text-lg">
            All On-Page SEO Recommendations
          </h3>
        </div>
        <div className="grid grid-cols-1 gap-4 recommendations-grid pdf-recommendation-grid">
          {/* Title Tag Recommendations */}
          {onPageSeoData.title_tag?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.title_tag.recommendation}
            />
          )}

          {/* Meta Description Recommendations */}
          {onPageSeoData.meta_description?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.meta_description.recommendation}
            />
          )}

          {/* SERP Preview Recommendations */}
          {onPageSeoData.serp_preview?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.serp_preview.recommendation}
            />
          )}

          {/* Content Amount Recommendations */}
          {onPageSeoData.content_amount?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.content_amount.recommendation}
            />
          )}

          {/* Headers Recommendations */}
          {onPageSeoData.headers?.hierarchy_recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.headers.hierarchy_recommendation}
            />
          )}

          {/* H1 Specific Recommendations */}
          {onPageSeoData.headers?.h1?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.headers.h1.recommendation}
            />
          )}

          {/* Keyword Consistency Recommendations */}
          {onPageSeoData.keyword_consistency?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.keyword_consistency.recommendation}
            />
          )}

          {/* Schema Markup Recommendations */}
          {onPageSeoData.schema_markup?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.schema_markup.recommendation}
            />
          )}

          {/* Language Recommendations */}
          {onPageSeoData.language?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.language.recommendation}
            />
          )}

          {/* Images Recommendations */}
          {onPageSeoData.images?.recommendation && (
            <RecommendationCard
              recommendation={onPageSeoData.images.recommendation}
            />
          )}

          {/* If no recommendations found */}
          {!onPageSeoData.title_tag?.recommendation &&
            !onPageSeoData.meta_description?.recommendation &&
            !onPageSeoData.serp_preview?.recommendation &&
            !onPageSeoData.content_amount?.recommendation &&
            !onPageSeoData.headers?.hierarchy_recommendation &&
            !onPageSeoData.headers?.h1?.recommendation &&
            !onPageSeoData.keyword_consistency?.recommendation &&
            !onPageSeoData.schema_markup?.recommendation &&
            !onPageSeoData.language?.recommendation &&
            !onPageSeoData.images?.recommendation && (
              <RecommendationCard
                recommendation={{
                  text: "Your on-page SEO appears to be well optimized. Continue monitoring and updating content regularly to maintain good search engine visibility.",
                  priority: "Low",
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};
