import React from "react";

type Variant = "primary" | "success";

type Props = {
  message: string;
  variant?: Variant;
  className?: string;
};

export default function MessageCard({
  message,
  variant = "primary",
  className,
}: Props) {
  const renderVariant = () => {
    switch (variant) {
      case "primary":
        return "bg-primary/10 text-primary";
      case "success":
        return "bg-primary-green/10 text-primary-green";

      default:
        break;
    }
  };
  return (
    <div className={`${renderVariant()} ${className} text-sm p-4 rounded-lg`}>
      {message}
    </div>
  );
}
