"use client";
import { PinIcon } from "@/ui/icons/general";
import { EmailIcon } from "@/ui/icons/socialMedia";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import ContactForm from "./ContactForm";

type Emails = {
  general: string;
  analyst: string;
};

export default function ContactDetails() {
  const [emails, setEmails] = useState<Emails>({ general: "", analyst: "" });

  const obfuscateEmail = (user: string, domain: string): string => {
    return `${user}@${domain}`;
  };

  useEffect(() => {
    setEmails({
      general: obfuscateEmail("hello", "seoanalyser.com.au"),
      analyst: obfuscateEmail("partners", "seoanalyser.com.au"),
    });
  }, []);

  return (
    <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8 ">
      {/* Contact Form Section - Left Column */}
      <div className="bg-white rounded-2xl p-8 lg:p-12 shadow-lg">
        <div className="mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold text-secondary mb-4">
            Let's Talk!
          </h2>
          <p className="text-gray-600 text-sm mb-6">
          We love hearing from fellow Aussie business owners, marketers, and partners.
              Whether you've got a question, need help getting started, or want to explore
              how SEOAnalyser can support your goals, we're here for you.
          </p>
        </div>

        {/* Contact Form */}
        <ContactForm />
      </div>

      {/* Image and Contact Info Section - Right Column */}
      <div className="space-y-6">
        {/* Contact Image */}
        <div className="w-full h-[300px] lg:h-[400px] relative rounded-2xl overflow-hidden shadow-lg">
          <Image
            src={"/images/contactUs.png"}
            alt="Contact Us Page Picture"
            width={1000}
            height={588}
            className="w-full h-full object-cover object-center"
          />
        </div>

        {/* Contact Information */}
        <div className="bg-white rounded-2xl p-6 lg:p-8 lg:pb-11  shadow-lg">
          <h3 className="text-xl font-bold text-secondary mb-6">Contact Information</h3>

          {/* Let's Connect Section */}
          {/* <div className="rounded-lg p-5 mb-4  bg-secondary/5">
            <h4 className="text-lg lg:text-xl text-primary font-bold  mb-2 flex items-center gap-2">
              
              Let's Connect
            </h4>
            <p className="text-gray-800 font-bold lg:text-sm text-xs leading-relaxed font-medium">
              We love hearing from fellow Aussie business owners, marketers, and partners.
              Whether you've got a question, need help getting started, or want to explore
              how SEOAnalyser can support your goals, we're here for you.
            </p>
          </div> */}

          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-3 text-secondary mb-3">
                <EmailIcon className="w-5 h-5 text-primary" />
                <div className="text-lg font-semibold">Email</div>
              </div>
              <div className="space-y-2 ml-8">
                <div className="text-sm text-gray-600">
                  General questions:{" "}
                  <Link
                    href={`mailto:${emails.general}`}
                    className="text-primary font-medium hover:underline"
                  >
                    {emails.general}
                  </Link>
                </div>
                <div className="text-sm text-gray-600">
                  Partnerships:{" "}
                  <Link
                    href={`mailto:${emails.analyst}`}
                    className="text-primary font-medium hover:underline"
                  >
                    {emails.analyst}
                  </Link>
                </div>
              </div>
            </div>

            <div>
              <div className="flex items-center gap-3 text-secondary mb-3">
                <PinIcon className="w-5 h-5 text-primary" />
                <div className="text-lg font-semibold">Headquarters</div>
              </div>
              <div className="text-sm text-primary  font-bold ml-8">
                Parramatta, Sydney NSW, Australia
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}