import Image from "next/image";
import Cards from "./_components/Cards";
import OurValues from "./_components/OurValues";

import Questions from "../pricing/_components/Questions";
import { SharedList, SharedListItem } from "@/components/shared/sharedList";
import Link from "next/link";
import FreeSEOToolbox from "@/components/landing/FreeSEOToolbox";
export default function AboutUs() {
  return (
    <div className="w-full mt-8 lg:mt-[84px] container">
      <div className="flex flex-col gap-6 lg:gap-16 lg:flex-row lg:items-center w-full">
        <div className="lg:flex-1 w-full h-[216px] lg:h-[457px] rounded-lg lg:rounded-2xl overflow-hidden relative">
          <Image
            src={"/images/aboutUsPagePic.png"}
            alt="About Us Page Picture"
            width={667}
            height={1000}
            className="w-full h-full object-cover object-[center_65%] lg:object-[center_57%]"
          />
        </div>
        <div className="lg:flex-1 lg:py-8">
          <span className="text-sm font-bold text-primary">About Us</span>
          <div className="text-2xl lg:text-[32px] font-black mt-2 lg:mt-4">
            <div className="text-secondary">Who we are and</div>
            <div className="text-primary">what we do</div>
          </div>
          <p className="text-secondary text-sm lg:text-base my-4 lg:mb-6 mb-4">
            At SEOAnalyser, we help businesses grow smarter in the digital
            space. Whether you're a local café in Melbourne, an e-commerce store
            in Sydney, or a growing agency in Brisbane, we empower you to
            harness the full potential of <strong>Google Australia</strong>, powered by
            cutting-edge AI, and built for local success.
          </p>
           
            <p className="font-black text-secondary text-sm lg:text-base mb-2">Our approach is simple, but powerful:</p>
              
          <SharedList>
            <SharedListItem><b>Local Focus :</b> We help you dominate local search results, ensuring your business gets seen by the right audience at the right time.</SharedListItem>
            <SharedListItem><b>Smarter Insights :</b> Leverage AI-driven analytics to uncover opportunities and stay ahead of the competition.</SharedListItem>
            <SharedListItem><b>Proven Results :</b> From higher rankings to increased traffic and conversions, we deliver measurable outcomes that fuel your growth.</SharedListItem>
          </SharedList>
          <button className="w- lg:w-auto btn btn--primary"><Link href="/contactUs">Let's Start
          </Link></button> <p>  and take your visibility to the next level</p>
        </div>
      </div>
      <Cards />
      <OurValues />
     
        <FreeSEOToolbox
                title=" Born in Sydney,
          Built for You"
                description="   Founded in Sydney, SEOAnalyser was built with one clear goal: to make
          local SEO smarter, faster, and more accessible for Australian
          businesses. Over time, we’ve grown into a trusted SEO platform that
          empowers companies across Australia, from small businesses to national
          brands. Try the platform to own your digital presence with clarity, confidence, and real results."
                button="Start your free audit"
                link_address="/"
              />
      <div id="FAQ" className="mt-8 lg:mt-[84px]">
        <Questions />
      </div>
    </div>
  );
}
