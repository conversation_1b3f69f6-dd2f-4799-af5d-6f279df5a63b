// import States from "@/components/landing/States";
import Title from "@/components/landing/Title";
import { Crwon2Icon, WandIcon } from "@/ui/icons/general";
import LikeIcon from "@/ui/icons/general/LikeIcon";
import { ReactNode } from "react";

export default function OurValues() {
  return (
    <div className="w-full mt-8 lg:mt-[84px]">
      <Title title="Our values" />
      <div className="flex flex-col gap-4 my-4 lg:my-6 lg:grid grid-cols-3 lg:gap-6">
        <OurValuesItem
          icon={<Crwon2Icon />}
          title="Empower locals"
          description="We build tools that give every business, from tradies to tech startups, the power to be found online."
        />
        <OurValuesItem
          icon={<WandIcon />}
          title="Fuel Real Growth"
          description="We’re results-driven, focusing on what truly matters: generating more leads, boosting visibility, and delivering measurable success for our clients."
        />
        <OurValuesItem
          icon={<LikeIcon/>}
          title="Bring Clarity to Complexity"
          description="We turn SEO jargon into simple, easy-to-understand actions that deliver real outcomes."
        />
      </div>
      {/* <States className="!mt-0 !px-0"/> */}
    </div>
  );
}

type OurValuesItemProps = {
  title: string;
  description: string;
  icon: ReactNode;
};

function OurValuesItem({ title, description, icon }: OurValuesItemProps) {
  return (
    <div className="bg-white rounded-lg p-6 flex flex-col items-start gap-4">
      <div className="w-11 h-11 rounded-full bg-primary/10 flex items-center justify-center">
        {icon}
      </div>
      <div className="text-lg text-secondary font-bold">{title}</div>
      <div className="text-sm lg:text-base text-secondary">{description}</div>
    </div>
  );
}
