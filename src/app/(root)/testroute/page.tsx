import DinoLoader from "@/components/loading/Loading";
export default function TestRoute() {
  return (
    <div className="w-full fixed top-0 left-0 z-50 bg-white/20 backdrop-blur-md flex-col gap-6 md:gap-8 h-full flex items-center justify-center ">
      <div className="w-full max-w-5xl">
        <DinoLoader />
      </div>
      <h2 className="text-xl opacity-80  md:text-3xl lg:text-3xl font-black analyzing-text text-center tracking-[0.3em]">
        ANALYZING<span className="dots"></span>
      </h2>
      {/* <svg
            width="113"
            height="32"
            viewBox="0 0 113 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="w-24 sm:w-32 md:w-40 mt-2 opacity-80"
          >
            <path
              d="M2.45215 5.27679C2.48438 5.20355 2.53857 5.14203 2.61475 5.09222C2.69385 5.03949 2.77441 5.01312 2.85645 5.01312C2.93555 5.01312 3.01465 5.03949 3.09375 5.09222C3.17578 5.14203 3.23145 5.20355 3.26074 5.27679L5.67773 10.8842C5.7041 10.9428 5.71729 11.0029 5.71729 11.0644C5.71729 11.1845 5.67334 11.2885 5.58545 11.3764C5.50049 11.4614 5.39795 11.5038 5.27783 11.5038C5.1958 11.5038 5.11523 11.4775 5.03613 11.4247C4.95703 11.372 4.90137 11.309 4.86914 11.2358L4.11768 9.49115L1.61279 9.45599L0.84375 11.2402C0.814453 11.3134 0.760254 11.3764 0.681152 11.4291C0.602051 11.4848 0.521484 11.5126 0.439453 11.5126C0.319336 11.5126 0.215332 11.4702 0.127441 11.3852C0.0424805 11.2973 0 11.1933 0 11.0732C0 11.0117 0.0131836 10.9516 0.0395508 10.893L2.45215 5.27679ZM2.85645 6.56439L1.99072 8.58148L3.73535 8.60345L2.85645 6.56439Z"
              fill="#999999"
            />
            <path
              d="M13.039 5.53607C13.039 5.41302 13.0829 5.31195 13.1708 5.23285C13.2616 5.15082 13.3686 5.1098 13.4916 5.1098C13.6293 5.1098 13.7421 5.16547 13.83 5.27679L17.1479 9.78119V5.54926C17.1479 5.43207 17.1903 5.33246 17.2753 5.25043C17.3632 5.16547 17.4672 5.12299 17.5873 5.12299C17.7074 5.12299 17.81 5.16547 17.8949 5.25043C17.9828 5.33246 18.0268 5.43207 18.0268 5.54926L18.0224 11.06C18.0224 11.183 17.977 11.2856 17.8862 11.3676C17.7983 11.4467 17.6928 11.4863 17.5697 11.4863C17.432 11.4863 17.3193 11.4277 17.2314 11.3105L13.9179 6.81049V11.0556C13.9179 11.1728 13.8739 11.2739 13.7861 11.3588C13.7011 11.4409 13.5986 11.4819 13.4784 11.4819C13.3583 11.4819 13.2543 11.4409 13.1664 11.3588C13.0815 11.2739 13.039 11.1728 13.039 11.0556V5.53607Z"
              fill="#999999"
            />
            <path
              d="M27.8094 5.27679C27.8416 5.20355 27.8958 5.14203 27.972 5.09222C28.0511 5.03949 28.1317 5.01312 28.2137 5.01312C28.2928 5.01312 28.3719 5.03949 28.451 5.09222C28.533 5.14203 28.5887 5.20355 28.618 5.27679L31.035 10.8842C31.0614 10.9428 31.0745 11.0029 31.0745 11.0644C31.0745 11.1845 31.0306 11.2885 30.9427 11.3764C30.8577 11.4614 30.7552 11.5038 30.6351 11.5038C30.5531 11.5038 30.4725 11.4775 30.3934 11.4247C30.3143 11.372 30.2586 11.309 30.2264 11.2358L29.4749 9.49115L26.9701 9.45599L26.201 11.2402C26.1717 11.3134 26.1175 11.3764 26.0384 11.4291C25.9593 11.4848 25.8787 11.5126 25.7967 11.5126C25.6766 11.5126 25.5726 11.4702 25.4847 11.3852C25.3997 11.2973 25.3573 11.1933 25.3573 11.0732C25.3573 11.0117 25.3704 10.9516 25.3968 10.893L27.8094 5.27679ZM28.2137 6.56439L27.348 8.58148L29.0926 8.60345L28.2137 6.56439Z"
              fill="#999999"
            />
            <path
              d="M38.9368 11.4423C38.8167 11.4423 38.7127 11.3998 38.6248 11.3149C38.5398 11.227 38.4973 11.1245 38.4973 11.0073L38.4841 5.4306V5.42621C38.4841 5.30902 38.5266 5.20941 38.6116 5.12738C38.6995 5.04242 38.8035 4.99994 38.9236 4.99994C39.0437 4.99994 39.1462 5.04242 39.2312 5.12738C39.3191 5.20941 39.363 5.30902 39.363 5.42621L39.3762 10.5634L42.7029 10.5546H42.7073C42.8274 10.5546 42.9299 10.5986 43.0149 10.6865C43.1028 10.7714 43.1467 10.874 43.1467 10.9941C43.1467 11.1142 43.1028 11.2182 43.0149 11.3061C42.9299 11.3911 42.8274 11.4335 42.7073 11.4335H42.7029L38.9368 11.4423Z"
              fill="#999999"
            />
            <path
              d="M50.1476 5.41742C50.1476 5.2973 50.196 5.19916 50.2926 5.12299C50.3893 5.04681 50.4963 5.00873 50.6134 5.00873C50.7658 5.00873 50.883 5.07904 50.965 5.21967L52.5339 7.9223L54.0676 5.24603C54.0968 5.19916 54.1437 5.15375 54.2082 5.1098C54.2756 5.06586 54.3444 5.04388 54.4147 5.04388C54.4821 5.04388 54.5422 5.05121 54.5949 5.06586C54.6506 5.07758 54.7077 5.1142 54.7663 5.17572C54.8483 5.26361 54.8893 5.3559 54.8893 5.45258C54.8893 5.54633 54.8674 5.62982 54.8234 5.70306L52.9557 8.87592L52.925 11.0644C52.925 11.1816 52.881 11.2827 52.7931 11.3676C52.7082 11.4496 52.6056 11.4907 52.4855 11.4907C52.3654 11.4907 52.2614 11.4496 52.1735 11.3676C52.0885 11.2827 52.0461 11.1816 52.0461 11.0644V11.0556L52.0768 8.81879L50.2179 5.66791C50.1711 5.60052 50.1476 5.51703 50.1476 5.41742Z"
              fill="#999999"
            />
            <path
              d="M86 5.42621C86 5.30316 86.0439 5.20209 86.1318 5.12299C86.2227 5.04095 86.3296 4.99994 86.4526 4.99994C86.5903 4.99994 86.7031 5.0556 86.791 5.16693L90.1089 9.67133V5.43939C90.1089 5.3222 90.1514 5.2226 90.2363 5.14056C90.3242 5.0556 90.4282 5.01312 90.5483 5.01312C90.6685 5.01312 90.771 5.0556 90.856 5.14056C90.9438 5.2226 90.9878 5.3222 90.9878 5.43939L90.9834 10.9501C90.9834 11.0732 90.938 11.1757 90.8472 11.2578C90.7593 11.3369 90.6538 11.3764 90.5308 11.3764C90.3931 11.3764 90.2803 11.3178 90.1924 11.2006L86.8789 6.70062V10.9457C86.8789 11.0629 86.835 11.164 86.7471 11.249C86.6621 11.331 86.5596 11.372 86.4395 11.372C86.3193 11.372 86.2153 11.331 86.1274 11.249C86.0425 11.164 86 11.0629 86 10.9457V5.42621Z"
              fill="#999999"
            />
            <path
              d="M76.0001 10.0634V5.92621C76.0001 5.80902 76.0477 5.70941 76.143 5.62738C76.2415 5.54242 76.358 5.49994 76.4927 5.49994C76.6273 5.49994 76.7422 5.54242 76.8375 5.62738C76.936 5.70941 76.9852 5.80902 76.9852 5.92621L77 10.0634C77 10.0634 77.0057 10.4999 76.4927 10.4999C75.9796 10.4999 76.0001 10.0634 76.0001 10.0634Z"
              fill="#999999"
            />
            <path
              d="M75.2619 10.9998H77.7442C77.8146 10.9998 77.8743 10.9522 77.9235 10.857C77.9745 10.7585 78 10.6419 78 10.5073C78 10.3726 77.9745 10.2577 77.9235 10.1625C77.8743 10.064 77.8146 10.0147 77.7442 10.0147L75.2619 9.99994C75.2619 9.99994 75 9.99423 75 10.5073C75 11.0203 75.2619 10.9998 75.2619 10.9998Z"
              fill="#999999"
            />
            <path
              d="M75.2619 5.99983L77.7442 5.99983C77.8146 5.99983 77.8743 5.95221 77.9235 5.85699C77.9745 5.75848 78 5.6419 78 5.50727C78 5.37264 77.9745 5.25771 77.9235 5.16248C77.8743 5.06397 77.8146 5.01472 77.7442 5.01472L75.2619 4.99994C75.2619 4.99994 75 4.99423 75 5.50727C75 6.02031 75.2619 5.99983 75.2619 5.99983Z"
              fill="#999999"
            />
            <path
              d="M103.438 8.94628C103.5 8.65618 103.5 8.65618 103.5 8.65618C103.5 8.5468 103.479 8.45937 103.438 8.382C103.398 8.30196 103.349 8.26194 103.292 8.26194L100.463 8.24993C100.463 8.24993 100.25 8.2453 100.25 8.66214C100.25 9.07898 100.463 9.06234 100.463 9.06234H102.379C102.284 9.31107 102.145 9.54323 101.967 9.74715C101.606 10.1621 101.106 10.432 100.561 10.5071C100.015 10.5821 99.4613 10.4572 99.001 10.1555C98.5406 9.85373 98.205 9.39552 98.0563 8.86554C97.9076 8.33556 97.9558 7.76965 98.1919 7.27244C98.4281 6.77522 98.8363 6.3803 99.341 6.16069C99.8458 5.94109 100.413 5.91163 100.938 6.07778C101.307 6.19467 101.638 6.4028 101.901 6.67963C102.086 6.87402 102.382 6.95137 102.611 6.81178C102.84 6.67219 102.915 6.37033 102.743 6.16455C102.348 5.69236 101.824 5.33935 101.231 5.15152C100.482 4.91453 99.6734 4.95654 98.9534 5.26979C98.2334 5.58305 97.6512 6.14636 97.3143 6.85561C96.9774 7.56485 96.9087 8.37207 97.1209 9.12804C97.333 9.88402 97.8117 10.5376 98.4684 10.9681C99.125 11.3985 99.9154 11.5766 100.693 11.4696C101.471 11.3625 102.184 10.9774 102.7 10.3856C103.034 10.0019 103.271 9.54805 103.397 9.06234L103.438 8.94628Z"
              fill="#999999"
            />
            <circle cx="106.5" cy="10.4999" r="0.5" fill="#999999" />
            <circle cx="109.5" cy="10.4999" r="0.5" fill="#999999" />
            <circle cx="112.5" cy="10.4999" r="0.5" fill="#999999" />
            <path
              d="M62.73 9.88L66.46 4.92H66.82C66.9867 5.05333 67.07 5.22333 67.07 5.43C67.07 5.54333 67.0467 5.64667 67 5.74C66.9533 5.83333 66.89 5.93333 66.81 6.04L63.1 11H62.7C62.6333 10.94 62.5767 10.8667 62.53 10.78C62.4833 10.6933 62.46 10.5967 62.46 10.49C62.46 10.3767 62.4833 10.2733 62.53 10.18C62.5833 10.08 62.65 9.98 62.73 9.88ZM62.91 11L63.15 10.14H67.02C67.0533 10.18 67.08 10.2367 67.1 10.31C67.1267 10.3833 67.14 10.4633 67.14 10.55C67.14 10.69 67.1033 10.8 67.03 10.88C66.9633 10.96 66.8733 11 66.76 11H62.91ZM66.6 4.92L66.41 5.78H62.66C62.6333 5.74 62.6067 5.68333 62.58 5.61C62.5533 5.53667 62.54 5.45667 62.54 5.37C62.54 5.22333 62.5733 5.11333 62.64 5.04C62.7133 4.96 62.81 4.92 62.93 4.92H66.6Z"
              fill="#999999"
            />
          </svg> */}
    </div>
  );
}
