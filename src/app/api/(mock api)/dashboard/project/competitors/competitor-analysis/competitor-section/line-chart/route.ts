import { NextResponse } from "next/server";
import type { LineChartDataResponse } from "@/app/(dashboard)/project/[projectId]/competitors/market-analysis/(market-analysis)/MarketAnalysis.types";
export const LINE_CHART_DATA: LineChartDataResponse = {
  cardsData: {
    digikala_com: { amount: 12000, growth: "+21.2%" },
    myescape_ir: { amount: 12000, growth: "0" },
    website1_ir: { amount: 12000, growth: "+21.2%" },
    website2_ir: { amount: 12000, growth: "+16%" },
    website3_com: { amount: 12000, growth: "-5%" },
  },
  colors: [
    { name: "digikala_com", color: "#ff00ff" },
    { name: "myescape_ir", color: "#31D37A" },
    { name: "website1_ir", color: "#00BBEC" },
    { name: "website2_ir", color: "#3C0866" },
    { name: "website3_com", color: "#F57D37" },
  ],
  selectedLines: [
    "digikala_com",
    "myescape_ir",
    "website1_ir",
    "website2_ir",
    "website3_com",
  ],
  lineChartData: [
    {
      name: "May",
      digikala_com: 3000,
      myescape_ir: 1398,
      website1_ir: 1400,
      website2_ir: 3110,
      website3_com: 3510,
    },
    {
      name: "Apr",
      digikala_com: 4000,
      myescape_ir: 2400,
      website1_ir: 2400,
      website2_ir: 3100,
      website3_com: 2110,
    },
    {
      name: "Jun",
      digikala_com: 2000,
      myescape_ir: 9800,
      website1_ir: 2290,
      website2_ir: 2190,
      website3_com: 4510,
    },
    {
      name: "Jul",
      digikala_com: 3000,
      myescape_ir: 1398,
      website1_ir: 2210,
      website2_ir: 1110,
      website3_com: 1410,
    },
    {
      name: "Aug",
      digikala_com: 3000,
      myescape_ir: 1398,
      website1_ir: 2210,
      website2_ir: 3110,
      website3_com: 3510,
    },
    {
      name: "Sep",
      digikala_com: 4000,
      myescape_ir: 2400,
      website1_ir: 2400,
      website2_ir: 1100,
      website3_com: 1210,
    },
  ],
};

const wait = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export async function GET() {
  await wait(1000);
  return NextResponse.json(LINE_CHART_DATA);
}
