import { NextResponse } from "next/server";
import { BubbleChartDataResponse } from "@/app/(dashboard)/project/[projectId]/competitors/competitor-analysis/(competitor-analysis)/seo-competitor-landscape/_components/BubbleChartComponent.types";
const DOUGHNUT_CHART_DATA = {
  total: 23000,
  growth: "+2%",
  data: [
    {
      name: "digikala_com",
      value: 300,
      color: "#ff00ff",
      shareOfVoice: 12000,
      keywordCount: 15,
      shareOfVoiceGrowth: "-1%",
      keywordCountGrowth: "-1%",
    },
    {
      name: "myescape_ir",
      value: 400,
      color: "#31D37A",
      shareOfVoice: 12000,
      keywordCount: 15,
      shareOfVoiceGrowth: "-1%",
      keywordCountGrowth: "-1%",
    },
    {
      name: "website1_ir",
      value: 400,
      color: "#00BBEC",
      shareOfVoice: 12000,
      keywordCount: 15,
      shareOfVoiceGrowth: "-1%",
      keywordCountGrowth: "-1%",
    },
    {
      name: "website2_ir",
      value: 800,
      color: "#3C0866",
      shareOfVoice: 12000,
      keywordCount: 15,
      shareOfVoiceGrowth: "-1%",
      keywordCountGrowth: "-1%",
    },
    {
      name: "website3_com",
      value: 400,
      color: "#F57D37",
      shareOfVoice: 12000,
      keywordCount: 15,
      shareOfVoiceGrowth: "-1%",
      keywordCountGrowth: "-1%",
    },
  ],
};

const wait = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export async function GET() {
  await wait(1000);
  return NextResponse.json(DOUGHNUT_CHART_DATA);
}
