import { NextResponse } from "next/server";
import type { BarChartDataResponse } from "@/app/(dashboard)/project/[projectId]/competitors/competitor-analysis/(competitor-analysis)/competitors/_components/BarChartComponent.types";

const BAR_CHART_DATA: BarChartDataResponse = {
  cardsData: {
    digikala_com: { amount: 12000, growth: "+21.2%" },
    myescape_ir: { amount: 12000, growth: "0" },
    website1_ir: { amount: 12000, growth: "+21.2%" },
    website2_ir: { amount: 12000, growth: "+16%" },
    website3_com: { amount: 12000, growth: "-5%" },
  },
  colors: [
    { name: "digikala_com", color: "#ff00ff" },
    { name: "myescape_ir", color: "#31D37A" },
    { name: "website1_ir", color: "#00BBEC" },
    { name: "website2_ir", color: "#3C0866" },
    { name: "website3_com", color: "#F57D37" },
  ],
  barChartData: [
    {
      name: "digikala.com",
      bar: 70,
    },
    {
      name: "myescape.ir",
      bar: 85,
    },
    {
      name: "website1.ir",
      bar: 30,
    },
    {
      name: "website2.ir",
      bar: 70,
    },
    {
      name: "website3.com",
      bar: 70,
    },
  ],
};
const wait = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export async function GET() {
  await wait(1000);
  return NextResponse.json(BAR_CHART_DATA);
}
