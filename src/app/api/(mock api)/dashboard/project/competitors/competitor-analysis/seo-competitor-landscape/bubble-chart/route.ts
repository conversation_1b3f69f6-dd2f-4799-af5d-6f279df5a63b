import { NextResponse } from "next/server";
import { BubbleChartDataResponse } from "@/app/(dashboard)/project/[projectId]/competitors/competitor-analysis/(competitor-analysis)/seo-competitor-landscape/_components/BubbleChartComponent.types";
const BUBBLE_CHART_DATA: BubbleChartDataResponse[] = [
  {
    x: 2,
    y: 75,
    z: 400,
    color: "#FDB600",
    title: "digikala.com",
    keywordRanked: 81,
    ABGPosition: 2,
    keywordCountInTop100: 45,
    growth: "+2%",
  },
  {
    x: 6,
    y: 15,
    z: 10,
    color: "#30BA73",
    title: "digikala.com",
    keywordRanked: 81,
    ABGPosition: 2,
    keywordCountInTop100: 45,
    growth: "+2%",
  },
  {
    x: 8,
    y: 20,
    z: 100,
    color: "#FD7C0C",
    title: "digikala.com",
    keywordRanked: 81,
    ABGPosition: 2,
    keywordCountInTop100: 45,
    growth: "+2%",
  },
  {
    x: 15,
    y: 40,
    z: 300,
    color: "#914AC4",
    title: "digikala.com",
    keywordRanked: 81,
    ABGPosition: 2,
    keywordCountInTop100: 45,
    growth: "+2%",
  },
];

const wait = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export async function GET() {
  await wait(1000);
  return NextResponse.json(BUBBLE_CHART_DATA);
}
