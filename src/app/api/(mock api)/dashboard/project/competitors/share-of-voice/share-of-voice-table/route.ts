import { AccordionTableData } from "@/app/(dashboard)/project/[projectId]/competitors/_components/AccordionTable";
import { NextResponse } from "next/server";

const SHARE_OF_VOICE_TABLE: AccordionTableData = {
  tableHeadings: [
    "DOMAIN",
    "SHARE OF VOICE",
    "KEYWORDS IN THE TOP 20",
    "URL IN THE TOP 20",
    "TOTAL TRAFFIC FORECAST",
    "TRAFFIC FORECAST",
  ],
  tableBody: [
    {
      data: [
        { value: "digikala.com" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      accordionData: [
        { value: "digikala.com/example" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
    },
    {
      data: [
        { value: "amazon.com" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      accordionData: [],
    },
    {
      data: [
        { value: "amazon.com" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
      accordionData: [
        { value: "amazon.com/example" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
        { value: "10.3K", growth: "+21.2" },
      ],
    },
  ],
};

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
export async function GET() {
  await wait(1000);
  return NextResponse.json(SHARE_OF_VOICE_TABLE);
}
