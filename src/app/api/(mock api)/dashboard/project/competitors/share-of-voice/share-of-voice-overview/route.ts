import { ChartResponse } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/overview/(overview)/audience-overview/AudienceOverview.types";
import { NextResponse } from "next/server";
const SHARE_OF_VOICE_CHART_DATA: ChartResponse[] = [
  {
    id: 1,
    title: "Total Traffic Forecast",
    bigNumber: "12.3K",
    smallNumber: "+16%",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "Mar", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 2,
    title: "Traffic Forecast",
    bigNumber: "12.3K",
    smallNumber: "+16%",
    data: [
      { name: "<PERSON>", value: 400 },
      { name: "Feb", value: 300 },
      { name: "<PERSON>", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 3,
    title: "Share Of Voice",
    bigNumber: "10.3K",
    smallNumber: "+16%",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "<PERSON>", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
  {
    id: 4,
    title: "Total Search Volume",
    bigNumber: "10.3K",
    smallNumber: "+16%",
    data: [
      { name: "Jan", value: 400 },
      { name: "Feb", value: 300 },
      { name: "Mar", value: 430 },
      { name: "Apr", value: 400 },
      { name: "May", value: 400 },
    ],
  },
];

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
export async function GET() {
  wait(1000);
  return NextResponse.json(SHARE_OF_VOICE_CHART_DATA);
}
