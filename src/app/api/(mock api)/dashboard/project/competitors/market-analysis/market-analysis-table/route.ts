import { TableData } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable.types";
import { NextRequest, NextResponse } from "next/server";

const ALL_HEADINGS = [
  "DOMAIN",
  "VISIBILITY RATING",
  "TRAFFIC FORECAST",
  "KEYWORDS",
  "BACKLINKS",
  "REFERRAL DOMAINS",
];

const ROW_TEMPLATE = [
  { value: "digikala.com" },
  { value: "10.3K", growth: "+21.2" },
  { value: "10.3K", growth: "+21.2" },
  { value: "10.3K", growth: "+21.2" },
  { value: "10.3K", growth: "+21.2" },
  { value: "10.3K", growth: "+21.2" },
];

const wait = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  const selectedColumns = searchParams.get("columns");

  const selectedColumnsArray = selectedColumns
    ? selectedColumns
        .split(",")
        .map((col) => col.trim())
        .filter(Boolean)
    : [];
  const finalHeadings = [...ALL_HEADINGS, ...selectedColumnsArray];

  const tableBody = Array.from({ length: 4 }, (_, index) => {
    const baseRow = ROW_TEMPLATE.map((cell) => {
      if (cell.value === "digikala.com") {
        return { ...cell, value: `digikala.com/${index + 1}` };
      }
      return cell;
    });

    const extraCells = selectedColumnsArray.map((col) => ({
      value: `Extra ${col} ${index + 1}`,
      growth: "+0.0",
    }));

    return [...baseRow, ...extraCells];
  });

  const MARKET_ANALYSIS_TABLE_DATA: TableData = {
    totalPages: 5,
    tableHeadings: finalHeadings,
    tableBody,
  };

  await wait(800);

  return NextResponse.json(MARKET_ANALYSIS_TABLE_DATA);
}
