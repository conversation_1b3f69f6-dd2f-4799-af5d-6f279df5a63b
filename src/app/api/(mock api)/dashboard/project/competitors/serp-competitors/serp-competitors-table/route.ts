import { NextRequest, NextResponse } from "next/server";

const ALL_HEADINGS = ["DOMAIN", "DT", "KEYWORDS", "REFERRAL DOMAINS"];

const ROW_TEMPLATE = [
  { value: "digikala.com", competitor: true },
  { value: "10.3K", growth: "+21.2" },
  { value: "10.3K", growth: "+21.2" },
  { value: "10.3K", growth: "+21.2" },
];

const wait = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const showUrl = searchParams.get("showUrl") === "true";

  const SERP_TABLE_DATA = {
    tableHeadings: [...ALL_HEADINGS],
    tableBody: Array.from({ length: 7 }, (_, index) => {
      const baseRow = ROW_TEMPLATE.map((cell, colIndex) => {
        if (colIndex === 0) {
          let domain = `digikala.com/${index + 1}`;
          if (showUrl) domain = `https://${domain}`;
          return { ...cell, value: domain };
        }
        return cell;
      });
      return baseRow;
    }),
  };

  await wait(1000);

  return NextResponse.json(SERP_TABLE_DATA);
}
