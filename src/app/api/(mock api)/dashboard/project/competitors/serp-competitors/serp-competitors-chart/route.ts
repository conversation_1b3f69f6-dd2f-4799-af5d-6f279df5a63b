import { SerpChartResponse } from "@/app/(dashboard)/project/[projectId]/competitors/serp-competitors/(serp-competitors)/serp-comparison-chart/SerpComparisonChart.types";
import { NextRequest, NextResponse } from "next/server";

const project_colors = ["#914AC4", "#30BA73", "#FDB600", "#FF00C3", "#FD7C0C"];

/* ================================ FAKE DATA =============================== */
const CHART_DATA: SerpChartResponse = {
  colors: {
    "digikala.com/1": project_colors[0],
    "digikala.com/2": project_colors[1],
    "digikala.com/3": project_colors[2],
    "digikala.com/4": project_colors[3],
    "digikala.com/5": project_colors[4],
  },
  chartData: [
    {
      name: "11 Jan",
      "digikala.com/1": 30,
      "digikala.com/2": 20,
      "digikala.com/3": 50,
      "digikala.com/4": 50,
      "digikala.com/5": 50,
    },
    {
      name: "12 Jan",
      "digikala.com/1": 50,
      "digikala.com/2": 40,
      "digikala.com/3": 60,
      "digikala.com/4": 60,
      "digikala.com/5": 60,
    },
    {
      name: "13 Jan",
      "digikala.com/1": 40,
      "digikala.com/2": 60,
      "digikala.com/3": 55,
      "digikala.com/4": 55,
      "digikala.com/5": 55,
    },
    {
      name: "14 Jan",
      "digikala.com/1": 70,
      "digikala.com/2": 30,
      "digikala.com/3": 70,
      "digikala.com/4": 65,
      "digikala.com/5": 65,
    },
    {
      name: "15 Jan",
      "digikala.com/1": 60,
      "digikala.com/2": 50,
      "digikala.com/3": 65,
      "digikala.com/4": 65,
      "digikala.com/5": 65,
    },
  ],
};

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  const selectedTableItems = searchParams.get("selectedTableItems") || "";

  /* ========== PARSE SELECTED DOMAINS INTO AN ARRAY, TRIMMING SPACES ========= */
  const selectedDomains = selectedTableItems
    .split(",")
    .map((d) => d.trim())
    .filter(Boolean);

  /* ============== IF NO DOMAINS SELECTED, RETURN FULL CHARTDATA ============= */
  if (selectedDomains.length === 0) {
    return NextResponse.json(CHART_DATA);
  }

  /* ====== FILTER CHARTDATA TO KEEP ONLY REQUESTED DOMAINS + 'NAME' KEY ====== */
  const filteredChartData = CHART_DATA.chartData.map((entry) => {
    /* ======================== START WITH THE 'NAME' KEY ======================= */
    const filteredEntry: Record<string, any> = { name: entry.name };

    /* =========== ADD ONLY SELECTED DOMAINS IF PRESENT IN THIS ENTRY =========== */
    selectedDomains.forEach((domain) => {
      if (domain in entry) {
        filteredEntry[domain] = entry[domain as keyof typeof entry];
      }
    });

    return filteredEntry;
  });

  /* =========== ALSO FILTER COLORS TO INCLUDE ONLY SELECTED DOMAINS ========== */
  const filteredColors = Object.fromEntries(
    Object.entries(CHART_DATA.colors).filter(([key]) =>
      selectedDomains.includes(key)
    )
  );

  /* ========================= COMPOSE FINAL RESPONSE ========================= */
  const response = {
    colors: filteredColors,
    chartData: filteredChartData,
  };

  return NextResponse.json(response);
}
