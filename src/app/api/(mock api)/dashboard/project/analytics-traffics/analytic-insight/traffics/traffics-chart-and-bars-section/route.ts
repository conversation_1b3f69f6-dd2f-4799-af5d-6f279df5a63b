import { ChartsAndBars } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/analytic-insight/(analytic-insight)/types/ChartAndBars.types";
import { NextResponse } from "next/server";

const CHART_AND_BARS_DATA: ChartsAndBars = {
  cardTabs: [
    { title: "All Users", value: "20.3K", changeValue: "+21.2" },
    { title: "Active Users", value: "10.3K", changeValue: "+21.2" },
    { title: "New Users", value: "10.3K", changeValue: "+21.2" },
    { title: "Returning Users", value: "10.3K", changeValue: "+21.2" },
    { title: "Sessions", value: "10.3K", changeValue: "+21.2" },
    { title: "Engaged Session", value: "10.3K", changeValue: "+21.2" },
    { title: "Views", value: "10.3K", changeValue: "+21.2" },
  ],
  lineChartData: [
    { name: "may 01", clicks: 100, impressions: 1000 },
    { name: "may 02", clicks: 120, impressions: 1100 },
    { name: "may 03", clicks: 130, impressions: 1150 },
    { name: "may 04", clicks: 90, impressions: 980 },
    { name: "may 05", clicks: 150, impressions: 1300 },
  ],
  colors: [
    { name: "clicks", color: "#4F46E5" },
    { name: "impressions", color: "#10B981" },
  ],
  selectedLines: ["clicks", "impressions"],
  cardsData: {
    clicks: { amount: 150, growth: "+5%" },
    impressions: { amount: 1300, growth: "-2%" },
  },
  progressbarData: [
    {
      title: "under 18",
      percentage: 50,
    },
    {
      title: "18-25",
      percentage: 20,
    },
    {
      title: "25-35",
      percentage: 90,
    },
    {
      title: "35-45",
      percentage: 80,
    },
    {
      title: "45-55",
      percentage: 20,
    },
    {
      title: "55-65",
      percentage: 60,
    },
    {
      title: "over 65",
      percentage: 50,
    },
  ],
};

export async function GET() {
  return NextResponse.json(CHART_AND_BARS_DATA);
}
