import { EventsAndConversionsResponse } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/analytic-insight/(analytic-insight)/events-and-conversions/_components/events-and-conversions-bars/EventsAndConversions.types";
import { NextResponse } from "next/server";

const BARS_DATA: EventsAndConversionsResponse = {
  cardTabs: [
    {
      title: "All Users",
      value: "20.3K",
      changeValue: "+21.2",
    },
    {
      title: "Active Users",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      title: "New Users",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      title: "Returning Users",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      title: "Sessions",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      title: "Engaged Session",
      value: "10.3K",
      changeValue: "+21.2",
    },
    {
      title: "Views",
      value: "10.3K",
      changeValue: "+21.2",
    },
  ],
  barsData: {
    maxValue: 30_000,
    bars: [
      {
        label: "event data flow test",
        barData: [
          { value: 4070, color: "bg-primary" },
          { value: 3000, color: "bg-primary-orange" },
        ],
      },
      {
        label: "page URL",
        barData: [
          { value: 2000, color: "bg-primary-orange" },
          { value: 1000, color: "bg-primary" },
        ],
      },
      {
        label: "page URL",
        barData: [
          { value: 4070, color: "bg-primary" },
          { value: 3000, color: "bg-primary-orange" },
        ],
      },
      {
        label: "page URL",
        barData: [
          { value: 2000, color: "bg-primary-orange" },
          { value: 1000, color: "bg-primary" },
        ],
      },
      {
        label: "page URL",
        barData: [
          { value: 2000, color: "bg-primary-orange" },
          { value: 1000, color: "bg-primary" },
        ],
      },
      {
        label: "page URL",
        barData: [
          { value: 2000, color: "bg-primary-orange" },
          { value: 1000, color: "bg-primary" },
        ],
      },
      {
        label: "page URL",
        barData: [
          { value: 2000, color: "bg-primary-orange" },
          { value: 1000, color: "bg-primary" },
        ],
      },
    ],
  },
};

export async function GET(request: Request) {
  return NextResponse.json(BARS_DATA);
}
