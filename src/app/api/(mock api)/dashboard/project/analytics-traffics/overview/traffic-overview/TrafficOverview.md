## Traffic Overview API

**Endpoint:**  
`GET /api/dashboard/project/analytics-traffics/traffic-overview`

---

## Query Parameters

| Name              | Type     | Required | Description                            |
| ----------------- | -------- | -------- | -------------------------------------- |
| `trafficOverview` | `string` | Yes      | The selected metric to filter the data |

---

## Acceptable Values for `trafficOverview`

_(Case-sensitive! `Sessions` ≠ `sessions`)_

- `Total Users`
- `New Users`
- `Sessions`
- `Active Users`
- `Views`
- `Event Count`
- `Conversions`

---

## Response Schema

```ts
{
  maxValue: number,
  bars: [
    {
      label: string,
      barData: [
        { value: number, color: string },
        { value: number, color: string }
      ]
    }
  ]
}
```

```json
{
  "maxValue": 30000,
  "bars": [
    {
      "label": "Organic Traffics",
      "barData": [
        { "value": 4070, "color": "bg-primary" },
        { "value": 3000, "color": "bg-primary-orange" }
      ]
    }
  ]
}
```

## Important Notes

- Colors are Tailwind utility class names used for bar styling.
- Colors can be `bg-primary` , `bg-primary-orange` or `bg-[#HEXCODE]`
- The number of bars and barData entries may vary depending on the query.
