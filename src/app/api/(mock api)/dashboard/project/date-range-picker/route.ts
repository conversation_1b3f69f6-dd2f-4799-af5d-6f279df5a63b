import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { selected, selectedNextMonth } = body;

    // Mock API - just log the received data
    console.log("Date range posted:", {
      selected,
      selectedNextMonth,
      timestamp: new Date().toISOString(),
    });

    // Return success response
    return NextResponse.json(
      { 
        success: true, 
        message: "Date range saved successfully",
        data: { selected, selectedNextMonth }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error in date range picker API:", error);
    return NextResponse.json(
      { 
        success: false, 
        message: "Failed to save date range",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
