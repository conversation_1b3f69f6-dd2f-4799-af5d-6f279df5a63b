import { LineChartDataResponse } from "@/app/(dashboard)/project/[projectId]/competitors/market-analysis/(market-analysis)/MarketAnalysis.types";

export type CompetitorApiResponse = {
  competitors: {
    domain: string;
    totals: {
      traffic_forecast: number;
      search_visibility: number;
      // ...other totals
    };
    daily_metrics: {
      date: string;
      traffic_forecast: number;
      search_visibility: number;
    }[];
  }[];
};

export function lineChartConvertor(
  data: CompetitorApiResponse
): LineChartDataResponse {
  const colors = ["#ff00ff", "#31D37A", "#00BBEC", "#3C0866", "#F57D37"];

  const cardsData: Record<string, { amount: number; growth: string }> = {};
  const colorDefs: { name: string; color: string }[] = [];
  const selectedLines: string[] = [];
  const lineChartData: any[] = [];

  data.competitors.forEach((comp, idx) => {
    const safeName = comp.domain.replace(/\./g, "_");

    // cardsData
    cardsData[safeName] = {
      amount: comp.totals.traffic_forecast,
      // fake growth calc for now, adjust as needed
      growth: "+0%",
    };

    // colors
    colorDefs.push({ name: safeName, color: colors[idx % colors.length] });
    selectedLines.push(safeName);

    // lineChartData (loop daily metrics)
    comp.daily_metrics.forEach((metric, i) => {
      const dateObj = new Date(Number(metric.date)); // assuming it's a numeric timestamp string
      const monthLabel = dateObj.toLocaleString("default", {
        month: "short", // "Jan", "Feb", etc
        day: "numeric",
      });

      if (!lineChartData[i]) {
        lineChartData[i] = { name: monthLabel };
      }

      lineChartData[i][safeName] = metric.traffic_forecast;
    });
  });

  return {
    cardsData,
    colors: colorDefs,
    selectedLines,
    lineChartData,
  };
}
