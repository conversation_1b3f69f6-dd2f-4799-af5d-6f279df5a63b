import { useAuthStore } from "@/store/authStore";

/**
 * Enhanced utility function to require login before performing an action
 * Preserves current URL context and analysis state
 *
 * @param callback Function to execute if user is authenticated
 * @param actionType Type of action requiring authentication ('pdf', 'share', or 'whiteLabel')
 * @param preserveContext Whether to preserve the current URL and analysis context
 * @param taskId Optional taskId to include in the redirect URL for share functionality
 * @returns boolean indicating if the user is authenticated
 */
export function requireLogin(
  callback: () => void,
  actionType: "pdf" | "share" | "whiteLabel" = "pdf",
  preserveContext: boolean = true,
  taskId?: string
): boolean {
  const { isAuthenticated } = useAuthStore.getState();

  if (isAuthenticated) {
    // If user is already authenticated, execute the callback immediately
    callback();
    return true;
  } else {
    // If not authenticated, redirect to login page with context preservation
    if (typeof window !== "undefined") {
      const currentUrl = window.location.href;
      const urlParams = new URLSearchParams(window.location.search);

      // Construct the redirect URL with taskId for share functionality
      let redirectUrl = currentUrl;
      if (taskId && actionType === "share") {
        // For share actions, ensure the URL includes the share parameter
        const baseUrl = window.location.origin + window.location.pathname;
        redirectUrl = `${baseUrl}?share=${taskId}`;
      } else if (taskId && !urlParams.get("share")) {
        // For other actions, add taskId as share parameter if not already present
        const url = new URL(currentUrl);
        url.searchParams.set("share", taskId);
        redirectUrl = url.toString();
      }

      // Store the callback and context information
      const redirectData = {
        actionType,
        timestamp: Date.now(),
        preserveContext,
        originalUrl: redirectUrl, // Use the enhanced URL with taskId
        // Preserve important URL parameters for analysis context
        shareId: taskId || urlParams.get("share"),
        taskId: taskId || urlParams.get("taskId"),
        uid: urlParams.get("uid"), // Payment ID
        urlName: window.location.pathname.split("/")[1], // Extract urlName from path
        // Preserve all search parameters to maintain full context
        searchParams: window.location.search,
      };

      sessionStorage.setItem("postLoginCallback", JSON.stringify(redirectData));

      // Store the enhanced URL for redirect
      if (preserveContext) {
        sessionStorage.setItem("postLoginRedirect", redirectUrl);
      }

      window.location.href = "/login";
    }
    return false;
  }
}

/**
 * Enhanced React hook version of requireLogin with context preservation
 *
 * @param actionType Type of action requiring authentication ('pdf', 'share', or 'whiteLabel')
 * @param preserveContext Whether to preserve the current URL and analysis context
 * @param taskId Optional taskId to include in the redirect URL for share functionality
 * @returns Function that takes a callback and requires login before executing it
 */
export function useRequireLogin(
  actionType: "pdf" | "share" | "whiteLabel" = "pdf",
  preserveContext: boolean = true,
  taskId?: string
) {
  const { isAuthenticated } = useAuthStore();

  return (callback: () => void) => {
    return requireLogin(callback, actionType, preserveContext, taskId);
  };
}

/**
 * Utility function to restore analysis context after login
 * This should be called in the login success handler
 */
export function restoreAnalysisContext(): string | null {
  if (typeof window === "undefined") return null;

  try {
    const callbackData = sessionStorage.getItem("postLoginCallback");
    if (callbackData) {
      const data = JSON.parse(callbackData);

      // If we have context preservation enabled and original URL
      if (data.preserveContext && data.originalUrl) {
        // Clean up the stored data
        sessionStorage.removeItem("postLoginCallback");

        // Return the original URL for redirect
        return data.originalUrl;
      }
    }
  } catch (error) {
    console.error("Failed to restore analysis context:", error);
  }

  return null;
}

/**
 * Check if there's a pending analysis action after login
 */
export function hasPendingAnalysisAction(): boolean {
  if (typeof window === "undefined") return false;

  try {
    const callbackData = sessionStorage.getItem("postLoginCallback");
    if (callbackData) {
      const data = JSON.parse(callbackData);
      return data.preserveContext && data.originalUrl;
    }
  } catch (error) {
    console.error("Failed to check pending analysis action:", error);
  }

  return false;
}
