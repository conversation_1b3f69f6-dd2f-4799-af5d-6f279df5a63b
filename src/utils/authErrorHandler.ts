/**
 * Centralized authentication error handler
 * This utility provides functions to handle authentication errors consistently across the application
 */

// We'll use a simple event system to notify components about auth errors
type AuthErrorListener = () => void;
const authErrorListeners: AuthErrorListener[] = [];

/**
 * Add a listener for authentication errors
 * @param listener Function to call when an auth error occurs
 */
export const addAuthErrorListener = (listener: AuthErrorListener): void => {
  if (!authErrorListeners.includes(listener)) {
    authErrorListeners.push(listener);
  }
};

/**
 * Remove a listener for authentication errors
 * @param listener Function to remove from listeners
 */
export const removeAuthErrorListener = (listener: AuthErrorListener): void => {
  const index = authErrorListeners.indexOf(listener);
  if (index !== -1) {
    authErrorListeners.splice(index, 1);
  }
};

/**
 * Notify all listeners about an authentication error
 * This will be called when a 401 error is detected
 */
export const notifyAuthError = (): void => {
  console.log('Auth error detected, notifying listeners');
  authErrorListeners.forEach(listener => {
    try {
      listener();
    } catch (error) {
      console.error('Error in auth error listener:', error);
    }
  });
};

/**
 * Handle authentication errors by showing the auth modal
 * This function should be imported and used in components that need to handle auth errors
 */
export const handleAuthError = (): void => {
  // This will be implemented to show the auth modal
  // We'll import this in the store to avoid circular dependencies
  notifyAuthError();
};

const authErrorHandler = {
  addAuthErrorListener,
  removeAuthErrorListener,
  notifyAuthError,
  handleAuthError
};

export default authErrorHandler;
