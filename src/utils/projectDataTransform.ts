import {
  ProjectInfo,
  SearchEngineConfig,
  KeywordWithConfigs,
  Competitor,
} from "@/store/createProjectStore";
import { ProjectResponse } from "@/services/projectService";
import { getFlagImageUrl, getSearchEngineImageUrl } from "@/utils/flagUtils";

// Memoization cache for expensive transformations
const transformCache = new Map<string, any>();
const CACHE_SIZE_LIMIT = 50; // Limit cache size to prevent memory leaks

// Helper function to manage cache size
function manageCacheSize() {
  if (transformCache.size > CACHE_SIZE_LIMIT) {
    const firstKey = transformCache.keys().next().value;
    transformCache.delete(firstKey);
  }
}

/**
 * Transform API project response to store format (optimized with caching)
 */
export function transformApiToStore(projectData: ProjectResponse) {
  // Create cache key based on project data
  const cacheKey = `transform_${projectData.id}_${JSON.stringify(
    projectData
  ).slice(0, 100)}`;

  // Check cache first
  if (transformCache.has(cacheKey)) {
    return transformCache.get(cacheKey);
  }
  // Transform project info
  const transformedProjectInfo: ProjectInfo = {
    name: projectData.project_name,
    domain: projectData.url,
    color: projectData.project_color,
    domainType: projectData.domain_type,
    id: projectData.id,
  };

  // Transform search engines - handle the actual API response format
  const transformedSearchEngines: SearchEngineConfig[] = [];

  if (projectData.primary_search_engines?.length > 0) {
    projectData.primary_search_engines.forEach((se: any, seIndex: number) => {
      // Each search engine can have multiple countries and languages
      se.countries?.forEach((countryCode: string, countryIndex: number) => {
        se.languages?.forEach((languageCode: string, langIndex: number) => {
          transformedSearchEngines.push({
            id: `config-${Date.now()}-${seIndex}-${countryIndex}-${langIndex}`,
            searchEngine: {
              name: se.search_engine,
              image: getSearchEngineImageUrl(se.search_engine),
            },
            country: {
              name: getCountryName(countryCode), // We'll need a helper function for this
              code: countryCode,
              image: getFlagImageUrl(countryCode),
            },
            language: {
              name: getLanguageName(languageCode), // We'll need a helper function for this
              code: languageCode,
            },
            location: null, // API doesn't seem to have location data
          });
        });
      });
    });
  }

  // Transform keywords - match with corresponding search engine configs
  const transformedKeywords: KeywordWithConfigs[] =
    projectData.keywords?.map((kw: any) => {
      // Find matching search engine configs for this keyword
      const matchingConfigIds = transformedSearchEngines
        .filter((se) => {
          // Check if this search engine config matches the keyword's criteria
          const matchesEngine = kw.search_engines?.includes(
            se.searchEngine.name
          );
          const matchesCountry = kw.countries?.includes(se.country.code);
          const matchesLanguage = kw.languages?.includes(se.language.code);
          return matchesEngine && matchesCountry && matchesLanguage;
        })
        .map((se) => se.id);

      return {
        keyword: kw.keyword,
        configIds:
          matchingConfigIds.length > 0
            ? matchingConfigIds
            : transformedSearchEngines.map((se) => se.id),
        isSuggested: false,
        // Flatten additional data to match store shape used by UI
        search_volume: kw.search_volume,
        competition: kw.competition,
        cpc: kw.cpc,
        difficulty: kw.difficulty,
        relevance_score: kw.relevance_score,
      } as KeywordWithConfigs;
    }) || [];

  // Transform competitors
  const transformedCompetitors: Competitor[] =
    projectData.competitors?.map((comp: any, index: number) => ({
      id: `competitor-${Date.now()}-${index}`,
      domain: comp.url,
      searchEngines: comp.search_engines || comp.searchEngines || [],
      countries: comp.countries || [],
      isSuggested: false,
      title: comp.title,
      description: comp.description,
      relevanceScore: comp.relevance_score,
      intersections: comp.intersections,
      avg_position: comp.avg_position,
      sum_position: comp.sum_position,
      organic_keywords: comp.organic_keywords,
      organic_traffic: comp.organic_traffic,
      organic_cost: comp.organic_cost,
      competitor_score: comp.competitor_score,
      strength_level: comp.strength_level,
    })) || [];

  // Enhanced step completion logic for edit mode
  // In edit mode, we should be more permissive with step completion
  // to allow users to navigate freely through the flow
  const stepCompletion = {
    // Project information is always complete if we have a project
    projectInformation: true,

    // Search engines step is complete if we have at least one config OR if project exists
    // This allows editing projects that might not have search engines yet
    searchEngines: transformedSearchEngines.length > 0 || !!projectData.id,

    // Keywords step is complete if we have keywords OR if we have search engines
    // This allows users to skip keywords and come back later
    keywords:
      transformedKeywords.length > 0 || transformedSearchEngines.length > 0,

    // Competitors step is complete if we have competitors OR if we have keywords
    // This allows users to skip competitors and come back later
    competitors:
      transformedCompetitors.length > 0 ||
      transformedKeywords.length > 0 ||
      transformedSearchEngines.length > 0,

    // Analytics services is always considered complete for existing projects
    // This allows users to access all steps in edit mode
    analyticsServices: true,
  };

  const result = {
    projectInfo: transformedProjectInfo,
    searchEngineConfigs: transformedSearchEngines,
    keywords: transformedKeywords,
    competitors: transformedCompetitors,
    selectedConfigIds: transformedSearchEngines.map((se) => se.id),
    stepCompletion,
  };

  // Cache the result for future use
  manageCacheSize();
  transformCache.set(cacheKey, result);

  return result;
}

/**
 * Transform store data to API format for updates
 */
export function transformStoreToApi(
  projectInfo: ProjectInfo,
  searchEngineConfigs: SearchEngineConfig[],
  keywords: KeywordWithConfigs[],
  competitors: Competitor[]
) {
  return {
    url: projectInfo.domain,
    domain_type:
      projectInfo.domainType || `*.${new URL(projectInfo.domain).hostname}`,
    project_name: projectInfo.name,
    project_color: projectInfo.color || "#8C00FF",
    status: "enabled",
    primary_search_engines: searchEngineConfigs.map((config) => ({
      search_engine: config.searchEngine.name,
      country_name: config.country.name,
      country_code: config.country.code,
      language_name: config.language.name,
      language_code: config.language.code,
      location_name: config.location?.name || null,
    })),
    keywords: keywords.map((kw) => ({
      keyword: kw.keyword,
      search_volume: kw.search_volume,
      competition: kw.competition,
      cpc: kw.cpc,
      difficulty: kw.difficulty,
      relevance_score: kw.relevance_score,
    })),
    competitors: competitors.map((comp) => ({
      url: comp.domain,
      search_engines: comp.searchEngines,
      countries: comp.countries,
      title: comp.title,
      description: comp.description,
      relevance_score: comp.relevanceScore,
      intersections: comp.intersections,
      avg_position: comp.avg_position,
      sum_position: comp.sum_position,
      organic_keywords: comp.organic_keywords,
      organic_traffic: comp.organic_traffic,
      organic_cost: comp.organic_cost,
      competitor_score: comp.competitor_score,
      strength_level: comp.strength_level,
    })),
  };
}

/**
 * Check if we're in edit mode based on URL parameters
 */
export function isEditMode(searchParams: URLSearchParams | null): boolean {
  return !!searchParams?.get("project_id");
}

/**
 * Get project ID from URL parameters
 */
export function getEditProjectId(
  searchParams: URLSearchParams | null
): string | null {
  return searchParams?.get("project_id") || null;
}

/**
 * Deep compare two objects to check if they're equal
 */
export function deepEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) return true;

  if (obj1 == null || obj2 == null) return obj1 === obj2;

  if (typeof obj1 !== typeof obj2) return false;

  if (typeof obj1 !== "object") return obj1 === obj2;

  if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
}

/**
 * Check if project data has meaningful changes (ignores timestamps and IDs)
 */
export function hasProjectDataChanged(current: any, original: any): boolean {
  if (!original) return true;

  // Create clean versions without timestamps and generated IDs
  const cleanCurrent = {
    projectInfo: current.projectInfo
      ? {
          name: current.projectInfo.name,
          domain: current.projectInfo.domain,
          color: current.projectInfo.color,
          domainType: current.projectInfo.domainType,
        }
      : null,
    searchEngineConfigs:
      current.searchEngineConfigs?.map((config: any) => ({
        searchEngine: config.searchEngine,
        country: config.country,
        language: config.language,
        location: config.location,
      })) || [],
    keywords:
      current.keywords?.map((kw: any) => ({
        keyword: kw.keyword,
        search_volume: kw.search_volume,
        competition: kw.competition,
        cpc: kw.cpc,
        difficulty: kw.difficulty,
        relevance_score: kw.relevance_score,
      })) || [],
    competitors:
      current.competitors?.map((comp: any) => ({
        domain: comp.domain,
        searchEngines: comp.searchEngines,
        countries: comp.countries,
        title: comp.title,
        description: comp.description,
      })) || [],
  };

  const cleanOriginal = {
    projectInfo: original.projectInfo
      ? {
          name: original.projectInfo.name,
          domain: original.projectInfo.domain,
          color: original.projectInfo.color,
          domainType: original.projectInfo.domainType,
        }
      : null,
    searchEngineConfigs:
      original.searchEngineConfigs?.map((config: any) => ({
        searchEngine: config.searchEngine,
        country: config.country,
        language: config.language,
        location: config.location,
      })) || [],
    keywords:
      original.keywords?.map((kw: any) => ({
        keyword: kw.keyword,
        search_volume: kw.search_volume,
        competition: kw.competition,
        cpc: kw.cpc,
        difficulty: kw.difficulty,
        relevance_score: kw.relevance_score,
      })) || [],
    competitors:
      original.competitors?.map((comp: any) => ({
        domain: comp.domain,
        searchEngines: comp.searchEngines,
        countries: comp.countries,
        title: comp.title,
        description: comp.description,
      })) || [],
  };

  return !deepEqual(cleanCurrent, cleanOriginal);
}

/**
 * Helper function to get country name from country code
 */
function getCountryName(countryCode: string): string {
  const countryNames: Record<string, string> = {
    AU: "Australia",
    US: "United States",
    GB: "United Kingdom",
    CA: "Canada",
    DE: "Germany",
    FR: "France",
    IT: "Italy",
    ES: "Spain",
    NL: "Netherlands",
    BR: "Brazil",
    IN: "India",
    JP: "Japan",
    CN: "China",
    KR: "South Korea",
    RU: "Russia",
    MX: "Mexico",
    AR: "Argentina",
    CL: "Chile",
    CO: "Colombia",
    PE: "Peru",
    // Add more as needed
  };

  return countryNames[countryCode] || countryCode;
}

/**
 * Helper function to get language name from language code
 */
function getLanguageName(languageCode: string): string {
  const languageNames: Record<string, string> = {
    en: "English",
    es: "Spanish",
    fr: "French",
    de: "German",
    it: "Italian",
    pt: "Portuguese",
    ru: "Russian",
    ja: "Japanese",
    ko: "Korean",
    zh: "Chinese",
    ar: "Arabic",
    hi: "Hindi",
    nl: "Dutch",
    sv: "Swedish",
    da: "Danish",
    no: "Norwegian",
    fi: "Finnish",
    pl: "Polish",
    tr: "Turkish",
    th: "Thai",
    // Add more as needed
  };

  return languageNames[languageCode] || languageCode;
}
