/**
 * Performance optimization utilities for the edit flow
 * Provides tools to prevent blocking animations and improve user experience
 */

// Debounce utility for expensive operations
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle utility for frequent operations
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Schedule work during idle time to avoid blocking animations
export function scheduleIdleWork(callback: () => void): () => void {
  if (typeof window !== "undefined" && "requestIdleCallback" in window) {
    const idleId = window.requestIdleCallback(callback);
    return () => window.cancelIdleCallback(idleId);
  } else {
    // Fallback for browsers without requestIdleCallback
    const timeoutId = setTimeout(callback, 0);
    return () => clearTimeout(timeoutId);
  }
}

// Schedule work for the next animation frame
export function scheduleAnimationFrame(callback: () => void): () => void {
  const frameId = requestAnimationFrame(callback);
  return () => cancelAnimationFrame(frameId);
}

// Batch multiple state updates to prevent excessive re-renders
export function batchUpdates<T>(
  updates: Array<() => void>,
  delay: number = 0
): void {
  if (delay === 0) {
    // Execute immediately in a single frame
    requestAnimationFrame(() => {
      updates.forEach((update) => update());
    });
  } else {
    // Execute with delay
    setTimeout(() => {
      requestAnimationFrame(() => {
        updates.forEach((update) => update());
      });
    }, delay);
  }
}

// Memoization utility with size limit
export class LRUCache<K, V> {
  private cache = new Map<K, V>();
  private maxSize: number;

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize;
  }

  get(key: K): V | undefined {
    if (this.cache.has(key)) {
      // Move to end (most recently used)
      const value = this.cache.get(key)!;
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return undefined;
  }

  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      // Update existing
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // Remove least recently used
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// Performance monitoring utilities
export const performanceMonitor = {
  // Measure execution time of a function
  measureTime: <T>(name: string, fn: () => T): T => {
    const start = performance.now();
    const result = fn();
    const end = performance.now();

    if (process.env.NODE_ENV === "development") {
      console.log(`⏱️ ${name}: ${(end - start).toFixed(2)}ms`);
    }

    return result;
  },

  // Check if the main thread is busy
  isMainThreadBusy: (): Promise<boolean> => {
    return new Promise((resolve) => {
      const start = performance.now();
      setTimeout(() => {
        const delay = performance.now() - start;
        // If setTimeout was delayed by more than 10ms, main thread is busy
        resolve(delay > 10);
      }, 0);
    });
  },

  // Wait for the main thread to be less busy
  waitForIdleTime: (timeout: number = 100): Promise<void> => {
    return new Promise((resolve) => {
      const checkIdle = () => {
        performanceMonitor.isMainThreadBusy().then((busy) => {
          if (!busy) {
            resolve();
          } else {
            setTimeout(checkIdle, 10);
          }
        });
      };

      // Start checking
      checkIdle();

      // Timeout fallback
      setTimeout(resolve, timeout);
    });
  },
};

// React hook for performance-aware state updates
export function usePerformantState<T>(
  initialValue: T
): [T, (value: T | ((prev: T) => T)) => void] {
  const [state, setState] = React.useState(initialValue);

  const setPerformantState = React.useCallback(
    (value: T | ((prev: T) => T)) => {
      // Schedule state update during idle time to avoid blocking animations
      scheduleIdleWork(() => {
        setState(value);
      });
    },
    []
  );

  return [state, setPerformantState];
}

// Export React for the hook
import React from "react";

/**
 * Mode validation utilities to prevent data leakage between edit and create modes
 */
export const modeValidation = {
  // Validate that we're in the correct mode for the current operation
  validateCreateMode: (
    isInCreateMode: boolean,
    hasProjectData: boolean
  ): boolean => {
    // In create mode, we can have project data (after creating the project)
    // The key is that we're in create mode, not whether we have data
    return isInCreateMode;
  },

  validateEditMode: (
    isInEditMode: boolean,
    hasProjectData: boolean,
    projectId: string | null
  ): boolean => {
    // In edit mode, we should have project data and a valid project ID
    return isInEditMode && hasProjectData && !!projectId;
  },

  // Check for potential data leakage
  detectDataLeakage: (
    isInCreateMode: boolean,
    isInEditMode: boolean,
    hasProjectData: boolean,
    projectId: string | null
  ): { hasLeakage: boolean; reason?: string } => {
    // Both modes active (should never happen)
    if (isInCreateMode && isInEditMode) {
      return {
        hasLeakage: true,
        reason: "Both create and edit modes are active",
      };
    }

    // Edit mode without project ID (invalid state)
    if (isInEditMode && !projectId) {
      return { hasLeakage: true, reason: "Edit mode without project ID" };
    }

    // Edit mode with project data but mismatched project ID
    if (isInEditMode && hasProjectData && projectId) {
      // This would require checking if the project data matches the project ID
      // For now, we'll assume this is valid
    }

    // Neither mode active but has project data (potential leakage)
    if (!isInCreateMode && !isInEditMode && hasProjectData) {
      return {
        hasLeakage: true,
        reason: "No active mode but has project data",
      };
    }

    // Note: We removed the "Create mode with project data" check because
    // it's normal to have project data in create mode after creating a project
    // The create flow: no data -> create project -> have data -> continue steps

    return { hasLeakage: false };
  },

  // Clean up leaked data
  cleanupLeakedData: (
    resetStore: (preserveCreateMode?: boolean) => void,
    setCreateFromScratch: () => void
  ) => {
    resetStore(false); // Don't preserve any mode
    setCreateFromScratch(); // Set to create mode as default
  },
};
