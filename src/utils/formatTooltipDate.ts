/**
 * Formats a date string to display format like "jul 15"
 * @param dateString - Date string in various formats (e.g., "2024-07-15", "Jul 15, 2024", "20240715", etc.)
 * @returns Formatted date string like "jul 15"
 */
export const formatTooltipDate = (dateString: string): string => {
  try {
    let date: Date;
    
    // Handle YYYYMMDD format (API format)
    if (/^\d{8}$/.test(dateString)) {
      const year = dateString.substring(0, 4);
      const month = dateString.substring(4, 6);
      const day = dateString.substring(6, 8);
      date = new Date(`${year}-${month}-${day}`);
    } else {
      // Handle other date formats
      date = new Date(dateString);
    }
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return dateString; // Return original if invalid
    }
    
    const months = [
      'jan', 'feb', 'mar', 'apr', 'may', 'jun',
      'jul', 'aug', 'sep', 'oct', 'nov', 'dec'
    ];
    
    const month = months[date.getMonth()];
    const day = date.getDate();
    
    return `${month} ${day}`;
  } catch (error) {
    // Return original string if parsing fails
    return dateString;
  }
};