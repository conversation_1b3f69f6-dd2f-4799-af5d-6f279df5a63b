import React from 'react';

type SizeType = 'sm' | 'md' | 'lg';

type SizeMapType = {
  [key in SizeType]: {
    svgWidth: number;
    svgHeight: number;
    radius: number;
    center: number;
    circleRadius: number;
    scoreFont: number;
    titleFont: number;
    titleY: number;
  }
};

const sizeMap: SizeMapType = {
  sm: { svgWidth: 120, svgHeight: 120, radius: 48, center: 60, circleRadius: 20, scoreFont: 16, titleFont: 12, titleY: 20 },
  md: { svgWidth: 200, svgHeight: 180, radius: 80, center: 100, circleRadius: 35, scoreFont: 24, titleFont: 16, titleY: 30 },
  lg: { svgWidth: 280, svgHeight: 240, radius: 112, center: 140, circleRadius: 50, scoreFont: 32, titleFont: 18, titleY: 35 },
};

interface ScoreMeterProps {
  score?: number;
  title?: string;
  size?: SizeType;
}

const ScoreMeter: React.FC<ScoreMeterProps> = ({
  score = 0,
  title = 'Overall Performance',
  size = 'md',
}) => {
  const {
    svgWidth,
    svgHeight,
    radius,
    center,
    circleRadius,
    scoreFont,
    titleFont,
    titleY,
  } = sizeMap[size];

  const totalBars = 30;
  const activeBars = Math.round((score / 100) * totalBars);

  // Divide the 180° top-half into (totalBars - 1) steps
  const angleStep = 180 / (totalBars - 1);
  // Start from left side (180°) and go to right side (0°) but in the top half
  const startAngle = 180;

  // Shift the whole gauge down by titleY so it doesn't overlap the title
  const meterCenterY = center + titleY;

  const bars = Array.from({ length: totalBars }, (_, i) => {
    // Sweep from 180° to 0° in the top half (using negative sin values)
    const angle = startAngle - i * angleStep;
    const rad = (Math.PI / 180) * angle;

    const barLength = radius * 0.18;
    // Fix hydration issues by using toFixed(4) to ensure consistent string representation
    const x1 = Number((center + radius * Math.cos(rad)).toFixed(4));
    // Use negative sin to flip to the top half of the circle
    const y1 = Number((meterCenterY - radius * Math.sin(rad)).toFixed(4));
    const x2 = Number((center + (radius - barLength) * Math.cos(rad)).toFixed(4));
    // Use negative sin to flip to the top half of the circle
    const y2 = Number((meterCenterY - (radius - barLength) * Math.sin(rad)).toFixed(4));

    // Active bars should start from the left (i=0 is the leftmost bar at 180°)
    const isActive = i < activeBars;

    return (
      <line
        key={i}
        x1={x1}
        y1={y1}
        x2={x2}
        y2={y2}
        stroke={isActive ? '#914ac4' : '#CCCCCC'}
        strokeWidth="2.5"
        strokeLinecap="round"
      />
    );
  });

  return (
    <div style={{ textAlign: 'center' }}>
      <svg
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
      >
        {/* Title */}
        <text
          x={center}
          y={titleY / 2}
          fontSize={titleFont}
          fontWeight="600"
          textAnchor="middle"
          fill="#333"
        >
          {title}
        </text>

        {/* Gauge Bars */}
        {bars}

        {/* Score Circle in the upper half */}
        <circle
          cx={center}
          cy={meterCenterY }
          r={circleRadius}
          fill="#F8F0FF"
        />
        <text
          x={center}
          y={meterCenterY  + scoreFont / 3}
          fontSize={scoreFont}
          fontWeight="bold"
          textAnchor="middle"
          fill="#914ac4"
        >
          {score}
        </text>
      </svg>
    </div>
  );
};

export default ScoreMeter;
