"use client";
import React from "react";
import { Line<PERSON><PERSON>, Line, ResponsiveContainer } from "recharts";

type Variant = "primary" | "green" | "red";

type Props = {
  data: { name: string; pv: number }[];
  variant?: Variant;
};

const variants: Record<Variant, { stroke: string }> = {
  primary: {
    stroke: "var(--color-primary)",
  },
  green: {
    stroke: "var(--color-primary-green-2)",
  },
  red: {
    stroke: "var(--color-primary-red-2)",
  },
};

export default function MiniLineChart({ data, variant = "primary" }: Props) {
  const renderVariant = () => variants[variant] || variants.primary;
  return (
    <div className="w-[100px] h-[50px]">
      <ResponsiveContainer width={"100%"} height={"100%"}>
        <LineChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 10,
          }}
        >
          <Line
            type="basis"
            dataKey="pv"
            stroke={renderVariant().stroke}
            strokeWidth={1.42}
            dot={{
              r: 4,
              fill: "#fff",
              stroke: renderVariant().stroke,
              strokeWidth: 1.42,
              style: { filter: "none" },
            }}
            connectNulls
            style={{
              filter: "drop-shadow(0px 4px 0px rgba(213, 215, 248, 0.4))",
              strokeLinecap: "round",
              strokeLinejoin: "round",
            }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
