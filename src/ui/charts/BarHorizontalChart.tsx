"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

type Props = {
  data?: { name: string; uv: number; pv: number; amt: number }[];
};

const datas = [
  {
    name: "Dribble",
    uv: 600,
    pv: 400,
    amt: 800,
  },
  {
    name: "LinkedIn",
    uv: 600,
    pv: 400,
    amt: 800,
  },
  {
    name: "GoogleAds",
    uv: 800,
    pv: 400,
    amt: 800,
  },
  {
    name: "<PERSON>han<PERSON>",
    uv: 200,
    pv: 400,
    amt: 500,
  },
];

export default function BarHorizontalChart({ data = datas }: Props) {
  //   const xAxisLabels = document?.querySelectorAll(
  //     ".recharts-cartesian-axis.xAxis g .recharts-layer text tspan"
  //   );
  return (
    <div className="relative w-full">
      <div className="w-[calc(100%-60px)] h-[1px] bg-light-gray-5 absolute top-0 left-[60px] flex items-center justify-between">
        {/* {[...xAxisLabels].map((item, index) => (
          <div
            key={index}
            className="font-semibold text-light-gray-3 text-[10px] pb-4"
          >
            {item.textContent}
          </div>
        ))} */}
      </div>
      <ResponsiveContainer width={"100%"} height={200}>
        <BarChart
          layout="vertical"
          width={500}
          height={165}
          data={data}
          barSize={14}
          barCategoryGap="70%"
          margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
        >
          <CartesianGrid
            stroke="var(--color-light-gray-2)"
            strokeWidth={1}
            horizontal={false}
          />
          <XAxis
            type="number"
            tickLine={false}
            axisLine={true}
            tick={{
              fontSize: 10,
              fill: "var(--color-light-gray-3)",
              fontWeight: 600,
            }}
            tickCount={4}
            stroke="var(--color-light-gray-5)"
            strokeWidth={"1px"}
          />

          <YAxis
            dataKey="name"
            type="category"
            axisLine={false}
            tickLine={false}
            tick={{
              fontSize: 10,
              fill: "var(--color-light-gray-3)",
              fontWeight: 600,
            }}
            stroke="var(--color-light-gray-5)"
            strokeWidth={"1px"}
            padding={{ top: 16, bottom: 16 }}
          />

          <Bar
            dataKey="pv"
            stackId="a"
            fill="var(--color-primary)"
            radius={[4, 0, 0, 4]}
          />

          <Bar dataKey="uv" stackId="a" fill="var(--color-primary-pink)" />

          <Bar
            dataKey="amt"
            stackId="a"
            fill="var(--color-primary)"
            radius={[0, 4, 4, 0]}
            label={{
              position: "insideRight",
              dx: 35,
              fontSize: 10,
              fontWeight: "600",
              fill: "var(--color-light-gray-3)",
            }}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
