import React, { useState, useEffect } from 'react';

interface Segment {
  name: string;
  value: number;
}

interface Thresholds {
  good: number;
  moderate: number;
}

type SizeOption = 'sm' | 'md' | 'lg';

interface CircleChartProps {
  data: Segment[];
  size?: number;          // custom diameter in px (overrides sizeOption)
  sizeOption?: SizeOption; // predefined size option (sm, md, lg)
  strokeWidth?: number;   // thickness of arcs, default 20
  thresholds?: Thresholds; // coloring thresholds (used in expanded view)
  scoreMax?: number;      // maximum possible score (default 100)
}

// Size configurations for predefined size options
const sizeConfigs: Record<SizeOption, { diameter: number, strokeWidth: number, fontSize: number }> = {
  sm: {
    diameter: 150,
    strokeWidth: 16,  // Slightly thinner for small size
    fontSize: 0.85   // Slightly larger font for better readability
  },
  md: {
    diameter: 200,
    strokeWidth: 24,
    fontSize: 1
  },
  lg: {
    diameter: 300,
    strokeWidth: 32,  // Slightly thicker for large size
    fontSize: 1.1    // Slightly smaller font for better proportions
  }
};

/**
 * CircleChart
 * - Collapsed: displays score percent as a partial arc using circle stroke-dasharray.
 * - Expanded (on hover): shows individual segments with curved arcs, overlaid on a light gray background circle.
 * - Uses a white theme with green, yellow, and red color coding based on scores.
 * - Supports three size options: 'sm' (small), 'md' (medium, default), and 'lg' (large).
 * - All elements (arcs, text, gaps, etc.) scale proportionally with the selected size.
 * - Displays segment scores inside each arc section.
 */
const CircleChart: React.FC<CircleChartProps> = ({
  data,
  size,
  sizeOption = 'md',
  strokeWidth,
  thresholds = { good: 50, moderate: 80 },
  scoreMax = 100,
}) => {
  // Get size configuration based on sizeOption or use custom size
  const config = sizeConfigs[sizeOption];
  const chartSize = size || config.diameter;
  const chartStrokeWidth = strokeWidth || config.strokeWidth;
  const fontSizeMultiplier = config.fontSize;
  const [expanded, setExpanded] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [animationProgress, setAnimationProgress] = useState(0);

  // Compute average score and percent
  const total = data.reduce((sum, seg) => sum + seg.value, 0);
  const score = data.length ? Math.round(total / data.length) : 0;
  const percent = Math.min(Math.max(score / scoreMax, 0), 1);

  // Get color for overall score
  const getOverallScoreColor = () => {
    if (score >= thresholds.moderate) return '#4CD964'; // Green for high scores
    if (score >= thresholds.good) return '#FFCC00'; // Yellow for medium scores
    return '#FF3B30'; // Red for low scores
  };

  const radius = (chartSize - chartStrokeWidth) / 2;
  const center = chartSize / 2;
  const circumference = 2 * Math.PI * radius;

  // Animation effect for expanding/collapsing with debounce for smoother transitions
  useEffect(() => {
    let animationFrame: number;
    let startTime: number;
    let debounceTimeout: NodeJS.Timeout | null = null;

    // Clear any pending animation before starting a new one
    const startAnimation = () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }

      startTime = 0;

      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp;
        const elapsed = timestamp - startTime;
        const duration = 500; // Slightly longer animation duration for smoother effect

        // Use easeInOutCubic easing function for smoother animation
        const easeInOutCubic = (t: number) => {
          return t < 0.5
            ? 4 * t * t * t
            : 1 - Math.pow(-2 * t + 2, 3) / 2;
        };

        // Calculate raw progress (0 to 1)
        const rawProgress = Math.min(elapsed / duration, 1);
        // Apply easing function
        const easedProgress = easeInOutCubic(rawProgress);

        // Update animation progress
        setAnimationProgress(expanded ? easedProgress : 1 - easedProgress);

        // Continue animation if not complete
        if (rawProgress < 1) {
          animationFrame = requestAnimationFrame(animate);
        }
      };

      // Start animation
      animationFrame = requestAnimationFrame(animate);
    };

    // Debounce the animation start to prevent flickering on rapid hover changes
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
    }
    debounceTimeout = setTimeout(startAnimation, 50);

    // Cleanup
    return () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
      }
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [expanded]);

  // Polar conversion for expanded state arcs
  const polarToCartesian = (cx: number, cy: number, r: number, angleDeg: number) => {
    const angleRad = ((angleDeg - 90) * Math.PI) / 180;
    // Fix hydration issues by using toFixed(4) to ensure consistent string representation
    return {
      x: Number((cx + r * Math.cos(angleRad)).toFixed(4)),
      y: Number((cy + r * Math.sin(angleRad)).toFixed(4))
    };
  };

  const describeArc = (cx: number, cy: number, r: number, startAngle: number, endAngle: number) => {
    const start = polarToCartesian(cx, cy, r, endAngle);
    const end = polarToCartesian(cx, cy, r, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1';
    return `M ${start.x} ${start.y} A ${r} ${r} 0 ${largeArcFlag} 0 ${end.x} ${end.y}`;
  };

  // Updated color function with more vibrant colors
  const getColor = (value: number, isHovered: boolean) => {
    // Base colors
    const greenColor = '#4CD964';
    const greenColorLight = 'rgba(76, 217, 100, 0.7)';
    const yellowColor = '#FFCC00';
    const yellowColorLight = 'rgba(255, 204, 0, 0.7)';
    const redColor = '#FF3B30';
    const redColorLight = 'rgba(255, 59, 48, 0.7)';

    // Return appropriate color based on value and hover state
    if (value <= thresholds.good) {
      return isHovered ? greenColor : greenColorLight;
    } else if (value <= thresholds.moderate) {
      return isHovered ? yellowColor : yellowColorLight;
    } else {
      return isHovered ? redColor : redColorLight;
    }
  };

  // Prepare segments for expanded state with wider gaps
  // Scale the gap based on chart size
  const GAP = 15 * (chartSize / 200); // Scale gap relative to medium size
  let angleAcc = 0;
  const segments = data.map((seg, idx) => {
    const rawAng = total ? (seg.value / total) * 360 : 0;
    const ang = Math.max(rawAng - GAP, 0);
    const startAng = angleAcc + GAP / 2;
    const endAng = startAng + ang;
    const midAng = startAng + ang / 2;

    // Calculate positions for labels and markers
    const labelDistance = radius + chartStrokeWidth * 1.2; // Position for outer labels (category name)
    const labelPos = polarToCartesian(center, center, labelDistance, midAng);

    // Calculate position for score labels (inside the arc)
    const scoreDistance = radius * 0.7; // Position score labels inside the arc
    const scorePos = polarToCartesian(center, center, scoreDistance, midAng);

    // Calculate text anchor based on angle
    const textAnchor = midAng < 90 || midAng > 270 ? 'start' : midAng > 90 && midAng < 270 ? 'end' : 'middle';

    angleAcc += rawAng;
    return {
      ...seg,
      startAng,
      endAng,
      midAng,
      labelPos,
      scorePos,
      textAnchor,
      idx
    };
  });

  return (
    <svg
      width={chartSize}
      height={chartSize}
      style={{ cursor: 'pointer', overflow: 'visible' }}
      onMouseEnter={() => setExpanded(true)}
      onMouseLeave={() => {
        setExpanded(false);
        setHoveredIndex(null);
      }}
    >
      {/* Background track - lighter gray in white theme */}
      <circle
        cx={center}
        cy={center}
        r={radius}
        fill="none"
        stroke="#EEEEEE"
        strokeWidth={chartStrokeWidth}
      />

      {animationProgress < 0.5 ? (
        <>
          {/* Progress arc - using color based on score */}
          <circle
            cx={center}
            cy={center}
            r={radius}
            fill="none"
            stroke={getOverallScoreColor()}
            strokeWidth={chartStrokeWidth}
            strokeDasharray={circumference}
            strokeDashoffset={circumference * (1 - percent)}
            strokeLinecap="round"
            style={{
              transform: `rotate(-90deg)`,
              transformOrigin: 'center',
              opacity: 1 - animationProgress * 2,
              transition: 'stroke 0.3s ease'
            }}
          />
          {/* Background for center score */}
          <circle
            cx={center}
            cy={center}
            r={chartSize * 0.12}
            fill="white"
            style={{
              opacity: 1 - animationProgress * 2,
              filter: `drop-shadow(0px 0px ${chartSize * 0.008}px rgba(0,0,0,0.1))`
            }}
          />
          {/* Center score */}
          <text
            x={center}
            y={center + chartSize * 0.02}
            textAnchor="middle"
            fontFamily="Helvetica, Arial, sans-serif"
            fontSize={chartSize * 0.16 * fontSizeMultiplier}
            fill={getOverallScoreColor()}
            fontWeight={700}
            letterSpacing="1px"
            pointerEvents="none"
            style={{
              opacity: 1 - animationProgress * 2,
              transition: 'fill 0.3s ease'
            }}
          >
            {score}
          </text>
        </>
      ) : (
        <>
          {/* Render segments in expanded view */}
          {segments.map((seg) => {
            const isHovered = hoveredIndex === seg.idx;
            const segmentOpacity = (animationProgress - 0.5) * 2; // 0 to 1 during second half of animation

            return (
              <g
                key={seg.name}
                onMouseEnter={() => setHoveredIndex(seg.idx)}
                onMouseLeave={() => setHoveredIndex(null)}
                style={{ pointerEvents: 'all' }}
              >
                {/* Segment arc with rounded caps and smoother appearance */}
                <path
                  d={describeArc(center, center, radius, seg.startAng, seg.endAng)}
                  fill="none"
                  stroke={getColor(seg.value, isHovered)}
                  strokeWidth={isHovered ? chartStrokeWidth * 1.2 : chartStrokeWidth}
                  strokeLinecap="round"
                  style={{
                    transition: 'all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1)', // More natural easing
                    filter: isHovered ? `drop-shadow(0px 0px ${chartSize * 0.015}px rgba(0,0,0,0.15))` : 'none', // Scale shadow with chart size
                    opacity: segmentOpacity,
                    transform: isHovered ? `scale(1.03)` : 'scale(1)', // Subtle scale without translation for stability
                    transformOrigin: 'center center'
                  }}
                />

                {/* Category label on top of each segment */}
                <text
                  x={seg.labelPos.x}
                  y={seg.labelPos.y}
                  textAnchor={seg.textAnchor}
                  fontFamily="Helvetica, Arial, sans-serif"
                  fontSize={chartSize * 0.05 * fontSizeMultiplier}
                  fill={isHovered ? "#333333" : "#666666"}
                  fontWeight={isHovered ? 700 : 500}
                  pointerEvents="none"
                  style={{
                    opacity: segmentOpacity * (isHovered ? 1 : 0.85),
                    transition: 'all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1)',
                    transform: isHovered ? 'scale(1.05)' : 'scale(1)', // More subtle scale
                    transformOrigin: `${seg.labelPos.x}px ${seg.labelPos.y}px`,
                    textShadow: isHovered ? '0px 0px 1px rgba(255,255,255,0.8)' : 'none' // Add subtle text shadow on hover
                  }}
                >
                  {seg.name}
                </text>

                {/* Score label under each segment */}
                <g>
                  {/* Background circle for better readability */}
                  <circle
                    cx={seg.scorePos.x}
                    cy={seg.scorePos.y}
                    r={chartSize * 0.04}
                    fill="white"
                    style={{
                      opacity: segmentOpacity * 0.8,
                      transition: 'all 0.3s ease',
                      filter: `drop-shadow(0px 0px ${chartSize * 0.005}px rgba(0,0,0,0.1))`
                    }}
                  />
                  <text
                    x={seg.scorePos.x}
                    y={seg.scorePos.y + chartSize * 0.02} // Adjust vertical alignment
                    textAnchor="middle" // Always center the score
                    fontFamily="Helvetica, Arial, sans-serif"
                    fontSize={chartSize * 0.06 * fontSizeMultiplier}
                    fill={getColor(seg.value, isHovered)}
                    fontWeight={700}
                    pointerEvents="none"
                    style={{
                      opacity: segmentOpacity * (isHovered ? 1 : 0.9),
                      transition: 'all 0.3s ease',
                      filter: isHovered ? 'drop-shadow(0px 0px 1px rgba(0,0,0,0.2))' : 'none',
                    }}
                  >
                    {seg.value}
                  </text>
                </g>

                {/* Value display when hovered */}
                {isHovered && (
                  <g style={{ opacity: segmentOpacity }}>
                    <circle
                      cx={center}
                      cy={center}
                      r={radius * 0.5}
                      fill="rgba(255, 255, 255, 0.9)"
                      style={{
                        transition: 'all 0.3s ease',
                        filter: `drop-shadow(0px 0px ${chartSize * 0.01}px rgba(0,0,0,0.1))`
                      }}
                    />
                    <text
                      x={center}
                      y={center - chartSize * 0.05}
                      textAnchor="middle"
                      fontFamily="Helvetica, Arial, sans-serif"
                      fontSize={chartSize * 0.07 * fontSizeMultiplier}
                      fill="#333333"
                      fontWeight={600}
                      pointerEvents="none"
                    >
                      {seg.name}
                    </text>
                    <text
                      x={center}
                      y={center + chartSize * 0.07}
                      textAnchor="middle"
                      fontFamily="Helvetica, Arial, sans-serif"
                      fontSize={chartSize * 0.12 * fontSizeMultiplier}
                      fill="#333333"
                      fontWeight={700}
                      pointerEvents="none"
                    >
                      {seg.value}
                    </text>
                  </g>
                )}
              </g>
            );
          })}

          {/* Show score in center when no segment is hovered */}
          {hoveredIndex === null && (
            <>
              {/* Background circle for center score */}
              <circle
                cx={center}
                cy={center}
                r={chartSize * 0.12}
                fill="white"
                style={{
                  opacity: (animationProgress - 0.5) * 2,
                  filter: `drop-shadow(0px 0px ${chartSize * 0.008}px rgba(0,0,0,0.1))`,
                  transition: 'opacity 0.3s ease'
                }}
              />
              <text
                x={center}
                y={center + chartSize * 0.02}
                textAnchor="middle"
                fontFamily="Helvetica, Arial, sans-serif"
                fontSize={chartSize * 0.16 * fontSizeMultiplier}
                fill={getOverallScoreColor()}
                fontWeight={700}
                letterSpacing="1px"
                pointerEvents="none"
                style={{
                  opacity: (animationProgress - 0.5) * 2,
                  transition: 'opacity 0.3s ease, fill 0.3s ease'
                }}
              >
                {score}
              </text>
            </>
          )}
        </>
      )}
    </svg>
  );
};

export default CircleChart;
