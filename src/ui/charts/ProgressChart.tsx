"use client";
import abbreviateNumber from "@/utils/abbreviateNumber";
import React, { useEffect, useState } from "react";
import { CircularProgressbar } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

type Size = "sm" | "md" | "lg";

type Variant = "default" | "green" | "red" | "yellow" | "orange";
type Grade =
  | "A+"
  | "A"
  | "A-"
  | "B+"
  | "B"
  | "B-"
  | "C+"
  | "C"
  | "C-"
  | "D+"
  | "D"
  | "D-"
  | "F";

type GradeInfo = {
  score: number;
  progress: string;
  textBox: string;
};

type ProgressStateProps = {
  label: string;
  value: number;
  variant: string;
  isNoColor?: boolean;
};

type ValueOrScore = {
  value?: Grade;
  score?: number;
};

type Props = ValueOrScore & {
  variant?: Variant;
  title?: string;
  size?: Size;
  progressStates?: Omit<ProgressStateProps, "variant">[];
  showGrade?: boolean;
  onClick?: () => void;
};

const sizes = {
  sm: {
    progress: "w-[90px] sm:w-[100px]",
    textBox: "w-[35px] h-[35px] sm:w-[39px] sm:h-[39px] text-base sm:text-lg",
    box: "w-[120px] sm:w-[139px]",
  },
  md: {
    progress: "w-[130px] sm:w-[150px]",
    textBox:
      "w-[50px] h-[50px] sm:w-[59px] sm:h-[59px] text-[22px] sm:text-[27px]",
    box: "w-[180px] sm:w-[208px]",
  },
  lg: {
    progress: "w-[150px] sm:w-[173px]",
    textBox:
      "w-[60px] h-[60px] sm:w-[69px] sm:h-[69px] text-[26px] sm:text-[31px]",
    box: "w-[210px] sm:w-[240px]",
  },
};

const variants = {
  default: {
    progress: "var(--color-primary)",
    textBox: "bg-primary/10 text-primary",
  },
  green: {
    progress: "var(--color-primary-green)",
    textBox: "bg-primary-green/10 text-primary-green",
  },
  red: {
    progress: "var(--color-primary-red)",
    textBox: "bg-primary-red/10 text-primary-red",
  },
  yellow: {
    progress: "var(--color-primary-yellow)",
    textBox: "bg-primary-yellow/10 text-primary-yellow",
  },
  orange: {
    progress: "var(--color-primary-orange)",
    textBox: "bg-primary-orange/10 text-primary-orange",
  },
};

const getGradeScore = (grade: Grade): number => {
  const gradeScores: Record<Grade, number> = {
    "A+": 100,
    A: 90,
    "A-": 85,
    "B+": 80,
    B: 75,
    "B-": 70,
    "C+": 65,
    C: 60,
    "C-": 55,
    "D+": 50,
    D: 45,
    "D-": 40,
    F: 10,
  };
  return gradeScores[grade];
};

const getGradeColor = (grade: Grade): Variant => {
  const gradeColors: Record<Grade, Variant> = {
    "A+": "default",
    A: "default",
    "A-": "default",
    "B+": "green",
    B: "green",
    "B-": "green",
    "C+": "yellow",
    C: "yellow",
    "C-": "yellow",
    "D+": "orange",
    D: "orange",
    "D-": "orange",
    F: "red",
  };

  return gradeColors[grade];
};

const getColorByScore = (score: number): Variant => {
  if (score >= 85) return "default";
  if (score >= 70) return "green";
  if (score >= 55) return "yellow";
  if (score >= 40) return "orange";
  return "red";
};

// Function to determine grade based on score
const getGradeFromScore = (score: number): Grade => {
  if (score >= 97) return "A+";
  if (score >= 93) return "A";
  if (score >= 90) return "A-";
  if (score >= 87) return "B+";
  if (score >= 83) return "B";
  if (score >= 80) return "B-";
  if (score >= 77) return "C+";
  if (score >= 73) return "C";
  if (score >= 70) return "C-";
  if (score >= 67) return "D+";
  if (score >= 63) return "D";
  if (score >= 60) return "D-";
  return "F";
};

const gradeResult = (
  grade: Grade | undefined,
  score: number | undefined
): GradeInfo => {
  if (score !== undefined) {
    return {
      score: score,
      progress: variants[getColorByScore(score)].progress,
      textBox: variants[getColorByScore(score)].textBox,
    };
  } else if (grade) {
    return {
      score: getGradeScore(grade),
      progress: variants[getGradeColor(grade)].progress,
      textBox: variants[getGradeColor(grade)].textBox,
    };
  } else {
    // Default fallback
    return {
      score: 50,
      progress: variants["default"].progress,
      textBox: variants["default"].textBox,
    };
  }
};

export default function ProgressChart({
  value,
  variant,
  title,
  size = "md",
  progressStates = [],
  score,
  showGrade = true,
  onClick,
}: Props) {
  const renderColorProgress = variant
    ? variants[variant].progress
    : gradeResult(value, score).progress;
  const renderColorTextBox = variant
    ? variants[variant].textBox
    : gradeResult(value, score).textBox;
  const renderSize = (key: Size) => sizes[key] || sizes["md"];

  const [progress, setProgress] = useState(0);
  const [progressGrade, setProgressGrade] = useState<Grade>("F");
  // progressScore is used in the animation logic but not directly rendered
  const [progressScore, setProgressScore] = useState(0);

  useEffect(() => {
    const targetValue = gradeResult(value as Grade | undefined, score).score;
    const interval = setInterval(() => {
      setProgress(targetValue);
    }, 100);

    const grades = [
      "F",
      "D-",
      "D",
      "D+",
      "C-",
      "C",
      "C+",
      "B-",
      "B",
      "B+",
      "A-",
      "A",
      "A+",
    ];

    // Determine target grade - either from value prop or calculated from score
    let targetGrade: Grade;
    if (value) {
      targetGrade = value;
    } else if (score !== undefined) {
      targetGrade = getGradeFromScore(score);
      // Set the grade immediately for score-based charts
      setProgressGrade(targetGrade);
    } else {
      targetGrade = "F"; // Default fallback
    }

    const targetIndex = grades.indexOf(targetGrade);

    // Define interval2 with proper type
    let interval2: NodeJS.Timeout | undefined = undefined;

    if (score !== undefined) {
      interval2 = setInterval(() => {
        setProgressScore((prevState) => {
          if (prevState >= score) {
            if (interval2) clearInterval(interval2);
            return score;
          }

          if (prevState < 40) {
            return prevState + Math.floor(Math.random() * 7) + 1;
          } else {
            if (prevState < 70) {
              return prevState + Math.floor(Math.random() * 3) + 1;
            } else {
              return prevState + 1;
            }
          }
        });
      }, 39);
    }

    // Define intervalGrade with proper type
    let intervalGrade: NodeJS.Timeout | undefined = undefined;

    if (value) {
      intervalGrade = setInterval(() => {
        setProgressGrade((prevState: Grade): Grade => {
          const currentIndex = grades.indexOf(prevState as Grade);
          if (currentIndex === targetIndex) {
            if (intervalGrade) clearInterval(intervalGrade);
            return value as Grade;
          }
          return grades[currentIndex + 1] as Grade;
        });
        setProgressScore(targetValue);
      }, 100);
    }

    return () => {
      clearInterval(interval);
      if (interval2) clearInterval(interval2);
      if (intervalGrade) clearInterval(intervalGrade);
    };
  }, [value, score]);
  return (
    <div
      className={`${renderSize(size).box} flex flex-col items-center gap-4 ${
        onClick ? "cursor-pointer hover:opacity-80 transition-opacity" : ""
      }`}
      onClick={onClick}
    >
      {title && (
        <div className="text-[12px] lg:text-[14px] font-semibold text-secondary">
          {title}
        </div>
      )}
      <div className={`${renderSize(size).progress} relative`}>
        <CircularProgressbar
          value={progress}
          circleRatio={0.75}
          styles={{
            root: {
              overflow: "visible",
            },
            path: {
              stroke: renderColorProgress,
              strokeLinecap: "round",
              transition: "all 1.2s  ease-in-out 0s",
              transform: "rotate(0.63turn)",
              transformOrigin: "center center",
              strokeWidth: "12",
            },
            trail: {
              stroke: "var(--color-light-gray)",
              strokeLinecap: "round",
              transform: "rotate(0.63turn)",
              transformOrigin: "center center",
              strokeWidth: "5",
            },
          }}
        />
        <div
          className={`${
            renderSize(size).textBox
          } ${renderColorTextBox} bg-primary/10 rounded-full flex items-center justify-center text-primary font-black absolute inset-0 m-auto`}
        >
          {showGrade ? progressGrade : Math.round(progressScore)}
        </div>
      </div>

      {progressStates.length > 0 && (
        <div className="flex items-center gap-7 lg:gap-8 -mt-4">
          {progressStates.map((item, index) => (
            <ProgressState
              key={index}
              value={item.value}
              label={item.label}
              variant={renderColorProgress}
              isNoColor={item.isNoColor}
            />
          ))}
        </div>
      )}
    </div>
  );
}

function ProgressState({
  label,
  value,
  variant,
  isNoColor,
}: ProgressStateProps) {
  return (
    <div className="text-[9px] lg:text-[11px] font-medium flex items-center">
      <div
        className={`w-2 h-2 lg:w-[9px] lg:h-[9px] rounded-full`}
        style={{
          backgroundColor: !isNoColor ? variant : "var(--color-light-gray)",
        }}
      ></div>
      <div className="text-secondary/50 ml-2 mr-2 lg:mr-1">{label}</div>
      <div className="text-secondary">{abbreviateNumber(value)}</div>
    </div>
  );
}
