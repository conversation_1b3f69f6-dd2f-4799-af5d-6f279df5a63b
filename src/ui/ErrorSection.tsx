"use client";

import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import { motion } from "framer-motion";

interface ErrorSectionProps {
  message?: string;
  onRetry: () => void;
}

export default function ErrorSection({ message, onRetry }: ErrorSectionProps) {
  return (
    <motion.div
      className="flex flex-col items-center justify-center rounded-2xl border border-red-200 bg-red-50 p-6 shadow-sm w-full"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <AlertCircle className="mb-3 h-10 w-10 text-red-500/70" />
      <h2 className="text-lg font-semibold text-red-700/70">
        Something went wrong
      </h2>
      <p className="mb-4 text-center text-sm text-red-600/70">
        {message ?? "We couldn’t load your data. Try again?"}
      </p>
      <Button
        variant="destructive"
        onClick={onRetry}
        className="rounded-xl px-5 shadow bg-red-500/70"
      >
        Retry
      </Button>
    </motion.div>
  );
}
