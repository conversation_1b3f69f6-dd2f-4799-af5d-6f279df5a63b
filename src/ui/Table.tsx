import { ReactNode } from "react";

type TableProps = {
  children?: ReactNode;
};

export default function Table({ children }: TableProps) {
  return (
    <div className="overflow-hidden">
      <div className="relative overflow-x-auto custome-scrollbar max-w-full">
        <table className="w-full border-separate border-spacing-0 mt-4 border-spacing-y-2 table-auto">
          {children}
        </table>
      </div>
    </div>
  );
}

function TableHeader({
  children,
  className,
}: TableProps & { className?: string }) {
  return (
    <thead>
      <tr className={`${className}`}>{children}</tr>
    </thead>
  );
}

function TableBody({ children }: TableProps) {
  return <tbody>{children}</tbody>;
}

function TableRow({
  children,
  className,
}: TableProps & { className?: string }) {
  return (
    <tr className={`${className} h-[51px] group odd:bg-light-gray-6`}>
      {children}
    </tr>
  );
}

Table.Header = TableHeader;
Table.Body = TableBody;
Table.Row = TableRow;
