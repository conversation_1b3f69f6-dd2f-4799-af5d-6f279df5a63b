import { ComponentProps, useCallback, useState, useEffect, memo } from "react";

type InputProps = {
  label: string;
  name: string;
  onChange: (value: string) => void;
} & Omit<ComponentProps<"textarea">, "onChange">;

function RHTextAreaComponent({
  label,
  className = "",
  onChange,
  name,
  value,
  ...rest
}: InputProps) {
  // Local state to manage textarea value for smoother typing experience
  const [localValue, setLocalValue] = useState(value || "");

  // Update local value when prop value changes
  useEffect(() => {
    if (value !== undefined && value !== localValue) {
      setLocalValue(value as string);
    }
  }, [value]);

  // Optimized onChange handler without debounce for immediate response
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;

      // Update local state immediately for responsive UI
      setLocalValue(newValue);

      // Call parent onChange without debounce for immediate response
      onChange(newValue);
    },
    [onChange]
  );

  return (
    <div className="flex flex-col gap-2">
      <label htmlFor={name} className="text-secondary">
        {label}
      </label>
      <textarea
        name={name}
        {...rest}
        value={localValue}
        onChange={handleChange}
        className={`textField__input ${className}`}
        // Add performance attributes to prevent browser features from interfering
        data-lpignore="true" // Disable LastPass autofill
        autoComplete="off" // Prevent browser autofill
      ></textarea>
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(RHTextAreaComponent);
