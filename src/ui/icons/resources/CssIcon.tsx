import { SVGProps } from "react";

export function CssIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="65"
      height="64"
      viewBox="0 0 65 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M56 20.6257V52.5186C56 54.2993 55.2926 56.0071 54.0334 57.2663C52.7743 58.5254 51.0665 59.2328 49.2857 59.2328H45.9286V55.8757H49.2857C50.1761 55.8757 51.03 55.522 51.6596 54.8924C52.2892 54.2628 52.6429 53.4089 52.6429 52.5186V20.6257H45.9286C44.593 20.6257 43.3122 20.0952 42.3678 19.1508C41.4234 18.2064 40.8929 16.9255 40.8929 15.59V8.8757H22.4286C21.5382 8.8757 20.6843 9.2294 20.0547 9.85898C19.4251 10.4886 19.0714 11.3425 19.0714 12.2328V42.4471H15.7143V12.2328C15.7143 10.4521 16.4217 8.7443 17.6809 7.48512C18.94 6.22595 20.6478 5.51855 22.4286 5.51855H40.8929L56 20.6257ZM20.4042 55.3419C20.4234 55.8709 20.5531 56.39 20.785 56.8658C21.0168 57.3417 21.3457 57.7637 21.7504 58.1048C22.1869 58.4674 22.7218 58.7494 23.3551 58.9508C23.9908 59.1545 24.7349 59.2563 25.5876 59.2563C26.7224 59.2563 27.6836 59.0795 28.4714 58.7259C29.2637 58.3723 29.8669 57.8799 30.2809 57.2488C30.6995 56.6132 30.9087 55.8791 30.9087 55.0465C30.9087 54.2945 30.7588 53.6678 30.4589 53.1665C30.1517 52.6634 29.7175 52.25 29.1999 51.968C28.6059 51.6382 27.9652 51.4005 27.2998 51.263L25.215 50.7796C24.7246 50.6859 24.2613 50.4841 23.8587 50.1887C23.7055 50.0706 23.5821 49.9183 23.4981 49.744C23.4142 49.5698 23.3721 49.3783 23.3753 49.1849C23.3753 48.6612 23.5823 48.2315 23.9964 47.8958C24.4171 47.5556 24.9901 47.3855 25.7152 47.3855C26.1942 47.3855 26.6082 47.4616 26.9574 47.6138C27.2804 47.7417 27.565 47.951 27.7832 48.2214C27.9898 48.4706 28.1286 48.769 28.1861 49.0876H30.7039C30.6606 48.4042 30.4281 47.7463 30.0325 47.1874C29.6095 46.5837 29.0289 46.1076 28.3539 45.811C27.53 45.4479 26.6353 45.2736 25.7354 45.3007C24.7528 45.3007 23.8845 45.4686 23.1302 45.8043C22.376 46.1377 21.7862 46.6089 21.361 47.2176C20.9358 47.8286 20.7231 48.5437 20.7231 49.3628C20.7231 50.0387 20.8597 50.6251 21.1327 51.122C21.4102 51.6211 21.8041 52.0318 22.3144 52.3541C22.8247 52.6741 23.4279 52.9125 24.1239 53.0691L26.1986 53.5526C26.8925 53.7159 27.4106 53.9319 27.753 54.2005C27.9197 54.3286 28.053 54.4949 28.1418 54.6854C28.2307 54.8759 28.2723 55.0849 28.2633 55.2949C28.2709 55.6411 28.1712 55.9812 27.9779 56.2685C27.7616 56.5631 27.4641 56.7882 27.1219 56.9164C26.7481 57.0731 26.2859 57.1514 25.7354 57.1514C25.3437 57.1514 24.9856 57.1067 24.6611 57.0171C24.3635 56.9372 24.0818 56.8066 23.8285 56.6311C23.6053 56.4861 23.4141 56.2969 23.2669 56.0752C23.1197 55.8535 23.0194 55.6039 22.9724 55.3419H20.4042ZM11.7059 51.4879C11.7059 50.6531 11.82 49.9447 12.0483 49.3628C12.2478 48.8267 12.6003 48.3609 13.0621 48.0233C13.5318 47.7055 14.09 47.5445 14.6568 47.5634C15.1604 47.5634 15.6057 47.672 15.9929 47.8891C16.3713 48.0912 16.6871 48.3931 16.9061 48.7619C17.1401 49.1502 17.2782 49.5887 17.3089 50.041H19.8771V49.7993C19.8549 49.1809 19.7043 48.5739 19.435 48.0167C19.1657 47.4596 18.7835 46.9646 18.3127 46.563C17.8325 46.1512 17.2742 45.8407 16.6711 45.6498C16.0166 45.4256 15.3284 45.3155 14.6366 45.3242C13.4415 45.3242 12.422 45.5737 11.5783 46.0728C10.739 46.5697 10.0989 47.2769 9.658 48.1946C9.22157 49.1122 9.00224 50.2077 9 51.4812V53.1531C9 54.4243 9.21598 55.5165 9.64793 56.4296C10.0888 57.3405 10.7289 58.0411 11.5682 58.5312C12.4075 59.0169 13.4303 59.2597 14.6366 59.2597C15.6192 59.2597 16.4976 59.0762 17.272 58.7091C18.0464 58.3421 18.6619 57.834 19.1184 57.185C19.5816 56.5183 19.8448 55.7334 19.8771 54.9223V54.6671H17.3123C17.2805 55.0994 17.1446 55.5175 16.9161 55.8858C16.6924 56.2423 16.377 56.5321 16.003 56.7251C15.5827 56.9248 15.122 57.0247 14.6568 57.0171C14.0897 57.0322 13.531 56.8779 13.0521 56.574C12.5919 56.2471 12.2419 55.7882 12.0483 55.258C11.8029 54.5841 11.6867 53.87 11.7059 53.1531V51.4879ZM31.7614 56.8493C31.5406 56.3757 31.4141 55.8638 31.3888 55.3419H33.9536C34.0007 55.6039 34.1009 55.8535 34.2481 56.0752C34.3954 56.2969 34.5865 56.4861 34.8097 56.6311C35.047 56.7944 35.3267 56.922 35.649 57.0138C35.9668 57.1055 36.3238 57.1514 36.7199 57.1514C37.2683 57.1514 37.7304 57.0731 38.1064 56.9164C38.4487 56.7882 38.7462 56.5631 38.9625 56.2685C39.1558 55.9812 39.2554 55.6411 39.2479 55.2949C39.2565 55.0846 39.2143 54.8754 39.1249 54.6849C39.0355 54.4944 38.9015 54.3282 38.7342 54.2005C38.394 53.9319 37.8759 53.7159 37.1799 53.5526L35.1051 53.0725C34.4653 52.9384 33.8532 52.6954 33.2956 52.3541C32.8034 52.0516 32.3975 51.6272 32.1173 51.122C31.8361 50.5788 31.6942 49.9744 31.7044 49.3628C31.7044 48.5437 31.917 47.8286 32.3422 47.2176C32.7675 46.6089 33.3572 46.1366 34.1114 45.8009C34.8657 45.4652 35.7352 45.2985 36.7199 45.3007C37.7405 45.3007 38.6122 45.4708 39.3351 45.811C40.0648 46.1534 40.6243 46.6122 41.0137 47.1874C41.4166 47.7581 41.6404 48.3915 41.6851 49.0876H39.1673C39.1098 48.769 38.971 48.4706 38.7644 48.2214C38.5462 47.951 38.2617 47.7417 37.9386 47.6138C37.5452 47.451 37.1219 47.3732 36.6964 47.3855C35.9713 47.3855 35.3983 47.5556 34.9776 47.8958C34.7804 48.0469 34.6217 48.2424 34.5144 48.4664C34.407 48.6904 34.3541 48.9366 34.3599 49.1849C34.3599 49.5878 34.5199 49.9235 34.8399 50.1921C35.2428 50.4862 35.7061 50.6869 36.1962 50.7796L38.281 51.2596C39.0106 51.4275 39.644 51.6636 40.1811 51.968C40.7183 52.2724 41.1379 52.673 41.4401 53.1698C41.7422 53.6667 41.8933 54.2934 41.8933 55.0498C41.8933 55.8779 41.6829 56.612 41.2621 57.2521C40.8176 57.9112 40.188 58.4239 39.4526 58.7259C38.6671 59.0795 37.7069 59.2563 36.5722 59.2563C35.7195 59.2563 34.9753 59.1556 34.3397 58.9542C33.7549 58.7791 33.2099 58.4917 32.735 58.1082C32.3228 57.7643 31.9906 57.3347 31.7614 56.8493Z"
        fill="currentColor"
      />
    </svg>
  );
}
