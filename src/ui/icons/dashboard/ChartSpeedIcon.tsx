import React from 'react'

export default function ChartSpeedIcon() {
    return (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_2640_7139)">
                <path d="M14.6654 8.00016C14.6654 11.682 11.6806 14.6668 7.9987 14.6668C4.3168 14.6668 1.33203 11.682 1.33203 8.00016C1.33203 4.31826 4.3168 1.3335 7.9987 1.3335C11.6806 1.3335 14.6654 4.31826 14.6654 8.00016Z" stroke="currentColor" />
                <path d="M12.668 12.6665L11.668 11.6665" stroke="currentColor" strokeLinecap="round" />
                <path d="M12.668 3.3335L11.668 4.3335" stroke="currentColor" strokeLinecap="round" />
                <path d="M3.33203 12.6665L4.33203 11.6665" stroke="currentColor" strokeLinecap="round" />
                <path d="M3.33203 3.3335L4.33203 4.3335" stroke="currentColor" strokeLinecap="round" />
                <path d="M1.33203 8H2.66536" stroke="currentColor" strokeLinecap="round" />
                <path d="M13.332 8H14.6654" stroke="currentColor" strokeLinecap="round" />
                <path d="M8 2.66683V1.3335" stroke="currentColor" strokeLinecap="round" />
                <path d="M9.18423 9.57632C9.9653 8.79525 9.9653 7.52892 9.18423 6.74792C8.40323 5.96684 7.1369 5.96684 6.35583 6.74792C6.05873 7.04499 5.85124 7.66172 5.70817 8.31199C5.49422 9.28432 5.38725 9.77052 5.77445 10.1577C6.16166 10.5449 6.64783 10.4379 7.62017 10.224C8.27043 10.0809 8.88716 9.87339 9.18423 9.57632Z" stroke="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_2640_7139">
                    <rect width="16" height="16" fill="none" />
                </clipPath>
            </defs>
        </svg>
    )
}