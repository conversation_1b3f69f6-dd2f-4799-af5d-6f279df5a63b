import React from 'react'

export default function KeyDownIcon() {
    return (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_2640_7152)">
                <path d="M10.4525 9.72476C12.7792 9.72476 14.6654 7.8463 14.6654 5.52912C14.6654 3.21194 12.7792 1.3335 10.4525 1.3335C8.12583 1.3335 6.2397 3.21194 6.2397 5.52912C6.2397 6.60242 6.72956 7.38303 6.72956 7.38303L1.63497 12.4568C1.40636 12.6844 1.08632 13.2764 1.63497 13.8228L2.2228 14.4082C2.4514 14.6034 3.02616 14.8766 3.49643 14.4082L4.18224 13.7252C4.86805 14.4082 5.65183 14.018 5.94575 13.6276C6.43562 12.9446 5.84778 12.2616 5.84778 12.2616L6.04372 12.0665C6.98423 13.0032 7.80723 12.4568 8.10116 12.0665C8.59103 11.3835 8.10116 10.7005 8.10116 10.7005C7.90523 10.3102 7.51336 10.3102 8.00316 9.82236L8.59103 9.2369C9.0613 9.62716 10.028 9.72476 10.4525 9.72476Z" stroke="currentColor" strokeLinejoin="round" />
                <path d="M11.9235 5.52854C11.9235 6.33686 11.2656 6.99212 10.454 6.99212C9.64231 6.99212 8.98438 6.33686 8.98438 5.52854C8.98438 4.72022 9.64231 4.06494 10.454 4.06494C11.2656 4.06494 11.9235 4.72022 11.9235 5.52854Z" stroke="currentColor" />
            </g>
            <defs>
                <clipPath id="clip0_2640_7152">
                    <rect width="16" height="16" fill="none" />
                </clipPath>
            </defs>
        </svg>

    )
}