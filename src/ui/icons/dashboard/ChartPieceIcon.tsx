import React, { SVGProps } from 'react'

export default function ChartPieceIcon(props: SVGProps<SVGSVGElement>) {
    return (
        <svg {...props} width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_2640_7164)">
                <path d="M4.2949 2.45727C4.64613 2.22259 5.01714 2.02296 5.40255 1.86001C6.33782 1.46459 6.80543 1.26687 7.4021 1.66238C7.9987 2.05789 7.9987 2.70539 7.9987 4.00041V5.33374C7.9987 6.59081 7.9987 7.21934 8.38923 7.60987C8.77976 8.00041 9.4083 8.00041 10.6654 8.00041H11.9987C13.2937 8.00041 13.9412 8.00041 14.3367 8.59701C14.7322 9.19367 14.5345 9.66127 14.1391 10.5965C13.9762 10.9819 13.7765 11.3529 13.5418 11.7042C12.8093 12.8005 11.7681 13.655 10.5499 14.1596C9.33176 14.6642 7.9913 14.7962 6.6981 14.5389C5.40489 14.2817 4.217 13.6468 3.28466 12.7145C2.3523 11.7821 1.71737 10.5942 1.46013 9.30101C1.2029 8.00781 1.33492 6.66734 1.83951 5.44918C2.34409 4.23101 3.19857 3.18982 4.2949 2.45727Z" stroke="currentColor" />
                <path d="M9.66797 1.54346C12.0106 2.14642 13.855 3.99075 14.458 6.33343" stroke="currentColor" strokeLinecap="round" />
            </g>
            <defs>
                <clipPath id="clip0_2640_7164">
                    <rect width="16" height="16" fill="none" />
                </clipPath>
            </defs>
        </svg>

    )
}
