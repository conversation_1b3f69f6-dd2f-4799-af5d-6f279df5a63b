import { SVGProps } from "react";

export function LocationIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.38501 19.1406C9.38501 19.1406 3.33334 14.044 3.33334 9.02897C3.33334 7.26086 4.03572 5.56517 5.28596 4.31493C6.53621 3.06468 8.2319 2.3623 10 2.3623C11.7681 2.3623 13.4638 3.06468 14.7141 4.31493C15.9643 5.56517 16.6667 7.26086 16.6667 9.02897C16.6667 14.044 10.615 19.1406 10.615 19.1406C10.2783 19.4506 9.72418 19.4473 9.38501 19.1406ZM10 11.9456C10.383 11.9456 10.7623 11.8702 11.1162 11.7236C11.47 11.577 11.7916 11.3622 12.0624 11.0914C12.3332 10.8205 12.5481 10.499 12.6947 10.1451C12.8412 9.79127 12.9167 9.41199 12.9167 9.02897C12.9167 8.64595 12.8412 8.26668 12.6947 7.91281C12.5481 7.55895 12.3332 7.23741 12.0624 6.96658C11.7916 6.69574 11.47 6.4809 11.1162 6.33432C10.7623 6.18775 10.383 6.1123 10 6.1123C9.22646 6.1123 8.4846 6.4196 7.93762 6.96658C7.39063 7.51356 7.08334 8.25542 7.08334 9.02897C7.08334 9.80252 7.39063 10.5444 7.93762 11.0914C8.4846 11.6383 9.22646 11.9456 10 11.9456Z"
        fill="currentColor"
      />
    </svg>
  );
}
