import { SVGProps } from "react";

export function HotjarIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_204_3876)">
        <path
          d="M24 8.38278C24 12.8051 21.7824 15.1343 19.3774 16.6664L18.9101 16.9533L18.4414 17.2224L17.9746 17.4758L17.5129 17.7155L15.767 18.5864C15.6761 18.6325 15.5865 18.6785 15.4982 18.7244L14.9841 18.9996C13.1585 20.0126 11.9974 21.1061 11.8999 23.5813L11.8932 23.9275H6.72675C6.72675 19.6479 8.80406 17.3284 11.1178 15.7946L11.5832 15.4979C11.6611 15.4502 11.7391 15.4031 11.8172 15.3569L12.2859 15.0878L12.7526 14.8344L13.6675 14.3664L14.9601 13.7238L15.4899 13.4484C17.4608 12.3942 18.7253 11.3168 18.8272 8.72903L18.8338 8.38278H24ZM17.2736 0.0127786C17.2736 4.29195 15.1968 6.61153 12.883 8.14536L12.4177 8.44203C12.3397 8.48981 12.2617 8.53683 12.1836 8.58311L11.7148 8.85228L11.248 9.10561L10.333 9.57361L9.04041 10.2164L8.51053 10.4918C8.42484 10.5376 8.34047 10.5835 8.25741 10.6295L7.77637 10.9088C6.2295 11.8528 5.262 12.961 5.17331 15.2113L5.16656 15.5575H0C0 10.9925 2.36306 8.65811 4.85597 7.1282L5.32444 6.85045L5.79262 6.58936L6.25734 6.34303L8.23312 5.3537L8.763 5.0782C10.7339 4.02403 11.9983 2.94653 12.1002 0.358945L12.107 0.0126953L17.2736 0.0127786Z"
          fill="#FF3C00"
        />
      </g>
      <defs>
        <clipPath id="clip0_204_3876">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0 0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
