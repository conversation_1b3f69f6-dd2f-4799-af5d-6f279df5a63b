import { SVGProps } from "react";

export function XIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_204_3867)">
        <path
          d="M3.42857 0.000976562C1.5375 0.000976562 0 1.53848 0 3.42955V20.5724C0 22.4635 1.5375 24.001 3.42857 24.001H20.5714C22.4625 24.001 24 22.4635 24 20.5724V3.42955C24 1.53848 22.4625 0.000976562 20.5714 0.000976562H3.42857ZM19.3446 4.50098L13.7839 10.8545L20.325 19.501H15.2036L11.1964 14.2563L6.60536 19.501H4.06071L10.0071 12.7028L3.73393 4.50098H8.98393L12.6107 9.29562L16.8 4.50098H19.3446ZM17.3196 17.9795L8.21786 5.94205H6.70179L15.9054 17.9795H17.3143H17.3196Z"
          fill="#344054"
        />
      </g>
      <defs>
        <clipPath id="clip0_204_3867">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0 0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
