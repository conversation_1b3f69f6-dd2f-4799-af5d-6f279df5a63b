import { SVGProps } from "react";

export function LinkedinIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_204_3863)">
        <path
          d="M18.375 0.000976562H5.625C2.5184 0.000976562 0 2.51937 0 5.62598V18.376C0 21.4826 2.5184 24.001 5.625 24.001H18.375C21.4816 24.001 24 21.4826 24 18.376V5.62598C24 2.51937 21.4816 0.000976562 18.375 0.000976562Z"
          fill="white"
        />
        <path
          d="M18.375 0.000976562H5.625C2.5184 0.000976562 0 2.51937 0 5.62598V18.376C0 21.4826 2.5184 24.001 5.625 24.001H18.375C21.4816 24.001 24 21.4826 24 18.376V5.62598C24 2.51937 21.4816 0.000976562 18.375 0.000976562Z"
          fill="#0A66C2"
        />
        <path
          d="M17.317 20.4089H20.0611C20.1605 20.4089 20.2559 20.3694 20.3262 20.2991C20.3965 20.2288 20.4361 20.1335 20.4361 20.034L20.4375 14.2364C20.4375 11.2061 19.7845 8.87685 16.2433 8.87685C14.8972 8.82679 13.6277 9.52073 12.9427 10.6795C12.9393 10.6851 12.9342 10.6895 12.9281 10.6919C12.9221 10.6944 12.9154 10.6948 12.909 10.6931C12.9027 10.6914 12.8971 10.6877 12.8931 10.6825C12.8891 10.6773 12.8869 10.6709 12.8869 10.6644V9.5316C12.8869 9.43215 12.8474 9.33676 12.777 9.26644C12.7067 9.19611 12.6113 9.1566 12.5119 9.1566H9.90778C9.80832 9.1566 9.71294 9.19611 9.64262 9.26644C9.57229 9.33676 9.53278 9.43215 9.53278 9.5316V20.0335C9.53278 20.1329 9.57229 20.2283 9.64262 20.2986C9.71294 20.369 9.80832 20.4085 9.90778 20.4085H12.6517C12.7511 20.4085 12.8465 20.369 12.9168 20.2986C12.9871 20.2283 13.0267 20.1329 13.0267 20.0335V14.8423C13.0267 13.3744 13.3051 11.9529 15.1249 11.9529C16.9188 11.9529 16.942 13.6325 16.942 14.9373V20.0339C16.942 20.1334 16.9815 20.2288 17.0519 20.2991C17.1222 20.3694 17.2176 20.4089 17.317 20.4089ZM3.5625 5.5911C3.5625 6.70335 4.47816 7.61854 5.5905 7.61854C6.70256 7.61844 7.61766 6.7027 7.61766 5.59063C7.61747 4.47857 6.70228 3.56348 5.59012 3.56348C4.47769 3.56348 3.5625 4.47885 3.5625 5.5911ZM4.21491 20.4089H6.96244C7.06189 20.4089 7.15728 20.3694 7.2276 20.2991C7.29793 20.2288 7.33744 20.1334 7.33744 20.0339V9.5316C7.33744 9.43215 7.29793 9.33676 7.2276 9.26644C7.15728 9.19611 7.06189 9.1566 6.96244 9.1566H4.21491C4.11545 9.1566 4.02007 9.19611 3.94974 9.26644C3.87942 9.33676 3.83991 9.43215 3.83991 9.5316V20.0339C3.83991 20.1334 3.87942 20.2288 3.94974 20.2991C4.02007 20.3694 4.11545 20.4089 4.21491 20.4089Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_204_3863">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0 0.000976562)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
