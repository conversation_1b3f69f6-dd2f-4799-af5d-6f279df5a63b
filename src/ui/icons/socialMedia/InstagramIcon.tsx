import { SVGProps } from "react";

export function InstagramIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.99999 7.9873C9.2817 7.9873 8.59282 8.27265 8.08491 8.78056C7.577 9.28847 7.29166 9.97734 7.29166 10.6956C7.29166 11.4139 7.577 12.1028 8.08491 12.6107C8.59282 13.1186 9.2817 13.404 9.99999 13.404C10.7183 13.404 11.4072 13.1186 11.9151 12.6107C12.423 12.1028 12.7083 11.4139 12.7083 10.6956C12.7083 9.97734 12.423 9.28847 11.9151 8.78056C11.4072 8.27265 10.7183 7.9873 9.99999 7.9873Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.64167 3.26411C8.53837 2.94321 11.4616 2.94321 14.3583 3.26411C15.9408 3.44077 17.2167 4.68661 17.4025 6.27494C17.7458 9.21217 17.7458 12.1794 17.4025 15.1166C17.2167 16.7049 15.9408 17.9508 14.3592 18.1283C11.4622 18.4492 8.53865 18.4492 5.64167 18.1283C4.05917 17.9508 2.78334 16.7049 2.59751 15.1174C2.25416 12.1799 2.25416 9.21244 2.59751 6.27494C2.78334 4.68661 4.05917 3.44077 5.64167 3.26411ZM14.1667 5.69577C13.9457 5.69577 13.7337 5.78357 13.5774 5.93985C13.4211 6.09613 13.3333 6.30809 13.3333 6.52911C13.3333 6.75012 13.4211 6.96208 13.5774 7.11836C13.7337 7.27464 13.9457 7.36244 14.1667 7.36244C14.3877 7.36244 14.5997 7.27464 14.7559 7.11836C14.9122 6.96208 15 6.75012 15 6.52911C15 6.30809 14.9122 6.09613 14.7559 5.93985C14.5997 5.78357 14.3877 5.69577 14.1667 5.69577ZM6.04167 10.6958C6.04167 9.64596 6.45871 8.63914 7.20104 7.89681C7.94338 7.15448 8.95019 6.73744 10 6.73744C11.0498 6.73744 12.0566 7.15448 12.799 7.89681C13.5413 8.63914 13.9583 9.64596 13.9583 10.6958C13.9583 11.7456 13.5413 12.7524 12.799 13.4947C12.0566 14.2371 11.0498 14.6541 10 14.6541C8.95019 14.6541 7.94338 14.2371 7.20104 13.4947C6.45871 12.7524 6.04167 11.7456 6.04167 10.6958Z"
        fill="currentColor"
      />
    </svg>
  );
}
