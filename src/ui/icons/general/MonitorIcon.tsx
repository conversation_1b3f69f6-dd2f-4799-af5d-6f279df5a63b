import { ComponentProps } from "react";

type Props = {
  strokeColor?: string;
} & ComponentProps<"svg">;

export function MonitorIcon({ strokeColor, ...rest }: Props) {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M5.2408 21.8624V12.7068C5.2408 9.33576 7.97355 6.60303 11.3445 6.60303H29.6558C33.0268 6.60303 35.7595 9.33576 35.7595 12.7068V21.8624C35.7595 25.2334 33.0268 27.9661 29.6558 27.9661H11.3445C7.97355 27.9661 5.2408 25.2334 5.2408 21.8624Z"
        stroke="currentColor"
        strokeWidth="2.5"
      />
      <path
        d="M10.5659 34.0701H30.4337"
        stroke={strokeColor || "#914AC4"}
        strokeWidth="2.5"
        strokeLinecap="round"
      />
    </svg>
  );
}
