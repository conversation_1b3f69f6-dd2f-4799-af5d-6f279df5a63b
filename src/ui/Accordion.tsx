"use client";
import { useState } from "react";
import { CrossSmallIcon } from "./icons/general/CrossSmallIcon";

export default function Accordion({
  label,
  content,
  customClassName,
}: {
  label: string | React.ReactNode;
  content?: string | React.ReactNode;
  customClassName?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <div className={`overflow-hidden bg-white p-6 rounded-lg ${customClassName}`}>
      <div
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between text-secondary font-bold cursor-pointer"
      >
        <div className="flex-1">{label}</div>
        <button>
          <CrossSmallIcon
            className={`${isOpen ? "rotate-0" : "rotate-45"} duration-300`}
          />
        </button>
      </div>
      <div
        className={`${
          isOpen ? "max-h-screen mt-2.5" : "max-h-0"
        } overflow-hidden duration-300 ease-in-out text-sm text-secondary`}
      >
        {content}
      </div>
    </div>
  );
}
