import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { debounce, scheduleIdleWork } from "@/utils/performanceOptimizations";

// Types for the create project flow
export interface SearchEngineConfig {
  id: string;
  searchEngine: {
    name: string;
    image: string;
  };
  country: {
    name: string;
    code: string;
    image: string;
  };
  language: {
    name: string;
    code: string;
  };
  location: {
    name: string;
  } | null;
}

export interface ProjectInfo {
  name: string;
  domain: string;
  color?: string;
  domainType?: string;
  id?: string; // Project ID returned from API
}

export interface KeywordWithConfigs {
  keyword: string;
  configIds: string[]; // Array of configuration IDs this keyword is associated with
  isSuggested?: boolean; // Optional flag to indicate if keyword was AI-suggested
  search_volume?: number; // Optional search volume from API suggestions
  competition?: string; // Optional competition level from API suggestions
  cpc?: number; // Optional cost per click from API suggestions
  difficulty?: string; // Optional difficulty from API suggestions
  relevance_score?: number; // Optional relevance score from API suggestions
}

export interface Competitor {
  id: string;
  domain: string;
  searchEngines: string[]; // Array of search engine names (e.g., ["google"])
  countries: string[]; // Array of country codes (e.g., ["US"])
  isSuggested?: boolean; // Flag to indicate if competitor was AI-suggested
  title?: string; // Optional title from suggestion
  description?: string; // Optional description from suggestion
  relevanceScore?: number; // Optional relevance score from suggestion
  // Additional metrics from competitor suggestions API
  intersections?: number;
  avg_position?: number;
  sum_position?: number;
  organic_keywords?: number;
  organic_traffic?: number;
  organic_cost?: number;
  competitor_score?: number;
  strength_level?: string;
}

export interface StepCompletion {
  projectInformation: boolean;
  searchEngines: boolean;
  keywords: boolean;
  competitors: boolean;
  analyticsServices: boolean;
}

interface CreateProjectState {
  // Project basic information
  projectInfo: ProjectInfo | null;

  // Search engine configurations
  searchEngineConfigs: SearchEngineConfig[];

  // Keywords with their associated configurations
  keywords: KeywordWithConfigs[];

  // Competitors
  competitors: Competitor[];

  // Selected configurations for keywords page
  selectedConfigIds: string[];

  // Current step/page
  currentStep: string;

  // Step completion tracking
  stepCompletion: StepCompletion;

  // API state management
  isLoading: boolean;
  error: string | null;

  // Track changes for edit mode
  hasUnsavedChanges: boolean;
  originalProjectData: any | null;

  // Edit mode state tracking
  isEditMode: boolean;
  editProjectId: string | null;

  // Mode detection flags
  isCreateFromScratch: boolean; // Explicitly indicates fresh create mode
  lastModeTransition: number; // Timestamp of last mode change

  // Suggested keywords from API
  suggestedKeywords: string[];

  // Competitor suggestions state
  competitorSuggestions: Competitor[];
  isLoadingCompetitorSuggestions: boolean;
  competitorSuggestionsError: string | null;
  supportedLocations: Array<{
    code: string;
    name: string;
    primary_language: string;
  }>;

  // Actions
  setProjectInfo: (info: ProjectInfo) => void;
  addSearchEngineConfig: (config: Omit<SearchEngineConfig, "id">) => void;
  removeSearchEngineConfig: (id: string) => void;
  updateSearchEngineConfigs: (configs: SearchEngineConfig[]) => void;

  setSelectedConfigIds: (ids: string[]) => void;
  addKeyword: (
    keyword: string,
    configIds: string[],
    isSuggested?: boolean,
    additionalData?: {
      search_volume?: number;
      competition?: string;
      cpc?: number;
      difficulty?: string;
      relevance_score?: number;
    }
  ) => void;
  removeKeyword: (keyword: string) => void;
  updateKeywords: (keywords: KeywordWithConfigs[]) => void;

  // Competitor actions
  addCompetitor: (
    domain: string,
    searchEngines?: string[],
    countries?: string[],
    isSuggested?: boolean,
    title?: string,
    description?: string,
    relevanceScore?: number,
    intersections?: number,
    avg_position?: number,
    sum_position?: number,
    organic_keywords?: number,
    organic_traffic?: number,
    organic_cost?: number,
    competitor_score?: number,
    strength_level?: string
  ) => void;
  removeCompetitor: (id: string) => void;
  updateCompetitors: (competitors: Competitor[]) => void;
  validateCompetitors: () => boolean;

  // Competitor suggestions actions
  setCompetitorSuggestions: (suggestions: Competitor[]) => void;
  setLoadingCompetitorSuggestions: (loading: boolean) => void;
  setCompetitorSuggestionsError: (error: string | null) => void;
  setSupportedLocations: (
    locations: Array<{ code: string; name: string; primary_language: string }>
  ) => void;
  addSuggestedCompetitor: (suggestion: Competitor) => void;

  setCurrentStep: (step: string) => void;

  // API state actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSuggestedKeywords: (keywords: string[]) => void;

  // Step completion actions
  markStepComplete: (step: keyof StepCompletion) => void;
  markStepIncomplete: (step: keyof StepCompletion) => void;
  isStepComplete: (step: keyof StepCompletion) => boolean;
  canAccessStep: (step: string) => boolean;

  // Utility functions
  getConfigById: (id: string) => SearchEngineConfig | undefined;
  getKeywordsByConfigId: (configId: string) => string[];
  getTotalKeywordCount: () => number;
  getKeywordCountByConfig: (configId: string) => number;

  // Reset functions
  resetAll: () => void;
  resetFromStep: (step: string) => void;
  resetStore: () => void;

  // Load existing project data
  loadExistingProject: (projectData: any) => Promise<void>;

  // Change tracking
  markAsChanged: () => void;
  markAsSaved: () => void;
  hasDataChanged: () => boolean;

  // Edit mode management
  setEditMode: (isEditMode: boolean, projectId?: string) => void;
  clearEditMode: () => void;

  // Mode detection and management
  setCreateFromScratch: () => void;
  clearCreateFromScratch: () => void;
  isInCreateMode: () => boolean;
  isInEditMode: () => boolean;
}

const initialState = {
  projectInfo: null,
  searchEngineConfigs: [],
  keywords: [],
  competitors: [],
  selectedConfigIds: [],
  currentStep: "project-information",
  stepCompletion: {
    projectInformation: false,
    searchEngines: false,
    keywords: false,
    competitors: false,
    analyticsServices: false,
  },
  isLoading: false,
  error: null,
  hasUnsavedChanges: false,
  originalProjectData: null,
  isEditMode: false,
  editProjectId: null,
  isCreateFromScratch: false,
  lastModeTransition: 0,
  suggestedKeywords: [],
  competitorSuggestions: [],
  isLoadingCompetitorSuggestions: false,
  competitorSuggestionsError: null,
  supportedLocations: [],
};

export const useCreateProjectStore = create<CreateProjectState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Project info actions
        setProjectInfo: (info: ProjectInfo) => {
          set(
            { projectInfo: info, hasUnsavedChanges: true },
            false,
            "setProjectInfo"
          );
        },

        // Search engine config actions
        addSearchEngineConfig: (config: Omit<SearchEngineConfig, "id">) => {
          const id = `config-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
          const newConfig: SearchEngineConfig = { ...config, id };

          set(
            (state) => ({
              searchEngineConfigs: [...state.searchEngineConfigs, newConfig],
              hasUnsavedChanges: true,
            }),
            false,
            "addSearchEngineConfig"
          );
        },

        removeSearchEngineConfig: (id: string) => {
          set(
            (state) => ({
              searchEngineConfigs: state.searchEngineConfigs.filter(
                (config) => config.id !== id
              ),
              // Also remove this config from selected configs and keywords
              selectedConfigIds: state.selectedConfigIds.filter(
                (configId) => configId !== id
              ),
              keywords: state.keywords
                .map((keyword) => ({
                  ...keyword,
                  configIds: keyword.configIds.filter(
                    (configId) => configId !== id
                  ),
                }))
                .filter((keyword) => keyword.configIds.length > 0),
            }),
            false,
            "removeSearchEngineConfig"
          );
        },

        updateSearchEngineConfigs: (configs: SearchEngineConfig[]) => {
          set(
            { searchEngineConfigs: configs },
            false,
            "updateSearchEngineConfigs"
          );
        },

        // Selected configs actions
        setSelectedConfigIds: (ids: string[]) => {
          set({ selectedConfigIds: ids }, false, "setSelectedConfigIds");
        },

        // Keywords actions
        addKeyword: (
          keyword: string,
          configIds: string[],
          isSuggested?: boolean,
          additionalData?: {
            search_volume?: number;
            competition?: string;
            cpc?: number;
            difficulty?: string;
            relevance_score?: number;
          }
        ) => {
          set(
            (state) => {
              // Check if keyword already exists
              const existingKeywordIndex = state.keywords.findIndex(
                (k) => k.keyword === keyword
              );

              if (existingKeywordIndex >= 0) {
                // Update existing keyword with new config IDs
                const updatedKeywords = [...state.keywords];
                const existingConfigIds =
                  updatedKeywords[existingKeywordIndex].configIds;
                const mergedConfigIds = Array.from(
                  new Set([...existingConfigIds, ...configIds])
                );
                updatedKeywords[existingKeywordIndex] = {
                  ...updatedKeywords[existingKeywordIndex],
                  configIds: mergedConfigIds,
                  // Preserve existing isSuggested flag if not explicitly overriding
                  isSuggested:
                    isSuggested !== undefined
                      ? isSuggested
                      : updatedKeywords[existingKeywordIndex].isSuggested,
                  // Add additional data if provided
                  ...(additionalData && additionalData),
                };
                return { keywords: updatedKeywords };
              } else {
                // Add new keyword at the beginning (newest first)
                return {
                  keywords: [
                    {
                      keyword,
                      configIds,
                      isSuggested,
                      ...(additionalData && additionalData),
                    },
                    ...state.keywords,
                  ],
                };
              }
            },
            false,
            "addKeyword"
          );
        },

        removeKeyword: (keyword: string) => {
          set(
            (state) => ({
              keywords: state.keywords.filter((k) => k.keyword !== keyword),
            }),
            false,
            "removeKeyword"
          );
        },

        updateKeywords: (keywords: KeywordWithConfigs[]) => {
          set({ keywords }, false, "updateKeywords");
        },

        // Competitor actions
        addCompetitor: (
          domain: string,
          searchEngines?: string[],
          countries?: string[],
          isSuggested?: boolean,
          title?: string,
          description?: string,
          relevanceScore?: number,
          intersections?: number,
          avg_position?: number,
          sum_position?: number,
          organic_keywords?: number,
          organic_traffic?: number,
          organic_cost?: number,
          competitor_score?: number,
          strength_level?: string
        ) => {
          const id = `competitor-${Date.now()}-${Math.floor(
            Math.random() * 1000
          )}`;
          const newCompetitor: Competitor = {
            id,
            domain,
            searchEngines: searchEngines || ["google"],
            countries: countries || ["AU"],
            isSuggested,
            title,
            description,
            relevanceScore,
            intersections,
            avg_position,
            sum_position,
            organic_keywords,
            organic_traffic,
            organic_cost,
            competitor_score,
            strength_level,
          };

          set(
            (state) => ({
              competitors: [...state.competitors, newCompetitor],
            }),
            false,
            "addCompetitor"
          );
        },

        removeCompetitor: (id: string) => {
          set(
            (state) => ({
              competitors: state.competitors.filter(
                (competitor) => competitor.id !== id
              ),
            }),
            false,
            "removeCompetitor"
          );
        },

        updateCompetitors: (competitors: Competitor[]) => {
          set({ competitors }, false, "updateCompetitors");
        },

        validateCompetitors: () => {
          const state = get();
          return state.competitors.length > 0;
        },

        // Step management
        setCurrentStep: (step: string) => {
          set({ currentStep: step }, false, "setCurrentStep");
        },

        // API state actions
        setLoading: (loading: boolean) => {
          set({ isLoading: loading }, false, "setLoading");
        },

        setError: (error: string | null) => {
          set({ error }, false, "setError");
        },

        setSuggestedKeywords: (keywords: string[]) => {
          set({ suggestedKeywords: keywords }, false, "setSuggestedKeywords");
        },

        // Competitor suggestions actions
        setCompetitorSuggestions: (suggestions: Competitor[]) => {
          set(
            { competitorSuggestions: suggestions },
            false,
            "setCompetitorSuggestions"
          );
        },

        setLoadingCompetitorSuggestions: (loading: boolean) => {
          set(
            { isLoadingCompetitorSuggestions: loading },
            false,
            "setLoadingCompetitorSuggestions"
          );
        },

        setCompetitorSuggestionsError: (error: string | null) => {
          set(
            { competitorSuggestionsError: error },
            false,
            "setCompetitorSuggestionsError"
          );
        },

        setSupportedLocations: (
          locations: Array<{
            code: string;
            name: string;
            primary_language: string;
          }>
        ) => {
          set(
            { supportedLocations: locations },
            false,
            "setSupportedLocations"
          );
        },

        addSuggestedCompetitor: (suggestion: Competitor) => {
          set(
            (state) => ({
              competitors: [...state.competitors, suggestion],
            }),
            false,
            "addSuggestedCompetitor"
          );
        },

        // Step completion actions
        markStepComplete: (step: keyof StepCompletion) => {
          set(
            (state) => ({
              stepCompletion: {
                ...state.stepCompletion,
                [step]: true,
              },
            }),
            false,
            `markStepComplete-${step}`
          );
        },

        markStepIncomplete: (step: keyof StepCompletion) => {
          set(
            (state) => ({
              stepCompletion: {
                ...state.stepCompletion,
                [step]: false,
              },
            }),
            false,
            `markStepIncomplete-${step}`
          );
        },

        isStepComplete: (step: keyof StepCompletion) => {
          return get().stepCompletion[step];
        },

        canAccessStep: (step: string) => {
          const state = get();
          const {
            stepCompletion,
            projectInfo,
            searchEngineConfigs,
            keywords,
            isEditMode,
          } = state;

          // In edit mode, allow access to all steps
          if (isEditMode) {
            return true;
          }

          // In create mode, use step completion as the primary validation
          // This ensures that once a step is marked complete, the next step is accessible
          switch (step) {
            case "/create-project/project-information":
              return true; // Always accessible
            case "/create-project/search-engines":
              // Allow access if step is marked complete OR if we have project info
              return stepCompletion.projectInformation || !!projectInfo;
            case "/create-project/keywords":
              // Allow access if previous steps are complete OR if we have the required data
              return (
                stepCompletion.searchEngines ||
                (stepCompletion.projectInformation &&
                  searchEngineConfigs.length > 0) ||
                (!!projectInfo && searchEngineConfigs.length > 0)
              );
            case "/create-project/competitors":
              // Allow access if previous steps are complete OR if we have the required data
              return (
                stepCompletion.keywords ||
                (stepCompletion.searchEngines && keywords.length > 0) ||
                (stepCompletion.projectInformation &&
                  stepCompletion.searchEngines &&
                  keywords.length > 0) ||
                (!!projectInfo &&
                  searchEngineConfigs.length > 0 &&
                  keywords.length > 0)
              );
            case "/create-project/analytics-services":
              // Allow access if previous steps are complete OR if we have the required data
              return (
                stepCompletion.competitors ||
                stepCompletion.keywords ||
                (stepCompletion.projectInformation &&
                  stepCompletion.searchEngines &&
                  stepCompletion.keywords) ||
                (!!projectInfo &&
                  searchEngineConfigs.length > 0 &&
                  keywords.length > 0)
              );
            default:
              return false;
          }
        },

        // Utility functions
        getConfigById: (id: string) => {
          return get().searchEngineConfigs.find((config) => config.id === id);
        },

        getKeywordsByConfigId: (configId: string) => {
          return get()
            .keywords.filter((keyword) => keyword.configIds.includes(configId))
            .map((keyword) => keyword.keyword);
        },

        getTotalKeywordCount: () => {
          return get().keywords.length;
        },

        getKeywordCountByConfig: (configId: string) => {
          return get().keywords.filter((keyword) =>
            keyword.configIds.includes(configId)
          ).length;
        },

        // Reset functions
        resetAll: () => {
          // Clear the persisted store data from localStorage
          if (typeof window !== "undefined") {
            try {
              localStorage.removeItem("create-project-store");
            } catch (error) {
              console.warn(
                "Failed to clear create-project localStorage:",
                error
              );
            }
          }

          // Reset the store state
          set(initialState, false, "resetAll");
        },

        resetFromStep: (step: string) => {
          const state = get();
          switch (step) {
            case "project-information":
              set(initialState, false, "resetFromProjectInfo");
              break;
            case "search-engines":
              set(
                {
                  ...state,
                  searchEngineConfigs: [],
                  keywords: [],
                  competitors: [],
                  selectedConfigIds: [],
                  currentStep: "search-engines",
                  stepCompletion: {
                    ...state.stepCompletion,
                    searchEngines: false,
                    keywords: false,
                    competitors: false,
                    analyticsServices: false,
                  },
                },
                false,
                "resetFromSearchEngines"
              );
              break;
            case "keywords":
              set(
                {
                  ...state,
                  keywords: [],
                  competitors: [],
                  selectedConfigIds: [],
                  currentStep: "keywords",
                  stepCompletion: {
                    ...state.stepCompletion,
                    keywords: false,
                    competitors: false,
                    analyticsServices: false,
                  },
                },
                false,
                "resetFromKeywords"
              );
              break;
            case "competitors":
              set(
                {
                  ...state,
                  competitors: [],
                  currentStep: "competitors",
                  stepCompletion: {
                    ...state.stepCompletion,
                    competitors: false,
                    analyticsServices: false,
                  },
                },
                false,
                "resetFromCompetitors"
              );
              break;
            default:
              break;
          }
        },

        // Reset store to initial state with proper mode handling
        resetStore: (preserveCreateMode: boolean = false) => {
          const currentState = get();
          const newState = {
            ...initialState,
            // Preserve create mode flag if explicitly requested
            isCreateFromScratch: preserveCreateMode ? true : false,
            lastModeTransition: Date.now(),
          };
          set(newState, false, "resetStore");
        },

        // Change tracking methods (optimized with debouncing)
        markAsChanged: debounce(() => {
          set({ hasUnsavedChanges: true }, false, "markAsChanged");
        }, 100), // Debounce rapid changes

        markAsSaved: () => {
          const state = get();
          set(
            {
              hasUnsavedChanges: false,
              originalProjectData: {
                projectInfo: state.projectInfo,
                searchEngineConfigs: state.searchEngineConfigs,
                keywords: state.keywords,
                competitors: state.competitors,
              },
            },
            false,
            "markAsSaved"
          );
        },

        hasDataChanged: () => {
          const state = get();
          if (!state.originalProjectData) return state.hasUnsavedChanges;

          // Quick shallow comparison first to avoid expensive deep comparison
          if (state.hasUnsavedChanges) return true;

          // Use utility function for better comparison
          const currentData = {
            projectInfo: state.projectInfo,
            searchEngineConfigs: state.searchEngineConfigs,
            keywords: state.keywords,
            competitors: state.competitors,
          };

          // Import the utility function dynamically but cache it
          const {
            hasProjectDataChanged,
          } = require("@/utils/projectDataTransform");
          return hasProjectDataChanged(currentData, state.originalProjectData);
        },

        // Edit mode management
        setEditMode: (isEditMode: boolean, projectId?: string) => {
          set(
            {
              isEditMode,
              editProjectId: projectId || null,
            },
            false,
            "setEditMode"
          );
        },

        clearEditMode: () => {
          set(
            {
              isEditMode: false,
              editProjectId: null,
              lastModeTransition: Date.now(),
            },
            false,
            "clearEditMode"
          );
        },

        // Mode detection and management
        setCreateFromScratch: () => {
          set(
            {
              isCreateFromScratch: true,
              isEditMode: false,
              editProjectId: null,
              lastModeTransition: Date.now(),
            },
            false,
            "setCreateFromScratch"
          );
        },

        clearCreateFromScratch: () => {
          set(
            {
              isCreateFromScratch: false,
              lastModeTransition: Date.now(),
            },
            false,
            "clearCreateFromScratch"
          );
        },

        isInCreateMode: () => {
          const state = get();
          return (
            state.isCreateFromScratch &&
            !state.isEditMode &&
            !state.editProjectId
          );
        },

        isInEditMode: () => {
          const state = get();
          return state.isEditMode && !!state.editProjectId;
        },

        // Load existing project data (optimized for performance)
        loadExistingProject: async (projectData: any) => {
          const state = get();

          // Use utility function to transform data
          const { transformApiToStore } = await import(
            "@/utils/projectDataTransform"
          );

          // Schedule data transformation during idle time to avoid blocking animations
          return new Promise((resolve) => {
            scheduleIdleWork(() => {
              const transformedData = transformApiToStore(projectData);

              // In edit mode, preserve the current step if it's valid
              // Only reset to project-information if we're starting fresh or current step is invalid
              const preserveCurrentStep =
                state.currentStep &&
                state.currentStep !== "project-information" &&
                [
                  "project-information",
                  "search-engines",
                  "keywords",
                  "competitors",
                  "analytics-services",
                ].includes(state.currentStep);

              // Update store with transformed data
              set(
                {
                  ...state,
                  ...transformedData,
                  // Preserve current step in edit mode, otherwise start from beginning
                  currentStep: preserveCurrentStep
                    ? state.currentStep
                    : "project-information",
                  hasUnsavedChanges: false,
                  // Set edit mode state
                  isEditMode: true,
                  editProjectId: projectData.id,
                  originalProjectData: {
                    projectInfo: transformedData.projectInfo,
                    searchEngineConfigs: transformedData.searchEngineConfigs,
                    keywords: transformedData.keywords,
                    competitors: transformedData.competitors,
                  },
                },
                false,
                "loadExistingProject"
              );

              resolve(transformedData);
            });
          });
        },
      }),
      {
        name: "create-project-store",
        version: 1,
      }
    ),
    {
      name: "create-project-store",
    }
  )
);
