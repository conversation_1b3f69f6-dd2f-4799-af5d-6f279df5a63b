import { create } from "zustand";

type CompetitorPopupState = {
  isVisible: boolean;
  show: () => void;
  hide: () => void;
  toggleShow: (prev: boolean) => void;
};

export const useCompetitorPopupStore = create<CompetitorPopupState>((set) => ({
  isVisible: false,
  show: () => set({ isVisible: true }),
  hide: () => set({ isVisible: false }),
  toggleShow: () => set((state) => ({ isVisible: !state.isVisible })),
}));
