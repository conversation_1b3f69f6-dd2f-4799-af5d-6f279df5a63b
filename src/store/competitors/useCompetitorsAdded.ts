import { create } from "zustand";
import { persist } from "zustand/middleware";
import type { SelectedCompetitors } from "@/app/(dashboard)/project/[projectId]/competitors/types/Competitors.type";

type CompetitorState = {
  addedCompetitors: SelectedCompetitors[];
  addCompetitor: (competitor: SelectedCompetitors) => void;
  removeCompetitor: (index: number) => void;
  setCompetitors: (competitors: SelectedCompetitors[]) => void;
  resetCompetitors: () => void;
};

export const useCompetitorsStore = create<CompetitorState>()(
  persist(
    (set) => ({
      addedCompetitors: [],

      addCompetitor: (competitor) =>
        set((state) => ({
          addedCompetitors: [...state.addedCompetitors, competitor],
        })),

      removeCompetitor: (index) =>
        set((state) => ({
          addedCompetitors: state.addedCompetitors.filter(
            (_, i) => i !== index
          ),
        })),

      setCompetitors: (competitors) =>
        set({
          addedCompetitors: competitors,
        }),

      resetCompetitors: () => set({ addedCompetitors: [] }),
    }),
    {
      name: "competitors-storage",
      partialize: (state) => ({ addedCompetitors: state.addedCompetitors }),
    }
  )
);
