import { create } from "zustand";
import { persist } from "zustand/middleware";
import { ProjectResponse } from "@/services/projectService";

interface ProjectThemeColorState {
  themeColor: string;
  currentProject: ProjectResponse | null;
  setThemeColor: (color: string) => void;
  setCurrentProject: (project: ProjectResponse | null) => void;
  updateProjectColor: (projectId: string, color: string) => void;
}

export const useProjectThemeColor = create<ProjectThemeColorState>()(
  persist(
    (set, get) => ({
      themeColor: "#914AC4",
      currentProject: null,
      setThemeColor: (color: string) => set({ themeColor: color }),
      setCurrentProject: (project: ProjectResponse | null) => {
        set({
          currentProject: project,
          themeColor: project?.project_color || "#914AC4",
        });
      },
      updateProjectColor: (projectId: string, color: string) => {
        const { currentProject } = get();
        if (currentProject && currentProject.id === projectId) {
          set({
            currentProject: { ...currentProject, project_color: color },
            themeColor: color,
          });
        }
      },
    }),
    {
      name: "project-theme-color",
    }
  )
);
