import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import authService from "@/services/authService";
import profileService, { UserProfile } from "@/services/profileService";
import storageService from "@/services/storageService";
import authErrorHandler from "@/utils/authErrorHandler";
import errorHand<PERSON>, { AppError, ErrorType } from "@/utils/errorHandler";

// Enhanced error type for better error handling
interface AuthError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}

interface LoginData {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  confirm_password: string;
  first_name: string;
  last_name: string;
}

interface VerifyEmailData {
  email: string;
  otp: string;
}

interface AuthState {
  // User state
  user: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  formattedError: AppError | null;
  errors: AuthError[]; // Array of structured errors

  // Auth check state
  lastAuthCheck: number | null; // Timestamp of last successful auth check
  isCheckingAuth: boolean; // Flag to prevent concurrent auth checks
  authCheckPromise: Promise<boolean> | null; // Store the current auth check promise

  // Modal state - REMOVED: Using centralized auth provider instead

  // Error handling
  handleError: (error: any) => void;
  clearErrors: () => void;

  // No session timer management - rely on token expiry

  // Authentication actions
  login: (data: LoginData) => Promise<boolean>;
  register: (data: RegisterData) => Promise<{
    success: boolean;
    email?: string;
    fieldErrors?: Record<string, string[]> | null;
  }>;
  verifyEmail: (data: VerifyEmailData) => Promise<boolean>;
  resendOtp: (email: string) => Promise<boolean>;
  logout: () => void;
  checkAuth: (force?: boolean) => Promise<boolean>; // New centralized auth check
  fetchProfile: (force?: boolean) => Promise<boolean>; // Kept for backward compatibility

  // Modal actions - REMOVED: Using centralized auth provider instead

  // Utility
  requireLogin: (callback: () => void) => boolean;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state - check if we have a token to set initial loading state
        user: null,
        isAuthenticated: false,
        isLoading:
          typeof window !== "undefined" && storageService.getToken()
            ? true
            : false,
        error: null,
        formattedError: null,
        errors: [], // Initialize empty errors array

        // Auth check state
        lastAuthCheck: null,
        isCheckingAuth: false,
        authCheckPromise: null,

        // Modal state - REMOVED: Using centralized auth provider instead

        // Error handling
        handleError: (error: any) => {
          console.error("Auth error:", error);

          // Process the error into a structured format
          const processedErrors = errorHandler.processApiError(error);
          const errorMessage = processedErrors.message || "An error occurred";

          // Update state with error information
          set({
            error: errorMessage,
            formattedError: errorHandler.formatAuthError(error),
            errors: Array.isArray(processedErrors)
              ? processedErrors
              : [processedErrors],
          });

          // Check if it's an auth error (401)
          if (error.response?.status === 401) {
            authErrorHandler.notifyAuthError();
          }
        },

        clearErrors: () => {
          set({ error: null, formattedError: null, errors: [] });
        },

        // No session timer management - rely on token expiry and 401 errors

        // Actions
        login: async (data: LoginData) => {
          set({ isLoading: true });
          get().clearErrors();

          try {
            const response = await authService.login(data);

            if (
              response.statusCode &&
              response.statusCode >= 200 &&
              response.statusCode < 300
            ) {
              console.log("Login successful, fetching user profile...");

              // Force fetch user profile after successful login to ensure we have the latest data
              const profileResult = await get().checkAuth(true);

              if (profileResult) {
                console.log("Profile fetched successfully after login");
                set({
                  isAuthenticated: true,
                });
                return true;
              } else {
                console.error("Failed to fetch profile after successful login");
                // Even if profile fetch fails, we still consider login successful
                // since the token was obtained successfully
                set({
                  isAuthenticated: true,
                });
                return true;
              }
            } else {
              // Handle error
              get().handleError(response);
              return false;
            }
          } catch (error) {
            // Handle error
            get().handleError(error);
            return false;
          } finally {
            set({ isLoading: false });
          }
        },

        register: async (data: RegisterData) => {
          set({ isLoading: true });
          get().clearErrors();

          try {
            const response = await authService.register(data);

            if (
              response.statusCode &&
              response.statusCode >= 200 &&
              response.statusCode < 300
            ) {
              return { success: true, email: data.email };
            } else {
              // Handle error
              get().handleError(response);
              // Return field errors if they exist
              return {
                success: false,
                fieldErrors: response.fieldErrors || null,
              };
            }
          } catch (error) {
            // Handle error
            get().handleError(error);
            return { success: false };
          } finally {
            set({ isLoading: false });
          }
        },

        verifyEmail: async (data: VerifyEmailData) => {
          set({ isLoading: true });
          get().clearErrors();

          try {
            const response = await authService.verifyEmail(data);

            if (
              response.statusCode &&
              response.statusCode >= 200 &&
              response.statusCode < 300
            ) {
              console.log(
                "Email verification successful, fetching user profile..."
              );

              // Force fetch user profile after successful verification
              const profileResult = await get().checkAuth(true);

              if (profileResult) {
                console.log(
                  "Profile fetched successfully after email verification"
                );
                set({
                  isAuthenticated: true,
                });
                return true;
              } else {
                console.error(
                  "Failed to fetch profile after successful email verification"
                );
                // Even if profile fetch fails, we still consider verification successful
                set({
                  isAuthenticated: true,
                });
                return true;
              }
            } else {
              // Handle error
              get().handleError(response);
              return false;
            }
          } catch (error) {
            // Handle error
            get().handleError(error);
            return false;
          } finally {
            set({ isLoading: false });
          }
        },

        logout: () => {
          // Clear all authentication state immediately (optimistic)
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            lastAuthCheck: null,
            isCheckingAuth: false,
            authCheckPromise: null,
          });

          // Clear errors
          get().clearErrors();

          // Clear any browser storage related to auth
          if (typeof window !== "undefined") {
            // Clear session storage
            sessionStorage.removeItem("postLoginRedirect");
            sessionStorage.removeItem("postLoginCallback");
            sessionStorage.removeItem("auth-storage");

            // Clear localStorage auth data
            localStorage.removeItem("auth-storage");

            // Clear any cached query data that might contain user info
            try {
              // Clear React Query cache if available
              if (window.queryClient) {
                window.queryClient.clear();
              }
            } catch (error) {
              console.log("Query client not available for clearing");
            }
          }

          // Call authService logout (now optimistic - doesn't block)
          authService.logout();
        },

        // Centralized auth check function (renamed from fetchProfile for clarity)
        checkAuth: async (force = false) => {
          const state = get();

          // Check if we're already checking auth to prevent duplicate calls
          if (state.isCheckingAuth && state.authCheckPromise) {
            console.log(
              "Auth check already in progress, returning existing promise"
            );
            return state.authCheckPromise;
          }

          // Check if we have a recent auth check (within last 30 seconds) and not forcing refresh
          const now = Date.now();
          const AUTH_CHECK_THRESHOLD = 30000; // 30 seconds

          if (
            !force &&
            state.lastAuthCheck &&
            now - state.lastAuthCheck < AUTH_CHECK_THRESHOLD
          ) {
            console.log(
              "Using cached auth status, last check was",
              Math.round((now - state.lastAuthCheck) / 1000),
              "seconds ago"
            );
            return state.isAuthenticated;
          }

          // Set loading state and mark that we're checking auth
          set({
            isLoading: true,
            isCheckingAuth: true,
          });

          // Create a promise for this auth check
          const authCheckPromise = (async () => {
            try {
              console.log("Performing auth check");
              const isAuth = await authService.isAuthenticated();

              if (!isAuth) {
                console.log("Auth check failed: Token is invalid or expired");
                set({
                  isAuthenticated: false,
                  user: null,
                  isLoading: false,
                  isCheckingAuth: false,
                  lastAuthCheck: now,
                });
                return false;
              }

              console.log("Auth token is valid, fetching user profile data...");
              const response = await profileService.getUserProfile();

              if (response.success && response.data) {
                console.log("Profile data fetched successfully:", {
                  email: response.data.email,
                  name: `${response.data.first_name} ${response.data.last_name}`,
                  isVerified: response.data.is_verified,
                  subscriptions: response.data.subscriptions?.length || 0,
                });

                set({
                  user: response.data,
                  isAuthenticated: true,
                  isLoading: false,
                  isCheckingAuth: false,
                  lastAuthCheck: now,
                });
                return true;
              } else {
                console.error(
                  "Failed to fetch profile data:",
                  response.error || "Unknown error"
                );
                set({
                  isAuthenticated: false,
                  user: null,
                  isLoading: false,
                  isCheckingAuth: false,
                  lastAuthCheck: now,
                });
                return false;
              }
            } catch (error) {
              console.error("Error during auth check:", error);
              get().handleError(error);
              set({
                isAuthenticated: false,
                user: null,
                isLoading: false,
                isCheckingAuth: false,
                lastAuthCheck: now,
              });
              return false;
            }
          })();

          // Store the promise in state
          set({ authCheckPromise });

          // Return the promise
          return authCheckPromise;
        },

        // Keep fetchProfile for backward compatibility
        fetchProfile: async (force = false) => {
          return get().checkAuth(force);
        },

        // Background refresh that doesn't trigger loading states
        backgroundRefresh: async () => {
          const state = get();

          // Don't refresh if already checking auth or no token
          if (state.isCheckingAuth || !storageService.getToken()) {
            return false;
          }

          try {
            console.log("Performing background profile refresh...");
            const response = await profileService.getUserProfile();

            if (response.success && response.data) {
              // Update user data without changing loading states
              set({
                user: response.data,
                isAuthenticated: true,
                lastAuthCheck: Date.now(),
              });
              return true;
            } else {
              // If background refresh fails, don't logout user
              // They can still use cached data
              console.warn("Background refresh failed, keeping cached data");
              return false;
            }
          } catch (error) {
            console.error("Background refresh error:", error);
            return false;
          }
        },

        // Resend OTP verification code
        resendOtp: async (email: string) => {
          set({ isLoading: true });
          get().clearErrors();

          try {
            const response = await authService.resendOtp({ email });

            if (
              response.statusCode &&
              response.statusCode >= 200 &&
              response.statusCode < 300
            ) {
              return true;
            } else {
              // Handle error
              get().handleError(response);
              return false;
            }
          } catch (error) {
            // Handle error
            get().handleError(error);
            return false;
          } finally {
            set({ isLoading: false });
          }
        },

        // Modal actions - REMOVED: Using centralized auth provider instead

        // checkAuthAndShowModal and handleAuthSuccess - REMOVED: Using centralized auth provider instead

        // Utility
        requireLogin: (callback) => {
          const { isAuthenticated } = get();

          if (isAuthenticated) {
            callback();
            return true;
          } else {
            // Store the callback and redirect to login
            if (typeof window !== "undefined") {
              sessionStorage.setItem(
                "postLoginCallback",
                JSON.stringify({
                  actionType: "general",
                  timestamp: Date.now(),
                })
              );
              window.location.href = "/login";
            }
            return false;
          }
        },
      }),
      {
        name: "auth-storage",
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          lastAuthCheck: state.lastAuthCheck,
        }),
      }
    )
  )
);

// Auth error handler initialization - REMOVED: Using centralized auth provider instead
// The auth error handler now redirects to login page via the Providers component
