/**
 * Manual API Integration Test Script
 * 
 * This script can be used to manually test the API integration
 * without running the full test suite. Useful for debugging
 * and verifying API endpoints are working correctly.
 */

import { projectAPI, CreateProjectRequest, FullProjectRequest } from '../services/projectService';

// Test data
const testProjectData: CreateProjectRequest = {
  url: 'https://example-test.com',
  domain_type: '*.example-test.com',
  project_name: 'API Integration Test Project',
  project_color: '#FF6B35',
};

const testFullProjectData: FullProjectRequest = {
  url: 'https://example-test.com',
  domain_type: '*.example-test.com',
  project_name: 'API Integration Test Project',
  project_color: '#FF6B35',
  primary_search_engines: [
    {
      search_engine: 'google',
      countries: ['US'],
      languages: ['en'],
    },
  ],
  keywords: [
    {
      keyword: 'test keyword',
      search_engines: ['google'],
      countries: ['US'],
      languages: ['en'],
    },
  ],
  competitors: [],
};

/**
 * Test the complete API integration flow
 */
export async function testAPIIntegration() {
  console.log('🚀 Starting API Integration Test...\n');

  try {
    // Step 1: Test simple project creation
    console.log('📝 Step 1: Testing simple project creation...');
    const createResponse = await projectAPI.createProject(testProjectData);
    console.log('✅ Project created successfully:', createResponse.data.id);
    
    const projectId = createResponse.data.id;

    // Step 2: Test keyword extraction
    console.log('\n🔍 Step 2: Testing keyword extraction...');
    try {
      const keywordsResponse = await projectAPI.extractKeywords({ project_id: projectId });
      console.log('✅ Keywords extracted:', keywordsResponse.data.keywords.length, 'keywords found');
    } catch (error) {
      console.log('⚠️ Keyword extraction failed (this might be expected if the endpoint is not ready)');
    }

    // Step 3: Test project update
    console.log('\n📝 Step 3: Testing project update...');
    const updateData = {
      ...testFullProjectData,
      project_name: 'Updated Test Project',
      status: 'enabled' as const,
    };
    
    try {
      const updateResponse = await projectAPI.updateProject(projectId, updateData);
      console.log('✅ Project updated successfully');
    } catch (error) {
      console.log('⚠️ Project update failed, trying full project creation instead...');
      
      // Step 4: Test full project creation (alternative to update)
      console.log('\n🎯 Step 4: Testing full project creation...');
      const fullProjectResponse = await projectAPI.createFullProject(testFullProjectData);
      console.log('✅ Full project created successfully:', fullProjectResponse.data.id);
    }

    // Step 5: Test project retrieval
    console.log('\n📖 Step 5: Testing project retrieval...');
    try {
      const getResponse = await projectAPI.getProject(projectId);
      console.log('✅ Project retrieved successfully:', getResponse.data.project_name);
    } catch (error) {
      console.log('⚠️ Project retrieval failed');
    }

    // Step 6: Test partial update
    console.log('\n🔧 Step 6: Testing partial project update...');
    try {
      const partialUpdateResponse = await projectAPI.partialUpdateProject(projectId, {
        project_name: 'Partially Updated Test Project',
      });
      console.log('✅ Project partially updated successfully');
    } catch (error) {
      console.log('⚠️ Partial update failed');
    }

    console.log('\n🎉 API Integration Test completed successfully!');
    return true;

  } catch (error: any) {
    console.error('\n❌ API Integration Test failed:');
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    return false;
  }
}

/**
 * Test individual API endpoints
 */
export async function testIndividualEndpoints() {
  console.log('🔧 Testing individual API endpoints...\n');

  const tests = [
    {
      name: 'Create Project',
      test: () => projectAPI.createProject(testProjectData),
    },
    {
      name: 'Create Full Project',
      test: () => projectAPI.createFullProject(testFullProjectData),
    },
  ];

  for (const { name, test } of tests) {
    try {
      console.log(`Testing ${name}...`);
      const result = await test();
      console.log(`✅ ${name} passed`);
    } catch (error: any) {
      console.log(`❌ ${name} failed:`, error.message);
    }
  }
}

/**
 * Validate API request/response formats
 */
export function validateAPIFormats() {
  console.log('🔍 Validating API request/response formats...\n');

  // Validate CreateProjectRequest format
  const createRequestKeys = Object.keys(testProjectData);
  const expectedCreateKeys = ['url', 'domain_type', 'project_name', 'project_color'];
  
  console.log('Create Project Request validation:');
  expectedCreateKeys.forEach(key => {
    if (createRequestKeys.includes(key)) {
      console.log(`✅ ${key}: present`);
    } else {
      console.log(`❌ ${key}: missing`);
    }
  });

  // Validate FullProjectRequest format
  const fullRequestKeys = Object.keys(testFullProjectData);
  const expectedFullKeys = [
    'url', 'domain_type', 'project_name', 'project_color',
    'primary_search_engines', 'keywords', 'competitors'
  ];
  
  console.log('\nFull Project Request validation:');
  expectedFullKeys.forEach(key => {
    if (fullRequestKeys.includes(key)) {
      console.log(`✅ ${key}: present`);
    } else {
      console.log(`❌ ${key}: missing`);
    }
  });

  console.log('\n🎯 API format validation completed');
}

// Export for use in other files
export default {
  testAPIIntegration,
  testIndividualEndpoints,
  validateAPIFormats,
};
