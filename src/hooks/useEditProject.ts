import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { projectAPI } from "@/services/projectService";
import { useCreateProjectStore } from "@/store/createProjectStore";
import { useEffect, useMemo, useCallback } from "react";
import { modeValidation } from "@/utils/performanceOptimizations";

/**
 * Custom hook to manage edit project state and data sharing
 * Provides consistent edit mode detection and project data across all pages
 */
export function useEditProject() {
  const searchParams = useSearchParams();
  const {
    projectInfo,
    loadExistingProject,
    resetStore,
    setEditMode,
    clearEditMode,
    isEditMode: storeEditMode,
    editProjectId: storeEditProjectId,
    setCreateFromScratch,
    clearCreateFromScratch,
    isInCreateMode,
    isInEditMode,
  } = useCreateProjectStore();

  // Get project ID and mode from URL parameters
  const editProjectId = searchParams?.get("project_id");
  const modeParam = searchParams?.get("mode");
  const isEditMode = !!editProjectId;

  // Fetch existing project data when in edit mode
  const {
    data: existingProject,
    isLoading: isLoadingProject,
    error: projectError,
    isSuccess: isProjectLoaded,
  } = useQuery({
    queryKey: ["project", editProjectId],
    queryFn: () => projectAPI.getProject(editProjectId!),
    enabled: !!editProjectId,
    staleTime: 1000 * 60 * 30, // 30 minutes - longer cache for edit mode
    gcTime: 1000 * 60 * 60, // 1 hour - keep in cache longer
    refetchOnWindowFocus: false, // Don't refetch when window gains focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    retry: 2, // Retry failed requests twice
  });

  // Sync edit mode state with URL parameters
  useEffect(() => {
    if (editProjectId && !storeEditMode) {
      // URL has project_id but store doesn't know about edit mode
      setEditMode(true, editProjectId);
    } else if (!editProjectId && storeEditMode) {
      // URL doesn't have project_id but store thinks we're in edit mode
      clearEditMode();
    }
  }, [editProjectId, storeEditMode, setEditMode, clearEditMode]);

  // Memoize the load data function to prevent unnecessary re-renders
  const loadData = useCallback(async () => {
    if (existingProject?.data && editProjectId) {
      // Only load if we don't already have this project loaded
      if (!projectInfo?.id || projectInfo.id !== editProjectId) {
        await loadExistingProject(existingProject.data);
      }
    }
  }, [
    existingProject?.data,
    editProjectId,
    projectInfo?.id,
    loadExistingProject,
  ]);

  // Load project data into store when available
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Validate mode state and detect data leakage
  useEffect(() => {
    const leakageCheck = modeValidation.detectDataLeakage(
      isInCreateMode(),
      isInEditMode(),
      !!projectInfo?.id,
      editProjectId
    );

    if (leakageCheck.hasLeakage && process.env.NODE_ENV === "development") {
      console.warn("🚨 Data leakage detected:", leakageCheck.reason);
      // Auto-cleanup in development
      modeValidation.cleanupLeakedData(resetStore, setCreateFromScratch);
    }
  }, [
    projectInfo?.id,
    editProjectId,
    isInCreateMode,
    isInEditMode,
    resetStore,
    setCreateFromScratch,
  ]);

  // Handle mode transitions and store cleanup
  useEffect(() => {
    const currentUrl = window.location.href;
    const hasProjectIdInUrl = currentUrl.includes("project_id=");
    const isOnCreateProjectPage = currentUrl.includes("/create-project/");

    if (isOnCreateProjectPage) {
      if (!hasProjectIdInUrl && !editProjectId) {
        // We're in create mode - ensure proper state
        // Only reset if explicitly requested via mode param or if we're not in create mode
        // Don't reset just because projectInfo has an ID (that's normal after creating a project)
        if (modeParam === "create" || !isInCreateMode()) {
          // Clear any existing project data and set create mode
          resetStore(true); // Preserve create mode flag
          setCreateFromScratch();
        }
      } else if (hasProjectIdInUrl && editProjectId) {
        // We're in edit mode - clear create mode flag
        if (isInCreateMode()) {
          clearCreateFromScratch();
        }
      }
    }
  }, [
    editProjectId,
    modeParam,
    projectInfo?.id,
    isLoadingProject,
    resetStore,
    setCreateFromScratch,
    clearCreateFromScratch,
    isInCreateMode,
  ]);

  // Memoize helper functions to prevent unnecessary re-renders
  const buildUrl = useCallback(
    (path: string) => {
      if (!isEditMode) return path;
      const separator = path.includes("?") ? "&" : "?";
      return `${path}${separator}project_id=${editProjectId}`;
    },
    [isEditMode, editProjectId]
  );

  // Memoize project data ready check
  const isProjectDataReady = useMemo(() => {
    if (!isEditMode) return true; // In create mode, always ready
    return isProjectLoaded && projectInfo?.id === editProjectId;
  }, [isEditMode, isProjectLoaded, projectInfo?.id, editProjectId]);

  return {
    // Edit mode state
    isEditMode,
    editProjectId,

    // Project data state
    existingProject: existingProject?.data,
    isLoadingProject,
    projectError,
    isProjectLoaded,
    isProjectDataReady,

    // Helper functions
    buildUrl,

    // Mode detection with validation
    isInCreateMode: isInCreateMode(),
    isInEditMode: isInEditMode(),
    isValidCreateMode: modeValidation.validateCreateMode(
      isInCreateMode(),
      !!projectInfo?.id
    ),
    isValidEditMode: modeValidation.validateEditMode(
      isInEditMode(),
      !!projectInfo?.id,
      editProjectId
    ),

    // Store data (for convenience)
    projectInfo,
  };
}

/**
 * Hook for navigation with edit mode support
 * Automatically preserves project_id parameter when navigating
 */
export function useEditNavigation() {
  const { isEditMode, editProjectId, buildUrl } = useEditProject();

  const navigateToStep = useCallback(
    (step: string) => {
      const basePath = `/create-project/${step}`;
      return buildUrl(basePath);
    },
    [buildUrl]
  );

  // Memoize URLs to prevent unnecessary re-calculations
  const urls = useMemo(
    () => ({
      projectInformation: navigateToStep("project-information"),
      searchEngines: navigateToStep("search-engines"),
      keywords: navigateToStep("keywords"),
      competitors: navigateToStep("competitors"),
      analyticsServices: navigateToStep("analytics-services"),
    }),
    [navigateToStep]
  );

  return {
    isEditMode,
    editProjectId,
    buildUrl,
    navigateToStep,
    urls,
  };
}
