import { useEffect, useRef } from 'react';

/**
 * Custom hook to lock/unlock body scroll when modals are open
 * Preserves scroll position and handles multiple modal instances
 *
 * Features:
 * - Preserves scroll position when modal opens/closes
 * - Handles multiple modals (nested modals)
 * - Prevents background scrolling on mobile devices
 * - Maintains scrollbar width to prevent layout shift
 */
export default function useScrollLock(isLocked: boolean) {
  const scrollPositionRef = useRef<number>(0);
  const originalStyleRef = useRef<string>('');
  const originalHtmlStyleRef = useRef<string>('');
  const lockCountRef = useRef<number>(0);
  const scrollbarWidthRef = useRef<number>(0);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const body = document.body;
    const html = document.documentElement;

    // Helper function to get scrollbar width
    const getScrollbarWidth = () => {
      const outer = document.createElement('div');
      outer.style.visibility = 'hidden';
      outer.style.overflow = 'scroll';
      outer.style.msOverflowStyle = 'scrollbar';
      body.appendChild(outer);

      const inner = document.createElement('div');
      outer.appendChild(inner);

      const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
      outer.parentNode?.removeChild(outer);

      return scrollbarWidth;
    };

    if (isLocked) {
      // Increment lock count for multiple modals
      lockCountRef.current += 1;

      // Only apply scroll lock on first modal
      if (lockCountRef.current === 1) {
        // Store current scroll position
        scrollPositionRef.current = window.pageYOffset || html.scrollTop || body.scrollTop || 0;

        // Store original styles
        originalStyleRef.current = body.style.cssText;
        originalHtmlStyleRef.current = html.style.cssText;

        // Calculate scrollbar width to prevent layout shift
        scrollbarWidthRef.current = getScrollbarWidth();

        // Apply scroll lock styles
        body.style.position = 'fixed';
        body.style.top = `-${scrollPositionRef.current}px`;
        body.style.left = '0';
        body.style.right = '0';
        body.style.width = '100%';
        body.style.overflow = 'hidden';

        // Add padding to compensate for scrollbar width (prevents layout shift)
        if (scrollbarWidthRef.current > 0) {
          body.style.paddingRight = `${scrollbarWidthRef.current}px`;
        }

        // Prevent scrolling on html element as well
        html.style.overflow = 'hidden';

        // Add class for CSS targeting
        body.classList.add('modal-scroll-locked');
      }
    } else {
      // Decrement lock count
      lockCountRef.current = Math.max(0, lockCountRef.current - 1);

      // Only remove scroll lock when no modals are open
      if (lockCountRef.current === 0) {
        // Remove class
        body.classList.remove('modal-scroll-locked');

        // Restore original styles
        body.style.cssText = originalStyleRef.current;
        html.style.cssText = originalHtmlStyleRef.current;

        // Restore scroll position with a small delay to ensure DOM is ready
        requestAnimationFrame(() => {
          window.scrollTo(0, scrollPositionRef.current);
        });
      }
    }

    // Cleanup function
    return () => {
      if (isLocked && lockCountRef.current > 0) {
        lockCountRef.current = Math.max(0, lockCountRef.current - 1);

        // Clean up if this was the last modal
        if (lockCountRef.current === 0) {
          body.classList.remove('modal-scroll-locked');
          body.style.cssText = originalStyleRef.current;
          html.style.cssText = originalHtmlStyleRef.current;

          // Restore scroll position
          requestAnimationFrame(() => {
            window.scrollTo(0, scrollPositionRef.current);
          });
        }
      }
    };
  }, [isLocked]);

  // Return current lock state for debugging
  return {
    isLocked,
    lockCount: lockCountRef.current,
    scrollPosition: scrollPositionRef.current,
  };
}
