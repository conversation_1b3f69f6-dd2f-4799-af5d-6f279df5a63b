import { useProjectThemeColor } from "@/store/useProjectThemeColor";

/**
 * Hook specifically for sidebar components that should use project colors
 * This separates sidebar theming from general app theming
 */
export const useSidebarThemeColor = () => {
  const { themeColor, currentProject } = useProjectThemeColor();
  
  return {
    sidebarThemeColor: themeColor,
    currentProject,
  };
};

/**
 * Hook for general app components that should use the default primary color
 * regardless of the current project
 */
export const useAppThemeColor = () => {
  // Always return the default primary color for app components
  return {
    themeColor: "#914AC4", // Default primary color
  };
};
