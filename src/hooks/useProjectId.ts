"use client";

import { useParams } from "next/navigation";
import { useMemo } from "react";
import { useProjectContext } from "@/contexts/ProjectContext";

interface UseProjectIdReturn {
  projectId: string | null;
  isValidProjectId: boolean;
  projectIdError: string | null;
  isLoading: boolean;
}

/**
 * Custom hook to extract and validate project ID from URL parameters
 * Provides consistent project ID access across all project pages
 * Uses ProjectContext for enhanced validation when available
 */
export function useProjectId(): UseProjectIdReturn {
  try {
    // Try to use ProjectContext if available (enhanced validation with API checks)
    const context = useProjectContext();
    return {
      projectId: context.projectId,
      isValidProjectId: context.isValidProject,
      projectIdError: context.error,
      isLoading: context.isLoading,
    };
  } catch {
    // Fallback to basic URL parameter extraction if context is not available
    const params = useParams<{ projectId: string }>();

    const result = useMemo(() => {
      const projectId = params?.projectId || null;

      // Validation logic
      if (!projectId) {
        return {
          projectId: null,
          isValidProjectId: false,
          projectIdError: "Project ID is missing from URL parameters",
          isLoading: false,
        };
      }

      if (typeof projectId !== "string" || projectId.trim().length === 0) {
        return {
          projectId: null,
          isValidProjectId: false,
          projectIdError: "Invalid project ID format",
          isLoading: false,
        };
      }

      // Additional validation rules can be added here
      const trimmedProjectId = projectId.trim();

      if (trimmedProjectId.length < 1) {
        return {
          projectId: null,
          isValidProjectId: false,
          projectIdError: "Project ID cannot be empty",
          isLoading: false,
        };
      }

      return {
        projectId: trimmedProjectId,
        isValidProjectId: true,
        projectIdError: null,
        isLoading: false,
      };
    }, [params?.projectId]);

    return result;
  }
}

/**
 * Hook that returns just the project ID string or throws an error if invalid
 * Use this when you need the project ID and want to fail fast if it's not available
 */
export function useRequiredProjectId(): string {
  const { projectId, isValidProjectId, projectIdError } = useProjectId();

  if (!isValidProjectId || !projectId) {
    throw new Error(
      projectIdError || "Project ID is required but not available"
    );
  }

  return projectId;
}

/**
 * Hook that returns project ID with loading state
 * Useful for components that need to show loading while project ID is being resolved
 */
export function useProjectIdWithLoading(): {
  projectId: string | null;
  isLoading: boolean;
  error: string | null;
} {
  const { projectId, isValidProjectId, projectIdError, isLoading } =
    useProjectId();

  return {
    projectId: isValidProjectId ? projectId : null,
    isLoading,
    error: projectIdError,
  };
}
