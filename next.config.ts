import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    // Allow builds to proceed even if there are TypeScript errors
    ignoreBuildErrors: true,
  },
  eslint: {
    // Do not block builds on ESLint errors
    ignoreDuringBuilds: true,
  },
  images: {
    domains: [
      "seoanalyser.ai",
      "seoanalyser.com.au",
      'dev.seoanalyser.com.au',
      "seoanalyser.ai",
      "cdn.simpleicons.org",
      "cdn.jsdelivr.net",
    ],
  },
  // CORS headers with device ID support
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS",
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "X-Requested-With, Content-Type, Authorization, X-Device-ID",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
