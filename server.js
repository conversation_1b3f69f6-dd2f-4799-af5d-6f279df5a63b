

import { createServer } from "https";
import http from "http";
import { parse } from "url";
import next from "next";
import fs from "fs";

const app = next({ dev: false });
const handle = app.getRequestHandler();

// SSL Options
const httpsOptions = {
  key: fs.readFileSync("/etc/letsencrypt/live/seoanalyser.com.au/privkey.pem"),
  cert: fs.readFileSync("/etc/letsencrypt/live/seoanalyser.com.au/fullchain.pem"),
};

const HTTPS_PORT = 443;
const HTTP_PORT = 80;

app.prepare().then(() => {
  // HTTPS Server
  createServer(httpsOptions, (req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  }).listen(HTTPS_PORT, (err) => {
    if (err) throw err;
    console.log(`🚀 Next.js is running with SSL on https://seoanalyser.com.au`);
  });

  // HTTP to HTTPS Redirect
  http.createServer((req, res) => {
    res.writeHead(301, { Location: `https://seoanalyser.com.au${req.url}` });
    res.end();
  }).listen(HTTP_PORT, () => {
    console.log(`🔄 Redirecting HTTP to HTTPS`);
  });
});
