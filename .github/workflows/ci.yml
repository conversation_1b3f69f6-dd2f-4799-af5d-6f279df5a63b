name: CI/CD

on:
  push:
    branches:
      - dev
      - merge-analytics-traffic-overview

  pull_request:
    branches:
      - dev
      - merge-analytics-traffic-overview

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev-v2'

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"

      - name: Check Node.js version
        run: node -v

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.5.1
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Deploy to Linux Server
        env:
          HOST: ${{ secrets.SERVER_HOST }}
          USERNAME: ${{ secrets.SERVER_USERNAME }}
          TARGET_DIR: ${{ secrets.TARGET_DIR }}
          APP_NAME: Front-End
          PORT: 3000
        run: |
          ssh -o StrictHostKeyChecking=no $USERNAME@$HOST << EOF
            # Send CTRL-C to stop the existing process
            tmux send-keys -t ${GITHUB_REF#refs/heads/} C-c

            # Wait for process to stop
            sleep 5

            # Navigate and update code
            tmux send-keys -t ${GITHUB_REF#refs/heads/} "cd $TARGET_DIR" C-m
            tmux send-keys -t ${GITHUB_REF#refs/heads/} "git pull origin ${GITHUB_REF#refs/heads/}" C-m
            
            # Install dependencies and build
            tmux send-keys -t ${GITHUB_REF#refs/heads/} "npm install" C-m
            tmux send-keys -t ${GITHUB_REF#refs/heads/} "npm run build" C-m
            
            # Start the application
            tmux send-keys -t ${GITHUB_REF#refs/heads/} "npm run dev" C-m
          EOF
